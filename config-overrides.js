const path = require('path');
const { override, addBabelPreset } = require('customize-cra');
const jsconfig = require('./tsconfig.paths.json');
const packageJson = require('./package.json');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const postcssNormalize = require('postcss-normalize');
const webpack = require('webpack');
const MonacoWebpackPlugin = require('monaco-editor-webpack-plugin');

const adjustFileLoader = () => (config) => {
  const rules = config.module.rules.find((rule) => Array.isArray(rule.oneOf)).oneOf;
  const assetResourceRule = rules.find((rule) => rule.type === 'asset/resource');
  if (assetResourceRule) {
    const publicPath =
      process.env.NODE_ENV === 'development' ? `http://localhost:${process.env.PORT}/` : `/${packageJson.name}/`;
    assetResourceRule.generator = {
      ...assetResourceRule.generator,
      filename: 'media/[name].[hash:8][ext]', // 修改 filename 配置以移除多余的 'static/media/'
      publicPath,
    };
  }
  return config;
};

function excludeSpecificCSS(config) {
  config.module.rules.forEach((rule) => {
    if (rule.oneOf) {
      rule.oneOf.forEach((one) => {
        if (one.test && (one.test.toString().includes('.css') || one.test.toString().includes('.less'))) {
          // 如果已存在排除规则且是正则表达式，将其转换为数组
          if (one.exclude && !(one.exclude instanceof Array)) {
            one.exclude = [one.exclude];
          }
          // 如果不存在排除规则，初始化为空数组
          one.exclude = one.exclude || [];
          // 添加新的排除规则
          one.exclude.push(
            /node_modules\/@tencent\/tea-component/,
            /node_modules\/@tencent\/tcs-component/,
            /node_modules\/@ant-design/,
          );
        }
      });
    }
  });
}
const addLessLoader =
  (loaderOptions = {}, customCssModules = {}) =>
  (config) => {
    // const MiniCssExtractPlugin = require("mini-css-extract-plugin");
    const miniCssExtractPlugin = config.plugins.find((plugin) => plugin.constructor.name === 'MiniCssExtractPlugin');
    const cssLoaderOptions = loaderOptions.cssLoaderOptions || {};

    const { localIdentName } = loaderOptions;
    let cssModules = loaderOptions.cssModules || { localIdentName };

    if (!cssModules.localIdentName) {
      cssModules = customCssModules;
    }

    cssModules.localIdentName = cssModules.localIdentName || '[local]--[hash:base64:5]';

    const lessRegex = /\.less$/;
    const lessModuleRegex = /\.module\.less$/;

    const webpackEnv = process.env.NODE_ENV;
    const isEnvDevelopment = webpackEnv === 'development';
    const isEnvProduction = webpackEnv === 'production';
    const shouldUseSourceMap = process.env.GENERATE_SOURCEMAP !== 'false';
    const { publicPath } = config.output;
    const shouldUseRelativeAssetPaths = publicPath === './';

    // copy from react-scripts
    // https://github.com/facebook/create-react-app/blob/master/packages/react-scripts/config/webpack.config.js#L93
    const getStyleLoaders = (cssOptions, preProcessor) => {
      const loaders = [
        isEnvDevelopment && require.resolve('style-loader'),
        isEnvProduction && {
          loader: miniCssExtractPlugin.constructor.loader,
          options: shouldUseRelativeAssetPaths ? { publicPath: '../../' } : {},
        },
        {
          loader: require.resolve('css-loader'),
          options: cssOptions,
        },
        {
          loader: require.resolve('postcss-loader'),
          options: {
            ident: 'postcss',
            plugins: () => [
              require('postcss-flexbugs-fixes'),
              require('postcss-preset-env')({
                autoprefixer: {
                  flexbox: 'no-2009',
                },
                stage: 3,
              }),
              postcssNormalize(),
            ],
            sourceMap: isEnvProduction && shouldUseSourceMap,
          },
        },
      ].filter(Boolean);
      if (preProcessor) {
        loaders.push(
          {
            loader: require.resolve('resolve-url-loader'),
            options: {
              sourceMap: isEnvProduction && shouldUseSourceMap,
            },
          },
          {
            loader: require.resolve(preProcessor),
            // not the same as react-scripts
            options: Object.assign(
              {
                sourceMap: true,
              },
              loaderOptions,
            ),
          },
        );
      }
      return loaders;
    };
    const loaders = config.module.rules.find((rule) => Array.isArray(rule.oneOf)).oneOf;

    // Insert less-loader as the penultimate item of loaders (before file-loader)
    loaders.splice(
      loaders.length - 1,
      0,
      {
        test: lessRegex,
        exclude: lessModuleRegex,
        use: getStyleLoaders(
          Object.assign(
            {
              importLoaders: 2,
              sourceMap: isEnvProduction && shouldUseSourceMap,
            },
            cssLoaderOptions,
          ),
          'less-loader',
        ),
      },
      {
        test: lessModuleRegex,
        use: getStyleLoaders(
          Object.assign(
            {
              importLoaders: 2,
              sourceMap: isEnvProduction && shouldUseSourceMap,
            },
            cssLoaderOptions,
            {
              modules: cssModules,
            },
          ),
          'less-loader',
        ),
      },
    );
    return config;
  };

/** 配置webpack覆盖默认 */
module.exports = {
  webpack: override(
    addBabelPreset([
      '@babel/preset-typescript',
      {
        allowNamespaces: true,
      },
    ]),
    addLessLoader({
      lessOptions: {
        javascriptEnabled: true,
      },
    }),
    (() => (config, env) => {
      // 调用自定义函数来修改CSS和LESS加载规则
      excludeSpecificCSS(config);

      handleOne(config.module.rules[1].oneOf);
      const newConfig = config;
      newConfig.resolve.alias = getAlias(jsconfig);

      // 开发和生产都需要提取的公共依赖
      const externals = {
        react: 'React',
        'react-dom': 'ReactDOM',
        'react-router': 'ReactRouter',
        'react-router-dom': 'ReactRouterDOM',
        moment: 'moment',
        '@tencent/tea-component/lib': 'teaComponent',
        '@tencent/tea-component': 'teaComponent',
        '@tencent/tcs-component': 'tcsComponent',
        '@logicflow/core': 'LogicFlow',
        '@monaco-editor/react': 'monaco_react',
        'monaco-editor': 'monaco',
        exceljs: 'ExcelJS',
        lodash: '_',
      };

      if (process.env.NODE_ENV === 'development') {
        newConfig.devtool = 'cheap-module-eval-source-map';
        newConfig.output.publicPath = `http://localhost:${process.env.PORT}/`;
        newConfig.externals = externals;
      } else {
        newConfig.devtool = 'source-map';
        newConfig.externals = externals;
      }

      newConfig.output.filename = `static/js/${packageJson.name}.js`;
      newConfig.plugins.push(
        new MonacoWebpackPlugin({
          publicPath: `/imgcache/${packageJson.name}/js`,
          // 所需语言支持
          languages: ['yaml', 'json', 'shell'],
        }),
      );
      newConfig.output.chunkFilename = 'static/js/[id].js';
      newConfig.optimization.splitChunks = {
        chunks: 'all',
        minSize: 30000, // 生成 chunk 的最小体积（以字节为单位）
        maxSize: 0, // 生成 chunk 的最大体积（以字节为单位）
        minChunks: 1, // 模块被引用的最小次数
        maxAsyncRequests: 6, // 按需加载时的最大并行请求数
        maxInitialRequests: 4, // 入口点的最大并行请求数
        automaticNameDelimiter: '~', // 文件名的连接符
        cacheGroups: {
          defaultVendors: {
            test: /[\\/]node_modules[\\/]/,
            priority: -10,
            reuseExistingChunk: true,
          },
          default: {
            minChunks: 2,
            priority: -20,
            reuseExistingChunk: true,
          },
        },
      };
      newConfig.optimization.runtimeChunk = false;

      for (let i = 0; i < newConfig.plugins.length; i++) {
        const plugin = newConfig.plugins[i];
        if (plugin.constructor && plugin.constructor.name === MiniCssExtractPlugin.name) {
          const options = {
            ...plugin.options,
            moduleFilename() {
              return `static/css/${packageJson.name}.css`;
            },
          };
          newConfig.plugins[i] = new MiniCssExtractPlugin(options);
          break;
        }
      }

      newConfig.plugins.push(
        new webpack.optimize.LimitChunkCountPlugin({
          maxChunks: 1,
        }),
      );
      // newConfig.plugins.push(new BundleAnalyzerPlugin());
      const isDevelopment = env === 'development';

      newConfig.plugins.push(
        new webpack.DefinePlugin({
          'process.env': {
            NODE_ENV: isDevelopment ? JSON.stringify('development') : JSON.stringify('production'),
          },
        }),
      );

      newConfig.module.rules.push({
        test: /\.mjs$/,
        include: /node_modules/,
        type: 'javascript/auto',
        use: {
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env', '@babel/preset-react'],
            plugins: ['@babel/plugin-proposal-optional-chaining'],
          },
        },
      });
      return newConfig;
    })(),
    adjustFileLoader(),
  ),
  devServer: function (configFunction) {
    // Return the replacement function for create-react-app to use to generate the Webpack
    // Development Server config. "configFunction" is the function that would normally have
    // been used to generate the Webpack Development server config - you can use it to create
    // a starting configuration to then modify instead of having to create a config from scratch.
    return function (proxy, allowedHost) {
      // Create the default config by calling configFunction with the proxy/allowedHost parameters
      const config = configFunction(proxy, allowedHost);
      config.hot = true;
      config.headers = {
        'Access-Control-Allow-Credentials': 'true',
        'Access-Control-Allow-Origin': '*',
      };

      // Return your customised Webpack Development Server config.
      return config;
    };
  },
};

/**  处理alias * */
function getAlias(config) {
  const alias = {};

  for (let [name, mapping] of Object.entries(config.compilerOptions.paths)) {
    if (!mapping || !Array.isArray(mapping) || mapping.length !== 1) {
      console.warn(`[Tea] 无法从配置文件解析到路径映射: compilerOptions.path["${name}"]`);
      console.warn('      一个合法的路径应映射为包含单个路径的数组, 如: ["../node_modules/test"]');
    } else {
      let [aliasPath] = mapping;
      // WebPack 不支持通配，但是默认就是通配了，直接把通配符去掉
      name = name.replace(/\/\*$/, '');
      aliasPath = aliasPath.replace(/\/\*$/, '');
      alias[name] = path.resolve(__dirname, config.compilerOptions.baseUrl, aliasPath);
    }
  }
  return alias;
}

function handleOne(oneOf) {
  (oneOf || []).forEach((item) => {
    if (item.use) {
      item.use = (item.use || []).map((subItem) => {
        if (typeof subItem === 'string' && subItem.indexOf('style-loader') !== -1) {
          return {
            loader: subItem,
            options: {
              insert: (element) => {
                const parent = document.getElementById('root-resource');
                const lastInsertedElement = window._lastElementInsertedByStyleLoader;
                if (!lastInsertedElement) {
                  parent.insertBefore(element, parent.firstChild);
                } else if (lastInsertedElement.nextSibling) {
                  parent.insertBefore(element, lastInsertedElement.nextSibling);
                } else {
                  parent.appendChild(element);
                }
              },
            },
          };
        }
        return subItem;
      });
      // console.log(item.use);
    }
  });
}
