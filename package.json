{"name": "defect_manage", "version": "0.1.0", "private": true, "description": "缺陷单，交付单管理", "dependencies": {"@ant-design/icons": "^4.2.2", "@commitlint/resolve-extends": "^18.6.1", "@monaco-editor/react": "^4.5.1", "@tencent/exeditor3-plugin-picture": "^3.9.0", "@tencent/exeditor3-preset-editor": "^3.6.0", "@tencent/jpdesign-components": "1.0.215-beta.8", "@tencent/tcs-component": "^0.0.84", "@tencent/tcsc-base": "^0.0.10", "@tencent/tea-component": "^2.7.6", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.5.0", "@testing-library/user-event": "^7.2.1", "ahooks": "^3.7.5", "antd": "4.24.15", "axios": "^1.6.5", "cherry-markdown": "^0.8.58", "dayjs": "^1.11.7", "exceljs": "^4.4.0", "html-react-parser": "^5.2.1", "md5": "^2.3.0", "moment": "^2.29.4", "monaco-editor": "^0.30.1", "patch-package": "^6.5.1", "rc-field-form": "^1.42.1", "react": "^17.0.2", "react-dom": "^17.0.2", "react-hook-form": "^7.43.5", "react-redux": "^7.2.1", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "react-scripts": "4.0.3", "redux": "^4.0.5", "redux-devtools-extension": "^2.13.8", "redux-logger": "^3.0.6", "redux-thunk": "^2.3.0", "typescript": "^3.8.3"}, "scripts": {"start": "cross-env  DISABLE_ESLINT_PLUGIN=true PORT=3008 WDS_SOCKET_HOST=localhost WDS_SOCKET_PORT=3008 NODE_ENV=development react-app-rewired start", "build": "react-app-rewired --max_old_space_size=8192 build", "test": "react-scripts test", "eject": "react-scripts eject", "precommit": "lint-staged", "lint": "eslint src --fix --ext .js,.jsx,.ts,.tsx", "style": "stylelint  \"src/**/*.css\" --fix", "autofix": "npm run lint && npm run style", "preinstall": "npx npm-force-resolutions || echo 'Skipping npm-force-resolutions'"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "git add"], "*.css": "stylelint \"src/**/*.css\" --fix"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.22.9", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/preset-env": "^7.22.9", "@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@tencent/eslint-config-tencent": "0.16.1", "@tencent/stylelint-config-tencent": "^1.0.7", "@types/jest": "^24.9.1", "@types/node": "^12.12.62", "@types/react": "^16.9.49", "@types/react-dom": "^16.9.8", "@types/react-redux": "^7.1.9", "@types/react-router": "^5.1.8", "@types/react-router-dom": "^5.1.5", "@types/redux-logger": "^3.0.8", "@typescript-eslint/eslint-plugin": "^4.28.4", "@typescript-eslint/parser": "^4.28.4", "color-name": "^2.0.0", "cross-env": "^7.0.2", "customize-cra": "^1.0.0", "eslint": "^7.10.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-prettier": "3.4.0", "eslint-plugin-react": "^7.21.0", "eslint-plugin-react-hooks": "^4.1.2", "husky": "^2.3.0", "less-loader": "^6.2.0", "lint-staged": "^8.1.7", "monaco-editor-webpack-plugin": "^6.0.0", "npm-force-resolutions": "0.0.10", "postcss-selector-namespace": "^3.0.1", "prettier": "^2.3.2", "react-app-rewired": "^2.1.6", "react-error-overlay": "6.0.9", "sass": "^1.62.1", "stylelint": "^13.6.1", "webpack-bundle-analyzer": "^4.8.0", "yup": "^0.32.11"}}