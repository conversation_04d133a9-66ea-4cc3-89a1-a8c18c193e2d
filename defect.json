{"page": {"id": "c11320be-0b57-42ee-9546-607dfef02a8a", "name": "页面", "code": "__page__", "properties": {"pageWidth": "420", "pageAlign": "center", "showPageHeader": false, "inputParams": {"value": "return {\n  readonly:false,\n  formData:{\n    \"Arch\": \"rhel.amd64\",\n    \"CreatedAt\": \"2023-08-28 15:51:37\",\n    \"Creator\": \"leslielyuan\",\n    \"Description\": \"<div><div><div>【问题描述】：适配央行云海光7280</div><div>【重现步骤】：&nbsp;</div><div>【根本原因】：</div><div>【修复内容】：自测过程中发现问题修复:</div><div style=\\\"padding-left: 40px;\\\">1) 漏提交了RevcServer的二进制文件</div></div><div><div class=\\\"tox-clear-float\\\"><img src=\\\"//tapd.woa.com/tfl/captures/2023-08/tapd_20419092_base64_1693209031_121.png\\\" width=\\\"80%\\\"  /></div></div></div>\",\n    \"DevOwners\": \"leslielyuan\",\n    \"ID\": 220,\n    \"IssueID\": \"DE00220\",\n    \"Module\": \"安全-网络入侵防护系统（NIPS）\",\n    \"Owner\": \"leslielyuan\",\n    \"Priority\": \"medium\",\n    \"SeverityLevel\": \"serious\",\n    \"SolutionUUID\": \"55ea07d7c79b425695f04b8ab045d053\",\n    \"SolutionVersion\": \"TCE3.10.0\",\n    \"Source\": \"Other\",\n    \"Status\": \"open\",\n    \"TapdUrl\": \"http://tapd.oa.com/20419092/bugtrace/bugs/view/1020419092114492467\",\n    \"TenantUUID\": \"b4e51313d53b44a5b5f5d04caaab717d\",\n    \"Title\": \"【TCE3100X86】【PSG】央行云海光7280交付 自测问题修复单\",\n    \"Type\": \"OnlineBug\",\n    \"UpdateAt\": \"2023-08-28T16:30:47+08:00\"\n}\n}", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return {\n      readonly: false,\n      formData: {\n        \"Arch\": \"rhel.amd64\",\n        \"CreatedAt\": \"2023-08-28 15:51:37\",\n        \"Creator\": \"leslielyuan\",\n        \"Description\": \"<div><div><div>【问题描述】：适配央行云海光7280</div><div>【重现步骤】：&nbsp;</div><div>【根本原因】：</div><div>【修复内容】：自测过程中发现问题修复:</div><div style=\\\"padding-left: 40px;\\\">1) 漏提交了RevcServer的二进制文件</div></div><div><div class=\\\"tox-clear-float\\\"><img src=\\\"//tapd.woa.com/tfl/captures/2023-08/tapd_20419092_base64_1693209031_121.png\\\" width=\\\"80%\\\"  /></div></div></div>\",\n        \"DevOwners\": \"leslielyuan\",\n        \"ID\": 220,\n        \"IssueID\": \"DE00220\",\n        \"Module\": \"安全-网络入侵防护系统（NIPS）\",\n        \"Owner\": \"leslielyuan\",\n        \"Priority\": \"medium\",\n        \"SeverityLevel\": \"serious\",\n        \"SolutionUUID\": \"55ea07d7c79b425695f04b8ab045d053\",\n        \"SolutionVersion\": \"TCE3.10.0\",\n        \"Source\": \"Other\",\n        \"Status\": \"open\",\n        \"TapdUrl\": \"http://tapd.oa.com/20419092/bugtrace/bugs/view/1020419092114492467\",\n        \"TenantUUID\": \"b4e51313d53b44a5b5f5d04caaab717d\",\n        \"Title\": \"【TCE3100X86】【PSG】央行云海光7280交付 自测问题修复单\",\n        \"Type\": \"OnlineBug\",\n        \"UpdateAt\": \"2023-08-28T16:30:47+08:00\"\n      }\n    };\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": []}}, "events": {"eventList": [{"eventName": "onLoad", "eventType": "custom", "eventCode": "// 工具类\nfunction requestCloudApi(options) {\n  const {\n    action,\n    paramVar,\n    resultCode,\n    outerParamName,\n    isPaging = false\n  } = options\n  return $utils.api.requestCloudApi(action, {\n    [outerParamName]: paramVar\n  }).then(res => {\n    if (res.Error) {\n      throw new Error(res.Error.Message)\n    } else {\n      // 如果有分页，则返回数据和分页\n      if (isPaging) {\n        return {\n          data: res[resultCode],\n          total: res.Total\n        }\n      }\n      return res[resultCode]\n    }\n  }).catch(error => {\n    $utils.ui.message.error(error.message);\n    throw error;\n  })\n}\n\nasync function requestLookups(types, useTenantID) {\n  return await requestCloudApi({\n    action: \"ListProductDictionaryDetails\",\n    paramVar: $utils.api.transformCloudApiParams({\n      Type: {\n        Op: \"in\",\n        Value: types || []\n      },\n      TenantID: useTenantID ? window.jiguang_currentNs: 0\n    }, {\n      useEqFields: [\"TenantID\"]\n    }),\n    resultCode: \"ListProductDictionaries\",\n    outerParamName: \"ProductDictionary\"\n  })\n}\n\nfunction arrayToObject(array, key) {\n  const object = {}\n  array.forEach(item => {\n    if (!object[item[key]]) {\n      object[item[key]] = []\n    }\n    object[item[key]].push(item)\n  })\n  return object;\n}\n\n// 加载数据字典值\ntry {\n  const [lookupResult_PackageArch_Severity_Priority_IssueSource_IssueStatus_BugType, IssueStatus] = await Promise.all([ requestLookups([\"PackageArch\",\n    \"Severity\",\n    \"Priority\",\n    \"IssueSource\",\n    \"IssueStatus\",\n    \"BugType\"],\n    false), requestLookups([\"IssueStatus\"],true)]);\n    \n    const lookupMaps = arrayToObject(lookupResult_PackageArch_Severity_Priority_IssueSource_IssueStatus_BugType || [], \"Type\")\n    \n    if(IssueStatus?.length) {\n      const globalIssueStatus = lookupMaps[\"IssueStatus\"] || [];\n      const globalIssueStatusCodes = globalIssueStatus.map(item => item.Code);\n      IssueStatus.forEach(issue => {\n        if(!globalIssueStatusCodes.includes(issue.Code)){\n          globalIssueStatus.push(issue)\n        }\n      })\n    }\n    \n  $utils.state.updateState(state => {\n    state.lookupMaps = {\n      ...state.lookupMaps,\n      ...lookupMaps\n    }\n  })\n\n} catch (error) {}", "codeGenerateInfo": "[{\"id\":\"requestLookup\",\"uuid\":\"c12cfa62-9e8a-49a9-934b-6a523530a6ad\",\"title\":\"加载数据字典值\",\"properties\":{\"type\":\"OSArch,Severity,Priority,IssueSource,IssueStatus\",\"useTenantID\":false,\"mountGlobalState\":true,\"globalStateName\":\"lookupMaps\"},\"catchList\":[],\"finallyList\":[],\"chosen\":false,\"selected\":false,\"name\":\"\"}]", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = async function () {\n  try {\n    // 工具类\n    function requestCloudApi(options) {\n      const {\n        action,\n        paramVar,\n        resultCode,\n        outerParamName,\n        isPaging = false\n      } = options;\n      return $utils.api.requestCloudApi(action, {\n        [outerParamName]: paramVar\n      }).then(res => {\n        if (res.Error) {\n          throw new Error(res.Error.Message);\n        } else {\n          // 如果有分页，则返回数据和分页\n          if (isPaging) {\n            return {\n              data: res[resultCode],\n              total: res.Total\n            };\n          }\n          return res[resultCode];\n        }\n      }).catch(error => {\n        $utils.ui.message.error(error.message);\n        throw error;\n      });\n    }\n    async function requestLookups(types, useTenantID) {\n      return await requestCloudApi({\n        action: \"ListProductDictionaryDetails\",\n        paramVar: $utils.api.transformCloudApiParams({\n          Type: {\n            Op: \"in\",\n            Value: types || []\n          },\n          TenantID: useTenantID ? window.jiguang_currentNs : 0\n        }, {\n          useEqFields: [\"TenantID\"]\n        }),\n        resultCode: \"ListProductDictionaries\",\n        outerParamName: \"ProductDictionary\"\n      });\n    }\n    function arrayToObject(array, key) {\n      const object = {};\n      array.forEach(item => {\n        if (!object[item[key]]) {\n          object[item[key]] = [];\n        }\n        object[item[key]].push(item);\n      });\n      return object;\n    }\n\n    // 加载数据字典值\n    try {\n      const [lookupResult_PackageArch_Severity_Priority_IssueSource_IssueStatus_BugType, IssueStatus] = await Promise.all([requestLookups([\"PackageArch\", \"Severity\", \"Priority\", \"IssueSource\", \"IssueStatus\", \"BugType\"], false), requestLookups([\"IssueStatus\"], true)]);\n      const lookupMaps = arrayToObject(lookupResult_PackageArch_Severity_Priority_IssueSource_IssueStatus_BugType || [], \"Type\");\n      if (IssueStatus !== null && IssueStatus !== void 0 && IssueStatus.length) {\n        const globalIssueStatus = lookupMaps[\"IssueStatus\"] || [];\n        const globalIssueStatusCodes = globalIssueStatus.map(item => item.Code);\n        IssueStatus.forEach(issue => {\n          if (!globalIssueStatusCodes.includes(issue.Code)) {\n            globalIssueStatus.push(issue);\n          }\n        });\n      }\n      $utils.state.updateState(state => {\n        state.lookupMaps = {\n          ...state.lookupMaps,\n          ...lookupMaps\n        };\n      });\n    } catch (error) {}\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()"}]}, "list": [{"properties": {"initialValues": {"value": "const formData = $context.inputParams?.formData || {}\n\nreturn {\n  ...formData,\n  TapdUrl: formData.TapdUrl ? \"点击查看\" : \"-\",\n  IterationName: $context.globalState.currentIterationDetail?.Name\n}", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    var _$context$inputParams, _$context$globalState;\n    const formData = ((_$context$inputParams = $context.inputParams) === null || _$context$inputParams === void 0 ? void 0 : _$context$inputParams.formData) || {};\n    return {\n      ...formData,\n      TapdUrl: formData.TapdUrl ? \"点击查看\" : \"-\",\n      IterationName: (_$context$globalState = $context.globalState.currentIterationDetail) === null || _$context$globalState === void 0 ? void 0 : _$context$globalState.Name\n    };\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.inputParams.formData", "$context.globalState.currentIterationDetail.Name"]}, "colNum": 1, "layout": "horizontal", "disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "labelWrap": true, "labelWidth": 110, "dataMonitor": true, "submitterButtons": {"showCount": 3, "preRender": {"useExpr": true, "wrapFunction": false, "dependencyVariableList": []}, "postRender": {"useExpr": true, "wrapFunction": false, "dependencyVariableList": []}}, "readonly": {"value": "return $context.inputParams?.readonly", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    var _$context$inputParams;\n    return (_$context$inputParams = $context.inputParams) === null || _$context$inputParams === void 0 ? void 0 : _$context$inputParams.readonly;\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.inputParams.readonly"]}}, "id": "fe387b01-158f-42ce-8113-d056ddce9e9c", "type": "form", "category": "container", "list": [{"properties": {"formItemProps": {"label": {"value": "状态", "useExpr": false, "wrapFunction": false}, "name": "Status", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": true, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"useExpr": false, "wrapFunction": false}, "tag": false, "showSearch": true, "searchCaseSensitive": false, "defaultValue": {"useExpr": false, "wrapFunction": false}, "placeholder": {"useExpr": false, "wrapFunction": false}, "labelInValue": false, "propLoading": {"value": false, "useExpr": false, "wrapFunction": false}, "dataSource": {"sourceType": "context", "contextInfo": {"source": "globalState", "key": "lookupMaps.IssueStatus"}}, "autoTransformData": true, "changeDataSelectValue": "nothing", "transformDataType": "object", "labelField": "Name", "valueField": "Code", "colorField": "Extra.Color"}}, "id": "32320faa-0bf8-4d96-bc24-1425b78ab6e2", "type": "select", "category": "component", "events": {"eventList": []}, "name": "下拉框", "code": "select_1686654958559", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "下拉框", "code": "select_1686654958559", "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}, "isRender": {"value": "return $context.inputParams.readonly", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return $context.inputParams.readonly;\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.inputParams.readonly"]}}}, {"properties": {"formItemProps": {"label": {"value": "关联需求", "useExpr": false, "wrapFunction": false}, "name": "ParentStoryIssueID", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"value": "if( $context.inputParams?.readonly){\n  return \"\"\n}\n\nreturn <$components.Button size=\"small\" type=\"link\" onClick={() => {\n  $utils.dialog.open({ dialogCode: \"modal_1689249881197\", inputParams: {\n    Priority:$context.globalState.lookupMaps.Priority,\n    IssueStatus: $context.globalState.lookupMaps.IssueStatus,\n    ParentStoryIssueID:$context.pageMapRef[\"form_1679296507834\"].current.getFieldValue(\"ParentStoryIssueID\")\n  }, closeCallback:(result) => {\n    if(result.operationType === \"confirm\"){\n       $context.pageMapRef[\"form_1679296507834\"].current.setFieldsValue({\"ParentStoryIssueID\":result.storyID || \"\"})\n    }\n  }})\n}}>选择需求</$components.Button>", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    var _$context$inputParams;\n    if ((_$context$inputParams = $context.inputParams) !== null && _$context$inputParams !== void 0 && _$context$inputParams.readonly) {\n      return \"\";\n    }\n    return /*#__PURE__*/React.createElement($components.Button, {\n      size: \"small\",\n      type: \"link\",\n      onClick: () => {\n        $utils.dialog.open({\n          dialogCode: \"modal_1689249881197\",\n          inputParams: {\n            Priority: $context.globalState.lookupMaps.Priority,\n            IssueStatus: $context.globalState.lookupMaps.IssueStatus,\n            ParentStoryIssueID: $context.pageMapRef[\"form_1679296507834\"].current.getFieldValue(\"ParentStoryIssueID\")\n          },\n          closeCallback: result => {\n            if (result.operationType === \"confirm\") {\n              $context.pageMapRef[\"form_1679296507834\"].current.setFieldsValue({\n                \"ParentStoryIssueID\": result.storyID || \"\"\n              });\n            }\n          }\n        });\n      }\n    }, \"\\u9009\\u62E9\\u9700\\u6C42\");\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.inputParams.readonly", "$context.globalState.lookupMaps.Priority", "$context.globalState.lookupMaps.IssueStatus", "$context.pageMapRef[\"form_1679296507834\"].current.getFieldValue", "$context.pageMapRef[\"form_1679296507834\"].current.setFieldsValue"]}}, "fieldProps": {"type": "link", "copyable": false, "editable": false}}, "id": "232dac42-601c-4d09-80ea-3ad579c45f53", "type": "editText", "category": "component", "events": {"eventList": [{"eventName": "onClick", "eventType": "custom", "eventCode": "if($context.editText) {\n  window.open(`//${window.location.host}/page/defect_manage/__develop_center_next/iteration_manage/story/detail?issue_id=${$context.editText}`)\n}", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    if ($context.editText) {\n      window.open(\"//\".concat(window.location.host, \"/page/defect_manage/__develop_center_next/iteration_manage/story/detail?issue_id=\").concat($context.editText));\n    }\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()"}]}, "name": "可编辑文本", "code": "editText_1689249355010", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "可编辑文本", "code": "editText_1689249355010", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "关联迭代UUID", "useExpr": false, "wrapFunction": false}, "name": "IterationUUID", "hidden": {"value": true, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"showType": "text", "disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "trim": true, "placeholder": {"useExpr": false, "wrapFunction": false}}}, "id": "88913210-b1cf-4f84-aaef-8e559587dabb", "type": "input", "category": "component", "events": {"eventList": []}, "name": "关联迭代", "code": "input_1692066003073", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "关联迭代", "code": "input_1692066003073", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "关联迭代", "useExpr": false, "wrapFunction": false}, "name": "IterationName", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"value": "// 暂时屏蔽掉选择迭代能力\n// if( $context.inputParams?.readonly){\n//   return \"\"\n// }\n\n// return <$components.Button size=\"small\" type=\"link\" onClick={() => {\n//   $utils.dialog.open({ dialogCode: \"modal_1692005257215\", inputParams: {\n//     IterationUUID:$context.pageMapRef[\"form_1679296507834\"].current.getFieldValue(\"IterationUUID\")\n//   }, closeCallback:(result) => {\n//     if(result.operationType === \"confirm\"){\n//       console.log($context)\n//       $context.pageMapRef[\"form_1679296507834\"].current.setFieldsValue({\"IterationUUID\":result.iteration?.UUID || \"\",\"IterationName\":result.iteration?.Name || \"\"})\n//     }\n//   }})\n// }}>选择迭代</$components.Button>", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n        \"use strict\";\n\nconst __result__ = function () {\n  try {\n    // 暂时屏蔽掉选择迭代能力\n    // if( $context.inputParams?.readonly){\n    //   return \"\"\n    // }\n\n    // return <$components.Button size=\"small\" type=\"link\" onClick={() => {\n    //   $utils.dialog.open({ dialogCode: \"modal_1692005257215\", inputParams: {\n    //     IterationUUID:$context.pageMapRef[\"form_1679296507834\"].current.getFieldValue(\"IterationUUID\")\n    //   }, closeCallback:(result) => {\n    //     if(result.operationType === \"confirm\"){\n    //       console.log($context)\n    //       $context.pageMapRef[\"form_1679296507834\"].current.setFieldsValue({\"IterationUUID\":result.iteration?.UUID || \"\",\"IterationName\":result.iteration?.Name || \"\"})\n    //     }\n    //   }})\n    // }}>选择迭代</$components.Button>\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n          return __result__;\n      })()", "dependencyVariableList": ["$context.inputParams.readonly", "$context.pageMapRef[\"form_1679296507834\"].current.getFieldValue", "$context.pageMapRef[\"form_1679296507834\"].current.setFieldsValue"]}}, "fieldProps": {"type": "link", "copyable": false, "editable": false}}, "id": "c1ef3685-c054-4662-9d08-17a7ecd90838", "type": "editText", "category": "component", "events": {"eventList": [{"eventName": "onClick", "eventType": "custom", "eventCode": "const uuid = $context.parentRef.getFieldValue(\"IterationUUID\")\nif(uuid) {\n  window.open(`//${window.location.host}/page/defect_manage/__develop_center_next/requirement_iteration_manage/detail?iteration_id=${uuid}`)\n}", "compileExprCode": "return (function(){\n        \"use strict\";\n\nconst __result__ = function () {\n  try {\n    const uuid = $context.parentRef.getFieldValue(\"IterationUUID\");\n    if (uuid) {\n      window.open(\"//\".concat(window.location.host, \"/page/defect_manage/__develop_center_next/requirement_iteration_manage/detail?iteration_id=\").concat(uuid));\n    }\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n          return __result__;\n      })()"}]}, "name": "可编辑文本", "code": "editText_1692006152759", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "可编辑文本", "code": "editText_1692006152759", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}}}, {"properties": {"formItemProps": {"label": {"value": "缺陷类型", "useExpr": false, "wrapFunction": false}, "name": "Type", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": true, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}, "requiredMessage": {"value": "该项为必填项", "useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"useExpr": false, "wrapFunction": false}, "tag": false, "showSearch": true, "searchCaseSensitive": false, "defaultValue": {"value": "Other", "useExpr": false, "wrapFunction": false}, "placeholder": {"value": "请选择缺陷类型", "useExpr": false, "wrapFunction": false}, "labelInValue": false, "propLoading": {"value": false, "useExpr": false, "wrapFunction": false}, "dataSource": {"sourceType": "context", "contextInfo": {"source": "globalState", "key": "lookupMaps.BugType"}}, "autoTransformData": true, "changeDataSelectValue": "nothing", "transformDataType": "object", "labelField": "Name", "valueField": "Code", "colorField": ""}}, "id": "3b145e8a-1dea-4086-ab5c-a0bf07f7d522", "type": "select", "category": "component", "events": {"eventList": []}, "name": "缺陷类型", "code": "select_1688451991174", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "缺陷类型", "code": "select_1688451991174", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "解决方案", "useExpr": false, "wrapFunction": false}, "name": "SolutionUUID", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": true, "useExpr": false, "wrapFunction": false}, "tooltip": {"value": "", "useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}, "requiredMessage": {"value": "该项为必填项", "useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"useExpr": false, "wrapFunction": false}, "tag": false, "showSearch": true, "searchCaseSensitive": false, "defaultValue": {"useExpr": false, "wrapFunction": false}, "placeholder": {"value": "请选择解决方案", "useExpr": false, "wrapFunction": false}, "labelInValue": false, "propLoading": {"value": false, "useExpr": false, "wrapFunction": false}, "dataSource": {"sourceType": "request", "requestDependParams": {"value": "return {}", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return {};\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": []}, "requestCode": {"value": "// 工具类\nfunction requestCloudApi(options) {\n  const {\n    action,\n    paramVar,\n    resultCode,\n    outerParamName,\n    isPaging = false\n  } = options\n  return $utils.api.requestCloudApi(action, {\n    [outerParamName]: paramVar\n  }).then(res => {\n    if (res.Error) {\n      throw new Error(res.Error.Message)\n    } else {\n      // 如果有分页，则返回数据和分页\n      if (isPaging) {\n        return {\n          data: res[resultCode],\n          total: res.Total\n        }\n      }\n      return res[resultCode]\n    }\n  }).catch(error => {\n    $utils.ui.message.error(error.message);\n    throw error;\n  })\n}\n\n// 请求数据引擎数据\ntry {\n  const selectData = await requestCloudApi({\n    action: \"ListSolutions\",\n    paramVar: {\n      _Filter: [{\n        \"Key\": \"TenantID\",\n        \"Op\": \"=\",\n        \"Value\": window.jiguang_currentNs\n      }]\n    },\n    resultCode: \"ListSolutions\",\n    outerParamName: \"Solution\",\n    isPaging: false\n  });\n\n  return selectData.map(item => {\n    return {\n      ...item,\n      Name: `${item.Name}(${item.NameEN})`\n    }\n\n  })\n\n} catch (error) {}", "useExpr": true, "wrapFunction": false, "codeGenerateInfo": "[{\"id\":\"requestCloud\",\"uuid\":\"2c927616-713a-4122-b3f1-d4722e66b5c7\",\"title\":\"请求数据引擎数据\",\"properties\":{\"action\":\"ListSolutions\",\"resultCode\":\"ListSolutions\",\"outerParamName\":\"Solution\",\"isPaging\":false,\"autoReturn\":true},\"catchList\":[],\"finallyList\":[],\"chosen\":false,\"selected\":false,\"name\":\"\"}]", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = async function () {\n  try {\n    // 工具类\n    function requestCloudApi(options) {\n      const {\n        action,\n        paramVar,\n        resultCode,\n        outerParamName,\n        isPaging = false\n      } = options;\n      return $utils.api.requestCloudApi(action, {\n        [outerParamName]: paramVar\n      }).then(res => {\n        if (res.Error) {\n          throw new Error(res.Error.Message);\n        } else {\n          // 如果有分页，则返回数据和分页\n          if (isPaging) {\n            return {\n              data: res[resultCode],\n              total: res.Total\n            };\n          }\n          return res[resultCode];\n        }\n      }).catch(error => {\n        $utils.ui.message.error(error.message);\n        throw error;\n      });\n    }\n\n    // 请求数据引擎数据\n    try {\n      const selectData = await requestCloudApi({\n        action: \"ListSolutions\",\n        paramVar: {\n          _Filter: [{\n            \"Key\": \"TenantID\",\n            \"Op\": \"=\",\n            \"Value\": window.jiguang_currentNs\n          }]\n        },\n        resultCode: \"ListSolutions\",\n        outerParamName: \"Solution\",\n        isPaging: false\n      });\n      return selectData.map(item => {\n        return {\n          ...item,\n          Name: \"\".concat(item.Name, \"(\").concat(item.NameEN, \")\")\n        };\n      });\n    } catch (error) {}\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": []}}, "autoTransformData": true, "changeDataSelectValue": "nothing", "transformDataType": "object", "labelField": "Name", "valueField": "UUID"}}, "id": "9d73b6d5-a92a-45de-922f-9a21d31d110c", "type": "select", "category": "component", "events": {"eventList": [{"eventName": "onChange", "eventType": "custom", "eventCode": "$context.parentRef.setFieldsValue({\n  SolutionVersion:\"\"\n})", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    $context.parentRef.setFieldsValue({\n      SolutionVersion: \"\"\n    });\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()"}]}, "name": "下拉框", "code": "select_1679302866543", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "下拉框", "code": "select_1679302866543", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "发现版本", "useExpr": false, "wrapFunction": false}, "name": "SolutionVersion", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": true, "useExpr": false, "wrapFunction": false}, "tooltip": {"value": "", "useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}, "requiredMessage": {"value": "该项为必填项", "useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"useExpr": false, "wrapFunction": false}, "tag": false, "showSearch": true, "searchCaseSensitive": false, "defaultValue": {"useExpr": false, "wrapFunction": false}, "placeholder": {"value": "请选择解决方案版本", "useExpr": false, "wrapFunction": false}, "labelInValue": false, "propLoading": {"value": false, "useExpr": false, "wrapFunction": false}, "dataSource": {"sourceType": "request", "requestDependParams": {"value": "return {\n  SolutionID: $context.formData.SolutionUUID,\n  Tag: \"\",\n  Branch:\"master\"\n}", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return {\n      SolutionID: $context.formData.SolutionUUID,\n      Tag: \"\",\n      Branch: \"master\"\n    };\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.formData.SolutionUUID"]}, "requestCode": {"value": "// 工具类\nfunction requestCloudApi(options) {\n  const {\n    action,\n    paramVar,\n    resultCode,\n    outerParamName,\n    isPaging = false\n  } = options\n  return $utils.api.requestCloudApi(action, {\n    [outerParamName]: paramVar\n  }).then(res => {\n    if (res.Error) {\n      throw new Error(res.Error.Message)\n    } else {\n      // 如果有分页，则返回数据和分页\n      if (isPaging) {\n        return {\n          data: res[resultCode],\n          total: res.Total\n        }\n      }\n      return res[resultCode]\n    }\n  }).catch(error => {\n    $utils.ui.message.error(error.message);\n    throw error;\n  })\n}\n\n// 将常规参数转换为数据引擎参数(将依赖参数转换为数据引擎可识别参数)\nconst requestDependParams = $utils.api.transformCloudApiParams(\n  $context.requestDependParams || {},\n  {\n    //TODO: 搜索条件默认会使用like模糊搜索，可以在这里将精准搜索的字段进行指定\n    useEqFields: [\"SolutionID\",\n      \"Tag\",\n      \"Branch\"],\n    //排序字段，格式为 {Key:\"Name\",Sort:\"DESC\"}\n    order: [],\n  }\n)\n// 编码模块\nif (!$context.requestDependParams?.SolutionID) {\n  return []\n}\n// 请求数据引擎数据(请求下拉框数据,请完善配置信息)\ntry {\n  const selectData = await requestCloudApi({\n    action: \"ListSolutionVersionWithMarketFlag\",\n    paramVar: requestDependParams,\n    resultCode: \"ListSolutionVersions\",\n    outerParamName: \"SolutionVersion\",\n    isPaging: false\n  });\n\n\n  $utils.state.updateState(state => {\n    // 更新的参数可在$context.globalState中读取\n    state.solutionVersionList = selectData\n  })\n\n\n  return selectData;\n\n} catch (error) {}", "useExpr": true, "wrapFunction": false, "codeGenerateInfo": "[{\"id\":\"transformCloudApiParams\",\"uuid\":\"352f7ae5-a193-49e8-92ed-91c03dca9ab6\",\"title\":\"将常规参数转换为数据引擎参数\",\"properties\":{\"paramVar\":\"$context.requestDependParams || {}\",\"returnVarName\":\"requestDependParams\"},\"catchList\":[],\"finallyList\":[],\"name\":\"将依赖参数转换为数据引擎可识别参数\",\"chosen\":false,\"selected\":false},{\"id\":\"customCode\",\"uuid\":\"dff3a833-0707-4f72-9566-5a71a6cdee8c\",\"title\":\"编码模块\",\"properties\":{\"code\":\"if(!$context.requestDependParams?.SolutionID){\\n  return []\\n}\",\"closure\":false},\"catchList\":[],\"finallyList\":[],\"chosen\":false,\"selected\":false,\"name\":\"\"},{\"id\":\"requestCloud\",\"uuid\":\"f3688ce0-f244-42e0-a0c4-a6445abda4fa\",\"title\":\"请求数据引擎数据\",\"properties\":{\"action\":\"ListSolutionVersionWithMarketFlag\",\"resultCode\":\"ListSolutionVersions\",\"outerParamName\":\"SolutionVersion\",\"paramVar\":\"requestDependParams\",\"isPaging\":false,\"returnVarName\":\"selectData\",\"autoReturn\":true},\"catchList\":[],\"finallyList\":[],\"chosen\":false,\"name\":\"请求下拉框数据,请完善配置信息\",\"selected\":false}]", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = async function () {\n  try {\n    var _$context$requestDepe;\n    // 工具类\n    function requestCloudApi(options) {\n      const {\n        action,\n        paramVar,\n        resultCode,\n        outerParamName,\n        isPaging = false\n      } = options;\n      return $utils.api.requestCloudApi(action, {\n        [outerParamName]: paramVar\n      }).then(res => {\n        if (res.Error) {\n          throw new Error(res.Error.Message);\n        } else {\n          // 如果有分页，则返回数据和分页\n          if (isPaging) {\n            return {\n              data: res[resultCode],\n              total: res.Total\n            };\n          }\n          return res[resultCode];\n        }\n      }).catch(error => {\n        $utils.ui.message.error(error.message);\n        throw error;\n      });\n    }\n\n    // 将常规参数转换为数据引擎参数(将依赖参数转换为数据引擎可识别参数)\n    const requestDependParams = $utils.api.transformCloudApiParams($context.requestDependParams || {}, {\n      //TODO: 搜索条件默认会使用like模糊搜索，可以在这里将精准搜索的字段进行指定\n      useEqFields: [\"SolutionID\", \"Tag\", \"Branch\"],\n      //排序字段，格式为 {Key:\"Name\",Sort:\"DESC\"}\n      order: []\n    });\n    // 编码模块\n    if (!((_$context$requestDepe = $context.requestDependParams) !== null && _$context$requestDepe !== void 0 && _$context$requestDepe.SolutionID)) {\n      return [];\n    }\n    // 请求数据引擎数据(请求下拉框数据,请完善配置信息)\n    try {\n      const selectData = await requestCloudApi({\n        action: \"ListSolutionVersionWithMarketFlag\",\n        paramVar: requestDependParams,\n        resultCode: \"ListSolutionVersions\",\n        outerParamName: \"SolutionVersion\",\n        isPaging: false\n      });\n      $utils.state.updateState(state => {\n        // 更新的参数可在$context.globalState中读取\n        state.solutionVersionList = selectData;\n      });\n      return selectData;\n    } catch (error) {}\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.requestDependParams", "$context.requestDependParams.SolutionID", "$context.globalState"]}}, "autoTransformData": true, "changeDataSelectValue": "nothing", "transformDataType": "object", "labelField": "Code", "valueField": "Code"}}, "id": "deda44fd-8ed9-4a1e-a61a-317e9ab87042", "type": "select", "category": "component", "events": {"eventList": []}, "name": "下拉框", "code": "select_1679296528351", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "下拉框", "code": "select_1679296528351", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "发现架构", "useExpr": false, "wrapFunction": false}, "name": "Arch", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": true, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}, "requiredMessage": {"value": "该项为必填项", "useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"useExpr": false, "wrapFunction": false}, "tag": false, "showSearch": true, "searchCaseSensitive": false, "defaultValue": {"useExpr": false, "wrapFunction": false}, "placeholder": {"useExpr": false, "wrapFunction": false}, "labelInValue": false, "propLoading": {"value": false, "useExpr": false, "wrapFunction": false}, "dataSource": {"sourceType": "context", "contextInfo": {"source": "globalState", "key": "lookupMaps.PackageArch"}}, "autoTransformData": true, "changeDataSelectValue": "nothing", "transformDataType": "object", "labelField": "Name", "valueField": "Code"}}, "id": "1c555efc-b5e1-4b37-a3b1-07ab8f690fd2", "type": "select", "category": "component", "events": {"eventList": []}, "name": "下拉框", "code": "select_1679296568942", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "下拉框", "code": "select_1679296568942", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "产品", "useExpr": false, "wrapFunction": false}, "name": "ProductUUID", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"useExpr": false, "wrapFunction": false}, "tag": false, "showSearch": true, "searchCaseSensitive": false, "defaultValue": {"useExpr": false, "wrapFunction": false}, "placeholder": {"useExpr": false, "wrapFunction": false}, "labelInValue": false, "propLoading": {"value": false, "useExpr": false, "wrapFunction": false}, "dataSource": {"sourceType": "request", "requestDependParams": {"value": "return {\n  TenantID:{\n    Value:window.jiguang_currentNs,\n    Op:\"=\"\n  }\n}\n", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return {\n      TenantID: {\n        Value: window.jiguang_currentNs,\n        Op: \"=\"\n      }\n    };\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": []}, "requestCode": {"value": "// 工具类\nfunction requestCloudApi(options) {\n  const {\n    action,\n    paramVar,\n    resultCode,\n    outerParamName,\n    isPaging = false\n  } = options\n  return $utils.api.requestCloudApi(action, {\n    [outerParamName]: paramVar\n  }).then(res => {\n    if (res.Error) {\n      throw new Error(res.Error.Message)\n    } else {\n      // 如果有分页，则返回数据和分页\n      if (isPaging) {\n        return {\n          data: res[resultCode],\n          total: res.Total\n        }\n      }\n      return res[resultCode]\n    }\n  }).catch(error => {\n    $utils.ui.message.error(error.message);\n    throw error;\n  })\n}\n\n// 将常规参数转换为数据引擎参数(将依赖参数转换为数据引擎可识别参数)\nconst requestDependParams = $utils.api.transformCloudApiParams(\n  $context.requestDependParams || {},\n  {\n    //TODO: 搜索条件默认会使用like模糊搜索，可以在这里将精准搜索的字段进行指定\n    useEqFields: [],\n    //排序字段，格式为 {Key:\"Name\",Sort:\"DESC\"}\n    order: [],\n  }\n)\n// 请求数据引擎数据(请求下拉框数据,请完善配置信息)\ntry {\n  const selectData = await requestCloudApi({\n    action: \"ListProductInfoDetails\",\n    paramVar: requestDependParams,\n    resultCode: \"ListProductInfos\",\n    outerParamName: \"ProductInfo\",\n    isPaging: false\n  });\n  return selectData;\n\n} catch (error) {}", "useExpr": true, "wrapFunction": false, "codeGenerateInfo": "[{\"id\":\"transformCloudApiParams\",\"uuid\":\"dda7a7e4-76c5-4659-aad7-61e9d1a80440\",\"title\":\"将常规参数转换为数据引擎参数\",\"properties\":{\"paramVar\":\"$context.requestDependParams || {}\",\"returnVarName\":\"requestDependParams\"},\"catchList\":[],\"finallyList\":[],\"name\":\"将依赖参数转换为数据引擎可识别参数\",\"chosen\":false,\"selected\":false},{\"id\":\"requestCloud\",\"uuid\":\"5cb12ce6-a3b4-467c-8aa8-9f1b69e0c588\",\"title\":\"请求数据引擎数据\",\"properties\":{\"action\":\"ListProductInfoDetails\",\"resultCode\":\"ListProductInfos\",\"outerParamName\":\"ProductInfo\",\"paramVar\":\"requestDependParams\",\"isPaging\":false,\"returnVarName\":\"selectData\",\"autoReturn\":true},\"catchList\":[],\"finallyList\":[],\"chosen\":false,\"name\":\"请求下拉框数据,请完善配置信息\",\"selected\":false}]", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = async function () {\n  try {\n    // 工具类\n    function requestCloudApi(options) {\n      const {\n        action,\n        paramVar,\n        resultCode,\n        outerParamName,\n        isPaging = false\n      } = options;\n      return $utils.api.requestCloudApi(action, {\n        [outerParamName]: paramVar\n      }).then(res => {\n        if (res.Error) {\n          throw new Error(res.Error.Message);\n        } else {\n          // 如果有分页，则返回数据和分页\n          if (isPaging) {\n            return {\n              data: res[resultCode],\n              total: res.Total\n            };\n          }\n          return res[resultCode];\n        }\n      }).catch(error => {\n        $utils.ui.message.error(error.message);\n        throw error;\n      });\n    }\n\n    // 将常规参数转换为数据引擎参数(将依赖参数转换为数据引擎可识别参数)\n    const requestDependParams = $utils.api.transformCloudApiParams($context.requestDependParams || {}, {\n      //TODO: 搜索条件默认会使用like模糊搜索，可以在这里将精准搜索的字段进行指定\n      useEqFields: [],\n      //排序字段，格式为 {Key:\"Name\",Sort:\"DESC\"}\n      order: []\n    });\n    // 请求数据引擎数据(请求下拉框数据,请完善配置信息)\n    try {\n      const selectData = await requestCloudApi({\n        action: \"ListProductInfoDetails\",\n        paramVar: requestDependParams,\n        resultCode: \"ListProductInfos\",\n        outerParamName: \"ProductInfo\",\n        isPaging: false\n      });\n      return selectData;\n    } catch (error) {}\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.requestDependParams"]}}, "autoTransformData": true, "changeDataSelectValue": "nothing", "transformDataType": "object", "labelField": "Name", "valueField": "UUID"}}, "id": "fec398dc-e2a4-4a86-b1d9-f0225b3e9f49", "type": "select", "category": "component", "events": {"eventList": []}, "name": "下拉框", "code": "select_1679296602333", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "下拉框", "code": "select_1679296602333", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "模块", "useExpr": false, "wrapFunction": false}, "name": "<PERSON><PERSON><PERSON>", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"showType": "text", "disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": true, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "trim": true, "placeholder": {"useExpr": false, "wrapFunction": false}}}, "id": "10f6d151-c67c-4f9e-ab54-af24061140aa", "type": "input", "category": "component", "events": {"eventList": []}, "name": "输入框", "code": "input_1693209750891", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "输入框", "code": "input_1693209750891", "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}, "isRender": {"value": "return $context.inputParams.readonly && $context.inputParams?.formData?.TapdUrl", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    var _$context$inputParams, _$context$inputParams2;\n    return $context.inputParams.readonly && ((_$context$inputParams = $context.inputParams) === null || _$context$inputParams === void 0 ? void 0 : (_$context$inputParams2 = _$context$inputParams.formData) === null || _$context$inputParams2 === void 0 ? void 0 : _$context$inputParams2.TapdUrl);\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.inputParams.readonly", "$context.inputParams.formData.TapdUrl"]}}}, {"properties": {"formItemProps": {"label": {"value": "问题来源", "useExpr": false, "wrapFunction": false}, "name": "Source", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": true, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}, "requiredMessage": {"value": "该项为必填项", "useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"value": false, "useExpr": false, "wrapFunction": false}, "tag": false, "showSearch": true, "searchCaseSensitive": false, "defaultValue": {"useExpr": false, "wrapFunction": false}, "placeholder": {"useExpr": false, "wrapFunction": false}, "labelInValue": false, "propLoading": {"value": false, "useExpr": false, "wrapFunction": false}, "dataSource": {"sourceType": "context", "contextInfo": {"source": "globalState", "key": "lookupMaps.IssueSource"}}, "autoTransformData": true, "changeDataSelectValue": "nothing", "transformDataType": "object", "labelField": "Name", "valueField": "Code"}}, "id": "13c3deaf-4e4d-4ef0-a0fb-c6128d1d0ed7", "type": "select", "category": "component", "events": {"eventList": []}, "name": "下拉框", "code": "select_1684753481788", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "下拉框", "code": "select_1684753481788", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "优先级", "useExpr": false, "wrapFunction": false}, "name": "Priority", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": true, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}, "requiredMessage": {"value": "该项为必填项", "useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"useExpr": false, "wrapFunction": false}, "tag": false, "showSearch": true, "searchCaseSensitive": false, "defaultValue": {"useExpr": false, "wrapFunction": false}, "placeholder": {"value": "请选择优先级", "useExpr": false, "wrapFunction": false}, "labelInValue": false, "propLoading": {"value": false, "useExpr": false, "wrapFunction": false}, "dataSource": {"sourceType": "context", "contextInfo": {"source": "globalState", "key": "lookupMaps.Priority"}}, "autoTransformData": true, "changeDataSelectValue": "nothing", "transformDataType": "object", "labelField": "Name", "valueField": "Code"}}, "id": "3a77e3e1-c0b8-453a-ac51-611642258724", "type": "select", "category": "component", "events": {"eventList": []}, "name": "下拉框", "code": "select_1679296679697", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "下拉框", "code": "select_1679296679697", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "严重程度", "useExpr": false, "wrapFunction": false}, "name": "SeverityLevel", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": true, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}, "requiredMessage": {"value": "该项为必填项", "useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"useExpr": false, "wrapFunction": false}, "tag": false, "showSearch": true, "searchCaseSensitive": false, "defaultValue": {"useExpr": false, "wrapFunction": false}, "placeholder": {"value": "请选择严重程度", "useExpr": false, "wrapFunction": false}, "labelInValue": false, "propLoading": {"value": false, "useExpr": false, "wrapFunction": false}, "dataSource": {"sourceType": "context", "contextInfo": {"source": "globalState", "key": "lookupMaps.Severity"}}, "autoTransformData": true, "changeDataSelectValue": "nothing", "transformDataType": "object", "labelField": "Name", "valueField": "Code"}}, "id": "1f4d35f0-c34b-44a6-ac23-4f6163ad5e00", "type": "select", "category": "component", "events": {"eventList": []}, "name": "严重程度", "code": "select_1679296719777", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "严重程度", "code": "select_1679296719777", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "处理人", "useExpr": false, "wrapFunction": false}, "name": "Owner", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": true, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}, "requiredMessage": {"value": "该项为必填项", "useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"value": true, "useExpr": false, "wrapFunction": false}, "tag": true}}, "id": "e09f1769-8b21-47ae-a718-e585b629f7c4", "type": "userSelect", "category": "component", "events": {"eventList": []}, "name": "人员选择框", "code": "userSelect_1679311054781", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "人员选择框", "code": "userSelect_1679311054781", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "研发负责人", "useExpr": false, "wrapFunction": false}, "name": "DevOwners", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": true, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}, "requiredMessage": {"value": "该项为必填项", "useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"value": true, "useExpr": false, "wrapFunction": false}, "tag": true}}, "id": "637bf748-4054-4b1f-8b01-c9e81bbed0a6", "type": "userSelect", "category": "component", "events": {"eventList": []}, "name": "人员选择框", "code": "userSelect_1679311134565", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "人员选择框", "code": "userSelect_1679311134565", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "测试负责人", "useExpr": false, "wrapFunction": false}, "name": "TestOwners", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"value": true, "useExpr": false, "wrapFunction": false}, "tag": true}}, "id": "6b3f306f-8354-4d30-8bac-889ef623b6c3", "type": "userSelect", "category": "component", "events": {"eventList": []}, "name": "人员选择框", "code": "userSelect_1679311163482", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "人员选择框", "code": "userSelect_1679311163482", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "交付负责人", "useExpr": false, "wrapFunction": false}, "name": "DeliverOwners", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"value": true, "useExpr": false, "wrapFunction": false}, "tag": true}}, "id": "4fc6e89f-a2d8-49ba-8530-d24a2ebcd938", "type": "userSelect", "category": "component", "events": {"eventList": []}, "name": "人员选择框", "code": "userSelect_1679311184972", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "人员选择框", "code": "userSelect_1679311184972", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "关联tapd单", "useExpr": false, "wrapFunction": false}, "name": "TapdUrl", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"type": "link", "copyable": false, "editable": false}}, "id": "d9ebbae0-bc97-4fa9-8a0b-f589ac2f6cfb", "type": "editText", "category": "component", "events": {"eventList": [{"eventName": "onClick", "eventType": "custom", "eventCode": "const tapdUrl = $context.inputParams?.formData?.TapdUrl\nif(tapdUrl) {\n  window.open(tapdUrl,\"_blank\")\n}", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    var _$context$inputParams, _$context$inputParams2;\n    const tapdUrl = (_$context$inputParams = $context.inputParams) === null || _$context$inputParams === void 0 ? void 0 : (_$context$inputParams2 = _$context$inputParams.formData) === null || _$context$inputParams2 === void 0 ? void 0 : _$context$inputParams2.TapdUrl;\n    if (tapdUrl) {\n      window.open(tapdUrl, \"_blank\");\n    }\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()"}]}, "name": "可编辑文本", "code": "editText_1686810319172", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "可编辑文本", "code": "editText_1686810319172", "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}, "isRender": {"value": "return $context.inputParams.readonly", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return $context.inputParams.readonly;\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.inputParams.readonly"]}}}, {"properties": {"formItemProps": {"label": {"value": "创建人", "useExpr": false, "wrapFunction": false}, "name": "Creator", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"showType": "text", "disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": true, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "trim": true, "placeholder": {"useExpr": false, "wrapFunction": false}}}, "id": "7827b5cc-bb27-49fc-a8be-30a372b2d6d6", "type": "input", "category": "component", "events": {"eventList": []}, "name": "输入框", "code": "input_1686655208452", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "输入框", "code": "input_1686655208452", "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}, "isRender": {"value": "return $context.inputParams.readonly", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return $context.inputParams.readonly;\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.inputParams.readonly"]}}}, {"properties": {"formItemProps": {"label": {"value": "创建时间", "useExpr": false, "wrapFunction": false}, "name": "CreatedAt", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": true, "useExpr": false, "wrapFunction": false}, "type": "datetime", "isRange": false, "placeholderLeft": {"value": "请选择日期", "useExpr": false, "wrapFunction": false}}}, "id": "c0099847-e915-449f-9d4d-5bf4eac55b71", "type": "datePicker", "category": "component", "events": {"eventList": []}, "name": "日期选择器", "code": "datePicker_1686655226395", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "日期选择器", "code": "datePicker_1686655226395", "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}, "isRender": {"value": "return $context.inputParams.readonly", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return $context.inputParams.readonly;\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.inputParams.readonly"]}}}, {"properties": {"formItemProps": {"label": {"value": "最后更新时间", "useExpr": false, "wrapFunction": false}, "name": "UpdateAt", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": true, "useExpr": false, "wrapFunction": false}, "type": "datetime", "isRange": false, "placeholderLeft": {"value": "请选择日期", "useExpr": false, "wrapFunction": false}}}, "id": "4af76326-0eb9-4a53-b034-0a1c0a95ca71", "type": "datePicker", "category": "component", "events": {"eventList": []}, "name": "日期选择器", "code": "datePicker_1686655254835", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "日期选择器", "code": "datePicker_1686655254835", "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}, "isRender": {"value": "return $context.inputParams.readonly", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return $context.inputParams.readonly;\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.inputParams.readonly"]}}}], "events": {"eventList": []}, "name": "常规表单", "code": "form_1679296507834", "extra": {"targetGroupName": "container", "groupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "常规表单", "code": "form_1679296507834", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}], "type": "page", "category": "container", "advancedProperties": {"triggerList": [{"type": "manualTrigger", "condition": {"useExpr": true, "wrapFunction": false, "dependencyVariableList": []}, "triggerCode": {"useExpr": true, "wrapFunction": false, "value": "// 工具类\nasync function getFormData(formCode) {\n  const formRef = $context.pageMapRef[formCode]\n  if (formRef) {\n    return await formRef.current.validateFields()\n  } else {\n    $utils.ui.message.warn(\"表单不存在，请检查组件code是否正确\")\n    throw new Error(\"表单不存在，请检查表单code是否正确\")\n  }\n}\n\n// 读取表单数据\ntry {\n  const formData = await getFormData(\"form_1679296507834\");\n  const solutionVersionList = $context.globalState?.solutionVersionList || [];\n  const SolutionVersionUUID = solutionVersionList.find( item=>item.Code === formData.SolutionVersion)?.UUID\n  \n  return {\n    ...formData,\n    SolutionVersionUUID\n  };\n\n} catch (error) {}", "codeGenerateInfo": "[{\"id\":\"formValidateField\",\"uuid\":\"8b0a36c8-e17e-472e-974c-3d1802e12410\",\"title\":\"读取表单数据\",\"properties\":{\"isBatch\":false,\"formCode\":\"form_1679296507834\",\"returnVarName\":\"formData\",\"autoReturn\":true},\"catchList\":[],\"finallyList\":[]}]", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = async function () {\n  try {\n    // 工具类\n    async function getFormData(formCode) {\n      const formRef = $context.pageMapRef[formCode];\n      if (formRef) {\n        return await formRef.current.validateFields();\n      } else {\n        $utils.ui.message.warn(\"表单不存在，请检查组件code是否正确\");\n        throw new Error(\"表单不存在，请检查表单code是否正确\");\n      }\n    }\n\n    // 读取表单数据\n    try {\n      var _$context$globalState, _solutionVersionList$;\n      const formData = await getFormData(\"form_1679296507834\");\n      const solutionVersionList = ((_$context$globalState = $context.globalState) === null || _$context$globalState === void 0 ? void 0 : _$context$globalState.solutionVersionList) || [];\n      const SolutionVersionUUID = (_solutionVersionList$ = solutionVersionList.find(item => item.Code === formData.SolutionVersion)) === null || _solutionVersionList$ === void 0 ? void 0 : _solutionVersionList$.UUID;\n      return {\n        ...formData,\n        SolutionVersionUUID\n      };\n    } catch (error) {}\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.pageMapRef[formCode]", "$context.globalState.solutionVersionList"]}, "title": "获取当前表单数据", "code": "getFormData"}, {"type": "conditionTrigger", "condition": {"useExpr": true, "wrapFunction": false, "value": "return {\n  ParentStoryIssueID: $context.allFormData[\"ParentStoryIssueID\"]\n}", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return {\n      ParentStoryIssueID: $context.allFormData[\"ParentStoryIssueID\"]\n    };\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.allFormData[\"ParentStoryIssueID\"]"]}, "triggerCode": {"useExpr": true, "wrapFunction": false, "value": "// 工具类\nfunction requestCloudApi(options) {\n  const {\n    action,\n    paramVar,\n    resultCode,\n    outerParamName,\n    isPaging = false\n  } = options\n  return $utils.api.requestCloudApi(action, {\n    [outerParamName]: paramVar\n  }).then(res => {\n    if (res.Error) {\n      throw new Error(res.Error.Message)\n    } else {\n      // 如果有分页，则返回数据和分页\n      if (isPaging) {\n        return {\n          data: res[resultCode],\n          total: res.Total\n        }\n      }\n      return res[resultCode]\n    }\n  }).catch(error => {\n    $utils.ui.message.error(error.message);\n    throw error;\n  })\n}\n\n// 请求数据引擎数据\ntry {\n  const {\n    ParentStoryIssueID\n  } = $context.triggerParams || {}\n\n  if (ParentStoryIssueID) {\n    const result = await requestCloudApi({\n      action: \"GetStoryWithDetail\",\n      paramVar: {\n        _Filter: [{\n          \"Key\": \"IssueID\",\n          \"Op\": \"=\",\n          \"Value\": ParentStoryIssueID\n        }]\n      },\n      resultCode: \"GetStory\",\n      outerParamName: \"Story\",\n      isPaging: false\n    });\n    console.log(result,\"result\")\n  }\n\n\n\n} catch (error) {}", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = async function () {\n  try {\n    // 工具类\n    function requestCloudApi(options) {\n      const {\n        action,\n        paramVar,\n        resultCode,\n        outerParamName,\n        isPaging = false\n      } = options;\n      return $utils.api.requestCloudApi(action, {\n        [outerParamName]: paramVar\n      }).then(res => {\n        if (res.Error) {\n          throw new Error(res.Error.Message);\n        } else {\n          // 如果有分页，则返回数据和分页\n          if (isPaging) {\n            return {\n              data: res[resultCode],\n              total: res.Total\n            };\n          }\n          return res[resultCode];\n        }\n      }).catch(error => {\n        $utils.ui.message.error(error.message);\n        throw error;\n      });\n    }\n\n    // 请求数据引擎数据\n    try {\n      const {\n        ParentStoryIssueID\n      } = $context.triggerParams || {};\n      if (ParentStoryIssueID) {\n        const result = await requestCloudApi({\n          action: \"GetStoryWithDetail\",\n          paramVar: {\n            _Filter: [{\n              \"Key\": \"IssueID\",\n              \"Op\": \"=\",\n              \"Value\": ParentStoryIssueID\n            }]\n          },\n          resultCode: \"GetStory\",\n          outerParamName: \"Story\",\n          isPaging: false\n        });\n        console.log(result, \"result\");\n      }\n    } catch (error) {}\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.triggerParams"]}, "title": "获取关联需求单详情", "code": "loadStoryDetail"}, {"type": "conditionTrigger", "condition": {"useExpr": true, "wrapFunction": false, "value": "return {\n  UUID: $context.allFormData[\"form_1679296507834\"]?.IterationUUID\n}", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    var _$context$allFormData;\n    return {\n      UUID: (_$context$allFormData = $context.allFormData[\"form_1679296507834\"]) === null || _$context$allFormData === void 0 ? void 0 : _$context$allFormData.IterationUUID\n    };\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.allFormData[\"form_1679296507834\"].IterationUUID"]}, "triggerCode": {"useExpr": true, "wrapFunction": false, "value": "// 工具类\nfunction requestCloudApi(options) {\n  const {\n    action,\n    paramVar,\n    resultCode,\n    outerParamName,\n    isPaging = false\n  } = options\n  return $utils.api.requestCloudApi(action, {\n    [outerParamName]: paramVar\n  }).then(res => {\n    if (res.Error) {\n      throw new Error(res.Error.Message)\n    } else {\n      // 如果有分页，则返回数据和分页\n      if (isPaging) {\n        return {\n          data: res[resultCode],\n          total: res.Total\n        }\n      }\n      return res[resultCode]\n    }\n  }).catch(error => {\n    $utils.ui.message.error(error.message);\n    throw error;\n  })\n}\n\nconst {\n  UUID\n} = $context.triggerParams || {}\n\nif (!UUID) {\n  return\n}\n\n// 请求数据引擎数据\ntry {\n  const result = await requestCloudApi({\n    action: \"GetIterationDetail\",\n    paramVar: $utils.api.transformCloudApiParams($context.triggerParams, {\n      useEqFields: [\"UUID\"]\n    }),\n    resultCode: \"GetIteration\",\n    outerParamName: \"Iteration\",\n    isPaging: false\n  });\n\n\n  $utils.state.updateState(state => {\n    state.currentIterationDetail = result\n  })\n\n\n} catch (error) {}", "codeGenerateInfo": "[{\"id\":\"requestCloud\",\"uuid\":\"7810e7ed-5c5d-4bd4-932e-20ad5818d47f\",\"title\":\"请求数据引擎数据\",\"properties\":{\"action\":\"GetIterationDetail\",\"resultCode\":\"GetIteration\",\"outerParamName\":\"Iteration\",\"paramVar\":\"params\",\"isPaging\":false,\"returnVarName\":\"result\",\"autoReturn\":false},\"catchList\":[],\"finallyList\":[]}]", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = async function () {\n  try {\n    // 工具类\n    function requestCloudApi(options) {\n      const {\n        action,\n        paramVar,\n        resultCode,\n        outerParamName,\n        isPaging = false\n      } = options;\n      return $utils.api.requestCloudApi(action, {\n        [outerParamName]: paramVar\n      }).then(res => {\n        if (res.Error) {\n          throw new Error(res.Error.Message);\n        } else {\n          // 如果有分页，则返回数据和分页\n          if (isPaging) {\n            return {\n              data: res[resultCode],\n              total: res.Total\n            };\n          }\n          return res[resultCode];\n        }\n      }).catch(error => {\n        $utils.ui.message.error(error.message);\n        throw error;\n      });\n    }\n    const {\n      UUID\n    } = $context.triggerParams || {};\n    if (!UUID) {\n      return;\n    }\n\n    // 请求数据引擎数据\n    try {\n      const result = await requestCloudApi({\n        action: \"GetIterationDetail\",\n        paramVar: $utils.api.transformCloudApiParams($context.triggerParams, {\n          useEqFields: [\"UUID\"]\n        }),\n        resultCode: \"GetIteration\",\n        outerParamName: \"Iteration\",\n        isPaging: false\n      });\n      $utils.state.updateState(state => {\n        state.currentIterationDetail = result;\n      });\n    } catch (error) {}\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.triggerParams", "$context.triggerParams"]}, "title": "获取关联迭代的详情", "code": "getIterationDetail"}]}}, "modalPage": [{"properties": {"inputParams": "{}", "title": {"value": "关联需求", "useExpr": false, "wrapFunction": false}, "type": "modal", "width": "1000", "height": "auto", "isMove": false, "customButton": false, "okText": "确认", "cancelText": "取消"}, "id": "4d8c1cd2-3f69-477e-8e4d-66f32aa13813", "name": "关联需求", "type": "modal", "category": "container", "list": [{"properties": {"cardProps": {"title": {"useExpr": false, "wrapFunction": false}}, "cardBordered": false, "height": {"fixed": false}, "columns": [{"title": "需求ID", "dataIndex": "IssueID", "width": 50, "valueType": "default", "align": "left", "fixed": "none", "tooltip": "", "isSearch": true, "isSorter": false, "ellipsis": false, "copyable": false, "customRender": false}, {"title": "需求标题", "dataIndex": "Title", "width": 230, "valueType": "link", "eventList": [{"eventName": "onClickLink", "eventType": "custom", "eventCode": "console.log($context.tableCurrentRow);\n\nwindow.open(`/page/defect_manage/__develop_center_next/iteration_manage/story/detail?issue_id=${$context.tableCurrentRow.IssueID}`,\"_blank\")", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    console.log($context.tableCurrentRow);\n    window.open(\"/page/defect_manage/__develop_center_next/iteration_manage/story/detail?issue_id=\".concat($context.tableCurrentRow.IssueID), \"_blank\");\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()"}], "align": "left", "fixed": "none", "tooltip": "", "isSearch": true, "isSorter": false, "ellipsis": false, "copyable": false, "customRender": false}, {"width": 70, "align": "left", "fixed": "none", "tooltip": "", "isSearch": false, "isSorter": false, "ellipsis": false, "copyable": false, "customRender": false, "valueType": "select", "dataIndex": "Priority", "title": "优先级", "selectOptions": {"useExpr": true, "wrapFunction": false, "value": "return ($context.inputParams.Priority || []).map(item => {\n  return {\n    label: item.Name,\n    value: item.Code\n  }\n})", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return ($context.inputParams.Priority || []).map(item => {\n      return {\n        label: item.Name,\n        value: item.Code\n      };\n    });\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.inputParams.Priority"]}, "selectShowType": "default"}, {"width": 70, "align": "left", "fixed": "none", "tooltip": "", "isSearch": false, "isSorter": false, "ellipsis": false, "copyable": false, "customRender": false, "valueType": "select", "title": "状态", "dataIndex": "Status", "selectOptions": {"useExpr": true, "wrapFunction": false, "value": "return ($context.inputParams.IssueStatus || []).map(item => {\n  return {\n    label: item.Name,\n    value: item.Code\n  }\n})", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return ($context.inputParams.IssueStatus || []).map(item => {\n      return {\n        label: item.Name,\n        value: item.Code\n      };\n    });\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.inputParams.IssueStatus"]}, "selectShowType": "default"}], "handleColumn": {"title": "操作", "width": 120, "align": "center", "fixed": "right"}, "handleColumnButtons": {"showCount": 3, "preRender": {"useExpr": true, "wrapFunction": false, "dependencyVariableList": []}, "postRender": {"useExpr": true, "wrapFunction": false, "dependencyVariableList": []}}, "topButtons": {"showCount": 3, "preRender": {"useExpr": true, "wrapFunction": false, "dependencyVariableList": []}, "postRender": {"useExpr": true, "wrapFunction": false, "dependencyVariableList": []}}, "options": {"show": false}, "topRightButtons": {"showCount": 3, "preRender": {"useExpr": true, "wrapFunction": false, "dependencyVariableList": []}, "postRender": {"useExpr": true, "wrapFunction": false, "dependencyVariableList": []}}, "pagination": {"enabled": true, "pageSize": 10}, "dataSource": {"sourceType": "request", "requestDependParams": {"value": "return {}", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return {};\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": []}, "requestCode": {"value": "// 工具类\nfunction requestAuroraApi(options) {\n  // isPaging 是否分页\n  const {\n    action,\n    paramVar,\n    resultCode,\n    isPaging = false\n  } = options\n  return $utils.api.requestAuroraApi(action, paramVar).then(res => {\n    if (res.Error) {\n      throw new Error(res.Error.Message)\n    } else {\n      // 如果有分页，则返回数据和分页\n      if (isPaging) {\n        return {\n          data: res[resultCode],\n          total: res.Total\n        }\n      }\n      return res[resultCode]\n    }\n  }).catch(error => {\n    $utils.ui.message.error(error.message);\n    throw error;\n  })\n}\n\n// 编码模块(获取表格过滤数据)\nconst {\n  pageSize, current, IssueID, Title\n} = $context.tableFilterParams\n\n// 获取表格过滤条件\nconst tableFilterParams = {\n  IssueType: \"stories\",\n  PageNo: current,\n  PageSize: pageSize,\n  FilterExpr: [{\n    \"Key\": \"Status\",\n    \"Op\": \"!=\",\n    \"Value\": \"closed\"\n  }]\n}\n\nif(IssueID) {\n  tableFilterParams.FilterExpr.push({\n    Key:\"IssueID\",\n    Op: \"=\",\n    Value: IssueID\n  })\n}\nif(Title) {\n  tableFilterParams.FilterExpr.push({\n    Key:\"Title\",\n    Op: \"like\",\n    Value: `%${Title}%`\n  })\n}\n\n// 请求Aurora数据(请求表格数据，需完善参数)\ntry {\n  const tableData = await requestAuroraApi({\n    action: \"ListStoriesWithDetail\",\n    paramVar: tableFilterParams,\n    resultCode: \"Items\",\n    isPaging: true\n  })\n  // 编码模块(将请求返回的数据处理为表格可识别的数据格式)\n  //如果返回总条数，说明分页了，否则就是没有分页\n  const {\n    data,\n    total\n  } = typeof tableData.total === \"number\" ? tableData: {\n    data: tableData\n  }\n  return {\n    data: data || [],\n    success: true,\n    total: total\n  }\n} catch (error) {}", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = async function () {\n  try {\n    // 工具类\n    function requestAuroraApi(options) {\n      // isPaging 是否分页\n      const {\n        action,\n        paramVar,\n        resultCode,\n        isPaging = false\n      } = options;\n      return $utils.api.requestAuroraApi(action, paramVar).then(res => {\n        if (res.Error) {\n          throw new Error(res.Error.Message);\n        } else {\n          // 如果有分页，则返回数据和分页\n          if (isPaging) {\n            return {\n              data: res[resultCode],\n              total: res.Total\n            };\n          }\n          return res[resultCode];\n        }\n      }).catch(error => {\n        $utils.ui.message.error(error.message);\n        throw error;\n      });\n    }\n\n    // 编码模块(获取表格过滤数据)\n    const {\n      pageSize,\n      current,\n      IssueID,\n      Title\n    } = $context.tableFilterParams;\n\n    // 获取表格过滤条件\n    const tableFilterParams = {\n      IssueType: \"stories\",\n      PageNo: current,\n      PageSize: pageSize,\n      FilterExpr: [{\n        \"Key\": \"Status\",\n        \"Op\": \"!=\",\n        \"Value\": \"closed\"\n      }]\n    };\n    if (IssueID) {\n      tableFilterParams.FilterExpr.push({\n        Key: \"IssueID\",\n        Op: \"=\",\n        Value: IssueID\n      });\n    }\n    if (Title) {\n      tableFilterParams.FilterExpr.push({\n        Key: \"Title\",\n        Op: \"like\",\n        Value: \"%\".concat(Title, \"%\")\n      });\n    }\n\n    // 请求Aurora数据(请求表格数据，需完善参数)\n    try {\n      const tableData = await requestAuroraApi({\n        action: \"ListStoriesWithDetail\",\n        paramVar: tableFilterParams,\n        resultCode: \"Items\",\n        isPaging: true\n      });\n      // 编码模块(将请求返回的数据处理为表格可识别的数据格式)\n      //如果返回总条数，说明分页了，否则就是没有分页\n      const {\n        data,\n        total\n      } = typeof tableData.total === \"number\" ? tableData : {\n        data: tableData\n      };\n      return {\n        data: data || [],\n        success: true,\n        total: total\n      };\n    } catch (error) {}\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.tableFilterParams"]}}, "rowSelection": {"show": true, "rowKey": "IssueID", "preserveSelectedRowKeys": {"value": true, "useExpr": false, "wrapFunction": false}, "type": "radio", "fixed": false, "tableAlertRender": true, "defaultSelectedRowKeys": {"value": "return $context.inputParams.ParentStoryIssueID ? [ $context.inputParams.ParentStoryIssueID] : []", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return $context.inputParams.ParentStoryIssueID ? [$context.inputParams.ParentStoryIssueID] : [];\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.inputParams.ParentStoryIssueID", "$context.inputParams.ParentStoryIssueID]"]}, "getCheckboxProps": {"value": "", "useExpr": true, "wrapFunction": true, "codeGenerateInfo": "[{\"id\":\"customCode\",\"uuid\":\"74c43499-224e-412c-b482-cebb6d563528\",\"title\":\"编码模块\",\"properties\":{\"code\":\"const {pageSize,current,...otherParams} = $context.tableFilterParams\\n\\n// 获取表格过滤条件\\nconst tableFilterParams = {\\n  ...otherParams,\\n  IssueType: \\\"bugs\\\",\\n  PageNo: current + 1,\\n  PageSize:pageSize,\\n  FilterExpr: [{\\n    \\\"Key\\\": \\\"Status\\\",\\n    \\\"Op\\\": \\\"!=\\\",\\n    \\\"Value\\\": \\\"closed\\\"\\n  }]\\n}\",\"closure\":false},\"catchList\":[],\"finallyList\":[],\"selected\":false,\"chosen\":false,\"name\":\"获取表格过滤数据\"},{\"id\":\"requestAurora\",\"uuid\":\"7b3b653b-5aca-4062-8899-d0ed02bec1af\",\"title\":\"请求Aurora数据\",\"properties\":{\"action\":\"ListBugsWithDetail\",\"resultCode\":\"Items\",\"paramVar\":\"tableFilterParams\",\"isPaging\":true,\"returnVarName\":\"tableData\",\"autoReturn\":false},\"catchList\":[],\"finallyList\":[],\"name\":\"请求表格数据，需完善参数\",\"chosen\":false,\"selected\":false},{\"id\":\"customCode\",\"uuid\":\"40f2351c-f432-48b9-9c9a-3335fc7d87a3\",\"title\":\"编码模块\",\"properties\":{\"returnVarName\":\"result\",\"autoReturn\":true,\"code\":\"//如果返回总条数，说明分页了，否则就是没有分页\\nconst {\\n  data,\\n  total\\n} = typeof tableData.total === \"number\" ? tableData: {\\n  data: tableData\\n}\\nreturn {\\n  data: data || [],\\n  success: true,\\n  total: total\\n}\"},\"catchList\":[],\"finallyList\":[],\"chosen\":false,\"selected\":false,\"name\":\"将请求返回的数据处理为表格可识别的数据格式\"}]", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {} catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": []}}, "search": {"labelWidth": 100, "defaultCollapsed": true, "filterType": "query", "syncToUrl": false}}, "id": "de89c884-1cd3-4d71-bbdc-4c66448b4d48", "type": "table", "category": "component", "events": {"eventList": []}, "name": "基础表格", "code": "table_1689249938722", "extra": {"targetGroupName": "container"}, "advancedProperties": {}, "commonProperties": {"name": "基础表格", "code": "table_1689249938722", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}], "events": {"eventList": [{"eventName": "onOk", "eventType": "custom", "eventCode": "// 工具类\nfunction getComponentRef(code) {\n  const ref = $context.pageMapRef[code]\n  if (ref?.current) {\n    return ref\n  } else {\n    console.error(\"组件不存在\")\n  }\n}\n\n// 读取表格勾选的数据key\nconst result = getComponentRef(\"table_1689249938722\")?.current?.getSelectedRowKeys?.();\nif(result?.length) {\n   $utils.dialog.close({\n    operationType:\"confirm\",\n    storyID: result[0]\n  });\n}else{\n  $utils.dialog.close({\n    operationType:\"confirm\"\n  });\n}", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    var _getComponentRef, _getComponentRef$curr, _getComponentRef$curr2;\n    // 工具类\n    function getComponentRef(code) {\n      const ref = $context.pageMapRef[code];\n      if (ref !== null && ref !== void 0 && ref.current) {\n        return ref;\n      } else {\n        console.error(\"组件不存在\");\n      }\n    }\n\n    // 读取表格勾选的数据key\n    const result = (_getComponentRef = getComponentRef(\"table_1689249938722\")) === null || _getComponentRef === void 0 ? void 0 : (_getComponentRef$curr = _getComponentRef.current) === null || _getComponentRef$curr === void 0 ? void 0 : (_getComponentRef$curr2 = _getComponentRef$curr.getSelectedRowKeys) === null || _getComponentRef$curr2 === void 0 ? void 0 : _getComponentRef$curr2.call(_getComponentRef$curr);\n    if (result !== null && result !== void 0 && result.length) {\n      $utils.dialog.close({\n        operationType: \"confirm\",\n        storyID: result[0]\n      });\n    } else {\n      $utils.dialog.close({\n        operationType: \"confirm\"\n      });\n    }\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "codeGenerateInfo": "[{\"id\":\"tableGetSelectedRowKeys\",\"uuid\":\"ff47fba5-8156-46f3-b9d3-35fcc5be3c75\",\"title\":\"读取表格勾选的数据key\",\"properties\":{\"tableCode\":\"table_1689249938722\",\"returnVarName\":\"result\",\"autoReturn\":false},\"catchList\":[],\"finallyList\":[]}]"}, {"eventName": "onCancel", "eventType": "custom", "eventCode": "\n$utils.dialog.close({\n  operationType:\"cancel\"\n});", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    $utils.dialog.close({\n      operationType: \"cancel\"\n    });\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()"}]}, "extra": {}, "code": "modal_1689249881197", "advancedProperties": {}}, {"properties": {"inputParams": "{}", "title": {"value": "关联迭代", "useExpr": false, "wrapFunction": false}, "type": "modal", "width": "1000", "height": "auto", "isMove": false, "customButton": false, "okText": "确认", "cancelText": "取消"}, "id": "5bda11bf-3c8a-4248-a4a6-607efeb3afb1", "name": "关联迭代", "type": "modal", "category": "container", "list": [{"properties": {"cardProps": {"title": {"useExpr": false, "wrapFunction": false}}, "cardBordered": false, "height": {"fixed": false}, "columns": [{"title": "迭代名称", "dataIndex": "Name", "width": 150, "valueType": "default", "align": "left", "fixed": "none", "tooltip": "", "isSearch": true, "isSorter": false, "ellipsis": false, "copyable": false, "customRender": false}, {"width": 100, "align": "left", "fixed": "none", "tooltip": "", "isSearch": false, "isSorter": false, "ellipsis": false, "copyable": false, "customRender": false, "valueType": "default", "title": "解决方案", "dataIndex": "SolutionVersion.Solution.Name"}, {"width": 100, "align": "left", "fixed": "none", "tooltip": "", "isSearch": false, "isSorter": false, "ellipsis": false, "copyable": false, "customRender": false, "valueType": "default", "dataIndex": "SolutionVersion.Code", "title": "解决方案版本"}, {"title": "迭代状态", "dataIndex": "Status", "valueType": "select", "width": 100, "align": "left", "fixed": "none", "tooltip": "", "isSearch": true, "isSorter": false, "ellipsis": false, "copyable": false, "customRender": false, "selectOptions": {"useExpr": true, "wrapFunction": false, "value": "return ($context.globalState?.lookupMaps?.IterationStatus || []).map(item => {\n  return {\n    label: item.Name,\n    value: item.Code,\n    color: item.Extra?.Color\n  }\n  \n})", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    var _$context$globalState, _$context$globalState2;\n    return (((_$context$globalState = $context.globalState) === null || _$context$globalState === void 0 ? void 0 : (_$context$globalState2 = _$context$globalState.lookupMaps) === null || _$context$globalState2 === void 0 ? void 0 : _$context$globalState2.IterationStatus) || []).map(item => {\n      var _item$Extra;\n      return {\n        label: item.Name,\n        value: item.Code,\n        color: (_item$Extra = item.Extra) === null || _item$Extra === void 0 ? void 0 : _item$Extra.Color\n      };\n    });\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.globalState.lookupMaps.IterationStatus"]}, "selectShowType": "badge"}, {"title": "迭代类型", "dataIndex": "Type", "width": 100, "valueType": "select", "align": "left", "fixed": "none", "tooltip": "", "isSearch": true, "isSorter": false, "ellipsis": false, "copyable": false, "customRender": false, "selectOptions": {"useExpr": true, "wrapFunction": false, "value": "return ($context.globalState?.lookupMaps?.IterationType || []).map(item => {\n  return {\n    label: item.Name,\n    value: item.Code,\n    color: item.Extra?.Color\n  }\n  \n})", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    var _$context$globalState, _$context$globalState2;\n    return (((_$context$globalState = $context.globalState) === null || _$context$globalState === void 0 ? void 0 : (_$context$globalState2 = _$context$globalState.lookupMaps) === null || _$context$globalState2 === void 0 ? void 0 : _$context$globalState2.IterationType) || []).map(item => {\n      var _item$Extra;\n      return {\n        label: item.Name,\n        value: item.Code,\n        color: (_item$Extra = item.Extra) === null || _item$Extra === void 0 ? void 0 : _item$Extra.Color\n      };\n    });\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.globalState.lookupMaps.IterationType"]}, "selectShowType": "badge"}], "handleColumn": {"title": "操作", "width": 120, "align": "center", "fixed": "right"}, "handleColumnButtons": {"showCount": 3, "preRender": {"useExpr": true, "wrapFunction": false, "dependencyVariableList": []}, "postRender": {"useExpr": true, "wrapFunction": false, "dependencyVariableList": []}}, "topButtons": {"showCount": 3, "preRender": {"useExpr": true, "wrapFunction": false, "dependencyVariableList": []}, "postRender": {"useExpr": true, "wrapFunction": false, "dependencyVariableList": []}}, "options": {"show": true}, "topRightButtons": {"showCount": 3, "preRender": {"useExpr": true, "wrapFunction": false, "dependencyVariableList": []}, "postRender": {"useExpr": true, "wrapFunction": false, "dependencyVariableList": []}}, "pagination": {"enabled": true, "pageSize": 10}, "dataSource": {"sourceType": "request", "requestDependParams": {"value": "return {\n  TenantUUID: window.jiguang_currentNs,\n  DeletedAt: 0\n}", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return {\n      TenantUUID: window.jiguang_currentNs,\n      DeletedAt: 0\n    };\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": []}, "requestCode": {"value": "// 工具类\nfunction requestCloudApi(options) {\n  const {\n    action,\n    paramVar,\n    resultCode,\n    outerParamName,\n    isPaging = false\n  } = options\n  return $utils.api.requestCloudApi(action, {\n    [outerParamName]: paramVar\n  }).then(res => {\n    if (res.Error) {\n      throw new Error(res.Error.Message)\n    } else {\n      // 如果有分页，则返回数据和分页\n      if (isPaging) {\n        return {\n          data: res[resultCode],\n          total: res.Total\n        }\n      }\n      return res[resultCode]\n    }\n  }).catch(error => {\n    $utils.ui.message.error(error.message);\n    throw error;\n  })\n}\n\n// 将常规参数转换为数据引擎参数(读取表格依赖参数并转换数据格式)\nconst tableFilterParams = $utils.api.transformCloudApiParams(\n  $context.tableFilterParams || {},\n  {\n    //TODO: 搜索条件默认会使用like模糊搜索，可以在这里将精准搜索的字段进行指定\n    useEqFields: [\"TenantUUID\",\"DeletedAt\"],\n    //排序字段，格式为 {Key:\"Name\",Sort:\"DESC\"}\n    order: [],\n  }\n)\n// 请求数据引擎数据(请求表格数据，需完善参数)\ntry {\n  const tableData = await requestCloudApi({\n    action: \"ListIterationsWithDetail\",\n    paramVar: tableFilterParams,\n    resultCode: \"ListIterations\",\n    outerParamName: \"Iteration\",\n    isPaging: true\n  });\n  return tableData;\n  // 编码模块(将请求返回的数据处理为表格可识别的数据格式)\n  //如果返回总条数，说明分页了，否则就是没有分页\n  const {\n    data,\n    total\n  } = typeof tableData.total === \"number\" ? tableData: {\n    data: tableData\n  }\n  return {\n    data: data || [],\n    success: true,\n    total: total\n  }\n} catch (error) {\n  // 编码模块(接口请求失败返回空数组给表格)\n  return {\n    success: false,\n    data: []\n  }\n}", "useExpr": true, "wrapFunction": false, "codeGenerateInfo": "[{\"id\":\"transformCloudApiParams\",\"uuid\":\"9a2c4106-63ab-47fb-8394-d44fcdecdcc6\",\"title\":\"将常规参数转换为数据引擎参数\",\"name\":\"读取表格依赖参数并转换数据格式\",\"properties\":{\"paramVar\":\"$context.tableFilterParams || {}\",\"returnVarName\":\"tableFilterParams\"},\"catchList\":[],\"finallyList\":[],\"chosen\":false},{\"id\":\"requestCloud\",\"uuid\":\"35477cf5-7a2e-4ba6-a687-5b561d3f334a\",\"title\":\"请求数据引擎数据\",\"properties\":{\"paramVar\":\"tableFilterParams\",\"returnVarName\":\"tableData\",\"action\":\"ListIterationsWithDetail\",\"resultCode\":\"ListIterations\",\"outerParamName\":\"Iteration\",\"isPaging\":true,\"autoReturn\":true},\"catchList\":[{\"id\":\"customCode\",\"uuid\":\"0ed5775e-a786-4467-835f-762d93bdfec4\",\"title\":\"编码模块\",\"properties\":{\"returnVarName\":\"\",\"autoReturn\":true,\"code\":\"return {\\n  success: false,\\n  data:[]\\n}\"},\"catchList\":[],\"finallyList\":[],\"chosen\":false,\"selected\":false,\"name\":\"接口请求失败返回空数组给表格\"}],\"finallyList\":[],\"selected\":false,\"chosen\":false,\"name\":\"请求表格数据，需完善参数\"},{\"id\":\"customCode\",\"uuid\":\"ac31ceca-e231-4d1e-84a6-8306a1b24cef\",\"title\":\"编码模块\",\"properties\":{\"returnVarName\":\"result\",\"autoReturn\":true,\"code\":\"//如果返回总条数，说明分页了，否则就是没有分页\\nconst {\\n  data,\\n  total\\n} = typeof tableData.total === \"number\" ? tableData: {\\n  data: tableData\\n}\\nreturn {\\n  data: data || [],\\n  success: true,\\n  total: total\\n}\"},\"catchList\":[],\"finallyList\":[],\"chosen\":false,\"selected\":false,\"name\":\"将请求返回的数据处理为表格可识别的数据格式\"}]", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = async function () {\n  try {\n    // 工具类\n    function requestCloudApi(options) {\n      const {\n        action,\n        paramVar,\n        resultCode,\n        outerParamName,\n        isPaging = false\n      } = options;\n      return $utils.api.requestCloudApi(action, {\n        [outerParamName]: paramVar\n      }).then(res => {\n        if (res.Error) {\n          throw new Error(res.Error.Message);\n        } else {\n          // 如果有分页，则返回数据和分页\n          if (isPaging) {\n            return {\n              data: res[resultCode],\n              total: res.Total\n            };\n          }\n          return res[resultCode];\n        }\n      }).catch(error => {\n        $utils.ui.message.error(error.message);\n        throw error;\n      });\n    }\n\n    // 将常规参数转换为数据引擎参数(读取表格依赖参数并转换数据格式)\n    const tableFilterParams = $utils.api.transformCloudApiParams($context.tableFilterParams || {}, {\n      //TODO: 搜索条件默认会使用like模糊搜索，可以在这里将精准搜索的字段进行指定\n      useEqFields: [\"TenantUUID\", \"DeletedAt\"],\n      //排序字段，格式为 {Key:\"Name\",Sort:\"DESC\"}\n      order: []\n    });\n    // 请求数据引擎数据(请求表格数据，需完善参数)\n    try {\n      const tableData = await requestCloudApi({\n        action: \"ListIterationsWithDetail\",\n        paramVar: tableFilterParams,\n        resultCode: \"ListIterations\",\n        outerParamName: \"Iteration\",\n        isPaging: true\n      });\n      return tableData;\n      // 编码模块(将请求返回的数据处理为表格可识别的数据格式)\n      //如果返回总条数，说明分页了，否则就是没有分页\n      const {\n        data,\n        total\n      } = typeof tableData.total === \"number\" ? tableData : {\n        data: tableData\n      };\n      return {\n        data: data || [],\n        success: true,\n        total: total\n      };\n    } catch (error) {\n      // 编码模块(接口请求失败返回空数组给表格)\n      return {\n        success: false,\n        data: []\n      };\n    }\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.tableFilterParams"]}}, "rowSelection": {"show": true, "rowKey": "UUID", "preserveSelectedRowKeys": {"value": true, "useExpr": false, "wrapFunction": false}, "type": "radio", "fixed": false, "tableAlertRender": true, "defaultSelectedRowKeys": {"value": "return $context.inputParams.IterationUUID ? [ $context.inputParams.IterationUUID] : []", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return $context.inputParams.IterationUUID ? [$context.inputParams.IterationUUID] : [];\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.inputParams.IterationUUID", "$context.inputParams.IterationUUID]"]}, "getCheckboxProps": {"value": "\n    /**\n     * 可返回的属性\n     * disabled: boolean  是否禁用复选框\n     * tooltip: string | React.ReactNode  在复选框上面添加冒泡信息\n     */\n    return {}\n                    ", "useExpr": true, "wrapFunction": true, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    /**\n     * 可返回的属性\n     * disabled: boolean  是否禁用复选框\n     * tooltip: string | React.ReactNode  在复选框上面添加冒泡信息\n     */\n    return {};\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": []}}, "search": {"labelWidth": 100, "defaultCollapsed": true, "filterType": "query", "syncToUrl": false}}, "id": "f2209aeb-4613-479e-bb57-2aae5c2ff1bb", "type": "table", "category": "component", "events": {"eventList": []}, "name": "基础表格", "code": "table_1692005392953", "extra": {"targetGroupName": "container"}, "advancedProperties": {}, "commonProperties": {"name": "基础表格", "code": "table_1692005392953", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}], "events": {"eventList": [{"eventName": "onOk", "eventType": "custom", "eventCode": "// 工具类\nfunction getComponentRef(code) {\n  const ref = $context.pageMapRef[code]\n  if (ref?.current) {\n    return ref\n  } else {\n    console.error(\"组件不存在\")\n  }\n}\n\n// 读取表格勾选的数据key\nconst result = getComponentRef(\"table_1692005392953\")?.current?.getSelectedRows?.();\nif(result?.length) {\n   $utils.dialog.close({\n    operationType:\"confirm\",\n    iteration: result[0]\n  });\n}else{\n  $utils.dialog.close({\n    operationType:\"confirm\"\n  });\n}", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    var _getComponentRef, _getComponentRef$curr, _getComponentRef$curr2;\n    // 工具类\n    function getComponentRef(code) {\n      const ref = $context.pageMapRef[code];\n      if (ref !== null && ref !== void 0 && ref.current) {\n        return ref;\n      } else {\n        console.error(\"组件不存在\");\n      }\n    }\n\n    // 读取表格勾选的数据key\n    const result = (_getComponentRef = getComponentRef(\"table_1692005392953\")) === null || _getComponentRef === void 0 ? void 0 : (_getComponentRef$curr = _getComponentRef.current) === null || _getComponentRef$curr === void 0 ? void 0 : (_getComponentRef$curr2 = _getComponentRef$curr.getSelectedRows) === null || _getComponentRef$curr2 === void 0 ? void 0 : _getComponentRef$curr2.call(_getComponentRef$curr);\n    if (result !== null && result !== void 0 && result.length) {\n      $utils.dialog.close({\n        operationType: \"confirm\",\n        iteration: result[0]\n      });\n    } else {\n      $utils.dialog.close({\n        operationType: \"confirm\"\n      });\n    }\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()"}, {"eventName": "onCancel", "eventType": "custom", "eventCode": "\n$utils.dialog.close({\n  operationType:\"cancel\"\n});", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    $utils.dialog.close({\n      operationType: \"cancel\"\n    });\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()"}, {"eventName": "onOpen", "eventType": "custom", "eventCode": "// 工具类\nfunction requestCloudApi(options) {\n  const {\n    action,\n    paramVar,\n    resultCode,\n    outerParamName,\n    isPaging = false\n  } = options\n  return $utils.api.requestCloudApi(action, {\n    [outerParamName]: paramVar\n  }).then(res => {\n    if (res.Error) {\n      throw new Error(res.Error.Message)\n    } else {\n      // 如果有分页，则返回数据和分页\n      if (isPaging) {\n        return {\n          data: res[resultCode],\n          total: res.Total\n        }\n      }\n      return res[resultCode]\n    }\n  }).catch(error => {\n    $utils.ui.message.error(error.message);\n    throw error;\n  })\n}\n\nasync function requestLookups(types, useTenantID) {\n  return await requestCloudApi({\n    action: \"ListProductDictionaryDetails\",\n    paramVar: $utils.api.transformCloudApiParams({\n      Type: {\n        Op: \"in\",\n        Value: types || []\n      },\n      TenantID: useTenantID ? window.jiguang_currentNs: 0\n    }, {\n      useEqFields: [\"TenantID\"]\n    }),\n    resultCode: \"ListProductDictionaries\",\n    outerParamName: \"ProductDictionary\"\n  })\n}\n\nfunction arrayToObject(array, key) {\n  const object = {}\n  array.forEach(item => {\n    if (!object[item[key]]) {\n      object[item[key]] = []\n    }\n    object[item[key]].push(item)\n  })\n  return object;\n}\n\n// 加载数据字典值\ntry {\n  const lookupResult_IterationType_IterationStatus = await requestLookups([\"IterationType\",\n    \"IterationStatus\"],\n    false);\n  $utils.state.updateState(state => {\n    state.lookupMaps = {\n      ...state.lookupMaps,\n      ...arrayToObject(lookupResult_IterationType_IterationStatus || [], \"Type\")\n    }\n  })\n\n} catch (error) {}", "codeGenerateInfo": "[{\"id\":\"requestLookup\",\"uuid\":\"c3563e63-f492-4ea2-86d8-73f3a1129539\",\"title\":\"加载数据字典值\",\"properties\":{\"type\":\"IterationType,IterationStatus\",\"useTenantID\":false,\"mountGlobalState\":true,\"globalStateName\":\"lookupMaps\"},\"catchList\":[],\"finallyList\":[]}]", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = async function () {\n  try {\n    // 工具类\n    function requestCloudApi(options) {\n      const {\n        action,\n        paramVar,\n        resultCode,\n        outerParamName,\n        isPaging = false\n      } = options;\n      return $utils.api.requestCloudApi(action, {\n        [outerParamName]: paramVar\n      }).then(res => {\n        if (res.Error) {\n          throw new Error(res.Error.Message);\n        } else {\n          // 如果有分页，则返回数据和分页\n          if (isPaging) {\n            return {\n              data: res[resultCode],\n              total: res.Total\n            };\n          }\n          return res[resultCode];\n        }\n      }).catch(error => {\n        $utils.ui.message.error(error.message);\n        throw error;\n      });\n    }\n    async function requestLookups(types, useTenantID) {\n      return await requestCloudApi({\n        action: \"ListProductDictionaryDetails\",\n        paramVar: $utils.api.transformCloudApiParams({\n          Type: {\n            Op: \"in\",\n            Value: types || []\n          },\n          TenantID: useTenantID ? window.jiguang_currentNs : 0\n        }, {\n          useEqFields: [\"TenantID\"]\n        }),\n        resultCode: \"ListProductDictionaries\",\n        outerParamName: \"ProductDictionary\"\n      });\n    }\n    function arrayToObject(array, key) {\n      const object = {};\n      array.forEach(item => {\n        if (!object[item[key]]) {\n          object[item[key]] = [];\n        }\n        object[item[key]].push(item);\n      });\n      return object;\n    }\n\n    // 加载数据字典值\n    try {\n      const lookupResult_IterationType_IterationStatus = await requestLookups([\"IterationType\", \"IterationStatus\"], false);\n      $utils.state.updateState(state => {\n        state.lookupMaps = {\n          ...state.lookupMaps,\n          ...arrayToObject(lookupResult_IterationType_IterationStatus || [], \"Type\")\n        };\n      });\n    } catch (error) {}\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()"}]}, "extra": {}, "code": "modal_1692005257215", "advancedProperties": {}}]}