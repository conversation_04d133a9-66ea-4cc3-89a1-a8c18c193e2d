极光平台应用开发 模版项目: master 分支是适用于新极光的, old 分支是适用于旧极光的

## 技术栈

- 基础前端技术栈：react + typescript + webpack + redux（全局状态管理可选 react.context 或者 redux）
- 脚手架： Create-React-App
- 基础 UI： Ant Design
- 自制工具库： jiguang-lib(jiguang 工具库)
- 自制标准化组件库： jiguang-component(jiguang 业务组件)

## 项目结构说明

此项目作为新极光应用开发的模版 demo 参考项目,后续会逐步集成复用一些常规页面，节省开发时间,整体目录介绍

- 1.docs 文档目录为文档和自测用例目录
- 2.src 源码目录 为各个系统模块源码
- 3.packetage.json 需要修改此文件里的 name 字段 为自己的应用名,方便识别区分
- 4.config-overrides.js 主要用来自定义 cra 里的一些 webpack 配置

## src 源码目录 目录说明

- `src/index.tsx` 入口文件，主要是业务路由和组件映射定义，及应用注册
- `src/modules/common/` 存放公共业务相关
  - api api 的 定义及接口 ts 类型定义等
  - components 模块间的公共组件,如果复用的场景比较多可以提取到 jiguang-component 里
  - constants 通用常量的定义
  - redux 通用的业务 redux 定义,使用 redux 时各个模块的 redux 相关定义会集中到这里
  - utils 通用工具定义
  - routePath 定义路由变量,应用中所用的路由，通常会使用 withRouteBasename 包裹
- `src/modules/solutionManagementRedux` 业务模块的 demo，此模块使用的 redux

  - components 为业务组件的定义
    - index 为 components 统一的路由入口
    - definition 和 kanban 为具体的组件实现
  - redux 为模块相关的 redux 定义 demo
    - actions redux 定义的 aciton
    - actionType redux 中的 action type 的定义
    - reducers redux 定义的 reducer
  - utils 业务相关的工具定义

    - dic.ts 一些字典定义
    - tools.ts 一些工具定义

### 开发调试步骤

先在 Whistle 配置代理

```
http://test.jiguang2.tce.woa.com/admin/upload/basic_data_center/js/basic_data_center.js http://localhost:3008/static/js/jiguang-dev-basic.js
http://test.jiguang2.tce.woa.com/admin/upload/basic_data_center/js/bundle.js.map http://localhost:3008/static/js/bundle.js.map
```

安装：npm install 或者 tnpm install
开发：npm run start
打包：npm run build
测试：进入http://test.jiguang2.tce.woa.com/，规划平台->基础数据管理中心
demo对应的三个菜单是：解决方案看板，解决方案定义，子产品看板


### 配置CI
配置CI时node版本要选14.0以上
可参考overview项目配置，教程参考：https://iwiki.woa.com/pages/viewpage.action?pageId=1491133855

### 项目独立运行方法
- 配置/page/basic_data_center的nginx path到html

``` 参考
   location /page/basic_data_center {
            root   /data/release/platform;
            #autoindex_format json;
            autoindex on;
            autoindex_exact_size off;
            autoindex_localtime on;
            add_header Content-Type "application/octet-stream";
            index  index.html index.htm;
    }

```
- 修改应用的js路径即可可以放在cdn或者极光等可从浏览器访问到的地方
- 访问方式：http://localhost:3008/page/basic_data_center/