{"extends": "./tsconfig.paths.json", "compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react", "baseUrl": "./", "noImplicitAny": false, "noFallthroughCasesInSwitch": true, "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["src", "src/react-app-env.d.ts"]}