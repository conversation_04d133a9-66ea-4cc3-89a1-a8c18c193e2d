module.exports = {
  env: {
    browser: true,
    es2015: true
  },
  extends: [
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
    '@tencent/eslint-config-tencent',
    '@tencent/eslint-config-tencent/ts',
    '@tencent/eslint-config-tencent/prettier'
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaFeatures: {
      jsx: true
    },
    ecmaVersion: 12,
    sourceType: 'module',
    project: './tsconfig.json',
  },
  plugins: ['react', '@typescript-eslint', 'react-hooks'],
  ignorePatterns: ['.eslintrc.js'],
  settings: {
    react: {
      version: "detect", // Automatically detect the react version
    },
  },
  rules: {
    'react/display-name': 0,
    'react/prop-types': 0,
    '@typescript-eslint/no-namespace': 'off',
    '@typescript-eslint/naming-convention': [0],
    'react/self-closing-comp': ['error'],
  }
};
