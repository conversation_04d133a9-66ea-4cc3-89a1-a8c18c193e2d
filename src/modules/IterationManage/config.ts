/*
 * @Author: superfeng
 * @Date: 2023-05-29 19:43:09
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-04-23 14:37:11
 * @Description: 请输入注释信息
 */
export enum IterationPathConfig {
  Defect = 'defect',
  Story = 'story',
}

export const listPageTitleMap = {
  [IterationPathConfig.Defect]: '缺陷管理',
  [IterationPathConfig.Story]: '需求管理',
};

export const detailPageTitleMap = {
  [IterationPathConfig.Defect]: '查看缺陷单',
  [IterationPathConfig.Story]: '查看需求单',
};

export const IssueTypeConfig = {
  [IterationPathConfig.Defect]: 'bug',
  [IterationPathConfig.Story]: 'story',
};

export const createButtonTextMap = {
  [IterationPathConfig.Defect]: '创建缺陷单',
  [IterationPathConfig.Story]: '创建需求单',
};

export const IssueIDHeaderMap = {
  [IterationPathConfig.Defect]: '缺陷ID',
  [IterationPathConfig.Story]: '需求ID',
};

export const EditPageTitleMap = {
  [IterationPathConfig.Defect]: '缺陷单',
  [IterationPathConfig.Story]: '需求单',
};

export const RelatedTypeMap = {
  [IterationPathConfig.Defect]: '需求',
  [IterationPathConfig.Story]: '缺陷',
};

export const CreateButtonEnterMap = {
  [IterationPathConfig.Defect]: '缺陷',
  [IterationPathConfig.Story]: '需求',
};
// SheetConclusion 变更单结论
export enum SheetConclusion {
  Passed = '通过',
  ConditionallyPassed = '受限通过',
  Failed = '不通过',
  NotApplicable = '不涉及',
}

// TestConclusion 测试结论
export enum TestConclusion {
  Passed = '通过',
  Failed = '不通过',
  Delayed = '延后测试',
  TestFree = '免测',
}

// 工具映射
export const toolNameMap = {
  kaleido: 'kaleido',
  产品市场: 'productMarket',
  产品运维中心: 'productDefect',
};
export const reverseToolNameMap = {
  kaleido: 'kaleido',
  productMarket: '产品市场',
  productDefect: '产品运维中心',
};
export const testConclusion = [
  {
    label: TestConclusion.Passed,
    value: 'Passed',
  },
  {
    label: TestConclusion.Delayed,
    value: 'Delayed',
  },
  {
    label: TestConclusion.Failed,
    value: 'Failed',
  },
  {
    label: TestConclusion.TestFree,
    value: 'TestFree',
  },
];

export const conclusion = [
  {
    label: SheetConclusion.Passed,
    value: 'Passed',
  },
  {
    label: SheetConclusion.ConditionallyPassed,
    value: 'ConditionallyPassed',
  },
  {
    label: SheetConclusion.Failed,
    value: 'Failed',
  },
  {
    label: SheetConclusion.NotApplicable,
    value: 'NotApplicable',
  },
];
