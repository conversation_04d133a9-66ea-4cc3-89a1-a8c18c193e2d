import { TcsLayout, TcsSpin, TcsButton } from '@tencent/tcs-component';
import React, { useEffect, useMemo, useState } from 'react';
import NewAccess from '../Detail/components/Left/NewAccess';
import { IPropsBug, ListIteration } from '@/common/api/interationManage';
import { getUrlParams } from '@/common/utils';
import { IssueTypeConfig } from '../../config';
import { Button, Status, Alert, message } from '@tencent/tea-component';
import { IterationManage } from '@/common/api/iterationManage.api';
import { withRouteBasename } from '@/common/routePath';
import parse from 'html-react-parser';

const TapdPage = () => {
  const [data, setData] = useState<IPropsBug>();
  const [loading, setLoading] = useState<boolean>(false);

  const urlParams = useMemo(() => getUrlParams(), []);
  const [tooltip, setTooltip] = useState('');

  useEffect(() => {
    async function fetchData() {
      if (urlParams.obj_id && urlParams.obj_type) {
        const pagePath = urlParams.obj_type === 'bug' ? 'defect' : 'story';
        setLoading(true);
        const res = await IterationManage.GetTapdWorkspaceConfig({
          WorkspaceID: urlParams.workspace_id,
        });

        const res1 = await ListIteration(
          {
            TAPD: urlParams.obj_id,
            PageNo: 1,
            PageSize: 1,
            IssueType: IssueTypeConfig[pagePath],
            TenantUUID: res?.GetTapdWorkspaceConfig?.Tenant,
          },
          pagePath,
        );

        if (res1.Error) {
          message.error({
            content: res1.Error.Message,
          });
        } else {
          if ((res1.Items || []).length) {
            setData(res1.Items[0]);
          }
        }
      }
    }
    setLoading(true);
    fetchData().finally(() => {
      setLoading(false);
    });
  }, [urlParams?.obj_id, urlParams.obj_type, urlParams.workspace_id]);

  useEffect(() => {
    const handleMessage = (event) => {
      // 确保消息来自预期的源
      console.log('接收', event, event.origin);
      if (event.origin !== 'https://tcsc_defect_validate.external.tapd-app.com') return;

      const { tooltip } = event.data;
      if (tooltip) {
        setTooltip(tooltip);
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);
  const text = urlParams.obj_type === 'bug' ? '缺陷单' : '需求单';
  return (
    <TcsLayout
      title="应用评估"
      subtitle={
        <TcsButton
          type="link"
          onClick={() => {
            window.open('https://iwiki.woa.com/p/4010339848', '_blank');
          }}
        >
          需求单出包使用文档
        </TcsButton>
      }
      customizeCard
      operation={
        data ? (
          <Button
            type="link"
            onClick={() => {
              window.open(
                `${withRouteBasename(
                  `/iteration_manage/${urlParams.obj_type === 'bug' ? 'defect' : 'story'}/detail`,
                )}?issue_id=${data?.IssueID}`,
              );
            }}
          >{`打开关联${text}`}</Button>
        ) : undefined
      }
    >
      <TcsSpin spinning={loading}>
        {tooltip && (
          <Alert type="warning">
            TAPD状态检查失败，处理方法请参考：
            <TcsButton
              onClick={() => {
                window.open('https://iwiki.woa.com/p/4010628485', '_blank');
              }}
              type="link"
            >
              TAPD扭转状态检查失败处理方法
            </TcsButton>
            <br />
            {parse(tooltip)}
            <br />
            若已完成相关操作，请点击确认按钮
          </Alert>
        )}
        {data ? (
          <NewAccess data={data} tapdId={urlParams?.obj_id} />
        ) : (
          <Status title="暂无数据" description={`当前TAPD单未在极光中${text}查询到，请联系TCSC小助手`} />
        )}
      </TcsSpin>
    </TcsLayout>
  );
};

export default TapdPage;
