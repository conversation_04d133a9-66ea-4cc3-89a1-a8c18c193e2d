/*
 * @Author: superfeng
 * @Date: 2023-03-17 15:41:01
 * @LastEditors: superfeng
 * @LastEditTime: 2023-05-16 15:25:30
 * @Description: 请输入注释信息
 */
import { Bubble, Text, message } from '@tencent/tea-component';
import React, { useEffect, useRef, useState } from 'react';
import styles from './index.module.less';
import Left from './components/Left';
import Right from './components/Right';
import { useParams } from 'react-router-dom';
import { withRouteBasename } from '@/common/routePath';
import { getUrlParams } from '@/common/utils';
import {
  getBugWithDetail,
  getStoryWithDetail,
  IPropsBug,
  syncTapdWithBug,
  syncTapdWithStory,
  updateBug,
  updateStory,
} from '@/common/api/interationManage';
import { deleteBug, deleteStory } from '@/common/api/deliveryManage';
import { IterationPathConfig, detailPageTitleMap } from '../../config';
import { Tcs<PERSON>utton, TcsCard, TcsLayout, TcsPopConfirm, TcsSpace, TcsSpin } from '@tencent/tcs-component';

const DeleteFunMap = {
  [IterationPathConfig.Defect]: deleteBug,
  [IterationPathConfig.Story]: deleteStory,
};

const DetailFunMap = {
  [IterationPathConfig.Defect]: getBugWithDetail,
  [IterationPathConfig.Story]: getStoryWithDetail,
};

export const UpdateFunMap = {
  [IterationPathConfig.Defect]: updateBug,
  [IterationPathConfig.Story]: updateStory,
};

const Detail = () => {
  const { history } = TcsLayout.useHistory();
  const { pagePath } = useParams<{ pagePath: string }>();
  const [update, setUpdate] = useState({});
  const urlParams = getUrlParams();
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const rightRef = useRef<any>();

  const [data, setData] = useState<IPropsBug>();

  useEffect(() => {
    if (urlParams.issue_id) {
      setLoading(true);
      const getDetailFun = DetailFunMap[pagePath];
      getDetailFun({ IssueID: urlParams.issue_id })
        .then((res) => {
          if (res.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            setData(res?.GetBug || res?.GetStory);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [pagePath, urlParams.issue_id, update]);

  function handleEdit() {
    setIsEdit(true);
    // history.push(`${withRouteBasename(`/iteration_manage/${pagePath}/edit`)}?issue_id=${data.IssueID}`);
  }

  function handleDelete() {
    const deleteFun = DeleteFunMap[pagePath];
    deleteFun({
      IssueID: data?.IssueID,
    }).then((res) => {
      if (res.Error) {
        message.error({
          content: res.Error.Message,
        });
      } else {
        message.success({
          content: '废弃成功',
        });
        history.push(`${withRouteBasename(`/iteration_manage/${pagePath}`)}`);
      }
    });
  }

  function handleSyncTapd() {
    const method = pagePath === IterationPathConfig.Defect ? syncTapdWithBug : syncTapdWithStory;
    setLoading(true);
    method({
      TapdUrl: data!.TapdUrl,
    })
      .then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          message.success({
            content: '同步成功',
          });
          setUpdate({});
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }

  function handleCancelEdit() {
    setIsEdit(false);
  }

  async function handleSave() {
    setLoading(true);
    try {
      const rightFormData = await rightRef.current?.getFormData();

      const { DeliverOwners, DevOwners, Owner, TestOwners, ...otherFormData } = rightFormData;

      const submitData = {
        ...data,
        ...otherFormData,
        DeliverOwners: DeliverOwners ? DeliverOwners.join(';') : '',
        DevOwners: DevOwners ? DevOwners.join(';') : '',
        Owner: Owner ? Owner.join(';') : '',
        TestOwners: TestOwners ? TestOwners.join(';') : '',
        IssueTemplateUUID: data!.IssueTemplateUUID,
      };

      const updateFun = UpdateFunMap[pagePath];

      const res = await updateFun({
        ...submitData,
        IssueID: urlParams.issue_id,
      });
      if (res.Error) {
        throw new Error(res.Error.Message);
      } else {
        message.success({
          content: '保存成功',
        });
        window.location.reload();
      }
    } catch (error) {
      console.error(error);
      if ((error as any)?.message) {
        message.error({
          content: (error as any)?.message,
        });
      }
    } finally {
      setLoading(false);
    }
  }

  return (
    <TcsLayout title={detailPageTitleMap[pagePath]} customizeCard history={history}>
      <TcsSpin spinning={loading}>
        <TcsCard style={{ width: '100%' }}>
          <div className={styles.iteration_detail_content__top}>
            <div className={styles.iteration_detail_content__top__title}>
              <Text theme="text" underline style={{ fontSize: 16 }}>
                【{data?.IssueID}】{data?.Title}
              </Text>
            </div>
            <div className={styles.iteration_detail_content__top__button}>
              {isEdit ? (
                <TcsSpace>
                  <TcsButton type="primary" onClick={handleSave}>
                    保存
                  </TcsButton>
                  <TcsButton onClick={handleCancelEdit}>取消</TcsButton>
                </TcsSpace>
              ) : (
                <TcsSpace>
                  <TcsButton type="primary" onClick={handleEdit}>
                    编辑
                  </TcsButton>
                  <TcsPopConfirm
                    title="确定要废弃？"
                    message="废弃后，配置将不再生效，也不做关联。"
                    okText="废弃"
                    onConfirm={handleDelete}
                  >
                    <TcsButton type="error">废弃</TcsButton>
                  </TcsPopConfirm>
                  {data?.TapdUrl && (
                    <Bubble content="在TAPD与需求单信息不同步时，可手动重新进行信息同步" placement="left-end">
                      <TcsButton onClick={handleSyncTapd}>重新同步TAPD</TcsButton>
                    </Bubble>
                  )}
                </TcsSpace>
              )}
            </div>
          </div>
          <div className={styles.iteration_detail_content}>
            <div className={styles.iteration_detail_content__left}>
              <Left data={data} pagePath={pagePath} update={update} />
            </div>
            <div className={styles.iteration_detail_content__right}>
              <Right isEdit={isEdit} ref={rightRef} data={data} pagePath={pagePath} />
            </div>
          </div>
        </TcsCard>
      </TcsSpin>
    </TcsLayout>
  );
};

export default Detail;
