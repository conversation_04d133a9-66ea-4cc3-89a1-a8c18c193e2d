.iteration_detail {
  &_content {
    width: 100%;
    display: flex;

    &__top {
      display: flex;
      &__title {
        font-size: 16px;
        padding: 10px;
        flex: 1;
      }

      &__buttons {
        width: 440px;
        text-align: right;
      }
    }

    &__left {
      flex: 1;
      // padding-top: 10px;
      padding-right: 20px;
      // border: 1px solid #e8eaee;
      min-height: 500px;
      width: 0;
      overflow: auto;
    }
    &__right {
      padding-top: 10px;
      width: 440px;
      border:1px solid #cfd5de;
      // border-left: 0;
      min-height: 500px;
    }
  }
  &__buttons {
    margin-top: 10px;
    margin-bottom: 10px;
    text-align: center;
  }
}
