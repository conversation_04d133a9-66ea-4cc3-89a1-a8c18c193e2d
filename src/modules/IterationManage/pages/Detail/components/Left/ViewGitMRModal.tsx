import { getIssueAPPRelatedMRList } from '@/common/api/interationManage';
import { Button, Table, message } from '@tencent/tea-component';
import React, { useEffect, useImperativeHandle, useState } from 'react';
import dayjs from 'dayjs';
import useLookup from '@/common/hookups/useLookup';
import { TcsButton, TcsModal } from '@tencent/tcs-component';

const getColumns = ({ getLookupByCode }: { getLookupByCode: (type: String, code: String) => any }) => [
  {
    header: '代码库',
    key: 'GitRepo',
    width: '15%',
  },
  {
    header: '发起人',
    key: 'Author',
    width: '10%',
  },
  {
    header: '评审人',
    key: 'Approver',
    width: '10%',
  },
  {
    header: '创建时间',
    key: 'CreatedAt',
    width: '15%',
    render(record) {
      return dayjs(record.CreatedAt).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    header: '合入时间',
    key: 'MergedAt',
    width: '15%',
    render(record) {
      return dayjs(record.MergedAt).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    header: '状态',
    key: 'Status',
    width: '6%',
    render(record) {
      const { Status } = record;
      const lookup = getLookupByCode('MRStatus', Status);
      return lookup?.Name || Status;
    },
  },
  {
    header: '源分支',
    key: 'SourceBranch',
    width: '8%',
  },
  {
    header: '目标分支',
    key: 'TargetBranch',
    width: '8%',
  },
  {
    header: '操作',
    key: 'action',
    width: 80,
    render(record) {
      return (
        <Button
          type="link"
          onClick={() => {
            window.open(record.URL, '_blank');
          }}
        >
          查看详情
        </Button>
      );
    },
  },
];

const ViewGitMRModal: React.ForwardRefRenderFunction<any, any> = (props, ref) => {
  const [visible, setVisible] = useState(false);
  const [record, setRecord] = useState<any>();
  const [issueID, setIssueID] = useState<string>();
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const { getLookupByCode } = useLookup();

  useImperativeHandle(
    ref,
    () => ({
      show(record: any, issueID: string) {
        setVisible(true);
        setRecord(record);
        setIssueID(issueID);
      },
    }),
    [],
  );

  useEffect(() => {
    if (visible) {
      setLoading(true);
      getIssueAPPRelatedMRList({
        IssueID: issueID!,
        ApplicationBranchUUID: record.ApplicationBranchUUID,
        // ApplicationBranchUUID: '521d7a9f052e43acb2046a9d7859defa',
      })
        .then((res) => {
          if (res.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            setDataSource(res.MRList);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [visible, record, issueID]);

  function handleCancel() {
    setVisible(false);
  }

  return (
    <TcsModal
      title="查看关联MR信息"
      visible={visible}
      onCancel={handleCancel}
      width={1200}
      footer={<TcsButton onClick={handleCancel}> 关闭</TcsButton>}
    >
      <Table
        columns={getColumns({
          getLookupByCode,
        })}
        records={dataSource}
        addons={[
          Table.addons.autotip({
            isLoading: loading,
          }),
        ]}
      />
    </TcsModal>
  );
};

export default React.forwardRef(ViewGitMRModal);
