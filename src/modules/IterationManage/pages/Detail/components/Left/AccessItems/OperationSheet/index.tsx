import { GetTAPDOperationSheet } from '@/common/api/iterationManage.api';
import React, { useEffect, useState } from 'react';
import ProductMarket from './ProductMarket';
import { TcsTabs } from '@tencent/tcs-component';
import Tops from './Tops';
import Kaleido from './Kaleido';

export interface IProps {
  record: any;
  onReload: (type: 'dawn' | 'tops' | 'kaleido') => void;
  showTops?: boolean;
  showDawn?: boolean;
  onReSyncTops: (onlyVersion?: boolean) => Promise<any>;
  isManager: boolean;
  hasSolutionVersion: boolean;
  pagePath: 'story' | 'defect';
  showKaleido?: boolean;
  Tops?: GetTAPDOperationSheet;
  OriginTops?: GetTAPDOperationSheet;
  kaleido?: GetTAPDOperationSheet;
  tapdInfo?: {
    workspace_id: string;
    tapd_id: string;
  };
  defaultTabCurrent: string;
}

const OperationSheet: React.FC<IProps> = ({
  record,
  onReSyncTops,
  onReload,
  isManager,
  hasSolutionVersion,
  pagePath,
  showKaleido,
  showTops,
  kaleido,
  tapdInfo,
  OriginTops: OriginTopsData,
  Tops: TopsData,
  defaultTabCurrent,
  showDawn,
}) => {
  const Dawn = (
    <ProductMarket
      record={record}
      onReload={() => onReload('dawn')}
      showTops={showTops}
      isOriginTops={!!(!TopsData && OriginTopsData)}
      onReSyncTops={onReSyncTops}
      isManager={isManager}
      hasSolutionVersion={hasSolutionVersion}
      pagePath={pagePath}
    />
  );

  const TopsEl = (
    <Tops
      record={record}
      onReSyncTops={onReSyncTops}
      Tops={TopsData}
      isManager={isManager}
      hasSolutionVersion={hasSolutionVersion}
      OriginTops={OriginTopsData}
      onReload={() => onReload('tops')}
      pagePath={pagePath}
    />
  );

  const KaleidoEl = (
    <Kaleido record={record} tapdInfo={tapdInfo} kaleido={kaleido} onReload={() => onReload('kaleido')} />
  );

  const operationList: Array<[React.JSX.Element, string, string]> = [];
  if (showDawn) {
    operationList.push([Dawn, 'dawn', '产品市场']);
  }
  if (showTops) {
    // 需求单，如果有产品运维中心但没有产品市场，也需要显示产品市场变更单，因为需要先填写产品市场变更单再转换为产品运维中心变更单
    if (!showDawn && pagePath === 'story') {
      operationList.push([Dawn, 'dawn', '产品市场']);
    }
    operationList.push([TopsEl, 'tops', '产品运维中心']);
  }
  if (showKaleido) {
    operationList.push([KaleidoEl, 'kaleido', 'Kaleido']);
  }

  const [current, setCurrent] = useState(operationList.length ? operationList[0][1] : 'tops');

  useEffect(() => {
    setCurrent(defaultTabCurrent);
  }, [defaultTabCurrent]);

  if (operationList.length === 0) {
    return null;
  }

  if (operationList.length === 1) {
    return operationList[0][0];
  }

  return (
    <TcsTabs activeKey={current} onChange={(activeKey) => setCurrent(activeKey)}>
      {operationList.map(([el, key, tab]) => (
        <TcsTabs.TabPane key={key} tabKey={key} tab={tab}>
          {el}
        </TcsTabs.TabPane>
      ))}

      {/* {showDawn && Dawn}
      {showTops && (
        <TcsTabs.TabPane key="tops" tabKey="tops" tab="产品运维中心">
          <Tops
            record={record}
            onReSyncTops={onReSyncTops}
            Tops={TopsData}
            isManager={isManager}
            hasSolutionVersion={hasSolutionVersion}
            OriginTops={OriginTopsData}
            onReload={() => onReload('tops')}
            pagePath={pagePath}
          />
        </TcsTabs.TabPane>
      )}
      {showKaleido && (
        <TcsTabs.TabPane key="kaleido" tabKey="kaleido" tab="Kaleido">
          <Kaleido record={record} tapdInfo={tapdInfo} kaleido={kaleido} onReload={() => onReload('kaleido')} />
        </TcsTabs.TabPane>
      )} */}
    </TcsTabs>
  );
};

export default OperationSheet;
