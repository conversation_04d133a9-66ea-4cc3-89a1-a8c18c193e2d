/*
 * @Author: lucyfang
 * @Date: 2025-03-03 10:36:36
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-03-27 15:01:47
 * @Description: 请输入注释信息
 */
// 默认的静态资源处理方法

export const getPackageHost = function () {
  return sessionStorage.getItem('PackageManagerHost');
};
interface FileUploadParams {
  atomicName?: string;
  // 变更单上传文件的包管理路径需要传sheetId
  sheetId?: string;
  atomicVersion: string;
  fileName: string;
  servicePrefix?: string;
  packageDomain?: string;
}
export const generateAtomicFileUrl = function (params: FileUploadParams) {
  // TODO:sheetID需要去除，暂时还未去除
  const { packageDomain, atomicVersion, fileName } = params;
  // eslint-disable-next-line max-len
  return `${packageDomain}/cgw/packages/images/${atomicVersion}/all/${fileName}`;
};

export const defaultFileUpload = async ({ file, markdownInstanceRef, packageDomain = window.location.origin }) => {
  const targetPackageServicePrefix = '/cgw';
  const fileName = file.name;
  const fileReader = new FileReader();

  const readAsPromise: any = (file) =>
    new Promise((resolve) => {
      fileReader.onload = function (event) {
        resolve(event.target.result);
      };
      fileReader.readAsArrayBuffer(file);
    });

  try {
    const fileBuffer: BlobPart = await readAsPromise(file);
    const params: FileUploadParams = {
      atomicVersion: '1.0.0',
      fileName,
      servicePrefix: targetPackageServicePrefix,
      packageDomain,
    };

    const serverUrl = generateAtomicFileUrl(params);
    const fileData = new Blob([fileBuffer], { type: 'application/octet-stream' });
    console.log('上传文件地址：', serverUrl);
    const config = {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/octet-stream',
      },
      body: fileData,
    };

    const response = await fetch(serverUrl, config);
    if (response.ok) {
      const responseJson = await response.json();
      // eslint-disabl e-next-line @tencent/tce/i18n-value-type
      const imageData = `![请输入图片描述#260px](${packageDomain}${targetPackageServicePrefix}/${responseJson.Key})`;

      markdownInstanceRef.current.insert(imageData);
      return { downloadUrl: responseJson.Key, fileName };
    }
    console.log('文件上传失败！错误：{{attr0}}', { attr0: response.statusText });
  } catch (err) {
    console.error(err);
  }
};

export function addTimestampToFilename(filename = '', uploadTime = '') {
  // 获取文件名和扩展 名
  const [basename, extension] = filename.split('.');
  // 按格式生成时间戳
  const timestamp = new Date().getTime();
  // 返回带时间戳的新文件名
  return `${basename}_${uploadTime || timestamp}.${extension}`;
}
