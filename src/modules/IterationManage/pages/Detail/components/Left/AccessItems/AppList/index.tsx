/*
 * @Author: lucyfang
 * @Date: 2025-02-20 11:20:46
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-04-15 10:52:56
 * @Description: 请输入注释信息
 */
import { IPropsIssueSolutionRel } from '@/common/api/interationManage';
import { GetComponentListForTAPDResponse } from '@/common/api/iterationManage.api';
import React, { useEffect, useState } from 'react';
import AppTable from './AppTable';
import { TcsTabs } from '@tencent/tcs-component';
import OldAppTable from './OldAppTable';

export interface IProps {
  record: any;
  pagePath: 'story' | 'defect';
  onReload: (type: 'next' | '2.0') => void;
  IssueID: string;
  onLoading: (status: boolean) => void;
  tableDataSource?: IPropsIssueSolutionRel[];
  isManager: boolean;
  oldAppList: GetComponentListForTAPDResponse['ComponentList'];
  isOnlineType?: boolean;
  defaultTabCurrent?: string;
}
const AppList: React.FC<IProps> = ({
  record,
  pagePath,
  onReload,
  IssueID,
  onLoading,
  tableDataSource,
  isManager,
  oldAppList,
  isOnlineType,
  defaultTabCurrent,
}) => {
  const [current, setCurrent] = useState('app');

  useEffect(() => {
    setCurrent(defaultTabCurrent || 'app');
  }, [defaultTabCurrent]);
  const Next = (
    <AppTable
      record={record}
      pagePath={pagePath}
      onReload={() => onReload('next')}
      onLoading={onLoading}
      IssueID={IssueID}
      isManager={isManager}
      tableDataSource={tableDataSource}
    />
  );
  if (oldAppList.length > 0 && isOnlineType) {
    return (
      <TcsTabs activeKey={current} onChange={setCurrent}>
        <TcsTabs.TabPane key="app" tabKey="app" tab="关联Next应用清单">
          {Next}
        </TcsTabs.TabPane>
        <TcsTabs.TabPane key="oldApp" tabKey="oldApp" tab="关联2.0应用清单">
          <OldAppTable oldAppList={oldAppList} onReload={() => onReload('2.0')} record={record} />
        </TcsTabs.TabPane>
      </TcsTabs>
    );
  }
  return Next;
};

export default AppList;
