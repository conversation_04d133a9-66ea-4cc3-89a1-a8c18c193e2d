import { GetTAPDOperationSheet, IterationManage } from '@/common/api/iterationManage.api';
import { TcsButton, TcsCard, TcsDescriptions, TcsPopConfirm, TcsSpin } from '@tencent/tcs-component';
import { TcsDescriptionsItemProps } from '@tencent/tcs-component/esm/components/description/components/DesriptionItem';
import { <PERSON><PERSON>, But<PERSON>, Status } from '@tencent/tea-component';
import React, { useEffect, useMemo, useState } from 'react';
import { SHEET_STATUS_MAP } from '../constant';
import { DELIVER_TYPE } from '@/common/constants';

export interface IProps {
  record: any;
  onReload: () => void;
  showTops?: boolean;
  onReSyncTops: (onlyVersion?: boolean) => Promise<any>;
  isManager: boolean;
  hasSolutionVersion: boolean;
  pagePath: 'story' | 'defect';
  isOriginTops?: boolean;
}

const ProductMarket: React.FC<IProps> = ({
  record,
  onReload,
  showTops,
  isOriginTops,
  onReSyncTops,
  // isManager,
  hasSolutionVersion,
  pagePath,
}) => {
  const [Dawn, setDawn] = useState<GetTAPDOperationSheet>();
  const [update, setUpdate] = useState({});
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    if (record.UUID && record.DeliverType !== DELIVER_TYPE.NO_NEED) {
      setLoading(true);
      IterationManage.GetTAPDOperationSheet({
        IssueSolutionRelUUID: record.UUID!,
        OperationTool: '产品市场',
      })
        .then((res) => {
          setDawn(res.Dawn);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [record.UUID, update, record.DeliverType]);

  const handleGotoOps = () => {
    window.open(
      `/page/product-market-jiguang/ops-sheet-manager/create-parent-sheet?bugId=${record?.IssueID}&solutionVersionId=${
        record?.SolutionVersionUUID
      }&solutionVersionName=${record?.SolutionVersion}&arch=${record?.Arch}&sheetSolutionId=${
        record?.UUID
      }&sheetScene=${pagePath === 'story' ? 'story' : 'bugfix'}&tapdUrl=${encodeURIComponent(record?.TapdUrl)}`,
      '_blank',
    );
  };

  function handleSyncTops(onlyVersion?: boolean) {
    setLoading(true);
    onReSyncTops(onlyVersion).finally(() => {
      setLoading(false);
    });
  }

  const operBtn = useMemo(() => {
    // if (!isManager) {
    //   return {
    //     disabled: true,
    //     tooltip: `只能由【处理人/研发负责人/测试负责人】进行操作，您不是当前${
    //       pagePath === 'story' ? '需求单' : '缺陷单'
    //     }的【处理人/研发负责人/测试负责人】`,
    //   };
    // }
    if (pagePath === 'story' && !hasSolutionVersion) {
      return {
        disabled: true,
        tooltip: `当前需求单没有指定解决方案与解决方案版本，请先指定后再进行操作`,
      };
    }
    return {
      disabled: false,
      tooltip: '',
    };
  }, [pagePath, hasSolutionVersion]);

  if (record.DeliverType === DELIVER_TYPE.NO_NEED) {
    return (
      <TcsCard>
        <Alert type="info">出包类型为仅后续新增客户出包，无需完善产品市场变更单</Alert>
      </TcsCard>
    );
  }

  return (
    <TcsSpin spinning={loading}>
      {Dawn ? (
        <TcsDescriptions
          dataSource={Dawn}
          extra={
            showTops &&
            !isOriginTops && (
              <>
                <TcsPopConfirm
                  title="警告"
                  onConfirm={() => handleSyncTops(false)}
                  message="如果您手动修改过产品运维中心变更单，点击此按钮同步会覆盖修改过的内容，请问是否继续。如果您只想更新产品运维中心变更单关联应用制品版本，请点击【仅同步应用制品版本至产品运维中心】按钮。"
                >
                  <TcsButton type="primary">同步至产品运维中心</TcsButton>
                </TcsPopConfirm>
                <TcsPopConfirm
                  title="警告"
                  onConfirm={() => handleSyncTops(true)}
                  message="点击此按钮只会更新产品运维中心变更单关联应用制品版本，是否继续？"
                >
                  <TcsButton>仅同步应用制品版本至产品运维中心</TcsButton>
                </TcsPopConfirm>
              </>
            )
          }
          column={2}
          columns={
            [
              {
                title: '变更单名称',
                dataIndex: 'Name',
                linkable: true,
                linkProps: {
                  linkUrl(text, record) {
                    return `/page/product-market-jiguang/ops-sheet-manager/parent-sheet-detail?parentSheetId=${
                      record?.SheetID
                    }&sheetScene=${pagePath === 'story' ? 'story' : 'bugfix'}`;
                  },
                  target: 'blank',
                },
              },
              {
                title: '变更单ID',
                dataIndex: 'SheetID',
                linkable: true,
                linkProps: {
                  linkUrl(text, record) {
                    return `/page/product-market-jiguang/ops-sheet-manager/parent-sheet-detail?parentSheetId=${
                      record?.SheetID
                    }&sheetScene=${pagePath === 'story' ? 'story' : 'bugfix'}`;
                  },
                  target: 'blank',
                },
              },
              {
                title: '变更单状态',
                dataIndex: 'Status',
                valueEnum: SHEET_STATUS_MAP,
                valueType: 'select',
              },
              {
                title: '最后更新时间',
                dataIndex: 'UpdatedAt',
                valueType: 'dateTime',
              },
              {
                title: '更新人',
                dataIndex: 'Modifier',
              },
            ] as TcsDescriptionsItemProps[]
          }
        />
      ) : (
        <Status
          icon="blank"
          size="m"
          title="未找到变更单"
          description="请点击下方按钮跳转到变更单管理页面完善变更单"
          operation={
            <>
              <Button
                type="primary"
                tooltip={operBtn.tooltip}
                disabled={operBtn.disabled}
                onClick={handleGotoOps}
                style={{ marginRight: 10 }}
              >
                跳转至变更单管理
              </Button>
              <Button
                type="primary"
                onClick={() => {
                  // 需要刷新外部表格，外部表格也会记录变更单ID
                  onReload();
                  setUpdate({});
                }}
              >
                已完善，刷新
              </Button>
            </>
          }
        />
      )}
    </TcsSpin>
  );
};

export default ProductMarket;
