import { getDeliveryInfosByFromType } from '@/common/api/commonApi';
import { ICreateIssueRelatedAppsApi } from '@/common/api/iterationManage.api';

import { DELIVER_TYPE } from '@/common/constants';
import { DefectManagementRoutePath } from '@/common/routePath';
import { TcsButton, TcsCard, TcsTable } from '@tencent/tcs-component';
import { Alert, message } from '@tencent/tea-component';
import React from 'react';

const getColumns = () => [
  {
    dataIndex: 'ClientName',
    title: '客户名称',
    width: '20%',
  },
  {
    dataIndex: 'SiteName',
    title: '局点名称',
    width: '20%',
  },
  {
    dataIndex: 'SiteType',
    title: '局点类型',
    width: '10%',
  },

  {
    dataIndex: 'PackageApplicationArch',
    title: '出包应用架构',
    width: '10%',
    renderText(text) {
      if (!text) {
        return '-';
      }
      return text === '1' ? '单架构' : '多架构';
    },
  },
  {
    dataIndex: 'PackageStatus',
    title: '出包状态',
    width: '15%',
    valueType: 'dictSelect',
    fieldProps: {
      dictType: 'DeliveryStatus',
      showType: 'tag',
    },
  },
  {
    dataIndex: 'BugfixType',
    title: '缺陷修复方式',
    width: '13%',
  },
  {
    dataIndex: 'OperationTool',
    title: '变更工具',
    width: '14%',
  },
];

export interface IProps {
  siteList: ICreateIssueRelatedAppsApi.ISiteInfo[];
  record: any;
  onReload: () => void;
  pagePath: 'story' | 'defect';
}
const SiteList: React.FC<IProps> = ({ siteList, record, onReload, pagePath }) => {
  const columns = getColumns();

  function handleGotoDelivery() {
    if (pagePath === 'story') {
      getDeliveryInfosByFromType({
        FromType: 'IssueSolution',
        FromKey: record.UUID,
      }).then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          const deliveryList = res.DeliveryDetailInfo || [];
          if (deliveryList.length === 0) {
            message.warning({
              content: '当前缺陷单没有关联交付单',
            });
          } else {
            // 直接跳转到交付单详情页
            window.open(`${DefectManagementRoutePath.DELIVERY_DETAIL_PAGE}?uuid=${deliveryList[0].UUID}`, '_blank');
          }
        }
      });
    }
  }

  if (record.DeliverType === DELIVER_TYPE.NO_NEED) {
    return (
      <TcsCard>
        <Alert type="info">出包类型为仅后续新增客户出包，无需关联局点</Alert>
      </TcsCard>
    );
  }

  return (
    <TcsTable
      columns={columns}
      scroll={{ x: 1000 }}
      options={false}
      headerTitle={
        pagePath === 'story' && record?.Type !== 'Other' ? (
          <TcsButton
            onClick={handleGotoDelivery}
            tooltip="跳转到交付单可增改关联局点清单，您也可以在当前页面修改出包类型同时修改关联局点"
          >
            查看关联交付单
          </TcsButton>
        ) : undefined
      }
      toolBarRender={() => (
        <>
          <TcsButton
            type="icon"
            icon="refresh"
            onClick={() => {
              onReload();
            }}
          />
        </>
      )}
      dataSource={siteList}
      pagination={{ defaultPageSize: 10 }}
      onRow={(record: any) => {
        const message = record?.CheckMessage?.trim();
        if (message) {
          return {
            tooltip: message,
          };
        }
        return {
          tooltip:
            record?.Status === 'Closed' || record?.Status === 'Failed'
              ? '已关闭或失败状态可至交付单页面启动出包流程补充出包'
              : '',
        };
      }}
    />
  );
};

export default SiteList;
