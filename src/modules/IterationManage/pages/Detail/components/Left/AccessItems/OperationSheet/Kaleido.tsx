import { GetTAPDOperationSheet } from '@/common/api/iterationManage.api';
import { TcsButton, TcsDescriptions, TcsSpace } from '@tencent/tcs-component';
import { TcsDescriptionsItemProps } from '@tencent/tcs-component/esm/components/description/components/DesriptionItem';
import { Alert } from '@tencent/tea-component';
import React from 'react';

export interface IProps {
  record: any;
  kaleido?: GetTAPDOperationSheet;
  onReload: () => void;
  tapdInfo?: {
    workspace_id: string;
    tapd_id: string;
  };
}

const Kaleido: React.FC<IProps> = ({ tapdInfo, onReload, kaleido }) => (
  <>
    <Alert
      type="info"
      extra={
        <TcsSpace>
          <TcsButton
            type="primary"
            onClick={() => () => {
              onReload();
            }}
          >
            刷新
          </TcsButton>
        </TcsSpace>
      }
    >
      请点击变更单标题跳转到变更单详情页面查看或修改变更单详情
    </Alert>
    <TcsDescriptions
      dataSource={kaleido || {}}
      column={2}
      columns={
        [
          {
            title: '变更单名称',
            dataIndex: 'Name',
            linkable: true,
            renderText: (text) => text || '暂未完善变更控制表基本信息，请点击完善变更控制表',
            linkProps: {
              linkUrl() {
                return `/page/change_control/change_control_sheet?tapd_id=${tapdInfo?.tapd_id}&workspace_id=${tapdInfo?.workspace_id}&hideSiderMenu=true&onlyKaleido=true`;
              },
              linkText(text, record) {
                return record.Name || '暂未完善变更控制表基本信息，请点击完善变更控制表';
              },
              target: 'blank',
            },
          },
          {
            title: '变更单状态',
            dataIndex: 'Status',
          },
          {
            title: '最后更新时间',
            dataIndex: 'UpdatedAt',
            valueType: 'dateTime',
          },
          {
            title: '更新人',
            dataIndex: 'Modifier',
          },
        ] as TcsDescriptionsItemProps[]
      }
    />
  </>
);
export default Kaleido;
