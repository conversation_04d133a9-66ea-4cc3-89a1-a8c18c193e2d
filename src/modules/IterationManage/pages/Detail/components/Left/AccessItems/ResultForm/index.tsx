/*
 * @Author: lucyfang
 * @Date: 2025-02-27 15:21:01
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-03-13 16:32:57
 * @Description: 请输入注释信息
 */
import React, { useEffect } from 'react';

import { TcsCard, TcsForm, TcsFormInstance, TcsFormRadio } from '@tencent/tcs-component';
import ResultFormItem from '../ResultFormItem';
import { ICreateIssueRelatedAppsApi } from '@/common/api/iterationManage.api';
import { toolNameMap, conclusion } from '../../../../../../config';
import TestAllResult from '../TestAllResult';
export interface IProps {
  dataSource: any;
  siteList: ICreateIssueRelatedAppsApi.ISiteInfo[];
  form: TcsFormInstance;
  record?: any;
  initialData: any;
  isDisabled?: boolean;
}

const ResulteForm: React.FC<IProps> = ({ form, initialData, isDisabled, siteList }) => {
  useEffect(() => {
    if (initialData && form) {
      const initialValues = getFormInitialValues();
      form.setFieldsValue(initialValues);
    }
  }, [initialData]);

  const operationTools = Array.from(new Set(siteList.map((site) => site?.OperationTool))) || [];
  // 生成正确的表单初始值结构
  const getFormInitialValues = () => {
    const toolsData: Record<string, any> = {};

    initialData?.SheetResults?.forEach((sheet) => {
      const toolKey = toolNameMap[sheet.OperationTool];
      if (toolKey) {
        toolsData[toolKey] = {
          update: {
            conclusion: sheet.Update?.Conclusion || '',
            remark: sheet.Update?.Remark || '',
          },
          rollback: {
            conclusion: sheet.Rollback?.Conclusion || '',
            remark: sheet.Rollback?.Remark || '',
          },
          conclusion: sheet.Conclusion || '',
        };
      }
    });

    return {
      tools: toolsData,
      test: {
        conclusion: initialData?.Conclusion || '', // 对应 Conclusion
        reasonClass: initialData?.ReasonClass || '', // 对应 ReasonClass
        remark: initialData?.Remark || '', // 对应 Remark
      },
    };
  };

  return (
    <>
      <TcsForm form={form}>
        {operationTools?.map((tool) => {
          const toolKey = toolNameMap[tool];
          if (!toolKey) {
            return null;
          }
          const toolData = initialData
            ? getFormInitialValues().tools[toolKey] || { update: {}, rollback: {} }
            : { update: {}, rollback: {} };
          return (
            <TcsCard key={toolKey} title={`${tool}变更单`} bordered style={{ marginBottom: 16 }}>
              {/* 验证结论 */}
              <TcsFormRadio
                style={{ marginBottom: 8 }}
                disabled={isDisabled}
                initialValue="Passed"
                rules={[
                  {
                    required: true,
                    message: '请选择验证结论',
                  },
                ]}
                colProps={{
                  span: 12,
                }}
                label="变更验证总结论"
                name={`tools.${toolKey}.conclusion`}
                fieldProps={{
                  options: conclusion,
                }}
              />
              {/* 升级部分 */}
              <TcsCard bordered>
                <TcsForm.Item name={`tools.${toolKey}.update`} label="升级">
                  <ResultFormItem
                    isDisabled={isDisabled}
                    resultForm={form}
                    name={`tools.${toolKey}.update`}
                    initialValue={{
                      conclusion: toolData?.update?.conclusion || '', // 设置验证结论的初始值
                      remark: toolData?.update?.remark || '', // 设置备注的初始值
                    }}
                  />
                </TcsForm.Item>
              </TcsCard>
              {/* 回滚部分 */}
              <TcsCard bordered>
                <TcsForm.Item name={`tools.${toolKey}.rollback`} label="回滚" disabled={isDisabled}>
                  <ResultFormItem
                    isDisabled={isDisabled}
                    resultForm={form}
                    name={`tools.${toolKey}.rollback`}
                    initialValue={{
                      conclusion: toolData?.rollback?.conclusion || '', // 设置验证结论的初始值
                      remark: toolData?.rollback?.remark || '', // 设置备注的初始值
                    }}
                  />
                </TcsForm.Item>
              </TcsCard>
            </TcsCard>
          );
        })}
        <TcsCard title="验证结论">
          <TestAllResult testForm={form} initialData={initialData} isDisabled={isDisabled} />
          <ResultFormItem
            isDisabled={isDisabled}
            resultForm={form}
            name="test"
            initialValue={{ remark: initialData?.Remark }}
            isShowSelect={false}
          />
        </TcsCard>
      </TcsForm>
    </>
  );
};
export default ResulteForm;
