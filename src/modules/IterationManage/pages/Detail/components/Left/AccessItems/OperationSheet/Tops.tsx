import { GetTAPDOperationSheet } from '@/common/api/iterationManage.api';
import { TcsButton, TcsDescriptions, TcsPopConfirm, TcsSpace, TcsSpin } from '@tencent/tcs-component';
import { Alert, Button, Status } from '@tencent/tea-component';
import React, { useMemo, useState } from 'react';
import { SHEET_STATUS_MAP } from '../constant';

export interface IProps {
  record: any;
  onReSyncTops: (onlyVersion?: boolean) => Promise<any>;
  Tops?: GetTAPDOperationSheet;
  OriginTops?: GetTAPDOperationSheet;
  onReload: () => void;
  isManager: boolean;
  hasSolutionVersion: boolean;
  pagePath: 'story' | 'defect';
}

const Tops: React.FC<IProps> = ({
  onReSyncTops,
  OriginTops,
  Tops,
  onReload,
  hasSolutionVersion,
  // isManager,
  pagePath,
}) => {
  const [loading, setLoading] = useState(false);

  const operBtn = useMemo(() => {
    // if (!isManager) {
    //   return {
    //     disabled: true,
    //     tooltip: `只能由【处理人/研发负责人/测试负责人】进行操作，您不是当前${
    //       pagePath === 'story' ? '需求单' : '缺陷单'
    //     }的【处理人/研发负责人/测试负责人】`,
    //   };
    // }
    if (pagePath === 'story' && !hasSolutionVersion) {
      return {
        disabled: true,
        tooltip: `当前需求单没有指定解决方案与解决方案版本，请先指定后再进行操作`,
      };
    }
    return {
      disabled: false,
      tooltip: '',
    };
  }, [pagePath, hasSolutionVersion]);

  function handleReSync(onlyVersion?: boolean) {
    setLoading(true);
    onReSyncTops(onlyVersion)
      .then(() => {
        onReload();
      })
      .finally(() => {
        setLoading(false);
      });
  }

  if (!OriginTops && !Tops) {
    return (
      <TcsSpin spinning={loading}>
        <Status
          icon="blank"
          size="m"
          title="未找到变更单"
          description={
            <div>
              <span>
                产品运维中心变更单由产品市场变更单同步而来，请先完善产品市场变更单，如已完善，请点击下方重新同步按钮手动同步，如遇到同步失败，可
              </span>
              <span>
                <a
                  href="https://doc.weixin.qq.com/doc/w3_ANEA0gZoACoUysgceakR0Gu6f5FS9?scode=AJEAIQdfAAokIN1vmdANEA0gZoACo"
                  target="_blank"
                  rel="noreferrer"
                  style={{ fontWeight: 600 }}
                >
                  点击链接
                </a>
              </span>
              <span> 查看同步失败处理手册</span>
            </div>
          }
          operation={
            <>
              <TcsSpace>
                <Button
                  disabled={operBtn.disabled}
                  tooltip={operBtn.tooltip}
                  type="primary"
                  onClick={() => {
                    handleReSync(false);
                  }}
                >
                  同步
                </Button>
                <Button onClick={() => onReload()}>刷新</Button>
              </TcsSpace>
            </>
          }
        />
      </TcsSpin>
    );
  }

  return (
    <TcsSpin spinning={loading}>
      {!OriginTops && (
        <Alert type="warning">
          <span>产品运维中心变更单由产品市场变更单同步而来，可以点击下方重新同步按钮进行同步，如遇到同步失败，可</span>
          <a
            href="https://doc.weixin.qq.com/doc/w3_ANEA0gZoACoUysgceakR0Gu6f5FS9?scode=AJEAIQdfAAokIN1vmdANEA0gZoACo"
            target="_blank"
            rel="noreferrer"
            style={{ fontWeight: 600 }}
          >
            点击链接
          </a>
          <span>查看同步失败处理手册</span>
        </Alert>
      )}
      <TcsDescriptions
        dataSource={Tops || OriginTops}
        column={2}
        extra={
          !OriginTops &&
          Tops && (
            <TcsSpace>
              <TcsPopConfirm
                title="警告"
                onConfirm={() => handleReSync(false)}
                message="如果您手动修改过产品运维中心变更单，点击此按钮同步会覆盖修改过的内容，请问是否继续。如果您只想更新产品运维中心变更单关联应用制品版本，请点击【仅同步应用制品版本至产品运维中心】按钮。"
              >
                <TcsButton type="primary">重新同步</TcsButton>
              </TcsPopConfirm>
              <TcsPopConfirm
                title="警告"
                onConfirm={() => handleReSync(true)}
                message="点击此按钮只会更新产品运维中心变更单关联应用制品版本，是否继续？"
              >
                <TcsButton>仅同步应用制品版本</TcsButton>
              </TcsPopConfirm>
              <Button onClick={() => onReload()}>刷新</Button>
            </TcsSpace>
          )
        }
        columns={[
          {
            title: '变更单名称',
            dataIndex: 'Name',
            linkable: true,
            linkProps: {
              linkUrl(text, record) {
                return `https://tops.woa.com/sheet/${record.SheetID}/detail`;
              },
              linkText(text, record) {
                return record.Name || record.SheetID;
              },
              target: 'blank',
            },
          },
          {
            title: '变更单ID',
            dataIndex: 'SheetID',
            linkable: true,
            linkProps: {
              linkUrl(text, record) {
                return `https://tops.woa.com/sheet/${record.SheetID}/detail`;
              },
              target: 'blank',
            },
          },
          {
            title: '变更单状态',
            dataIndex: 'Status',
            valueEnum: SHEET_STATUS_MAP,
            valueType: 'select',
          },
          {
            title: '最后更新时间',
            dataIndex: 'UpdatedAt',
            valueType: 'dateTime',
          },
          {
            title: '更新人',
            dataIndex: 'Modifier',
          },
          {
            title: '变更单来源',
            dataIndex: 'Source',
            renderText() {
              return OriginTops ? '产品运维中心填写' : '产品市场自动转换';
            },
          },
        ]}
      />
    </TcsSpin>
  );
};

export default Tops;
