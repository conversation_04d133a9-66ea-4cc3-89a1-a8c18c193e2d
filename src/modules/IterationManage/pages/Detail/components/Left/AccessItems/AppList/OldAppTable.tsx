/*
 * @Author: lucyfang
 * @Date: 2025-04-15 10:36:19
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-04-16 11:00:27
 * @Description: 请输入注释信息
 */
import React, { useState } from 'react';
import { TcsButton, TcsTable } from '@tencent/tcs-component';
import { GetComponentListForTAPDResponse } from '@/common/api/iterationManage.api';
import { Text } from '@tencent/tea-component';
import SyncOldAppModal from './SyncOldAppModal';

export const getOldColumns = () => [
  {
    dataIndex: 'ComponentName',
    title: '应用名称',
    width: '10%',
  },
  {
    dataIndex: 'IsQci',
    title: '是否合流',
    width: '10%',
    render: (text, record) => (record.IsQci ? '是' : <Text theme="danger">否</Text>),
  },
  {
    dataIndex: 'IsPlan',
    title: '是否评估',
    width: '10%',
    render: (text, record) => (record.IsPlan ? '是' : <Text theme="danger">否</Text>),
  },
  {
    dataIndex: 'Version',
    title: '合流版本',
    width: '20%',
    render: (text, record) => {
      const textValue = '暂无已合流版本';
      if (!record.PackageVersion) {
        return textValue;
      }
      return record.PackageVersion;
    },
  },
];

export interface IProps {
  oldAppList: GetComponentListForTAPDResponse['ComponentList'];
  onReload: () => void;
  record?: any;
}

const OldAppTable: React.FC<IProps> = ({ oldAppList, onReload, record }) => {
  const [visible, setVisible] = useState(false);
  const oldAppColumns = getOldColumns();
  const hanldeSync = () => {
    setVisible(true);
  };
  const hanldeSuccess = () => {
    setVisible(false);
    onReload();
  };

  return (
    <>
      <TcsTable
        toolBarRender={() => (
          <TcsButton
            type="icon"
            icon="refresh"
            onClick={() => {
              onReload();
            }}
          />
        )}
        headerTitle={
          <>
            <TcsButton
              type="primary"
              onClick={() => {
                window.open(`https://jiguang.woa.com/page/develop_merge_management/develop_merge`, '_blank');
              }}
            >
              修改合流版本
            </TcsButton>
            <TcsButton type="weak" onClick={hanldeSync}>
              同步Next应用版本
            </TcsButton>
          </>
        }
        dataSource={oldAppList || []}
        options={false}
        columns={oldAppColumns}
      />
      {visible && <SyncOldAppModal onClose={() => setVisible(false)} record={record} onSuccess={hanldeSuccess} />}
    </>
  );
};

export default OldAppTable;
