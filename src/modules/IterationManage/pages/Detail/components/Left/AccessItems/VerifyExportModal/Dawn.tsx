import { generateWorkflowInstance } from '@/common/api/deliveryManage';
import { VERIFY_EXPORT_WORKFLOW_UUID } from '@/common/config';
import { TcsButton, TcsTable } from '@tencent/tcs-component';
import { message } from '@tencent/tea-component';
import React, { useMemo, useState } from 'react';

export interface IProps {
  record: any;
  data: any;
  onRefresh: () => void;
}

export const getColumns = () => [
  {
    title: '创建人',
    dataIndex: 'Creator',
    width: '10%',
  },

  {
    title: '状态',
    dataIndex: 'Status',
    width: '10%',
    valueType: 'dictSelect',
    fieldProps: {
      dictType: 'VerificationStatus',
    },
  },
  {
    title: '测试用户',
    dataIndex: 'ClientName',
    width: '15%',
  },
  {
    title: '测试局点',
    dataIndex: 'SiteName',
    width: '20%',
  },
  {
    title: '物料下载',
    dataIndex: 'MaterialURL',
    width: '10%',
    linkable: true,
    copyable: true,
    linkProps: {
      linkText: () => '点击下载',
    },
  },
  {
    title: '创建时间',
    dataIndex: 'CreatedAt',
    width: '15%',
    valueType: 'dateTime',
  },
  {
    title: '操作',
    dataIndex: 'option',
    valueType: 'option',
    width: '10%',
    // eslint-disable-next-line react/display-name
    render: (text, record) => (
      <TcsButton
        type="link"
        onClick={() => {
          window.open(
            `/page/flow-design/flow-publish/exec?instance_id=${record.WorkflowInstanceID}&workflow_id=${record.WorkflowID}`,
            '_blank',
          );
        }}
      >
        查看出包历史
      </TcsButton>
    ),
  },
];

const Dawn: React.FC<IProps> = ({ record, data, onRefresh }) => {
  const [loading, setLoading] = useState(false);
  const btnDisabled = useMemo(() => {
    if (record?.IssueAppRel?.find((item) => !item.LatestRegisteredPackageUUID && !item.ReleasePackageUUID)) {
      return true;
    }
    return false;
  }, [record]);

  const isPublished = useMemo(() => record?.OperationSheet?.Status === 'Published', [record?.OperationSheet?.Status]);

  const buttonTooltip = useMemo(() => {
    if (btnDisabled) {
      return '所有应用已合流才能进行验证出包';
    }
    if (isPublished) {
      return '';
    }
    return '当前解决方案版本架构未关联变更单或变更单未发布，验证出包仅支持出制品包，请知悉';
  }, [isPublished, btnDisabled]);

  function handleStartFlow() {
    setLoading(true);
    generateWorkflowInstance({
      WorkflowUUID: VERIFY_EXPORT_WORKFLOW_UUID.ProductMarket,
      GlobalValues: {
        SourceKey: record.UUID,
        SourceType: 'IssueSolution',
      },
    })
      .then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          message.success({
            content: '流程启动成功',
          });
          window.open(
            `/page/flow-design/flow-publish/exec?instance_id=${res.WorkflowInstanceID}&workflow_id=${res.WorkflowID}`,
            '_blank',
          );
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }

  return (
    <TcsTable
      columns={getColumns()}
      dataSource={data}
      pagination={{}}
      scroll={{ x: 1000 }}
      headerTitle={
        <TcsButton
          type="primary"
          disabled={btnDisabled}
          tooltip={buttonTooltip}
          onClick={handleStartFlow}
          loading={loading}
        >
          启动验证出包流程
        </TcsButton>
      }
      options={{
        reload: false,
      }}
      toolBarRender={() => (
        <TcsButton
          type="icon"
          icon="refresh"
          onClick={() => {
            onRefresh();
          }}
        />
      )}
    />
  );
};

export default Dawn;
