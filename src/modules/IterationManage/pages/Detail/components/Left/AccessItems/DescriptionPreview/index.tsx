/*
 * @Author: lucyfang
 * @Date: 2025-02-28 18:44:19
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-03-05 13:38:32
 * @Description: 请输入注释信息
 */
import React from 'react';
import styles from './index.module.less';
import { Icon } from '@tencent/tea-component';

export interface IProps {
  onClick: (data: any) => void;
  children: React.ReactNode;
}

const DescriptionPreview: React.FC<IProps> = ({ onClick, children }) => {
  return (
    <div className={styles.box} onClick={onClick}>
      {children}
      <Icon className={styles.fullscreen} type="fullscreen" />
    </div>
  );
};

export default DescriptionPreview;
