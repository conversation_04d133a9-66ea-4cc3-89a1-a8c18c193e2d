import { generateWorkflowInstance } from '@/common/api/deliveryManage';
import { VERIFY_EXPORT_WORKFLOW_UUID } from '@/common/config';
import { Alert, Button, message } from '@tencent/tea-component';
import React, { useState } from 'react';

const Kaleido = () => {
  const [loading, setLoading] = useState(false);
  function handleStartFlow() {
    setLoading(true);
    generateWorkflowInstance({
      WorkflowUUID: VERIFY_EXPORT_WORKFLOW_UUID.Kaleido,
    })
      .then((response) => {
        if (response.Error) {
          message.error({
            content: response.Error.Message,
          });
        } else {
          message.success({
            content: '流程启动成功',
          });
          window.open(
            `/page/flow-design/flow-publish/exec?instance_id=${response.WorkflowInstanceID}&workflow_id=${response.WorkflowID}`,
            '_blank',
          );
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }
  return (
    <>
      <Alert type="info">点击下方按钮启动Kaleido验证出包流程（当前暂无验证出包记录清单）</Alert>
      <Button loading={loading} type="primary" onClick={handleStartFlow}>
        启动验证出包
      </Button>
    </>
  );
};

export default Kaleido;
