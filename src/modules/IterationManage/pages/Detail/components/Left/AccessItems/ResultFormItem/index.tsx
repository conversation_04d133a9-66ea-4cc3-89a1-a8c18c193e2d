/*
 * @Author: lucyfang
 * @Date: 2025-02-27 15:21:01
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-03-27 14:36:58
 * @Description: 请输入注释信息
 */
import React, { useEffect, useState } from 'react';
import { TcsForm, TcsFormInstance, TcsFormRadio, TcsButton } from '@tencent/tcs-component';
import { conclusion } from '../../../../../../config';
import MarkdownEditorModal from '../MarkdownEditorModal';
import DescriptionPreview from '../DescriptionPreview';
import styles from './index.module.less';

export interface IProps {
  isShowSelect?: boolean;
  initialValue: {
    conclusion?: string;
    remark?: string;
  };
  resultForm: TcsFormInstance;
  name?: string;
  isDisabled?: boolean;
}
const ResultFormItem: React.FC<IProps> = ({ initialValue, isShowSelect = true, resultForm, name = '', isDisabled }) => {
  const [edit, setEdit] = useState<boolean>(false);
  const [visible, setVisible] = useState(false);
  const [currentDes, setCurrentDes] = useState(initialValue?.remark || '');
  const title = currentDes ? '编辑备注' : '填写备注';
  useEffect(() => {
    setCurrentDes(initialValue?.remark || '');
  }, [initialValue]);
  const handleDescription = (val) => {
    const currentParentValues = resultForm.getFieldValue(name) || {};
    // 合并新的 remark 值
    const updatedValues = { ...currentParentValues, remark: val.des };
    // 更新父级字段的值
    resultForm?.setFieldsValue({
      [name]: updatedValues,
    });
    resultForm?.validateFields([name]).then(() => {});
    setCurrentDes(val.des);
  };
  const handleResult = () => {
    setVisible(true);
  };

  return (
    <>
      {isShowSelect && (
        <TcsFormRadio
          initialValue="Passed"
          disabled={isDisabled}
          rules={[
            {
              required: true,
              message: '请选择验证结论',
            },
          ]}
          colProps={{
            span: 12,
          }}
          label="验证结论"
          name={`${name}.conclusion`}
          fieldProps={{
            options: conclusion,
          }}
        />
      )}
      <TcsForm.Item
        label="备注"
        name={`${name}.remark`}
        rules={[
          {
            required: isShowSelect,
            message: '请填写验证结论',
          },
        ]}
        tooltip={
          isShowSelect ? (
            <>
              <p>1、用于记录变更过程的验证。需要附上变更成功的截图</p>
              <p>2、若是受限通过，需要说明什么环境，什么情况，什么依赖，会导致什么问题，周知风险</p>
              <p>3、不涉及，需要说明为何不涉及</p>
            </>
          ) : null
        }
      >
        {!isDisabled && (
          <TcsButton className={styles.btn} type="link" onClick={handleResult}>
            {title}
          </TcsButton>
        )}
        {currentDes && (
          <DescriptionPreview
            onClick={() => {
              setVisible(true);
              setEdit(true);
            }}
          >
            {currentDes}
          </DescriptionPreview>
        )}
      </TcsForm.Item>

      {visible && (
        <MarkdownEditorModal
          title={edit ? '预览备注' : title}
          onClose={() => {
            setVisible(false);
          }}
          desContent={currentDes}
          onChange={(val) => handleDescription(val)}
          setEdit={setEdit}
          status={edit ? 'previewOnly' : 'edit&preview'}
        />
      )}
    </>
  );
};
export default ResultFormItem;
