/*
 * @Author: lucyfang
 * @Date: 2025-04-11 11:27:45
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-04-16 17:52:37
 * @Description: 请输入注释信息
 */
import React, { useState } from 'react';
import { TcsButton, TcsModal, TcsTable } from '@tencent/tcs-component';
import { IterationManage } from '@/common/api/iterationManage.api';
import styles from './index.module.less';
import { message } from '@tencent/tea-component';
const getColumns = ({}) => {
  return [
    { dataIndex: 'Order', title: '序号', width: '5%' },
    {
      dataIndex: 'ApplicationName',
      title: '极光Next应用名称',
      width: '20%',
    },
    {
      dataIndex: 'ComponentName',
      title: '极光2.0应用名称',
      width: '20%',
    },
    {
      dataIndex: 'NextPackageVersion',
      title: '极光Next合流版本',
      width: '20%',
    },
    {
      dataIndex: 'OldPackageVersion',
      title: '极光2.0合流版本',
      width: '20%',
    },
    {
      dataIndex: 'IsPlan',
      title: '是否评估',
      render: (_, record) => {
        return record?.IsPlan ? '是' : '否';
      },
      width: '5%',
    },
    {
      dataIndex: 'IsQci',
      title: '是否合流',
      render: (_, record) => {
        return record?.IsQci ? '是' : '否';
      },
      width: '5%',
    },
    {
      fixed: 'right',
      dataIndex: 'IsDifferent',
      title: '是否有差异',
      render: (_, record) => {
        return record?.IsDifferent ? '是' : '否';
      },
      width: '5%',
    },
  ];
};
export interface IProps {
  onClose: () => void;
  record?: any;
  onSuccess: () => void;
}

const SyncOldAppModal: React.FC<IProps> = ({ onClose, record, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const handleUpdate = () => {
    setLoading(true);
    IterationManage.SyncTAPDApplicationPackages({ Method: 'NextToOld', IssueSolutionRelUUID: record.UUID })
      .then(() => {
        message.success({ content: '同步成功' });
        onSuccess();
      })
      .finally(() => {
        setLoading(false);
      });
  };
  const handleRequest = () => {
    setLoading(true);
    return IterationManage.DiffRegisteredApplicationPackages({ IssueSolutionRelUUID: record.UUID })
      .then((res) => {
        const dataList =
          res?.ApplicationList?.map((item, index) => {
            return {
              ...item,
              Order: index + 1,
            };
          }) || [];
        return {
          data: dataList,
          total: dataList?.length,
        };
      })
      .finally(() => {
        setLoading(false);
      });
  };
  return (
    <TcsModal
      visible
      title="极光2.0与极光Next应用版本同步"
      width={1000}
      footer={
        <TcsButton onClick={handleUpdate} loading={loading} type="primary">
          更新极光2.0合流版本
        </TcsButton>
      }
      onCancel={onClose}
    >
      <TcsTable
        rowKey="Order"
        columns={getColumns({})}
        request={handleRequest}
        onRow={(record) => {
          if (record?.IsDifferent) {
            return { className: styles.warn_info };
          }
          return {};
        }}
        pagination={{}}
        scroll={{ x: 1000 }}
      />
    </TcsModal>
  );
};

export default SyncOldAppModal;
