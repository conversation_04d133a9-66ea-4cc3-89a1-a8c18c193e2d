/*
 * @Author: lucyfang
 * @Date: 2025-02-26 15:07:53
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-03-05 17:22:32
 * @Description: 请输入注释信息
 */
import React, { useState, useEffect } from 'react';
import { TcsFormSelect, TcsFormInstance } from '@tencent/tcs-component';
import useLookup from '@/common/hookups/useLookup';

export interface IProps {
  testForm: TcsFormInstance;
  initialData: any;
  isDisabled?: boolean;
}
const TestAllResult: React.FC<IProps> = ({ testForm, initialData, isDisabled }) => {
  const { lookups } = useLookup(['TestConclusion']);
  const [classListOptions, setClassListOptions] = useState<any[]>([]);

  // 提取处理 classList 的逻辑
  const getClassListOptions = (conclusionCode: string) => {
    const selectedConclusionData = lookups?.TestConclusion?.find((item) => item.Code === conclusionCode);
    const classList = selectedConclusionData?.Extra?.classList || [];
    return classList.map((item) => ({
      label: item,
      value: item,
    }));
  };

  // 处理测试验证结论的变化
  const handleConclusionChange = (value: string) => {
    testForm.setFieldsValue({ ['test.reasonClass']: '' });
    setClassListOptions(getClassListOptions(value));
  };

  // 初始化时处理 initialData
  useEffect(() => {
    if (initialData?.Conclusion) {
      setClassListOptions(getClassListOptions(initialData.Conclusion));
    }
  }, [initialData?.Conclusion]);
  return (
    <>
      <TcsFormSelect
        disabled={isDisabled}
        rules={[
          {
            required: true,
            message: '请选择验证结论',
          },
        ]}
        name="test.conclusion"
        label="测试验证结论"
        options={lookups?.TestConclusion?.map((item) => {
          return {
            label: item?.Name,
            value: item?.Code,
          };
        })}
        onChange={handleConclusionChange}
      />
      {classListOptions?.length > 0 && (
        <TcsFormSelect
          disabled={isDisabled}
          rules={[
            {
              required: true,
              message: '请选择结论内容',
            },
          ]}
          name="test.reasonClass"
          label="结论内容"
          options={classListOptions}
        />
      )}
    </>
  );
};
export default TestAllResult;
