import { T<PERSON><PERSON><PERSON>, T<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TcsTabs } from '@tencent/tcs-component';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { IPropsIssueSolutionRel } from '@/common/api/interationManage';
import SiteList from './SiteList';
import styles from './index.module.less';
import {
  GetComponentListForTAPDResponse,
  GetTAPDOperationSheet,
  ICreateIssueRelatedAppsApi,
  IterationManage,
  listUnEvaluatedApps,
} from '@/common/api/iterationManage.api';
import { DELIVER_TYPE } from '@/common/constants';
import { parseTapd } from '@/modules/KaleidoManage/utils';

import { Alert, Bubble, message, Modal, Stepper } from '@tencent/tea-component';
import { TcscApiError } from '@tencent/tcsc-base';
import VerifyExport from './VerifyExport';
import OperationSheet from './OperationSheet';
import AppList from './AppList';
import { SHEET_STATUS } from './constant';

export interface IProps {
  record: any;
  pagePath: 'story' | 'defect';
  onReload: () => void;
  issueID: string;
  onLoading: (status: boolean) => void;
  tableDataSource?: IPropsIssueSolutionRel[];
  issueType?: string;
  // 是否问题单负责人
  isManager: boolean;
  // 当前问题单是否有解决方案版本
  hasSolutionVersion: boolean;
  // 是否显示依赖评估按钮
  showDependAssess?: boolean;
}

// const CCB_CLIENT_NAME = [
//   '中国建设银行股份有限公司',
//   '建行升级测试',
//   '建行临时环境',
//   '达烁高科(北京)信息技术有限公司',
//   '建行',
// ];

function useLoadingQueue() {
  const [loading, setLoading] = useState(false);
  const loadingCount = useRef(0);

  const startLoading = useCallback(() => {
    loadingCount.current += 1;
    if (loadingCount.current > 0) {
      setLoading(true);
    }
  }, []);

  const stopLoading = useCallback(() => {
    if (loadingCount.current > 0) {
      loadingCount.current -= 1;
    }
    if (loadingCount.current === 0) {
      setLoading(false);
    }
  }, []);

  return [loading, startLoading, stopLoading] as const;
}

const AccessItmes: React.FC<IProps> = ({
  record,
  pagePath,
  onReload,
  tableDataSource,
  onLoading,
  issueID,
  issueType,
  isManager,
  hasSolutionVersion,
  showDependAssess,
}) => {
  const [currentTab, setCurrentTab] = useState('app');
  // 是否现网单
  const isOnlineType = issueType === 'OnlineBug' || issueType === 'OnlineStory';
  const isTCE3100 = record.SolutionVersion === 'TCE3.10.0';
  // const [loading, setLoading] = useState(false);
  const [loading, startLoading, stopLoading] = useLoadingQueue();

  const [siteList, setSiteList] = useState<ICreateIssueRelatedAppsApi.ISiteInfo[]>([]);
  const [updateSite, setUpdateSite] = useState({});
  const [updateOldApp, setOldUpdateApp] = useState({});
  const [operationTools, setOperationTools] = useState<string[]>();
  const [oldAppList, setOldAppList] = useState<GetComponentListForTAPDResponse['ComponentList']>([]);
  const [OriginTopsData, setOriginTopsData] = useState<GetTAPDOperationSheet>();
  const [TopsData, setTopsData] = useState<GetTAPDOperationSheet>();
  const [updateTops, setUpdateTops] = useState({});
  const [kaleido, setKaleido] = useState<GetTAPDOperationSheet>();
  const [updateKaleido, setUpdatekaleido] = useState({});
  // 如果返回为true，则关联的应用全部已经完成依赖评估，否则有应用未完成依赖评估
  const [hasAllApplicationEvaluated, setHasAllApplicationEvaluated] = useState(false);
  // // 是否包含建行局点
  // const hasCCB = useMemo(() => {
  //   if (isTCE3100 && siteList.length) {
  //     return siteList.every((client) => CCB_CLIENT_NAME.includes(client.ClientName));
  //   }
  //   return false;
  // }, [isTCE3100, siteList]);

  // const isAllNextSite = useMemo(() => siteList.findIndex((site) => site.SiteType !== 'next') === -1, [siteList]);

  const tapdInfo = useMemo(() => {
    if (record.TapdUrl) {
      return parseTapd(record.TapdUrl);
    }
    return undefined;
  }, [record.TapdUrl]);

  useEffect(() => {
    if (isOnlineType) {
      // 如果获取到了TAPD信息，则使用TAPD信息获取局点列表和2.0应用列表
      if (tapdInfo) {
        startLoading();
        IterationManage.GetTAPDDeliverySites({
          IssueSolutionRelUUID: record.UUID,
        })
          .then((res) => {
            setSiteList(res.Sites);
            setOperationTools(res.OperationTools);
          })
          .finally(() => {
            stopLoading();
          });
      } else {
        if (record.UUID) {
          startLoading();
          IterationManage.ListDeliverySitesForIssueSolutionVersion({
            IssueSolutionRelUUIDs: [record.UUID],
          })
            .then((res) => {
              const data = res.IssueSolutionRelSites.map((item) => item.Sites || []).flat();
              setSiteList(data);
            })
            .finally(() => {
              stopLoading();
            });
        }
      }
    }
  }, [record?.UUID, updateOldApp, tapdInfo, pagePath, isOnlineType, startLoading, stopLoading]);

  useEffect(() => {
    if (showDependAssess) {
      const ApplicationUUIDs = record?.IssueAppRel?.map((item) => item?.ApplicationBranch?.ApplicationUUID || '');
      if (ApplicationUUIDs?.length > 0 && record?.SolutionVersionUUID && record?.TapdUrl && record?.IssueID) {
        startLoading();
        listUnEvaluatedApps({
          ApplicationUUIDs,
          SolutionVersionUUID: record.SolutionVersionUUID,
          TAPDUrl: record.TapdUrl,
          IssueID: record.IssueID,
        })
          .then((res) => {
            if (res?.Data) {
              setHasAllApplicationEvaluated(res?.Data?.HasAllApplicationEvaluated);
            }
          })
          .finally(() => {
            stopLoading();
          });
      }
    }
  }, [showDependAssess, record, startLoading, stopLoading]);

  useEffect(() => {
    if (issueType === 'OnlineBug' && isTCE3100) {
      // 如果获取到了TAPD信息，则使用TAPD信息获取局点列表和2.0应用列表
      if (tapdInfo) {
        startLoading();
        IterationManage.GetComponentListForTAPD({
          TAPD: `https://tapd.woa.com/tapd_fe/${tapdInfo.workspace_id}/${
            pagePath === 'defect' ? 'bug' : 'story'
          }/detail/${tapdInfo.tapd_id}`,
        })
          .then((res) => {
            setOldAppList(res.ComponentList);
          })
          .finally(() => {
            stopLoading();
          });
      }
    }
  }, [updateSite, tapdInfo, pagePath, issueType, isTCE3100, startLoading, stopLoading, updateOldApp]);

  const showTops = useMemo(
    () => isTCE3100 && operationTools?.includes('产品运维中心') && record?.DeliverType !== DELIVER_TYPE.NO_NEED,
    [isTCE3100, operationTools, record?.DeliverType],
  );

  const showKaleido = useMemo(() => isTCE3100 && operationTools?.includes('kaleido'), [isTCE3100, operationTools]);

  const showDawn = useMemo(
    () => operationTools?.includes('产品市场') || operationTools?.length === 0,
    [operationTools],
  );

  useEffect(() => {
    if (record.UUID && showTops) {
      startLoading();
      IterationManage.GetTAPDOperationSheet({
        IssueSolutionRelUUID: record.UUID!,
        OperationTool: '产品运维中心',
      })
        .then((res) => {
          setOriginTopsData(res.OriginTops);
          setTopsData(res.Tops);
        })
        .finally(() => {
          stopLoading();
        });
    }
  }, [record.UUID, showTops, updateTops, startLoading, stopLoading]);

  useEffect(() => {
    if (record.UUID && showKaleido) {
      startLoading();
      IterationManage.GetTAPDOperationSheet({
        IssueSolutionRelUUID: record.UUID!,
        OperationTool: 'kaleido',
      })
        .then((res) => {
          setKaleido(res.Kaleido);
        })
        .finally(() => {
          stopLoading();
        });
    }
  }, [record.UUID, updateKaleido, showKaleido, startLoading, stopLoading]);

  const [stepCurrent, defaultTabCurrent, tooltipMessage] = useMemo(() => {
    const issueAppRel = record.IssueAppRel || [];
    if (issueAppRel.length === 0) {
      return ['app', 'app', '未评估应用，请先评估应用'];
    }
    // 检测issueAppRel中的LatestRegisteredApplicationPackage是否已全部填写，全部填写说明next应用已全部合流
    const isNextMerge = issueAppRel.every((item) => item.LatestRegisteredApplicationPackage);
    if (!isNextMerge) {
      return ['merge', 'app', '存在未合流的Next应用，请先合流'];
    }
    // 检测2.0应用是否已全部评估合流
    if (oldAppList?.length) {
      const isOldMerge = oldAppList.every((item) => item.IsQci && item.IsPlan);
      if (!isOldMerge) {
        return ['merge', 'oldApp', '存在未合流或评估的2.0应用，请先合流或评估'];
      }
    }
    if (isOnlineType && record.DeliverType !== DELIVER_TYPE.NO_NEED) {
      if (siteList.length === 0) {
        return ['app', 'site', '未关联局点，请先关联局点'];
      }
      if (showDependAssess && !hasAllApplicationEvaluated) {
        return ['depend', 'app', '存在未评估的应用，请先评估依赖关系'];
      }
      if (!record.OperationSheet) {
        return ['operationSheet', 'dawn', '请先完善产品市场变更单'];
      }
      if (record.OperationSheet?.Status !== SHEET_STATUS.Published) {
        return ['operationSheet', 'dawn', '产品市场变更单未发布，请点击变更单标题跳转至变更单编辑页进行发布'];
      }
      if (showTops) {
        if (!TopsData && !OriginTopsData) {
          return ['operationSheet', 'tops', '请先完善产品运维中心变更单'];
        }
        if (
          (TopsData && TopsData.Status !== SHEET_STATUS.Published) ||
          (OriginTopsData && OriginTopsData.Status !== SHEET_STATUS.Published)
        ) {
          return ['operationSheet', 'tops', '产品运维中心变更单未发布，请先发布'];
        }
      }
      if (showKaleido && !kaleido) {
        return ['operationSheet', 'kaleido', '请先完善Kaleido变更控制表'];
      }
      const isNextAppTest = issueAppRel.every((item) => item.ReleaseApplicationPackage);

      if (!isNextAppTest) {
        return [
          'test',
          'app',
          '存在未关联正式制品的Next应用，请将已测试通过的应用制品版本标记为正式制品（点击关联合流版本打开弹框，点击验证通过按钮）',
        ];
      }
    }

    return ['test', 'test', '当前步骤未校验测试报告上传情况，请自行检查，如已上传，即可扭转TAPD单状态至待出包状态了'];
  }, [
    record,
    oldAppList,
    TopsData,
    OriginTopsData,
    showTops,
    kaleido,
    showKaleido,
    siteList,
    isOnlineType,
    hasAllApplicationEvaluated,
    showDependAssess,
  ]);

  useEffect(() => {
    if (['tops', 'dawn', 'kaleido'].includes(defaultTabCurrent)) {
      setCurrentTab('dawn');
    } else if (['app', 'oldApp'].includes(defaultTabCurrent)) {
      setCurrentTab('app');
    } else {
      setCurrentTab(defaultTabCurrent);
    }
  }, [defaultTabCurrent]);

  function handleReSyncTops(onlyVersion: false) {
    if (onlyVersion && record?.UUID) {
      return IterationManage.UpdateAppVersionInTopsSheet({
        IssueSolutionRelUUID: record.UUID!,
      }).then(() => {
        message.success({
          content: '同步成功',
        });
      });
    }
    if (record.OperationSheet?.SheetID) {
      return IterationManage.ConvertToTopsSheet({
        SheetId: record.OperationSheet.SheetID!,
        IssueSolutionRelUUID: record.UUID!,
      })
        .then(() => {
          message.success({
            content: '同步成功',
          });
        })
        .catch((error: TcscApiError) => {
          Modal.error({
            message: '同步失败',
            description: <div style={{ width: 400 }}>{error.message}</div>,
          });
        });
    }

    message.error({
      content: '请先完善产品市场变更单',
    });
    return Promise.reject();
  }

  const operationTitle = useMemo(() => {
    const trueCount = [showTops, showDawn, showKaleido].filter(Boolean).length;
    if (trueCount >= 2) {
      return '关联变更单';
    }
    if (showTops) {
      // 需求单，如果有产品运维中心但没有产品市场，也需要显示产品市场变更单，因为需要先填写产品市场变更单再转换为产品运维中心变更单
      if (!showDawn && pagePath === 'story') {
        return '关联变更单';
      }
      return '产品运维中心变更单';
    }
    if (showDawn) {
      return '产品市场变更单';
    }
    if (showKaleido) {
      return 'Kaleido变更控制表';
    }
    return '';
  }, [showTops, showDawn, showKaleido]);

  return (
    <div>
      <TcsSpin spinning={loading}>
        <TcsCard>
          <Stepper
            className={styles.access_items__stepper}
            current={stepCurrent}
            steps={[
              {
                id: 'app',
                label: '评估应用局点',
                detail: <Bubble content={stepCurrent === 'app' ? tooltipMessage : undefined}>研发填写</Bubble>,
              },
              {
                id: 'merge',
                label: '应用合流',
                detail: <Bubble content={stepCurrent === 'merge' ? tooltipMessage : undefined}>研发操作</Bubble>,
              },
              showDependAssess
                ? {
                    id: 'depend',
                    label: '评估依赖关系',
                    detail: <Bubble content={stepCurrent === 'depend' ? tooltipMessage : undefined}>研发填写</Bubble>,
                  }
                : undefined,
              isOnlineType
                ? {
                    id: 'operationSheet',
                    label: '填写变更控制表',
                    detail: (
                      <Bubble content={stepCurrent === 'operationSheet' ? tooltipMessage : undefined}>研发填写</Bubble>
                    ),
                  }
                : undefined,
              {
                id: 'test',
                label: '填写验证结论',
                detail: (
                  <Bubble content={stepCurrent === 'test' ? tooltipMessage : undefined}>测试操作</Bubble>
                  // <Bubble content="验证出包由测试自行操作，暂未进行步骤校验">
                  //   <Text theme="warning" style={{ fontWeight: 600 }}>
                  //     测试操作，暂未校验测试
                  //     <br />
                  //     报告，请在TAPD自行上传
                  //   </Text>
                  // </Bubble>
                ),
              },
            ].filter((item) => item)}
          />
          {tooltipMessage ? <Alert type="warning">{tooltipMessage}</Alert> : <TcsDivider />}

          <TcsTabs activeKey={currentTab} onChange={setCurrentTab}>
            <TcsTabs.TabPane key="app" tabKey="app" tab="关联应用清单">
              <AppList
                record={record}
                pagePath={pagePath}
                onReload={(type) => {
                  if (type === '2.0') {
                    setOldUpdateApp({});
                  } else {
                    onReload();
                  }
                }}
                onLoading={onLoading}
                IssueID={issueID}
                isManager={isManager}
                tableDataSource={tableDataSource}
                oldAppList={oldAppList}
                isOnlineType={isOnlineType}
                defaultTabCurrent={['app', 'oldApp'].includes(defaultTabCurrent) ? defaultTabCurrent : 'app'}
              />
            </TcsTabs.TabPane>
            {isOnlineType && (
              <TcsTabs.TabPane key="site" tabKey="site" tab="关联局点清单">
                <SiteList pagePath={pagePath} siteList={siteList} record={record} onReload={() => setUpdateSite({})} />
              </TcsTabs.TabPane>
            )}
            {isOnlineType && (
              <TcsTabs.TabPane key="dawn" tabKey="dawn" tab={operationTitle}>
                <OperationSheet
                  record={record}
                  onReload={(type) => {
                    if (type === 'dawn') {
                      onReload();
                    } else if (type === 'kaleido') {
                      setUpdatekaleido({});
                    } else if (type === 'tops') {
                      setUpdateTops({});
                    }
                  }}
                  showTops={showTops}
                  showDawn={showDawn}
                  showKaleido={showKaleido}
                  onReSyncTops={handleReSyncTops}
                  isManager={isManager}
                  hasSolutionVersion={hasSolutionVersion}
                  pagePath={pagePath}
                  kaleido={kaleido}
                  tapdInfo={tapdInfo}
                  Tops={TopsData}
                  OriginTops={OriginTopsData}
                  defaultTabCurrent={
                    ['tops', 'dawn', 'kaleido'].includes(defaultTabCurrent) ? defaultTabCurrent : 'dawn'
                  }
                />
              </TcsTabs.TabPane>
            )}

            <TcsTabs.TabPane key="test" tabKey="test" tab="填写验证结论">
              <VerifyExport
                record={record}
                Tops={TopsData}
                OriginTops={OriginTopsData}
                showKaleido={showKaleido}
                showTops={showTops}
                siteList={siteList}
                showDawn={showDawn}
                onReload={() => onReload()}
              />
            </TcsTabs.TabPane>
          </TcsTabs>
        </TcsCard>
      </TcsSpin>
    </div>
  );
};

export default AccessItmes;
