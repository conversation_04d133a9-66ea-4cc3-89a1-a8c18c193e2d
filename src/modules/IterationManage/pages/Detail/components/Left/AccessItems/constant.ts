export enum SHEET_STATUS {
  New = 'New',
  Orchestrating = 'Orchestrating',
  Orchestrated = 'Orchestrated',
  Unpublished = 'Unpublished',
  Published = 'Published',
  Ready = 'Ready',

  Updating = 'Updating',
  UpdateSucceeded = 'UpdateSucceeded',
  UpdateFailed = 'UpdateFailed',
  UpdateCompleted = 'UpdateCompleted',
  RollingBack = 'RollingBack',
  RollbackSucceeded = 'RollbackSucceeded',
  RollbackFailed = 'RollbackFailed',
  RollbackCompleted = 'RollbackCompleted',

  PendingApproval = 'PendingApproval',
  Executed = 'Executed',
}

export type TextMap<T extends string | number | symbol> = Record<
  T,
  {
    text: string;
    color?: string | null;
    theme?: 'text' | 'label' | 'weak' | 'strong' | 'primary' | 'success' | 'warning' | 'danger' | 'cost';
  }
>;

export const SHEET_STATUS_MAP: TextMap<SHEET_STATUS> = {
  [SHEET_STATUS.New]: {
    text: '待编排',
  },
  [SHEET_STATUS.Orchestrating]: {
    text: '编排中',
  },
  [SHEET_STATUS.Orchestrated]: {
    text: '待发布',
    theme: 'warning',
  },
  [SHEET_STATUS.Published]: {
    text: '已发布',
    theme: 'success',
  },
  [SHEET_STATUS.Updating]: {
    text: '变更中',
  },
  [SHEET_STATUS.UpdateSucceeded]: {
    text: '变更成功，待确认',
    theme: 'warning',
  },
  [SHEET_STATUS.UpdateFailed]: {
    text: '执行失败',
    theme: 'danger',
  },
  [SHEET_STATUS.UpdateCompleted]: {
    text: '变更完成',
    theme: 'success',
  },
  [SHEET_STATUS.RollingBack]: {
    text: '回滚执行中',
  },
  [SHEET_STATUS.RollbackSucceeded]: {
    text: '回滚成功，待确认',
    theme: 'warning',
  },
  [SHEET_STATUS.RollbackFailed]: {
    text: '回滚失败',
    theme: 'danger',
  },
  [SHEET_STATUS.RollbackCompleted]: {
    text: '回滚完成',
  },
  [SHEET_STATUS.Executed]: {
    text: '已触发执行',
  },
  [SHEET_STATUS.PendingApproval]: {
    text: '待审批',
  },
  [SHEET_STATUS.Ready]: {
    text: '待执行',
  },
  [SHEET_STATUS.Unpublished]: {
    text: '待发布',
  },
};
