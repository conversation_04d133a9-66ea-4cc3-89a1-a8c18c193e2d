/*
 * @Author: superfeng
 * @Date: 2023-03-20 11:18:57
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-11-27 10:43:11
 * @Description: 请输入注释信息
 */
import React, { useMemo, useRef, useState } from 'react';

import { useParams } from 'react-router-dom';
import AddRefAppModal from './AddRefAppModal';
import {
  IPropsIssueSolutionRel,
  // recallDeliveryInfo,
} from '@/common/api/interationManage';
import { isAdmin } from '@/common/utils';
import { SOLUTION_ARCH_STATUS } from '@/common/config';
import DeliveryListModal from './DeliveryListModal';
import { IssueTypeConfig } from '@/modules/IterationManage/config';
import UpdateDeliverTypeModal from './UpdateDeliverTypeModal';
import Metadatas from './Metadatas';
import { ActionType, Tcs<PERSON><PERSON>on, T<PERSON><PERSON><PERSON>onG<PERSON>, T<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, T<PERSON><PERSON><PERSON>, TcsTable } from '@tencent/tcs-component';
// import VerifyExportModal from './VerifyExportModal';
import { IterationManage } from '@/common/api/iterationManage.api';
import EditRelSiteModal from './EditRelSiteModal';
import VersionAssessModal from './VersionAssessModal';
import DependAssessModal from './DependAssessModal';
import AccessItmes from './AccessItems';
import { DELIVER_TYPE } from '@/common/constants';
import useLookup from '@/common/hookups/useLookup';
import { ILookup } from '@/common/context';
import { getDeliveryInfosByFromType } from '@/common/api/commonApi';
import { message } from '@tencent/tea-component';
import { DefectManagementRoutePath } from '@/common/routePath';

export interface IProps {
  data: any;
  update?: any;
  tapdId?: string;
}

// const PAGE_SCENE = {
//   defect: '缺陷单',
//   story: '需求单',
// };

function isShowDependAssess(record: any, lookups?: any) {
  const SolutionCode = record?.SolutionVersionDetail?.Solution?.Code;
  const SolutionVersionCode = record?.SolutionVersionDetail?.Code;
  if (!SolutionCode) {
    return false;
  }
  const EvaluateDependencies =
    (lookups.DefectEvaluateDependencies?.find((item) => item.Code === 'EvaluateDependencies')?.Extra as any)
      ?.SolutionVersionList || {};
  if (!EvaluateDependencies[SolutionCode]) {
    return false;
  }
  const dependSolutionVersion = EvaluateDependencies[SolutionCode] || [];
  return !!dependSolutionVersion.includes(SolutionVersionCode);
}

const getFirstColumnsNew = ({
  onViewDelivery,
  // onCompleteChangeOrders,
  onUpdateDeliverType,
  handelViewMateData,
  // onVerifyExport,
  data,
  // isManager,
  hasSolutionVersion,
  pagePath,
  onDependAssess,
  handleDelete,
  lookups,
}: {
  onViewDelivery: (record) => void;
  // onCompleteChangeOrders: (record) => void;
  onUpdateDeliverType: (record) => void;
  handelViewMateData: (record) => void;
  // onVerifyExport: (record) => void;
  data: any;
  isManager: boolean;
  hasSolutionVersion: boolean;
  pagePath: 'story' | 'defect';
  onDependAssess: (record) => void;
  handleDelete: (recore) => void;
  lookups: Record<string, ILookup[]>;
}) =>
  (
    [
      {
        title: '解决方案名称',
        dataIndex: 'SolutionVersionDetail',
        width: '20%',
        render(text, record) {
          return `${record?.SolutionVersionDetail?.Solution?.Name || '-'}(${
            record?.SolutionVersionDetail?.Solution?.NameEN || '-'
          })`;
        },
      },
      {
        title: '解决方案版本',
        dataIndex: 'SolutionVersion',
      },
      {
        title: '架构',
        dataIndex: 'Arch',
        valueType: 'dictSelect',
        fieldProps: {
          dictType: 'PackageArch',
        },
      },
      {
        title: '关联应用个数',
        dataIndex: 'AppNum',
        render(text, record) {
          return record.IssueAppRel?.length || 0;
        },
      },

      data?.Type !== 'Other'
        ? {
            title: '出包类型',
            dataIndex: 'DeliverType',
            valueType: 'dictSelect',
            fieldProps: {
              dictType: 'DeliverType',
            },
          }
        : undefined,
      {
        title: '修复状态',
        dataIndex: 'Status',
        valueType: 'dictSelect',
        fieldProps: {
          dictType: 'IssueSolutionArchStatus',
          showType: 'tag',
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: '20%',
        fixed: 'right',
        valueType: 'option',
        render: (text, record) => {
          let operBtnProps: {
            disabled: boolean;
            tooltip?: string;
          } = {
            disabled: false,
          };
          // if (!isManager) {
          //   operBtnProps = {
          //     disabled: true,
          //     tooltip: `只能由【处理人/研发负责人/测试负责人】进行操作，您不是当前${PAGE_SCENE[pagePath]}的【处理人/研发负责人/测试负责人】`,
          //   };
          // }
          if (pagePath === 'story' && !hasSolutionVersion) {
            operBtnProps = {
              disabled: true,
              tooltip: `当前需求单没有指定解决方案与解决方案版本，请先指定后再进行操作`,
            };
          }

          const dependAssess = {
            disabled: false,
            tooltip: '评估依赖关系操作同解决方案下不区分架构',
          };
          // if (!isManager) {
          //   dependAssess = {
          //     disabled: true,
          //     tooltip: `只能由【处理人/研发负责人/测试负责人】进行操作，您不是当前${PAGE_SCENE[pagePath]}的【处理人/研发负责人/测试负责人】`,
          //   };
          // }

          return (
            <TcsButtonGroup
              items={[
                {
                  text: record.OperationSheetUUID ? '查看变更单' : '完善变更单',
                  hidden: data?.Type === 'Other' || record?.DeliverType === DELIVER_TYPE.NO_NEED,
                  onClick: (e) => {
                    e?.stopPropagation();
                    TcsModal.info({
                      title: '提示',
                      content:
                        '请展开表格行在对应变更单页签进行操作，请注意：需要完善页签中展示的所有变更单内容后，才能进行下一步操作。',
                    });
                  },
                },
                {
                  text: '评估依赖关系',
                  onClick: (e) => {
                    e?.stopPropagation();
                    onDependAssess(record);
                  },
                  tooltip: dependAssess.tooltip,
                  disabled: dependAssess.disabled,
                  hidden: !isShowDependAssess(record, lookups),
                },
                {
                  text: '修改出包类型',
                  hidden: !(
                    ([SOLUTION_ARCH_STATUS.DEVELOPING].includes(record.Status) || record.DeliverType === 'NoNeed') &&
                    data?.Type !== 'Other'
                  ),
                  ...operBtnProps,
                  onClick: (e) => {
                    e?.stopPropagation();
                    onUpdateDeliverType(record);
                  },
                },
                {
                  text: '验证出包',
                  onClick: (e) => {
                    e?.stopPropagation();
                    TcsModal.info({
                      title: '提示',
                      content: '请展开表格行进入验证出包页签进行操作',
                    });
                  },
                },
                {
                  text: '删除',
                  type: 'link',
                  confirm: true,
                  confirmProps: {
                    title: '确定删除',
                    onConfirm: (e) => {
                      e?.stopPropagation();
                      handleDelete(record);
                    },
                  },

                  disabled: record?.IssueAppRel?.length,
                  tooltip: record?.IssueAppRel?.length ? '当前存在关联应用，全部应用解除才可删除' : '',
                },
                {
                  text: '查看关联交付单',
                  hidden: !(record.DeliverType !== DELIVER_TYPE.NO_NEED && data?.Type !== 'Other'),
                  onClick: (e) => {
                    e?.stopPropagation();
                    onViewDelivery(record);
                  },
                },
                {
                  text: '元数据',
                  onClick: (e) => {
                    e?.stopPropagation();
                    handelViewMateData(record);
                  },
                },
                {
                  text: '跳转至Tapd单',
                  type: 'link',
                  onClick: (e) => {
                    e?.stopPropagation();
                    window.open(record?.TapdUrl, '_blank');
                  },
                },
              ]}
            />
          );
        },
      },
    ] as TcsColumns[]
  ).filter((item) => item);

const Access: React.FC<IProps> = ({ data, update, tapdId }) => {
  const [loading, setLoading] = useState(false);

  const [dataSource, setDataSource] = useState<IPropsIssueSolutionRel[]>([]);
  const [visibleAddModal, setVisibleAddModal] = useState(false);
  const [visibleAssessModal, setVisibleAssessModal] = useState(false);
  const [visibleDependAssessModal, setVisibleDependAssessModal] = useState(false);
  const [recordDependAssess, setRecordDependAssess] = useState<any>();
  const deliveryListRef = useRef<any>();
  const updateDeliverTypeRef = useRef<any>();
  const { lookups } = useLookup(['DefectEvaluateDependencies']);

  const metadataRef = useRef<any>();
  // const verifyExportRef = useRef<any>();
  const { pagePath } = useParams<{ pagePath: 'story' | 'defect' }>();
  const actionRef = useRef<ActionType>();
  const selectRelSiteRef = useRef<any>();
  const [editRelSiteModalVisible, setEditRelSiteModalVisible] = useState<boolean>(false);
  // const currentRole = window.jiguang_currentRole;
  // const pmRoles = ['ft_productor', 'ft_pm', 'pm', 'productor'];
  // const isPmRole = pmRoles.includes(currentRole);
  const issueType = IssueTypeConfig[pagePath];

  // 判断是否是源单
  const isCopyTapd = useMemo(() => data?.TapdUrl?.includes(tapdId ?? '') ?? false, [data?.TapdUrl, tapdId]);

  // 判断是否是tce/tcs
  const spaceType = useMemo(() => {
    // 检查 Solution.Code 字段
    if (data?.Solution?.Code === 'tce' || data?.Solution?.Code === 'tcs-paas') {
      return true;
    }

    // 检查 SolutionVersion 字段
    const solutionVersion = data?.SolutionVersion;
    if (solutionVersion && /^(TCS|TCE)\d/.test(solutionVersion)) {
      return true;
    }

    return false;
  }, [data?.Solution?.Code, data?.SolutionVersion]);

  // if the current user is the Development Manager
  const isManager = useMemo(
    () =>
      data?.DevOwners?.includes((window as any).jiguang_username) ||
      data?.Owner?.includes((window as any).jiguang_username) ||
      data?.TestOwners?.includes((window as any).jiguang_username) ||
      isAdmin(),
    [data],
  );

  // 是否有解决方案版本
  const hasSolutionVersion = useMemo(
    () => data?.SolutionVersionUUID && data?.SolutionUUID,
    [data?.SolutionVersionUUID, data?.SolutionUUID],
  );

  const firstColumns = getFirstColumnsNew({
    onViewDelivery: handleViewDelivery,
    onUpdateDeliverType: handleUpdateDeliverType,
    data,
    handelViewMateData,
    isManager,
    hasSolutionVersion,
    pagePath,
    onDependAssess: handleDependAssess,
    handleDelete,
    lookups,
  });

  async function handleRequest(params: any) {
    if (params?.IssueType && params?.IssueID) {
      const result = await IterationManage.ListIssueSolutionRelsWithDetail({
        IssueType: issueType,
        IssueID: data.IssueID,
      });
      setDataSource(result.data || []);
      return result;
    }
    return {
      data: [],
      success: true,
    };
  }
  function handleDelete(record: any) {
    IterationManage.DeleteIssueSolutionRel({
      UUID: record?.UUID,
    }).then(() => {
      actionRef.current?.reload();
    });
  }
  // 通过缺陷单查看关联的交付单
  function handleViewDelivery(record: any) {
    setLoading(true);
    getDeliveryInfosByFromType({
      FromType: 'IssueSolution',
      FromKey: record.UUID,
    })
      .then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          const deliveryList = res.DeliveryDetailInfo || [];
          if (deliveryList.length === 0) {
            message.warning({
              content: '当前缺陷单没有关联交付单',
            });
          } else if (deliveryList.length === 1) {
            // 直接跳转到交付单详情页
            window.open(`${DefectManagementRoutePath.DELIVERY_DETAIL_PAGE}?uuid=${deliveryList[0].UUID}`, '_blank');
          } else {
            deliveryListRef.current.show(deliveryList);
          }
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }

  function handleUpdateDeliverType(record: IPropsIssueSolutionRel) {
    updateDeliverTypeRef?.current?.show(record);
  }
  function handelViewMateData(record) {
    metadataRef.current.show(record);
  }

  function handleAddApp() {
    setVisibleAddModal(true);
  }

  function handleAddSuccess() {
    setVisibleAddModal(false);
    actionRef.current?.reload();
  }

  function handleAddCancel() {
    setVisibleAddModal(false);
  }

  function handleUpdateDeliverSuccess() {
    actionRef.current?.reload();
  }

  function handleBatchEditSite() {
    selectRelSiteRef.current?.getData();

    setEditRelSiteModalVisible(true);
  }
  function handleEditSiteSuccess() {
    setEditRelSiteModalVisible(false);
    actionRef.current?.reload();
  }
  function handleVerisonAssess() {
    setVisibleAssessModal(true);
  }
  function handleAssessCancel() {
    setVisibleAssessModal(false);
  }
  function handleDependAssess(record) {
    setRecordDependAssess(record);
    setVisibleDependAssessModal(true);
  }

  const addAppButtonPermission = useMemo(() => {
    // if (!isManager) {
    //   return {
    //     disabled: true,
    //     tooltip: `只能由【处理人/研发负责人/测试负责人】进行操作，您不是当前${PAGE_SCENE[pagePath]}的【处理人/研发负责人/测试负责人】`,
    //   };
    // }
    if (pagePath === 'story' && !hasSolutionVersion) {
      return {
        disabled: true,
        tooltip: `当前需求单没有指定解决方案与解决方案版本，请先指定后再进行操作`,
      };
    }

    return {
      disabled: false,
      tooltip: undefined,
    };
  }, [hasSolutionVersion, pagePath]);

  const assesButtonPermission = useMemo(() => {
    // if (!isPmRole) {
    //   return {
    //     disabled: true,
    //     tooltip: `只能由【FT产品经理/FT_PM/TCE_PM/专有云中心产品经理】进行操作`,
    //   };
    // }
    if (!hasSolutionVersion) {
      return {
        disabled: true,
        tooltip: `当前需求单没有指定解决方案与解决方案版本，请先指定后再进行操作`,
      };
    }
    if (!isCopyTapd) {
      return {
        disabled: true,
        tooltip: `当前需求单为复制单，不支持操作`,
      };
    }

    return {
      disabled: false,
      tooltip: undefined,
    };
  }, [hasSolutionVersion, isCopyTapd]);

  const params = useMemo(
    () => ({
      IssueType: issueType,
      IssueID: data?.IssueID,
      update,
    }),
    [issueType, data?.IssueID, update],
  );

  return (
    <TcsSpin spinning={loading}>
      <TcsTable
        headerTitle={
          <>
            <TcsButton
              type="primary"
              onClick={handleAddApp}
              // disabled={addAppButtonPermission.disabled}
              // tooltip={addAppButtonPermission.tooltip}
            >
              新增关联应用
            </TcsButton>
            {data?.Type !== 'Other' && (
              <TcsButton
                type="primary"
                onClick={handleBatchEditSite}
                disabled={dataSource?.length === 0 || addAppButtonPermission.disabled}
                tooltip={dataSource?.length === 0 ? '请选择要批量修改的记录' : addAppButtonPermission.tooltip}
              >
                批量修改出包局点
              </TcsButton>
            )}
            {pagePath === 'story' && spaceType && (
              <TcsButton
                type="primary"
                disabled={assesButtonPermission.disabled}
                tooltip={assesButtonPermission.tooltip}
                onClick={handleVerisonAssess}
              >
                回合版本评估
              </TcsButton>
            )}
          </>
        }
        actionRef={actionRef}
        columns={firstColumns}
        columnsState={{
          persistenceKey: 'defect_manage/iteration_manage/defect/detail/access/first',
        }}
        scroll={{
          x: 1300,
        }}
        request={handleRequest}
        params={params}
        expandable={{
          expandRowByClick: true,
          expandedRowRender(record) {
            return (
              <AccessItmes
                pagePath={pagePath}
                record={record}
                issueID={data?.IssueID}
                issueType={data?.Type}
                showDependAssess={isShowDependAssess(record, lookups)}
                onReload={() => {
                  actionRef.current?.reload();
                }}
                isManager={isManager}
                hasSolutionVersion={hasSolutionVersion}
                onLoading={(loading) => setLoading(loading)}
                tableDataSource={dataSource}
              />
            );
          },
        }}
      />
      {visibleAssessModal && <VersionAssessModal onClose={handleAssessCancel} info={data} />}
      {visibleAddModal && (
        <AddRefAppModal
          onConfirm={handleAddSuccess}
          onCancel={handleAddCancel}
          issueID={data?.IssueID}
          tapdUrl={data?.TapdUrl}
          defectType={data?.Type}
          issueSolutions={dataSource}
          issueInfo={data}
        />
      )}
      <DeliveryListModal ref={deliveryListRef} />
      <UpdateDeliverTypeModal ref={updateDeliverTypeRef} onConfirm={handleUpdateDeliverSuccess} />
      <Metadatas ref={metadataRef} />

      <EditRelSiteModal
        visible={editRelSiteModalVisible}
        issueSolutions={dataSource}
        onClose={() => setEditRelSiteModalVisible(false)}
        batchEdit={true}
        onConfirm={handleEditSiteSuccess}
      />
      {visibleDependAssessModal && (
        <DependAssessModal
          issueID={data?.IssueID}
          record={recordDependAssess}
          onClose={() => setVisibleDependAssessModal(false)}
          onSuccess={() => {
            setVisibleDependAssessModal(false);
            actionRef.current?.reload();
          }}
        />
      )}
    </TcsSpin>
  );
};

export default Access;
