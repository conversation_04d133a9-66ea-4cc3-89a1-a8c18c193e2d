/*
 * @Author: superfeng
 * @Date: 2023-05-12 15:08:26
 * @LastEditors: superfeng
 * @LastEditTime: 2023-06-27 11:39:27
 * @Description: 请输入注释信息
 */
import { H4, List, MediaObject } from '@tencent/tea-component';
import React, { useState } from 'react';
import { IIssueComment } from '@/common/api/interationManage';
import ExEditor from '@/common/components/ExEditor';
// import dayjs from 'dayjs';
import styles from './CommentItem.module.less';

export interface IProps {
  comment: IIssueComment;
  parentCommentUUID?: string;
  onEdit: (comment: IIssueComment) => void;
  onRemove: (comment: IIssueComment) => void;
  onReply: (comment: IIssueComment, parentCommentUUID?: string) => void;
}

const CommentItem: React.FC<IProps> = ({ comment, onEdit, onRemove, onReply }) => {
  // const createdAt = useMemo(() => dayjs(comment.CreatedAt).format('YYYY-MM-DD HH:mm:ss'), [comment.CreatedAt]);

  const [src, setSrc] = useState(`https://r.hrc.woa.com/photo/150/${comment.Creator}.png`);

  function handleImageError() {
    setSrc('//jiguang.woa.com/static/img/logo_favicon.ico');
  }

  return (
    <List.Item>
      <MediaObject
        align="top"
        media={<img className={styles.comment_item__img} src={src} onError={handleImageError} />}
      >
        <H4>{comment.Creator}</H4>
        <p className={styles.comment_item__content}>
          <ExEditor minHeight={40} readonly value={comment.Description} />
        </p>
        {/* <div className={styles.comment_item__footer}>
          <Text>{createdAt}</Text>
          <span>
            <Button type="text" onClick={() => onEdit(comment)}>
              <Icon type="pencil" />
              编辑
            </Button>
            <PopConfirm
              title="确认要删除评论？"
              footer={(close) => (
                <>
                  <Button
                    type="link"
                    onClick={() => {
                      close();
                      onRemove(comment);
                    }}
                  >
                    删除
                  </Button>
                  <Button
                    type="text"
                    onClick={() => {
                      close();
                    }}
                  >
                    取消
                  </Button>
                </>
              )}
            >
              <Button type="text">
                <Icon type="delete" />
                删除
              </Button>
            </PopConfirm>

            <Button type="text" onClick={() => onReply(comment, parentCommentUUID)}>
              <Icon type="wechat" />
              回复
            </Button>
          </span>
        </div> */}
        {comment.children?.length ? (
          <div className={styles.comment_item__reply}>
            <List>
              {comment.children.map((child) => (
                <CommentItem
                  comment={child}
                  key={child.UUID}
                  onEdit={onEdit}
                  onRemove={onRemove}
                  onReply={onReply}
                  parentCommentUUID={comment.UUID}
                />
              ))}
            </List>
          </div>
        ) : undefined}
      </MediaObject>
    </List.Item>
  );
};

export default CommentItem;
