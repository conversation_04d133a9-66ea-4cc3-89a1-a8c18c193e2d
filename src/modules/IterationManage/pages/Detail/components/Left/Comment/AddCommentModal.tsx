/*
 * @Author: superfeng
 * @Date: 2023-05-12 15:08:59
 * @LastEditors: superfeng
 * @LastEditTime: 2023-05-15 15:49:53
 * @Description: 请输入注释信息
 */
import { Button, Modal, message } from '@tencent/tea-component';
import React, { useEffect, useMemo, useState } from 'react';
import { IIssueComment, createIssueComment, updateIssueComment } from '@/common/api/interationManage';
import ExEditor from '@/common/components/ExEditor';

export interface IProps {
  visible: boolean;
  onCancel: () => void;
  onConfirm: () => void;
  // 如果编辑的话，传入编辑的comment
  comment?: IIssueComment;
  // 缺陷单ID
  issueId: string;
  // 如果是回复评论，则需要传评论的uuid
  parentCommentUUID?: string;
}

const AddCommentModal: React.FC<IProps> = ({ visible, onCancel, onConfirm, comment, issueId, parentCommentUUID }) => {
  // 评论内容
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(false);

  // 回填评论内容
  useEffect(() => {
    if (visible && comment) {
      setDescription(comment.Description || '');
    }
  }, [visible, comment]);

  const caption = useMemo(() => {
    if (comment) {
      return '编辑评论';
    }
    return parentCommentUUID ? '回复评论' : '新增评论';
  }, [comment, parentCommentUUID]);

  const handleConfirm = () => {
    let promise: Promise<IResponseResult<object>> = undefined;
    if (comment) {
      promise = updateIssueComment({
        CommentID: comment.ID,
        Description: description,
      });
    } else {
      promise = createIssueComment({
        IssueID: issueId,
        IsTop: !parentCommentUUID,
        Description: description,
        ParentCommentUUID: parentCommentUUID,
      });
    }
    setLoading(true);
    promise
      .then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          message.success({
            content: `${caption}成功`,
          });
          onConfirm();
          setDescription('');
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleCancel = () => {
    onCancel();
    setDescription('');
  };

  const handleChange = (value: string) => {
    setDescription(value);
  };

  return (
    <Modal caption={caption} visible={visible} onClose={handleCancel} size={1250}>
      <Modal.Body>
        <ExEditor value={description} onChange={handleChange} minHeight={400} />
      </Modal.Body>
      <Modal.Footer>
        <Button type="primary" onClick={handleConfirm} loading={loading}>
          确定
        </Button>
        <Button type="weak" onClick={handleCancel}>
          取消
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default AddCommentModal;
