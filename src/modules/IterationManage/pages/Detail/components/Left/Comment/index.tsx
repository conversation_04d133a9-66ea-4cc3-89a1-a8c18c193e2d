/*
 * @Author: superfeng
 * @Date: 2023-05-12 14:51:07
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-03-17 14:35:36
 * @Description: 请输入注释信息
 */
import { listIssueComments, IIssueComment, deleteIssueComment } from '@/common/api/interationManage';
import { Card, List, message } from '@tencent/tea-component';
import React, { useEffect, useMemo, useState } from 'react';
import AddCommentModal from './AddCommentModal';
import CommentItem from './CommentItem';
import Loading from '@/common/components/Loading';

export interface IProps {
  issueId?: string;
}

const Comment: React.FC<IProps> = ({ issueId }) => {
  const [commentList, setCommentList] = useState<IIssueComment[]>([]);
  const [update, setUpdate] = useState({});
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [parentCommentUUID, setParentCommentUUID] = useState('');
  const [editComment, setEditComment] = useState<IIssueComment>(null);

  useEffect(() => {
    if (issueId) {
      setLoading(true);
      listIssueComments({
        IssueID: issueId,
      })
        .then((res) => {
          if (res.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            setCommentList(res.ListIssueComments || []);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [issueId, update]);

  function handleCancel() {
    setVisible(false);
    setEditComment(undefined);
    setParentCommentUUID('');
  }

  function handleConfirm() {
    handleCancel();
    setUpdate({});
  }

  const renderCommentList = useMemo(() => {
    const topCommentList: (IIssueComment & { children?: IIssueComment[] })[] = [];
    commentList.forEach((item) => {
      if (item.IsTop) {
        const children = commentList.filter((child) => child.ParentCommentUUID === item.UUID);
        topCommentList.push({
          ...item,
          children,
        });
      }
    });
    return topCommentList;
  }, [commentList]);

  function handleRemove(comment: IIssueComment) {
    setLoading(true);
    deleteIssueComment({
      CommentID: comment.ID,
    })
      .then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          setUpdate({});
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }

  function handleEdit(comment: IIssueComment) {
    setEditComment(comment);
    setVisible(true);
  }

  function handleReply(comment: IIssueComment, parentCommentUUID?: string) {
    // 如果是回复二级评论，则使用顶层评论的Uuid，否则直接使用当前评论的UUID
    setParentCommentUUID(parentCommentUUID ?? comment.UUID);
    setVisible(true);
  }
  return (
    <Loading loading={loading}>
      <Card bordered style={{ marginTop: 20 }}>
        <Card.Header>
          <h3 style={{ padding: 10, margin: 0 }}>
            <span style={{ marginRight: 10 }}>评论</span>
            {/* <Button onClick={() => setVisible(true)} type="text">
              <div>
                <Icon type="plus" />
                添加
              </div>
            </Button> */}
          </h3>
        </Card.Header>
        <Card.Body>
          <List>
            {renderCommentList.map((item) => (
              <CommentItem
                comment={item}
                key={item.UUID}
                onRemove={handleRemove}
                onEdit={handleEdit}
                onReply={handleReply}
              />
            ))}
          </List>
        </Card.Body>
      </Card>
      <AddCommentModal
        visible={visible}
        issueId={issueId}
        onCancel={handleCancel}
        onConfirm={handleConfirm}
        comment={editComment}
        parentCommentUUID={parentCommentUUID}
      />
    </Loading>
  );
};

export default Comment;
