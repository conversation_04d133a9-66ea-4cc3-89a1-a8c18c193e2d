import React from 'react';
import { TcsDescriptions, TcsTable, TcsButton, TcsColumns, TcsCard } from '@tencent/tcs-component';
import { IssueSolutionRelsWithDetail } from '@/common/api/iterationManage.api';

export interface IProps {
  currentRecord?: IssueSolutionRelsWithDetail;
  otherRecordList?: Array<IssueSolutionRelsWithDetail>;
}

export default function BasicInfo({ currentRecord, otherRecordList }: IProps) {
  const columns = [
    {
      title: '版本',
      dataIndex: 'SolutionVersion',
      width: '30%',
    },
    {
      title: '架构',
      dataIndex: 'Arch',
      width: '20%',
    },
    {
      title: 'TAPD单链接',
      dataIndex: 'TapdUrl',
      width: '50%',
      linkable: true,
    },
  ] as TcsColumns[];

  return (
    <div>
      <TcsDescriptions title="基础信息">
        <TcsDescriptions.Item label="解决方案版本">{currentRecord?.SolutionVersion || '-'}</TcsDescriptions.Item>
        <TcsDescriptions.Item label="应用架构">{currentRecord?.Arch || '-'}</TcsDescriptions.Item>
        <TcsDescriptions.Item label="涉及应用数量">{currentRecord?.IssueAppRel.length}</TcsDescriptions.Item>
        {/* <TcsDescriptions.Item label="是否需要更新产品模型">是</TcsDescriptions.Item> */}
        {/* <TcsDescriptions.Item label="更新后的解决方案快照号">-</TcsDescriptions.Item> */}
        {/* <TcsDescriptions.Item label="是否依赖其他dbsql应用">否</TcsDescriptions.Item> */}
        {/* <TcsDescriptions.Item label="版本修复局点策略">需要按局点出包</TcsDescriptions.Item> */}
      </TcsDescriptions>

      <TcsCard title="关联复制单信息">
        <TcsTable
          columns={columns}
          headerTitle={<TcsButton type="primary">新增复制单</TcsButton>}
          dataSource={otherRecordList}
        />
      </TcsCard>
    </div>
  );
}
