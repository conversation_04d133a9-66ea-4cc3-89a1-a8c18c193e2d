import { ProColumns } from '@tencent/tcs-component';
export const getColumns = ({ target }): ProColumns[] => {
  // 是否是我依赖谁
  const isDepend = target === 'upstream';
  return [
    {
      title: '应用',
      dataIndex: isDepend ? 'DependentApplication' : 'Application',
      width: '20%',
      search: false,
    },
    {
      title: '产品名称',
      dataIndex: isDepend ? 'DependentProductName' : 'ProductName',
      width: '20%',
      search: false,
    },
    {
      title: '产品版本',
      dataIndex: isDepend ? 'DependentProductVersion' : 'ProductVersion',
      width: '20%',
      search: false,
    },
    {
      title: '应用版本',
      dataIndex: isDepend ? 'DependentApplicationBranch' : 'SolutionVersion',
      width: '20%',
      search: false,
    },
    {
      title: '创建时间',
      dataIndex: 'CreatedAt',
      width: '20%',
      search: false,
      valueType: 'dateTime',
    },
  ];
};
