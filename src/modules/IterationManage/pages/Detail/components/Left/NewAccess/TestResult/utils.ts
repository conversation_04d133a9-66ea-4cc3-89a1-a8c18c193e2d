/*
 * @Author: lucyfang
 * @Date: 2025-03-04 18:24:51
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-03-05 13:49:47
 * @Description: 请输入注释信息
 */
// 转换函数
import { reverseToolNameMap } from '../../../../../../config';
export const transformFormData = (formData) => {
  const sheetResults = Object.entries(formData?.tools || {}).map(([toolKey, toolData]) => {
    return {
      OperationTool: reverseToolNameMap?.[toolKey] || toolKey, // 转换为中文工具名
      Update: {
        Conclusion: toolData?.update?.conclusion || '',
        Remark: toolData?.update?.remark || '',
      },
      Rollback: {
        Conclusion: toolData?.rollback?.conclusion || '',
        Remark: toolData?.rollback?.remark || '',
      },
      Conclusion: toolData?.conclusion || '', // 对应 tools.conclusion
    };
  });

  // 处理测试结论
  return {
    SheetResults: sheetResults,
    Conclusion: formData.test.conclusion, // 对应test.conclusion
    ReasonClass: formData.test.reasonClass, // 对应test.reasonClass
    Remark: formData.test.remark, // 对应test.remark
  };
};
