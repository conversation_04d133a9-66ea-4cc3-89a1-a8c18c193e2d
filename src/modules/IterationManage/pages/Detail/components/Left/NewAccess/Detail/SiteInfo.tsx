import React, { useState, useEffect } from 'react';
import { TcsTable, TcsColumns } from '@tencent/tcs-component';
import { IterationManage, IssueSolutionRelsWithDetail } from '@/common/api/iterationManage.api';

const renderPackageApplicationArch = (text: string) => {
  if (!text) return '-';
  return text === '1' ? '单架构' : '多架构';
};

interface SiteInfoProps {
  record?: IssueSolutionRelsWithDetail;
}

const SiteInfo: React.FC<SiteInfoProps> = ({ record }) => {
  const [siteList, setSiteList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const getColumns = () =>
    [
      {
        dataIndex: 'ClientName',
        title: '客户名称',
        width: '20%',
      },
      {
        dataIndex: 'SiteName',
        title: '局点名称',
        width: '20%',
      },
      {
        dataIndex: 'SiteType',
        title: '局点类型',
        width: '10%',
      },
      {
        dataIndex: 'PackageApplicationArch',
        title: '出包应用架构',
        width: '10%',
        render: renderPackageApplicationArch,
      },
      {
        dataIndex: 'PackageStatus',
        title: '出包状态',
        width: '15%',
        valueType: 'dictSelect',
        fieldProps: {
          dictType: 'DeliveryStatus',
          showType: 'tag',
        },
      },
      {
        dataIndex: 'BugfixType',
        title: '缺陷修复方式',
        width: '13%',
      },
      {
        dataIndex: 'OperationTool',
        title: '变更工具',
        width: '14%',
      },
    ] as TcsColumns[];

  useEffect(() => {
    if (record?.UUID) {
      setLoading(true);
      IterationManage.GetTAPDDeliverySites({
        IssueSolutionRelUUID: record.UUID,
      })
        .then((res) => {
          setSiteList(res.Sites || []);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [record?.UUID]);

  return (
    <TcsTable
      columns={getColumns()}
      dataSource={siteList}
      rowKey="SiteUUID"
      loading={loading}
      pagination={{}}
      options={false}
      bordered
    />
  );
};

export default SiteInfo;
