/*
 * @Author: lucyfang
 * @Date: 2025-02-13 17:24:01
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-04-16 19:13:53
 * @Description: 请输入注释信息
 */
import { TcsTabs } from '@tencent/tcs-component';
import React, { useEffect, useState } from 'react';
import Dawn from './Dawn';
import Kaleido from './Kaleido';
import { GetTAPDOperationSheet, IterationManage } from '@/common/api/iterationManage.api';
import Tops from './Tops';
import { ListPackageVerifications } from '@/common/api/interationManage';
import { message } from '@tencent/tea-component';
import { useParams } from 'react-router-dom';
export interface IProps {
  record: any;
  Tops?: GetTAPDOperationSheet;
  OriginTops?: GetTAPDOperationSheet;
  showTops?: boolean;
  showKaleido?: boolean;
  showDawn?: boolean;
}
const VerifyExportContent: React.FC<IProps> = ({
  record,
  Tops: TopsData,
  OriginTops: OriginTopsData,
  showTops,
  showKaleido,
  showDawn,
}) => {
  const { pagePath } = useParams<{ pagePath: 'story' | 'defect' }>();
  const [data, setData] = useState<any[]>([]);
  const fetchData = async () => {
    try {
      const res = await ListPackageVerifications.requestData({
        SourceKey: record.UUID,
      });
      if (res.Error) {
        message.error({
          content: res.Error.Message,
        });
        return;
      }
      const verifications = res?.ListPackageVerifications || [];
      if (verifications.length === 0) {
        return;
      }
      const siteUUIDs = Array.from(new Set(verifications.map((item) => item.SiteUUID)));

      const operationToolsRes = await IterationManage.ListSiteVariableDictionaries({
        SiteUUID: siteUUIDs,
        Key: 'OperationTool',
      });

      const operationTools = operationToolsRes?.ListSiteVariableDictionaries || [];
      const operationToolsMap = operationTools.reduce((acc, curr) => {
        acc[curr.SiteUUID] = curr.Value;
        return acc;
      }, {});

      setData(
        verifications.map((item) => ({
          ...item,
          OperationTool: operationToolsMap[item.SiteUUID] || '产品市场',
        })),
      );
    } catch (error) {
      message.error({ content: error });
    }
  };

  useEffect(() => {
    fetchData();
  }, [record.UUID]);
  const DawnEl = (
    <Dawn record={record} data={data?.filter((item) => item?.OperationTool === '产品市场')} onRefresh={fetchData} />
  );

  const TopsEl = (
    <Tops
      onRefresh={fetchData}
      Tops={TopsData}
      OriginTops={OriginTopsData}
      record={record}
      data={data?.filter((item) => item?.OperationTool === '产品运维中心') || []}
    />
  );

  const KaleidoEl = <Kaleido />;

  const operationList: Array<[React.JSX.Element, string, string]> = [];
  if (showDawn) {
    operationList.push([DawnEl, 'dawn', '产品市场']);
  }
  if (showTops) {
    // 需求单，如果有产品运维中心但没有产品市场，也需要显示产品市场变更单，因为需要先填写产品市场变更单再转换为产品运维中心变更单
    if (!showDawn && pagePath === 'story') {
      operationList.push([DawnEl, 'dawn', '产品市场']);
    }
    operationList.push([TopsEl, 'tops', '产品运维中心']);
  }

  if (showKaleido) {
    operationList.push([KaleidoEl, 'kaleido', 'Kaleido']);
  }

  if (operationList.length === 0) {
    return DawnEl;
  }
  if (operationList.length === 1) {
    return operationList[0][0];
  }
  return (
    <TcsTabs>
      {operationList.map(([el, key, tab]) => (
        <TcsTabs.TabPane tab={tab} key={key} tabKey={key}>
          {el}
        </TcsTabs.TabPane>
      ))}
      {/* <TcsTabs.TabPane tab="产品市场" key="dawn" tabKey="dawn">
        <Dawn
          record={record}
          data={data?.filter((item) => item?.OperationTool === '产品市场') || []}
          onRefresh={fetchData}
        />
      </TcsTabs.TabPane>
      {showTops ? (
        <TcsTabs.TabPane tab="产品运维中心" key="tops" tabKey="tops">
          <Tops
            onRefresh={fetchData}
            Tops={TopsData}
            OriginTops={OriginTopsData}
            record={record}
            data={data?.filter((item) => item?.OperationTool === '产品运维中心') || []}
          />
        </TcsTabs.TabPane>
      ) : undefined}
      {showKaleido ? (
        <TcsTabs.TabPane tab="Kaleido" key="kaleido" tabKey="kaleido">
          <Kaleido />
        </TcsTabs.TabPane>
      ) : undefined} */}
    </TcsTabs>
  );
};

export default VerifyExportContent;
