import { TcsModal, TcsTable } from '@tencent/tcs-component';
import { Button, TagSelect, message, Col, StatusTip, Alert } from '@tencent/tea-component';
import React, { useState, useEffect, useRef, useMemo } from 'react';
import { ProForm } from '@/common/components';
import { listAllApplicationNames, GeneratedApi, getAppSolutionProductRel } from '@/common/api/interationManage';
import { getUrlParams } from '@/common/utils';
import { TCE_VERSION_ORDER, TCS_VERSION_ORDER } from '../../../VersionAssessModal/config';
import { getColumns } from './config';

interface AssessFixVersionModalProps {
  visible: boolean;
  onClose: () => void;
  onConfirm?: (selectedApps: string[]) => void;
  tapdInfo?: any;
}

const AssessFixVersionModal: React.FC<AssessFixVersionModalProps> = ({ visible, onClose, onConfirm, tapdInfo }) => {
  const [applicationNames, setApplicationNames] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [appOptions, setAppOptions] = useState<Array<{ value: string; text: string; componentName?: string }>>([]);
  const [tableData, setTableData] = useState<any[]>([]);
  const [selectedVersions, setSelectedVersions] = useState<any[]>([]); // 选中的版本数据
  const [selectedArchs, setSelectedArchs] = useState<Record<string, string[]>>({}); // 每个版本选中的架构
  const formRef = useRef<any>(null);
  const [selectLoading, setSelectLoading] = useState(false);
  const urlParams = getUrlParams();
  // issueType: bug | story
  const isIssueBug = urlParams.obj_type === 'bug';
  console.log('tapdInfo', tapdInfo);

  // 版本比较函数
  const compareVersions = useMemo(
    () =>
      (version1: string, version2: string): number => {
        // 判断版本类型
        const isTCE1 = version1.startsWith('TCE');
        const isTCE2 = version2.startsWith('TCE');
        const isTCS1 = version1.startsWith('TCS');
        const isTCS2 = version2.startsWith('TCS');

        // 如果类型不同，TCE > TCS
        if (isTCE1 && isTCS2) return 1;
        if (isTCS1 && isTCE2) return -1;

        // 同类型比较
        if (isTCE1 && isTCE2) {
          const index1 = TCE_VERSION_ORDER.indexOf(version1);
          const index2 = TCE_VERSION_ORDER.indexOf(version2);
          if (index1 === -1 && index2 === -1) return 0;
          if (index1 === -1) return -1;
          if (index2 === -1) return 1;
          return index1 - index2;
        }

        if (isTCS1 && isTCS2) {
          const index1 = TCS_VERSION_ORDER.indexOf(version1);
          const index2 = TCS_VERSION_ORDER.indexOf(version2);
          if (index1 === -1 && index2 === -1) return 0;
          if (index1 === -1) return -1;
          if (index2 === -1) return 1;
          return index1 - index2;
        }

        return 0;
      },
    [],
  );

  // 获取当前单子的版本
  const currentVersion = tapdInfo?.SolutionVersion || '';

  // 判断版本是否应该默认选中且不可取消
  const shouldBeDefaultSelected = useMemo(
    () =>
      (version: string): boolean => {
        if (!currentVersion) return false;
        return compareVersions(version, currentVersion) >= 0;
      },
    [currentVersion, compareVersions],
  );

  // 处理架构选择变化
  const handleArchChange = (version: string, selectedArchsList: string[]) => {
    // 只有当版本被选中时才允许修改架构选择
    const isVersionSelected = selectedVersions.some((v) => v.SolutionVersionName === version);

    if (isVersionSelected) {
      setSelectedArchs((prev) => ({
        ...prev,
        [version]: selectedArchsList,
      }));
    }
  };

  // 初始化默认选中的架构和版本
  const initializeDefaultArchs = (data: any[]) => {
    const newSelectedArchs: Record<string, string[]> = {};
    const defaultSelectedVersions: any[] = [];

    data.forEach((item) => {
      const versionName = item.SolutionVersionName;
      const availableArchs = item.Archs || [];

      if (shouldBeDefaultSelected(versionName)) {
        // 默认选中且不可取消的版本，选中所有架构
        newSelectedArchs[versionName] = [...availableArchs];
        defaultSelectedVersions.push(item);
      } else {
        // 其他版本，不默认选中任何架构
        newSelectedArchs[versionName] = [];
      }
    });

    setSelectedArchs(newSelectedArchs);
    setSelectedVersions(defaultSelectedVersions);
  };

  const fetchAppList = async () => {
    setSelectLoading(true);
    setLoading(true);
    try {
      // 获取URL参数，判断当前空间

      if (isIssueBug) {
        // 缺陷 ListBugfixApplications 接口
        const res = await GeneratedApi.ListBugfixApplications({
          TapdWorkspaceID: urlParams.workspace_id,
        });
        if (res?.Applications?.length) {
          // 生成 options：value 取 ApplicationName，text 取 ApplicationName (ComponentName)，同时存储 componentName
          const options = (res.Applications || []).map((app: any) => ({
            value: app.ApplicationName,
            text: app.ComponentName ? `${app.ApplicationName} (${app.ComponentName})` : app.ApplicationName,
            componentName: app.ComponentName || '',
          }));
          setAppOptions(options);
          setApplicationNames(options.map((item) => item.value));
        } else {
          message.error({
            content: '获取应用列表失败',
          });
        }
      } else {
        // 其他使用 listAllApplicationNames 接口
        const res = await listAllApplicationNames();
        if (res.Error) {
          message.error({
            content: res.Error.Message || '获取应用列表失败',
          });
        } else {
          // 生成 options：value 和 text 都取 ApplicationName，componentName 为空字符串
          const options = (res.ApplicationNames || []).map((name: string) => ({
            value: name,
            text: name,
            componentName: '',
          }));
          setAppOptions(options);
          setApplicationNames(options.map((item) => item.value));
        }
      }
    } catch (error) {
      console.error('获取应用列表失败:', error);
      message.error({
        content: '获取应用列表失败',
      });
    } finally {
      setSelectLoading(false);
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible) {
      fetchAppList();
    }
  }, [visible]);

  // 获取解决方案版本数据的函数
  const fetchSolutionVersionData = async (apps: string[]) => {
    try {
      setLoading(true);
      // 缺陷类型为 bug 时，使用 ListBugfixApplications 接口
      if (isIssueBug) {
        try {
          // 处理所有应用名称，准备一次性请求
          const applications = apps.map((appName: string) => {
            // 在现网空间中，从appOptions中查找对应的组件名
            const appOption = appOptions.find((option) => option.value === appName);
            // 使用可选链表达式获取组件名，如果不存在则使用空字符串
            const componentName = appOption?.componentName || '';

            return {
              ApplicationName: appName,
              ComponentName: componentName,
            };
          });

          // 一次请求获取所有应用的解决方案版本数据
          const res = await GeneratedApi.ListSolutionVersionArchsForApp({
            ApplicationList: applications,
            TAPDWorkspaceID: urlParams.workspace_id,
          });
          if (res?.SolutionVersionArchs?.length) {
            // 按照SolutionVersion分组，合并相同版本的记录
            const groupedData: Record<string, any> = {};
            res.SolutionVersionArchs.forEach((item: any) => {
              const solutionVersion = item.SolutionVersion;
              if (!solutionVersion) {
                return; // 跳过没有SolutionVersion的记录
              }
              // 如果这个SolutionVersion还没有记录，创建一个新记录
              if (!groupedData[solutionVersion]) {
                groupedData[solutionVersion] = {
                  SolutionVersionUUID: item.SolutionVersionUUID || '', // 保留UUID字段，即使为空
                  SolutionVersionName: item.SolutionVersion,
                  Archs: [],
                };
              }
              // 添加架构信息
              if (item.Arch && !groupedData[solutionVersion].Archs.includes(item.Arch)) {
                groupedData[solutionVersion].Archs.push(item.Arch);
              }
            });
            const data = Object.values(groupedData);
            setTableData(data);
            initializeDefaultArchs(data);
          } else {
            setTableData([]);
          }
        } catch (error) {
          message.error({
            content: '获取解决方案版本失败',
          });
          setTableData([]);
        }
      } else {
        // 其他空间使用 getAppSolutionProductRel 接口
        try {
          // 调用接口获取解决方案版本数据
          const res = await getAppSolutionProductRel({
            ApplicationNames: apps,
            SolutionVersionUUID: tapdInfo?.SolutionVersionUUID || '',
          });

          if (res?.SolutionProductAppRels?.length) {
            // 处理API返回的数据，转换为表格需要的格式
            const data = res.SolutionProductAppRels.map((item: any) => ({
              SolutionVersionUUID: item.SolutionVersionUUID,
              SolutionVersionName: item.SolutionVersionName,
              Archs: item.Archs,
            }));
            setTableData(data);
            initializeDefaultArchs(data);
          } else {
            // 使用模拟数据进行测试
            const mockSolutionVersionArchs = [
              {
                ApplicationBranchName: 'tce3.10.0',
                ApplicationBranchUUID: '049c7c8c6b3e43bdbd2c187f66a82ac1',
                ApplicationName: 'product-cbs-common',
                Archs: ['rhel.arm64', 'rhel.amd64'],
                DeletedAt: 0,
                ID: 43608,
                ProductBranch: 'baseline',
                ProductCode: 'cbs',
                ProductName: '云硬盘（CBS）',
                ProductSubSystemCode: 'cell',
                ProductSubSystemName: 'cell',
                ProductSubSystemUUID: 'c2475c07d61a444597ef90e0ed0fa8fd',
                ProductVersionName: '3.10.0',
                ProductVersionUUID: 'fb5f77d1871b4736bcd55110839d4a1b',
                SolutionBranch: 'master',
                SolutionName: '腾讯专有云解决方案',
                SolutionVersionName: 'TCE3.10.0',
                SolutionVersionUUID: '3c2f24ca08a04671a418ac63fe5b9b2c',
              },
              {
                ApplicationBranchName: 'tce3.10.0',
                ApplicationBranchUUID: '049c7c8c6b3e43bdbd2c187f66a82ac1',
                ApplicationName: 'product-cbs-common',
                Archs: ['rhel.arm64', 'rhel.amd64'],
                DeletedAt: 0,
                ID: 18324,
                ProductBranch: 'master',
                ProductCode: 'cbs',
                ProductName: '云硬盘（CBS）',
                ProductSubSystemCode: 'cell',
                ProductSubSystemName: 'cell',
                ProductSubSystemUUID: 'c2475c07d61a444597ef90e0ed0fa8fd',
                ProductVersionName: '3.10.1',
                ProductVersionUUID: 'fb5f77d1871b4736bcd55110839d4a1b',
                SolutionBranch: 'master',
                SolutionName: '中兴实验室',
                SolutionVersionName: '1.0',
                SolutionVersionUUID: '06971213fbc340b0b323f65964da6bbd',
              },
            ];

            const data = mockSolutionVersionArchs.map((item: any) => ({
              SolutionVersionUUID: item.SolutionVersionUUID,
              SolutionVersionName: item.SolutionVersionName,
              Archs: item.Archs,
            }));
            setTableData(data);
            initializeDefaultArchs(data);
          }
        } catch (error) {
          console.error('获取解决方案版本失败:', error);
          message.error({
            content: '获取解决方案版本失败',
          });
          setTableData([]);
        }
      }
    } catch (error) {
      console.error('获取解决方案版本失败:', error);
      message.error({
        content: '获取解决方案版本失败',
      });
      setTableData([]);
    } finally {
      setLoading(false);
    }
  };

  // 确认按钮点击处理函数
  const handleConfirm = async () => {
    console.log('确认按钮点击');

    // 先触发表单验证
    try {
      const formData = await formRef.current?.validateFields();
      if (formData?.ApplicationNames?.length > 0) {
        // 表单验证通过，获取解决方案版本数据
        await fetchSolutionVersionData(formData.ApplicationNames);
        message.success({ content: '数据获取成功' });
      } else {
        message.error({ content: '请先选择应用' });
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 保存按钮点击处理函数
  const handleSave = () => {
    // 验证每个选中的版本都必须选择至少一个架构
    const errors: string[] = [];

    selectedVersions.forEach((version) => {
      const versionName = version.SolutionVersionName;
      const selectedArchsForVersion = selectedArchs[versionName] || [];

      if (selectedArchsForVersion.length === 0) {
        errors.push(`版本 ${versionName} 必须选择至少一个架构`);
      }
    });

    if (errors.length > 0) {
      message.error({ content: errors.join('; ') });
      return;
    }

    // 验证通过，调用确认回调
    if (onConfirm) {
      const result = selectedVersions.map((version) => ({
        ...version,
        selectedArchs: selectedArchs[version.SolutionVersionName] || [],
      }));
      console.log('onConfirm result:', result);
      onConfirm(result);
    }
    onClose();
  };

  return (
    <TcsModal
      title={'评估待修复版本'}
      visible={visible}
      onOk={handleSave}
      onCancel={onClose}
      width={1200}
      okText={'保存'}
    >
      <Alert type="warning">
        为了确保bug修复完整，请评估存在当前问题的版本，系统会根据选择范围复制TAPD单，如果同一版本的多个架构修复应用不一致，则务必选择每架构一个TAPD单
      </Alert>
      <Alert type="info" style={{ marginTop: 8 }}>
        当前单子版本：{currentVersion}
        。当前及以上版本默认选中全部架构且不可取消。
      </Alert>
      <div style={{ height: 600 }}>
        <ProForm style={{ width: '100%' }} grid formRef={formRef}>
          <ProForm.Item
            label="选择应用名称"
            dataIndex="ApplicationNames"
            tooltip="支持粘贴使用英文分号(;)分隔的多个应用名称字符串"
            colProps={{ span: 16 }}
            rules={{
              required: {
                value: true,
                message: '请选择应用',
              },
            }}
          >
            <TagSelect
              placeholder="请选择应用"
              options={appOptions}
              autoClearSearchValue
              optionsOnly
              tips={selectLoading ? <StatusTip.LoadingTip /> : undefined}
              onPaste={(e) => {
                const text = e.clipboardData.getData('text');
                const appNames = text.split(';').map((item) => item.trim());
                if (text) {
                  setApplicationNames(Array.from(new Set([...applicationNames, ...appNames])));
                }
              }}
            />
          </ProForm.Item>

          <Col span={4}>
            <Button type="primary" onClick={handleConfirm}>
              确认
            </Button>
          </Col>
        </ProForm>

        {/* 表格部分，显示选择应用后的解决方案版本数据 */}
        <div style={{ marginTop: 20 }}>
          <TcsTable
            columns={getColumns({
              shouldBeDefaultSelected,
              selectedArchs,
              selectedVersions,
              onArchChange: handleArchChange,
            })}
            dataSource={tableData}
            rowKey="SolutionVersionName"
            options={false}
            pagination={false}
            loading={loading}
            rowSelection={{
              type: 'checkbox',
              selectedRowKeys: selectedVersions.map((v) => v.SolutionVersionName),
              rowSelectable: (record) =>
                // 默认选中的版本不可取消勾选
                !shouldBeDefaultSelected(record.SolutionVersionName),
              onSelect: (record: any, selected: boolean, selectedRows: any) => {
                console.log('onSelect record:', record);
                console.log('onSelect selected:', selected);
                console.log('onSelect selectedRows:', selectedRows);

                // 处理单行选择
                let newSelectedVersions = [...selectedVersions];

                if (selected) {
                  // 选中版本，添加到列表
                  if (!newSelectedVersions.some((v) => v.SolutionVersionName === record.SolutionVersionName)) {
                    newSelectedVersions.push(record);
                  }
                } else {
                  // 取消选中版本，从列表移除（默认版本除外）
                  if (!shouldBeDefaultSelected(record.SolutionVersionName)) {
                    newSelectedVersions = newSelectedVersions.filter(
                      (v) => v.SolutionVersionName !== record.SolutionVersionName,
                    );
                  }
                }

                // 确保默认版本始终在选中列表中
                const defaultVersions = tableData.filter((item) => shouldBeDefaultSelected(item.SolutionVersionName));
                defaultVersions.forEach((defaultVersion) => {
                  if (!newSelectedVersions.some((v) => v.SolutionVersionName === defaultVersion.SolutionVersionName)) {
                    newSelectedVersions.push(defaultVersion);
                  }
                });

                setSelectedVersions(newSelectedVersions);

                // 处理架构选择联动
                const newSelectedArchs = { ...selectedArchs };

                if (selected) {
                  // 选中版本时，自动选中所有架构
                  const availableArchs = record.Archs || [];
                  newSelectedArchs[record.SolutionVersionName] = [...availableArchs];
                } else {
                  // 取消选中版本时，清空架构选择（默认版本除外）
                  if (!shouldBeDefaultSelected(record.SolutionVersionName)) {
                    newSelectedArchs[record.SolutionVersionName] = [];
                  }
                }

                setSelectedArchs(newSelectedArchs);
              },
              onChange: (selectedRowKeys, selectedRows) => {
                console.log('onChange selectedRowKeys:', selectedRowKeys);
                console.log('onChange selectedRows:', selectedRows);
                // 主要逻辑在onSelect中处理，这里只做日志记录
              },
            }}
          />
        </div>
      </div>
    </TcsModal>
  );
};

export default AssessFixVersionModal;
