import { getAppVersion } from '@/common/utils';
import { Tcs<PERSON><PERSON>on, TcsButtonGroup, TcsColumns, TcsTable } from '@tencent/tcs-component';
import { Button, message, PopConfirm } from '@tencent/tea-component';
import React, { useMemo, useRef, useState } from 'react';
import RelateVerifyPackage from '../RelateVerifyPackage';
import { deleteIssueRelatedApp, IPropsIssueSolutionRel } from '@/common/api/interationManage';
import MergeVersionModal from '../MergeVersionModal';
import ViewGitMRModal from '../ViewGitMRModal';
import { IssueTypeConfig } from '@/modules/IterationManage/config';
import { SOLUTION_ARCH_STATUS } from '@/common/config';
import useLookup from '@/common/hookups/useLookup';
// import { SOLUTION_ARCH_STATUS } from '@/common/config';
import VersionChangeModal from '../VersionChangeModal';
const getColumns = ({
  onRelieveRef,
  solutionArchRecord,
  onViewGitMR,
  onViewMergeVersion,
  pagePath,
  optionBtn,
  onVersion,
}: {
  onRelieveRef: (record: any, solutionArchRecord: any) => void;
  solutionArchRecord: any;
  onViewGitMR: (record: any, solutionArchRecord: any) => void;
  onViewMergeVersion: (record) => void;
  pagePath: 'story' | 'defect';
  optionBtn: {
    disabled: boolean;
    tooltip?: string;
  };
  onVersion: (record) => void;
}) =>
  [
    {
      title: '应用',
      dataIndex: 'ApplicationName',
      width: '15%',
      linkable: true,
      disable: true,
      copyable: true,
      linkProps: {
        target: 'blank',
        linkUrl: (text, record) =>
          `/page/develop_center_next/app_manage/product?app_id=${record?.ApplicationBranch?.Application?.UUID}&app_name=${record.ApplicationName}&select_branch=${record.ApplicationBranch?.UUID}`,
      },
      fixed: 'left',
    },

    {
      title: '应用版本',
      dataIndex: 'ApplicationBranch.BranchName',
      width: '10%',
    },
    {
      title: '产品名称',
      dataIndex: ['ProductVersionInfo', 'ProductInfo', 'Name'],
      width: '10%',
    },
    {
      title: '产品版本',
      dataIndex: 'ProductVersion',
      width: '10%',
      render: (text, record) => {
        if (text) {
          return (
            <>
              {text}
              <TcsButton icon="pencil" onClick={() => onVersion(record)} />
            </>
          );
        }
        return '-';
      },
    },
    {
      title: '研发状态',
      dataIndex: 'Status',
      width: 100,
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'IssueSolutionArchStatus',
        showType: 'tag',
      },
    },
    {
      title: '引入版本',
      dataIndex: 'BasePackageVersion',
      width: '10%',
    },
    {
      title: '已合流版本',
      dataIndex: 'ReleaseApplicationPackage',
      width: '15%',
      disable: true,
      render(_, record) {
        let text = '暂无已合流版本';
        const releasePackage = record?.LatestRegisteredApplicationPackage;
        if (releasePackage) {
          text = getAppVersion(releasePackage);
        } else {
          text = '-';
        }
        return (
          <>
            <div> {text}</div>
            <Button
              type="link"
              disabled={optionBtn.disabled}
              tooltip={optionBtn.tooltip}
              onClick={() => onViewMergeVersion(record)}
            >
              关联合流版本
            </Button>
          </>
        );
      },
    },
    {
      title: '正式制品版本',
      dataIndex: 'ReleaseApplicationPackageApplicationVersion',
      width: '20%',
      disable: true,
      render(_, record) {
        const { ReleaseApplicationPackage } = record;
        const text = ReleaseApplicationPackage ? getAppVersion(ReleaseApplicationPackage) : '-';
        return <span>{text}</span>;
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      valueType: 'option',
      width: '12%',
      fixed: 'right',
      render: (_, record) => {
        const releasePackage = record?.LatestRegisteredApplicationPackage;
        return (
          <>
            {/** 未交付的架构组合才能解除绑定 */}
            {!solutionArchRecord?.DeliveryUUID && (
              <>
                <PopConfirm
                  title="确定要解除应用制品与缺陷单的关联？"
                  message="解除后，配置将不再生效，也不做关联。"
                  disabled={optionBtn.disabled || releasePackage}
                  footer={(close) => (
                    <>
                      <Button
                        type="link"
                        onClick={() => {
                          close();
                          onRelieveRef(record, solutionArchRecord);
                        }}
                      >
                        解除
                      </Button>
                      <Button
                        type="text"
                        onClick={() => {
                          close();
                        }}
                      >
                        取消
                      </Button>
                    </>
                  )}
                  placement="top-start"
                >
                  <Button
                    type="link"
                    disabled={releasePackage}
                    tooltip={releasePackage ? '当前存在已合流版本，不允许解除关联，如需操作请先解除已合流版本' : ''}
                  >
                    {pagePath === 'story' ? '解除需求单关联' : '解除缺陷单关联'}
                  </Button>
                </PopConfirm>
              </>
            )}
            <Button type="link" onClick={() => onViewGitMR(record, solutionArchRecord)}>
              查看Git变更
            </Button>
          </>
        );
      },
    },
  ] as TcsColumns[];

export interface IProps {
  record: any;
  pagePath: 'story' | 'defect';
  onReload: () => void;
  IssueID: string;
  onLoading: (status: boolean) => void;
  tableDataSource?: IPropsIssueSolutionRel[];
  isManager: boolean;
}

const AppListMr: React.FC<IProps> = ({ record, pagePath, onReload, IssueID, onLoading, tableDataSource }) => {
  const { lookups } = useLookup(['branchRelatedSolutionVersion', 'AppRulesConfigLimitUsers']);
  const solutionVersionName = lookups?.branchRelatedSolutionVersion?.[0]?.Extra?.solutionVersionUUID || [];
  const appRulesConfigLimitUsers = lookups?.AppRulesConfigLimitUsers?.[0]?.Extra?.users || [];
  // 确保 solutionVersionName 是一个数组
  const solutionVersionNamesArray = Array.isArray(solutionVersionName) ? solutionVersionName : [];
  const AppRulesConfigLimitUsersArray = Array.isArray(appRulesConfigLimitUsers) ? appRulesConfigLimitUsers : [];
  const isSolutionVersionIncluded = solutionVersionNamesArray.includes(record?.SolutionVersionUUID);
  const jiguangUsename = (window as any).jiguang_username;
  const isAppRulesConfigLimitUsersIncluded = AppRulesConfigLimitUsersArray.includes(jiguangUsename);
  const relateRef = useRef<any>();
  const mergeVersionRef = useRef<any>();
  const viewGitMrRef = useRef<any>();
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const issueType = IssueTypeConfig[pagePath];
  const [batchModes, setBatchModes] = useState<{ [uuid: string]: boolean }>({});
  const [visible, setVisible] = useState(false);
  const [versionRecord, setVersionRecord] = useState<any>({});
  // 关联制品

  // 解除关联
  function handleRelieveRef(record: any, solutionArchRecord: any) {
    onLoading(true);
    deleteIssueRelatedApp({
      RelatedApplicationUUIDs: [record.UUID],
      IssueID: solutionArchRecord.IssueID,
      IssueType: pagePath === 'defect' ? 'bug' : 'story',
    })
      .then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          message.success({
            content: '解除关联成功',
          });
          onReload();
        }
      })
      .finally(() => {
        onLoading(false);
      });
  }

  function handleViewGitMR(record: any) {
    viewGitMrRef?.current?.show(record, IssueID);
  }

  function handleVersion(record: any) {
    setVisible(true);
    setVersionRecord(record);
  }

  function handleViewMergeVersion(record: any) {
    mergeVersionRef.current.show({
      IssueID,
      ApplicationBranchUUID: record.ApplicationBranchUUID,
      Arch: record.Arch,
      record,
    });
  }

  // 批量解除关联应用
  function handleRemoveApp(record) {
    const uuid = record?.UUID || '';
    setBatchModes(
      { [uuid]: true }, // 切换指定表格的批量模式状态
    );
    setSelectedRowKeys([]);
  }
  // 取消批量解除关联应用
  function handleCancelRemove(record) {
    const uuid = record?.UUID || '';
    setBatchModes(
      { [uuid]: false }, // 切换指定表格的批量模式状态
    );
    setSelectedRowKeys([]);
  }
  // 确认批量解除
  function handleConfirmRemoveApp(record) {
    if (!selectedRowKeys?.length) {
      message.error({ content: '请选择要解除关联的应用' });
      return;
    }
    onLoading(true);
    deleteIssueRelatedApp({
      RelatedApplicationUUIDs: selectedRowKeys,
      IssueID: record?.IssueID,
      IssueType: pagePath === 'defect' ? 'bug' : 'story',
    })
      .then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          message.success({
            content: '解除关联成功',
          });
          onReload();
        }
      })
      .finally(() => {
        onLoading(false);
        setBatchModes(
          { [record?.UUID]: false }, // 切换指定表格的批量模式状态
        );
        setSelectedRowKeys([]);
      });
  }

  const isBatchMode = batchModes?.[record?.UUID] || false;
  const optionBtn = useMemo(() => {
    if (record?.Status === SOLUTION_ARCH_STATUS.WAITING_FOR_DELIVERING) {
      return {
        disabled: true,
        tooltip: '当前修复状态为【待出包】，不支持修改，如需修改请将TAPD单打回',
      };
    }
    return {
      disabled: false,
    };
    // if (isManager) {
    //   return {
    //     disabled: false,
    //   };
    // }
    // return {
    //   disabled: true,
    //   tooltip: '只能由【处理人/研发负责人/测试负责人】进行操作，您不是当前缺陷单的【处理人/研发负责人/测试负责人】',
    // };
  }, [record?.Status]);
  return (
    <>
      <TcsTable
        headerTitle={
          <TcsButtonGroup
            type="button"
            items={[
              {
                text: '批量解除关联应用',
                hidden: isBatchMode,
                onClick: () => handleRemoveApp(record),
                disabled: optionBtn.disabled,
                tooltip: optionBtn.tooltip,
              },
              {
                text: '确定',
                hidden: !isBatchMode,
                confirm: true,
                confirmProps: {
                  title: '确定要批量解除应用制品与缺陷单的关联？',
                  message: '解除后，配置将不再生效，也不做关联。',
                  onConfirm() {
                    handleConfirmRemoveApp(record);
                  },
                },
              },
              {
                text: '取消',
                hidden: !isBatchMode,
                onClick: () => handleCancelRemove(record),
              },
            ]}
          />
        }
        columns={getColumns({
          onRelieveRef: handleRelieveRef,
          solutionArchRecord: record,
          onViewGitMR: handleViewGitMR,
          onViewMergeVersion: handleViewMergeVersion,
          pagePath,
          optionBtn,
          onVersion: handleVersion,
          // isAppRulesConfigLimitUsersIncluded,
          // isSolutionVersionIncluded,
          // AppRulesConfigLimitUsersArray,
        })}
        dataSource={record?.IssueAppRel || []}
        scroll={{
          x: 1300,
        }}
        options={{
          reload: false,
        }}
        columnsState={{
          persistenceKey: 'defect_manage/iteration_manage/defect/detail/access/second',
        }}
        rowKey="UUID"
        rowSelection={
          isBatchMode
            ? {
                type: 'checkbox',
                selectedRowKeys,
                onChange: (selectedRowKeys: string[]) => {
                  setSelectedRowKeys(selectedRowKeys);
                },
              }
            : undefined
        }
      />
      <RelateVerifyPackage
        ref={relateRef}
        issueID={IssueID!}
        defectType={issueType}
        tableDataSource={tableDataSource}
        onRelateChange={() => {
          onReload();
        }}
        isSolutionVersionIncluded={isSolutionVersionIncluded}
        isAppRulesConfigLimitUsersIncluded={isAppRulesConfigLimitUsersIncluded}
        AppRulesConfigLimitUsersArray={AppRulesConfigLimitUsersArray}
      />
      <MergeVersionModal
        ref={mergeVersionRef}
        onConfirm={() => {
          onReload();
        }}
        isSolutionVersionIncluded={isSolutionVersionIncluded}
        isAppRulesConfigLimitUsersIncluded={isAppRulesConfigLimitUsersIncluded}
        AppRulesConfigLimitUsersArray={AppRulesConfigLimitUsersArray}
      />
      <ViewGitMRModal ref={viewGitMrRef} />
      {visible && (
        <VersionChangeModal
          data={record}
          record={versionRecord}
          onConfirm={() => {
            setVisible(false);
            onReload();
          }}
          onCancel={() => setVisible(false)}
        />
      )}
    </>
  );
};

export default AppListMr;
