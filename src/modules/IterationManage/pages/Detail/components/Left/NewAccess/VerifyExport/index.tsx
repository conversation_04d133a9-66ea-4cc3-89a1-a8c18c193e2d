/*
 * @Author: lucyfang
 * @Date: 2025-02-13 17:24:01
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-04-23 14:35:59
 * @Description: 请输入注释信息
 */
import React, { useState, useRef } from 'react';
import VerifyExportModal from '../VerifyExportModal';
import { GetTAPDOperationSheet, ICreateIssueRelatedAppsApi, IterationManage } from '@/common/api/iterationManage.api';
import { TcsTable, TcsButton, TcsActionType } from '@tencent/tcs-component';
import { TestConclusion } from '../../../../../../config';
import TestResult from '../TestResult';
export interface IProps {
  record: any;
  Tops?: GetTAPDOperationSheet;
  OriginTops?: GetTAPDOperationSheet;
  showTops?: boolean;
  showKaleido?: boolean;
  siteList: ICreateIssueRelatedAppsApi.ISiteInfo[];
  showDawn?: boolean;
  onReload: () => void;
}
const VerifyExport: React.FC<IProps> = ({
  record,
  Tops: TopsData,
  OriginTops: OriginTopsData,
  showTops,
  showKaleido,
  siteList,
  showDawn,
  onReload,
}) => {
  const [visible, setVisible] = useState(false);
  const [resultVisible, setResultVisible] = useState(false);
  const [uuid, setUUID] = useState('');
  const [isDisabled, setIsDisabled] = useState(false);
  const actionRef = useRef<TcsActionType>();
  const handleRequest = () =>
    IterationManage.ListIssueSolutionTestResults({
      IssueSolutionRelUUID: record.UUID,
    });
  const hanldlePreview = (record) => {
    setIsDisabled(true);
    setUUID(record?.UUID);
    setResultVisible(true);
  };
  const getColumns = () => [
    {
      dataIndex: 'Creator',
      title: '测试人',
      width: '10%',
    },
    {
      dataIndex: 'Conclusion',
      title: '验证结论',
      width: '10%',
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'TestConclusion',
      },
    },
    {
      dataIndex: 'ReasonClass',
      title: '未通过的原因',
      render(_, record) {
        if (record?.Conclusion !== TestConclusion.Passed) {
          return record?.ReasonClass ? record.ReasonClass : '-';
        }
        return '-';
      },
      width: '15%',
    },
    {
      dataIndex: 'UpdatedAt',
      title: '提交时间',
      valueType: 'dateTime',
      width: '10%',
    },
    {
      dataIndex: 'operation',
      title: '操作',
      width: '5%',
      fixed: 'right',
      render(text, record) {
        return (
          <TcsButton type="link" onClick={() => hanldlePreview(record)}>
            查看
          </TcsButton>
        );
      },
    },
  ];
  const handleConfirm = () => {
    setResultVisible(false);
    actionRef.current?.reload();
    onReload();
  };
  return (
    <>
      {/* 测试人员灰度 */}
      <TcsTable
        columns={getColumns()}
        pagination={{}}
        request={handleRequest}
        scroll={{ x: 600 }}
        actionRef={actionRef}
        headerTitle={
          <>
            <TcsButton
              type="primary"
              onClick={() => {
                setResultVisible(true);
                setIsDisabled(false);
              }}
            >
              填写验证结论
            </TcsButton>
            <TcsButton
              type="primary"
              onClick={() => {
                setVisible(true);
              }}
            >
              启动验证出包流程
            </TcsButton>
          </>
        }
      />

      {visible && (
        <VerifyExportModal
          onClose={() => {
            setVisible(false);
          }}
          record={record}
          Tops={TopsData}
          OriginTops={OriginTopsData}
          showKaleido={showKaleido}
          showTops={showTops}
          showDawn={showDawn}
        />
      )}
      {resultVisible && (
        <TestResult
          onConfirm={handleConfirm}
          isDisabled={isDisabled}
          UUID={uuid}
          siteList={siteList}
          record={record}
          onClose={() => {
            setResultVisible(false);
          }}
        />
      )}
    </>
  );
};

export default VerifyExport;
