/*
 * @Author: lucyfang
 * @Date: 2025-02-28 18:44:19
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-03-27 15:07:42
 * @Description: 请输入注释信息
 */
import { TcsButton, TcsModal } from '@tencent/tcs-component';
import React, { useRef } from 'react';
import Cherry from 'cherry-markdown/dist/cherry-markdown.core';
import 'cherry-markdown/dist/cherry-markdown.css';
import VerificationMarkdownEditor from '../VerificationMarkdownEditor';
import { EditorMode } from 'cherry-markdown/types/cherry';
import { addTimestampToFilename, defaultFileUpload } from './util';
import { Alert, message } from '@tencent/tea-component';
export interface IProps {
  title: string;
  onClose: () => void;
  status: EditorMode;
  setEdit?: React.Dispatch<React.SetStateAction<boolean>>;
  /* 保存Markdown文件列表 */
  fileList?: React.MutableRefObject<FileList[]>;
  desContent: string;
  onChange?: ({ des }: { des: string }) => void;
}

const MarkdownEditorModal: React.FC<IProps> = ({ onChange, title, onClose, status, setEdit, fileList, desContent }) => {
  const editorRef = useRef<Cherry>();
  const internalFileList = useRef<FileList[]>([]); // 内部默认文件列表
  const hanldeMarkdownChange = () => {
    onChange?.({ des: editorRef.current?.getMarkdown() || '' });
    onClose();
  };
  return (
    <TcsModal
      width={900}
      title={title}
      visible
      onCancel={() => {
        setEdit?.(false);
        onClose();
      }}
      onOk={hanldeMarkdownChange}
      footer={
        status === 'previewOnly' || (
          <>
            <TcsButton onClick={hanldeMarkdownChange} type="primary">
              确定
            </TcsButton>
            <TcsButton
              onClick={() => {
                setEdit?.(false);
                onClose();
              }}
            >
              取消
            </TcsButton>
          </>
        )
      }
    >
      <Alert type="warning">
        <p>1、用于记录变更过程的验证。需要附上变更成功的截图（请将图片复制粘贴到文本框）</p>
        <p>2、若是受限通过，需要说明什么环境，什么情况，什么依赖，会导致什么问题，周知风险</p>
        <p>3、不涉及，需要说明为何不涉及</p>
      </Alert>
      <VerificationMarkdownEditor
        editorRef={editorRef}
        editor={{
          defaultModel: status,
          height: '400px',
        }}
        value={desContent || ''} // 根据模式传递不同值
        fileUpload={async (file) => {
          try {
            const { downloadUrl, fileName } = await defaultFileUpload({
              file,
              markdownInstanceRef: editorRef,
              packageDomain: window.location.origin,
            });

            // 统一使用有效文件列表引用
            const targetFileList = fileList || internalFileList;
            const urlParts = downloadUrl.split('/');
            const uploadTime = urlParts[urlParts.length - 2];

            if (targetFileList.current) {
              targetFileList.current.push({
                DownloadUrl: downloadUrl,
                LocalFilePath: `./Package/description/images/${addTimestampToFilename(fileName, uploadTime)}`,
              });
            }
          } catch (err) {
            message.error({ content: `文件上传失败:${err}` });
            console.error('文件上传失败:', err);
          }
        }}
      />
    </TcsModal>
  );
};

export default MarkdownEditorModal;
