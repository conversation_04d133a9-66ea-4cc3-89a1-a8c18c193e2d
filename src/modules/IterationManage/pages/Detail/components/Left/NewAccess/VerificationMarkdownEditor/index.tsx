/*
 * @Author: lucyfang
 * @Date: 2025-02-28 19:52:06
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-03-05 12:11:23
 * @Description: 请输入注释信息
 */
import Cherry from 'cherry-markdown/dist/cherry-markdown.core';
import 'cherry-markdown/dist/cherry-markdown.css';
import { CherryOptions } from 'cherry-markdown/types/cherry';
import React, { FC, MutableRefObject, useEffect } from 'react';
interface MarkdownEditorProps extends Partial<CherryOptions> {
  editorRef: MutableRefObject<Cherry>;
}

const VerificationMarkdownEditor: FC<MarkdownEditorProps> = ({ editorRef, ...props }) => {
  const initToolBars = {
    theme: 'light', // light or dark
    toolbar: ['bold', 'italic', 'strikethrough', '|', 'color', 'header', '|', 'list', 'togglePreview'],
    bubble: ['bold', 'italic', 'strikethrough', 'size'],
    float: ['h1', 'h2', 'h3'],
    customMenu: {},
  };
  useEffect(() => {
    const markdownEditorInstance = new Cherry({
      id: 'markdown-container',
      value: '',
      toolbars: initToolBars,
      engine: {
        syntax: {
          underline: false,
        },
      },
      ...props,
    });
    editorRef.current = markdownEditorInstance;
  }, []);

  return <div id="markdown-container" />;
};
export default VerificationMarkdownEditor;
