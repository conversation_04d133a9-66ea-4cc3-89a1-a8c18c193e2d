import { Button, Justify, Modal, PopConfirm, Table, TagSearchBox, message } from '@tencent/tea-component';
import React, { useEffect, useImperativeHandle, useMemo, useState } from 'react';
import {
  IPropsIssueSolutionRel,
  LinkApplicationPackageToIssues,
  ListApplicationPackageIssueRels,
  ListApplicationPackages,
  // UnlinkApplicationPackageFromIssues,
} from '@/common/api/interationManage';
import { unstable_batchedUpdates } from 'react-dom';
import { TcsModal } from '@tencent/tcs-component';
import { getAppVersion } from '@/common/utils';
import { scrollable } from '@tencent/tea-component/lib/table/addons';
import { PackageTestStatus } from '@/common/constants';

export interface IProps {
  issueID: string;
  defectType?: string;
  onRelateChange: () => void;
  // tableDataSource: any[];
  // isSolutionVersionIncluded?: boolean;
  // isAppRulesConfigLimitUsersIncluded?: boolean;
  // AppRulesConfigLimitUsersArray?: string[];
}
const { pageable, autotip } = Table.addons;

const RelateVerifyPackage: React.ForwardRefRenderFunction<any, IProps> = (
  {
    issueID,
    defectType = 'bug',
    onRelateChange,
    // isSolutionVersionIncluded,
    // isAppRulesConfigLimitUsersIncluded,
    // AppRulesConfigLimitUsersArray,
  },
  ref,
) => {
  const [visible, setVisible] = useState(false);
  const [searchParams, setSearchParams] = useState<any>();
  const [applicationPackageIssueRels, setApplicationPackageIssueRels] = useState<any[]>([]);

  const [record, setRecord] = useState<any>();
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [pageInfo, setPageInfo] = useState({
    pageSize: 10,
    pageIndex: 1,
    total: 0,
  });
  // // 当用户在弹框里面修改关联制品后，需要获取最新的effectiveVersion,只能从外部表格数据获取
  // const effectiveVersion = useMemo(() => {
  //   let newRecord: any = undefined;

  //   outerTableDataSource?.forEach((item) => {
  //     item?.IssueAppRel?.forEach((app) => {
  //       if (app.UUID === record?.UUID) {
  //         newRecord = app;
  //       }
  //     });
  //   });
  //   return newRecord?.ReleaseApplicationPackage ? getAppVersion(newRecord.ReleaseApplicationPackage) : undefined;
  // }, [record, outerTableDataSource]);

  const [loading, setLoading] = useState(false);
  const [update, setUpdate] = useState<any>({});

  useImperativeHandle(
    ref,
    () => ({
      show(record: IPropsIssueSolutionRel) {
        setRecord(record);
        setVisible(true);
      },
    }),
    [],
  );

  function handleCancel() {
    setVisible(false);
    unstable_batchedUpdates(() => {
      setPageInfo((info) => ({
        ...info,
        pageIndex: 1,
      }));
    });
  }

  const columns = [
    {
      header: '制品版本',
      key: 'version',
      width: '45%',
      render(row) {
        return getAppVersion(row);
      },
    },
    {
      header: '源代码分支',
      key: 'CodeBranch',
    },
    {
      header: '代码提交人',
      key: 'CodeCommitter',
    },

    {
      header: '操作',
      key: 'options',
      width: '10%',

      render(record) {
        if (record?.RelateState) {
          return (
            <Button type="link" disabled tooltip="当前制品版本已关联，无需重复操作">
              关联
            </Button>
            // <PopConfirm
            //   title="制品解除关联？"
            //   footer={(close) => (
            //     <>
            //       <Button
            //         type="link"
            //         onClick={() => {
            //           close();
            //           hanldeUnRelatePackage(record);
            //         }}
            //       >
            //         确认
            //       </Button>
            //       <Button
            //         type="text"
            //         onClick={() => {
            //           close();
            //         }}
            //       >
            //         取消
            //       </Button>
            //     </>
            //   )}
            // >
            //   <Button
            //     type="link"
            //     // disabled={!isAppRulesConfigLimitUsersIncluded && isSolutionVersionIncluded}
            //     // tooltip={
            //     //   !isAppRulesConfigLimitUsersIncluded && isSolutionVersionIncluded
            //     //     ? `当前解决方案版本为正式版本，该版本下的应用/制品不允许解除关联，如需解除，请联系${AppRulesConfigLimitUsersArray?.join(
            //     //         ';',
            //     //       )}`
            //     //     : ''
            //     // }
            //   >
            //     解除关联
            //   </Button>
            // </PopConfirm>
          );
        }
        return (
          <PopConfirm
            title="确定关联此制品？"
            footer={(close) => (
              <>
                <Button
                  type="link"
                  onClick={() => {
                    close();
                    hanldeRelatePackage(record);
                  }}
                >
                  关联
                </Button>
                <Button
                  type="text"
                  onClick={() => {
                    close();
                  }}
                >
                  取消
                </Button>
              </>
            )}
          >
            <Button type="link">关联</Button>
          </PopConfirm>
        );
      },
    },
  ];

  function hanldeRelatePackage(value) {
    LinkApplicationPackageToIssues({
      PackageUUID: value.UUID,
      IssueIDs: [issueID],
      IssueType: defectType,
    }).then((res) => {
      if (res.Error) {
        return message.error({ content: res.Error?.Message });
      }
      message.success({ content: res.Msg });
      setUpdate({});
      onRelateChange();
    });
  }
  // function hanldeUnRelatePackage(value) {
  //   UnlinkApplicationPackageFromIssues({
  //     PackageUUID: value.UUID,
  //     IssueIDs: [issueID],
  //   }).then((res) => {
  //     if (res.Error) {
  //       return message.error({ content: res.Error?.Message });
  //     }
  //     message.success({ content: res.Msg });
  //     setUpdate({});
  //     onRelateChange();
  //   });
  // }
  // 加载当前已关联制品列表
  useEffect(() => {
    if (visible && issueID) {
      ListApplicationPackageIssueRels({
        IssueID: issueID,
        ApplicationBranchUUID: record.ApplicationBranchUUID,
      }).then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          setApplicationPackageIssueRels(
            res.ListApplicationPackageIssueRels?.filter((item) => item.ApplicationPackage) || [],
          );
        }
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible, issueID, update]);

  useEffect(() => {
    if (visible && issueID) {
      setLoading(true);
      const params: any = {
        ApplicationUUID: record?.ApplicationBranch?.ApplicationUUID,
        ApplicationBranch: record?.ApplicationBranch?.BranchName,
        Arch: record?.Arch,
        // 默认都为正式制品
        PackageType: 1,
        pageNo: pageInfo.pageIndex,
        pageSize: pageInfo.pageSize,
        // 关联待验证版本
        Status: PackageTestStatus.WAITING_FOR_TESTING,
      };
      if (searchParams?.length) {
        searchParams.forEach((item) => {
          if (item.attr && item.values?.length) {
            params[item.attr.key] = item.values[0].name;
          } else {
            message.warning({
              content: '不支持使用单个关键字进行搜索',
            });
          }
        });
      }
      ListApplicationPackages(params)
        .then((res) => {
          if (res.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            setDataSource(res.ApplicationPackages || []);
            setPageInfo((page) => ({
              ...page,
              total: res.Total || 0,
            }));
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [visible, update, issueID, record, pageInfo.pageSize, pageInfo.pageIndex, searchParams]);

  const tableData = useMemo((): any[] => {
    if (dataSource && applicationPackageIssueRels) {
      return dataSource.map((item) => {
        if (applicationPackageIssueRels.some((value) => value?.PackageUUID === item.UUID)) {
          return {
            ...item,
            RelateState: true,
          };
        }
        return item;
      });
    }

    return dataSource || [];
  }, [dataSource, applicationPackageIssueRels]);

  function handleQuery(params: any) {
    setSearchParams(params);
  }

  return (
    <TcsModal
      visible={visible}
      title={
        <span style={{ display: 'flex' }}>
          关联制品
          <span style={{ flex: 1, marginTop: -4, marginLeft: 10 }}>
            <Modal.Message
              description={`应用：${record?.ApplicationName}；版本：${record?.ApplicationBranch?.BranchName}； 架构：${record?.Arch}`}
            />
          </span>
        </span>
      }
      onCancel={handleCancel}
      footer={null}
      width={950}
    >
      {/* {effectiveVersion && (
        <Alert>{`当前缺陷单已关联【${applicationPackageIssueRels.length}】个制品版本，当前生效制品版本为【${effectiveVersion}】`}</Alert>
      )} */}
      <Table.ActionPanel>
        <Justify
          right={
            <TagSearchBox
              attributes={[
                {
                  type: 'input',
                  key: 'CommitID',
                  name: 'CommitID',
                },
                {
                  type: 'input',
                  key: 'CodeBranch',
                  name: '源代码分支',
                },
                {
                  type: 'input',
                  key: 'CodeCommitter',
                  name: '代码提交人',
                },
              ]}
              onChange={handleQuery}
            />
          }
        />
      </Table.ActionPanel>
      <Table
        columns={columns}
        records={tableData}
        recordKey="ID"
        addons={[
          pageable({
            recordCount: pageInfo.total,
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            onPagingChange: (query) => {
              setPageInfo((pageInfo) => ({
                ...pageInfo,
                ...query,
              }));
            },
          }),
          autotip({
            isLoading: loading,
          }),

          scrollable({
            maxHeight: 'calc(100vh - 380px)',
          }),
        ]}
      />
    </TcsModal>
  );
};

export default React.forwardRef(RelateVerifyPackage);
