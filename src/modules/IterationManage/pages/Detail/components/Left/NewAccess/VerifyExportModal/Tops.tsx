/*
 * @Author: lucyfang
 * @Date: 2025-02-13 17:24:01
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-03-19 14:27:06
 * @Description: 请输入注释信息
 */
import { generateWorkflowInstance } from '@/common/api/deliveryManage';
import { GetTAPDOperationSheet } from '@/common/api/iterationManage.api';
import { VERIFY_EXPORT_WORKFLOW_UUID } from '@/common/config';
import { TcsButton, TcsTable } from '@tencent/tcs-component';
import { Alert, Button, message } from '@tencent/tea-component';
import React, { useState } from 'react';

export interface IProps {
  record: any;
  Tops?: GetTAPDOperationSheet;
  OriginTops?: GetTAPDOperationSheet;
  data: any;
  onRefresh: () => void;
}
export const getColumns = () => [
  {
    title: '创建人',
    dataIndex: 'Creator',
    width: '10%',
  },

  {
    title: '状态',
    dataIndex: 'Status',
    width: '10%',
    valueType: 'dictSelect',
    fieldProps: {
      dictType: 'VerificationStatus',
    },
  },
  {
    title: '测试用户',
    dataIndex: 'ClientName',
    width: '15%',
  },
  {
    title: '测试局点',
    dataIndex: 'SiteName',
    width: '20%',
  },
  {
    title: '物料下载',
    dataIndex: 'MaterialURL',
    width: '10%',
    linkable: true,
    copyable: true,
    linkProps: {
      linkText: () => '点击下载',
    },
  },
  {
    title: '创建时间',
    dataIndex: 'CreatedAt',
    width: '15%',
    valueType: 'dateTime',
  },
  {
    title: '操作',
    dataIndex: 'option',
    valueType: 'option',
    width: '10%',
    // eslint-disable-next-line react/display-name
    render: (text, record) => (
      <TcsButton
        type="link"
        onClick={() => {
          window.open(
            `/page/flow-design/flow-publish/exec?instance_id=${record.WorkflowInstanceID}&workflow_id=${record.WorkflowID}`,
            '_blank',
          );
        }}
      >
        查看出包历史
      </TcsButton>
    ),
  },
];

const Tops: React.FC<IProps> = ({ record, OriginTops: OriginTopsSheetInfo, Tops: TopsSheetInfo, data, onRefresh }) => {
  const [loading, setLoading] = useState(false);
  function handleStartFlow() {
    setLoading(true);
    generateWorkflowInstance({
      WorkflowUUID: VERIFY_EXPORT_WORKFLOW_UUID.Tops,
      GlobalValues: {
        SourceKey: record.UUID,
        SourceType: 'IssueSolution',
      },
    })
      .then((response) => {
        if (response.Error) {
          message.error({
            content: response.Error.Message,
          });
        } else {
          message.success({
            content: '流程启动成功',
          });
          window.open(
            `/page/flow-design/flow-publish/exec?instance_id=${response.WorkflowInstanceID}&workflow_id=${response.WorkflowID}`,
            '_blank',
          );
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }
  if (TopsSheetInfo) {
    return (
      <>
        <Alert type="info">点击下方按钮启动产品运维中心验证出包流程</Alert>
        <TcsTable
          columns={getColumns()}
          dataSource={data}
          pagination={{}}
          scroll={{ x: 1000 }}
          headerTitle={
            <TcsButton loading={loading} type="primary" onClick={handleStartFlow}>
              启动验证出包流程
            </TcsButton>
          }
          options={{
            reload: false,
          }}
          toolBarRender={() => (
            <TcsButton
              type="icon"
              icon="refresh"
              onClick={() => {
                onRefresh();
              }}
            />
          )}
        />
      </>
    );
  }

  if (OriginTopsSheetInfo) {
    return (
      <>
        <Alert type="info">
          检测到您的变更单是手动填写的，而不是产品市场变更单自动转换而来，请点击下方按钮跳转至产品市场变更单进行验证出包
        </Alert>
        <Button
          loading={loading}
          type="primary"
          onClick={() => {
            window.open(`https://tops.woa.com/sheet/${OriginTopsSheetInfo!.SheetID}/detail`, '_blank');
          }}
        >
          跳转至产品运维中心变更单
        </Button>
      </>
    );
  }

  return <Alert type="info">您还未完善产品运维中心变更单，请先完善后再操作</Alert>;
};

export default Tops;
