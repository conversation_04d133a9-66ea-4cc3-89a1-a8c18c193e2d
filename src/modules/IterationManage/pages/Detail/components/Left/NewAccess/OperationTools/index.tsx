import React, { useEffect, useState } from 'react';
import { TcsTable, ProColumns } from '@tencent/tcs-component';
import { Alert } from '@tencent/tea-component';
import { IterationManage } from '@/common/api/iterationManage.api';

interface OperationToolData {
  type: string;
  name: string;
  id: string;
  version: string;
  architecture: string;
  product: string;
  isRequired: '是' | '否';
  action: React.ReactNode;
}

const OperationTools: React.FC = () => {
  const [data, setData] = useState<OperationToolData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // 1. 调用 GetTAPDDeliverySites 接口获取变更单类型
        const deliverySitesResponse = await IterationManage.GetTAPDDeliverySites({
          IssueSolutionRelUUID: 'd541d658b65043e88e680eeee096f61e', // 替换为实际 UUID
        });
        const operationTools = deliverySitesResponse.OperationTools || [];
        const sites = deliverySitesResponse.Sites || [];

        // 2. 根据 OperationTools 动态调用 GetTAPDOperationSheet 接口
        const tableData: OperationToolData[] = await Promise.all(
          operationTools.map(async (tool) => {
            const operationSheetResponse = await IterationManage.GetTAPDOperationSheet({
              IssueSolutionRelUUID: 'd541d658b65043e88e680eeee096f61e', // 替换为实际 UUID
              OperationTool: tool,
            });

            return {
              type: tool,
              name: sites.find((site) => site.OperationTool === tool)?.SiteName || 'N/A',
              id: operationSheetResponse?.SheetID || 'N/A',
              version: operationSheetResponse?.Version || 'N/A',
              architecture: sites.find((site) => site.OperationTool === tool)?.SiteArch || 'N/A',
              product: sites.find((site) => site.OperationTool === tool)?.ClientName || 'N/A',
              isRequired: '是',
              action: <a href={`/create-change-order?type=${tool}`}>创建变更单</a>,
            };
          }),
        );

        // 3. 处理未包含的变更单类型（如产品运维中心、kaleido）
        const allTools = ['产品市场', '产品运维中心', 'kaleido'];
        const missingTools = allTools.filter((tool) => !operationTools.includes(tool));

        missingTools.forEach((tool) => {
          tableData.push({
            type: tool,
            name: `需要出包的局点中没有使用${tool}进行变更的局点，可以不用制作相应变更单，TAPD单不会强校验`,
            id: 'N/A',
            version: 'N/A',
            architecture: 'N/A',
            product: 'N/A',
            isRequired: '否',
            action: null,
          });
        });

        setData(tableData);
      } catch (error) {
        console.error('Failed to fetch data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const columns: ProColumns<OperationToolData>[] = [
    {
      title: '变更单类型',
      dataIndex: 'type',
    },
    {
      title: '变更单名称',
      dataIndex: 'name',
    },
    {
      title: '变更单ID',
      dataIndex: 'id',
    },
    {
      title: '使用的变更单版本',
      dataIndex: 'version',
    },
    {
      title: '架构',
      dataIndex: 'architecture',
    },
    {
      title: '产品',
      dataIndex: 'product',
    },
    {
      title: '是否必须',
      dataIndex: 'isRequired',
    },
    {
      title: '操作',
      dataIndex: 'action',
    },
  ];

  return (
    <div>
      <Alert type="warning" style={{ marginTop: 8 }}>
        变更单创建顺序依次是“产品市场-产品运维中心/kaleido”，需要先创建产品市场变更单，再基于产品市场变更单创建产品运维中心或kaleido变更单。
      </Alert>
      <TcsTable<OperationToolData> columns={columns} dataSource={data} loading={loading} bordered={'all'} />
    </div>
  );
};

export default OperationTools;
