import { updateApplicationPackageIssueStatus } from '@/common/api/interationManage';
import { TcsForm, TcsFormTextArea, TcsModal } from '@tencent/tcs-component';
import { message } from '@tencent/tea-component';
import React, { useImperativeHandle, useState } from 'react';

const VerifyStatusModal: React.ForwardRefRenderFunction<any, any> = ({ onConfirm }, ref) => {
  const [visible, setVisible] = useState(false);
  const [record, setRecord] = useState<any>();
  const [loading, setLoading] = useState(false);
  const [form] = TcsForm.useForm();

  useImperativeHandle(
    ref,
    () => ({
      show(record) {
        setRecord(record);
        setVisible(true);
      },
    }),
    [],
  );

  function handleOk() {
    const comments = form.getFieldValue('comments');
    setLoading(true);
    updateApplicationPackageIssueStatus({
      IssueID: record.IssueID,
      PackageUUID: record.PackageUUID,
      Status: record.Status,
      Comments: comments,
    })
      .then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          message.success({
            content: '状态更新成功',
          });
          setVisible(false);
          form.resetFields();
          onConfirm();
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }

  function handleCancel() {
    setVisible(false);
    form.resetFields();
  }

  return (
    <TcsModal title="验证备注" visible={visible} onOk={handleOk} onCancel={handleCancel} confirmLoading={loading}>
      <TcsForm form={form}>
        <TcsFormTextArea label="备注" name="comments" fieldProps={{ size: 'full' }} />
      </TcsForm>
    </TcsModal>
  );
};

export default React.forwardRef(VerifyStatusModal);
