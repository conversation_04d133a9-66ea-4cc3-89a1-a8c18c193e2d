/*
 * @Author: lucyfang
 * @Date: 2025-02-13 17:24:01
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-03-17 14:25:46
 * @Description: 请输入注释信息
 */
import { TcsModal } from '@tencent/tcs-component';
import React from 'react';
import { GetTAPDOperationSheet } from '@/common/api/iterationManage.api';
import VerifyExportContent from './VerifyExportContent';
export interface IProps {
  record: any;
  Tops?: GetTAPDOperationSheet;
  OriginTops?: GetTAPDOperationSheet;
  showTops?: boolean;
  showKaleido?: boolean;
  onClose: () => void;
  showDawn?: boolean;
}
const VerifyExportModal: React.FC<IProps> = ({
  record,
  Tops: TopsData,
  OriginTops: OriginTopsData,
  showTops,
  showKaleido,
  onClose,
  showDawn,
}) => (
  <TcsModal title="启动验证出包流程" visible onCancel={onClose} width={1200} footer={<></>}>
    <VerifyExportContent
      record={record}
      Tops={TopsData}
      OriginTops={OriginTopsData}
      showKaleido={showKaleido}
      showTops={showTops}
      showDawn={showDawn}
    />
  </TcsModal>
);

export default VerifyExportModal;
