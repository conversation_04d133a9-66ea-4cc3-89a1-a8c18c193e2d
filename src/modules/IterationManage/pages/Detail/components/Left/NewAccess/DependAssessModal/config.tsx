import React from 'react';
import { ProColumns, TcsButtonGroup } from '@tencent/tcs-component';
import { TagSelect, StatusTip } from '@tencent/tea-component';
import styles from './index.module.less';

export const getColumns = ({
  handleDepend,
  handleTagSelectFocus,
  dependenciesMap,
  dependentOnMap,
  selectLoading,
  tableData,
  selectedValuesMap,
  setSelectedValuesMap,
}): ProColumns[] => {
  const handleTagSelectChange = (record, value, context, type) => {
    setSelectedValuesMap((prev) => {
      const currentSelectedValues = prev[record.ApplicationUUID]?.[type] || [];
      // 检查并添加新的选项
      if (
        context?.option?.value &&
        !currentSelectedValues.some((item) => item.ApplicationUUID === context.option.value)
      ) {
        const text = context.option.text.props ? context.option.text.props.children : context.option.text;
        currentSelectedValues.push({
          ApplicationUUID: context.option.value,
          Application: text,
        });
      }
      // 过滤选中的值
      const updatedValues = currentSelectedValues.filter((item) => value.includes(item.ApplicationUUID));

      return {
        ...prev,
        [record.ApplicationUUID]: {
          ...prev[record.ApplicationUUID],
          [type]: updatedValues,
        },
      };
    });
  };
  const getOptionByValue = (value, options) => options.find((option) => option.value === value);

  return [
    {
      title: '应用',
      dataIndex: 'Application',
      width: '20%',
      search: false,
    },
    {
      title: '本次变更依赖',
      dataIndex: 'DependentApplications',
      width: '30%',
      search: false,
      render: (_, record) => {
        const selectedApps = selectedValuesMap[record.ApplicationUUID]?.dependencies || [];
        const selectedAppUUIDs = selectedApps.map((app) => app.ApplicationUUID);
        const dependencies = dependenciesMap[record.ApplicationUUID] || [];
        const initialOptions = record.DependentApplications.map((app) => ({
          value: app.ApplicationUUID,
          text: app.Application,
          group: 'Dependencies', // 依赖数据分组
        }));
        const combinedOptions = [
          ...tableData
            .filter((item) => item.ApplicationUUID !== record.ApplicationUUID)
            .map((item) => ({
              value: item.ApplicationUUID,
              text: item.Application,
              group: 'TableData', // 表格数据分组
            })),
          ...dependencies.map((item) => ({
            value: item.DependentApplicationUUID,
            text: item.DependentApplication,
            group: 'Dependencies', // 依赖数据分组
          })),
          ...initialOptions,
        ];

        // 过滤掉已经选中的值，避免重复
        const uniqueOptions = Array.from(new Set(combinedOptions.map((option) => option.value))).map((value) =>
          combinedOptions.find((option) => option.value === value),
        );

        return (
          <TagSelect
            placeholder="请选择应用"
            value={selectedAppUUIDs}
            options={uniqueOptions}
            optionFormat={(value) => {
              const option = getOptionByValue(value, uniqueOptions);
              return <span style={{ color: option.group === 'TableData' ? 'inherit' : '#0ABF5B' }}>{option.text}</span>;
            }}
            tips={selectLoading ? <StatusTip.LoadingTip /> : uniqueOptions.length ? undefined : <StatusTip.EmptyTip />}
            onFocus={() => handleTagSelectFocus(record, 'dependencies')}
            onChange={(value, context) => handleTagSelectChange(record, value, context, 'dependencies')}
            optionsOnly
          />
        );
      },
      tooltip: (
        <>
          <p>典型的应用依赖有如下场景:</p>
          <p>1. 新引入对其他应用依赖，例如应用A原先不依赖应用B，但本次进行了重构，开始依赖应用B</p>
          <p> 2. 对dbsql组件中新增的数据库/数据表/字段/配置等有依赖</p>
          <p> 3. 对新增的预设数据有依赖，如对preset组件、yuanapi、dbsql等组件新增的预设数据依赖</p>
          <p> 4. 对其他应用新增的API调用，如前端对后端某新增的API有依赖</p>
        </>
      ),
    },
    {
      title: '本次变更兼容性',
      dataIndex: 'AdaptedApplications',
      width: '30%',
      search: false,
      render: (_, record) => {
        const selectedApps = selectedValuesMap[record.ApplicationUUID]?.dependentOn || [];
        const selectedAppUUIDs = selectedApps.map((app) => app.ApplicationUUID);
        const dependentOn = dependentOnMap[record.ApplicationUUID] || [];
        const initialOptions = record.AdaptedApplications.map((app) => ({
          value: app.ApplicationUUID,
          text: app.Application,
        }));
        const combinedOptions = [
          ...initialOptions,
          ...dependentOn.map((item) => ({
            value: item.ApplicationUUID,
            text: item.Application,
          })),
        ];
        // 过滤掉已经选中的值，避免重复
        const uniqueOptions = Array.from(new Set(combinedOptions.map((option) => option.value))).map((value) =>
          combinedOptions.find((option) => option.value === value),
        );

        return (
          <>
            {selectedAppUUIDs.length > 0 && <div>不兼容，需要适配的应用：</div>}
            <TagSelect
              overlayClassName={styles.tagselect}
              placeholder="不兼容旧版本时选择影响的应用"
              value={selectedAppUUIDs}
              options={uniqueOptions}
              tips={
                selectLoading ? <StatusTip.LoadingTip /> : uniqueOptions.length ? undefined : <StatusTip.EmptyTip />
              }
              onFocus={() => handleTagSelectFocus(record, 'dependentOn')}
              onChange={(value, context) => handleTagSelectChange(record, value, context, 'dependentOn')}
              optionsOnly
            />
          </>
        );
      },
      tooltip: (
        <>
          <p>典型的不兼容的场景：</p>
          <p>1. 已有对外接口输入参数、默认参数、输出数据格式、默认行为等变更（如yunapi/rpc）</p>
          <p> 2. 已有配置默认值发生变更（如影响其他应用的默认行为）</p>
          <p> 3. 已有数据库/数据表变更（如数据表/字段废弃）</p>
          <p> 4. 对外发布的中间件topic废弃等</p>
        </>
      ),
    },
    {
      title: '操作',
      width: '20%',
      dataIndex: 'operation',
      fixed: 'right',
      render: (_, record) => (
        <TcsButtonGroup
          items={[
            {
              text: '谁依赖我',
              onClick: () => handleDepend(record, 'downstream'),
            },
            {
              text: '我依赖谁',
              onClick: () => handleDepend(record, 'upstream'),
            },
          ]}
        />
      ),
    },
  ];
};
