/*
 * @Author: lucyfang
 * @Date: 2025-02-26 15:07:53
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-03-14 14:17:05
 * @Description: 请输入注释信息
 */
import React, { useState, useEffect } from 'react';
import { TcsButton, TcsForm, TcsModal, TcsSpin, TcsTable } from '@tencent/tcs-component';
import { getAppVersion } from '@/common/utils';
import ResulteForm from '../ResultForm';
import {
  ICreateIssueRelatedAppsApi,
  IterationManage,
  GetIssueSolutionTestResultParams,
} from '@/common/api/iterationManage.api';
import { transformFormData } from './utils';

export interface IProps {
  onClose: () => void;
  onConfirm: () => void;
  record: any;
  siteList: ICreateIssueRelatedAppsApi.ISiteInfo[];
  UUID?: string;
  isDisabled?: boolean;
}
const TestResult: React.FC<IProps> = ({ onClose, record, siteList, UUID, isDisabled, onConfirm }) => {
  const [initialData, setInitialData] = React.useState<any>();
  const [formLoading, setFormLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    if (record?.UUID) {
      setFormLoading(true);
      const params: GetIssueSolutionTestResultParams = { IssueSolutionRelUUID: record.UUID, pageSize: 1 };
      if (isDisabled && UUID) params.UUID = UUID;
      IterationManage.GetIssueSolutionTestResult(params)
        .then((res) => {
          setInitialData(res?.GetIssueSolutionTestResult);
        })
        .finally(() => {
          setFormLoading(false);
        });
    }
  }, [record]);
  const [form] = TcsForm.useForm();
  // 新增：监听 initialData 的变化
  useEffect(() => {
    if (initialData) {
    }
  }, [initialData]); // 新增：依赖 initialData

  const hanleConfirm = () => {
    form.validateFields()?.then((values) => {
      const formValue = transformFormData(values);
      const params = {
        ...formValue,
        ValidatedPackages:
          record?.IssueAppRel?.map((item) => ({
            PackageUUID: item?.LatestRegisteredApplicationPackage?.UUID,
          })) || [],
        IssueSolutionRelUUID: record?.UUID,
      };
      setLoading(true);
      IterationManage.CreateIssueSolutionTestResult(params)
        .then(() => {
          onConfirm();
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };
  const getColumns = () => [
    {
      dataIndex: 'ApplicationName',
      title: '应用',
      width: '15%',
      linkable: true,
      disable: true,
      copyable: true,
      linkProps: {
        target: 'blank',
        linkUrl: (text, record) =>
          `/page/develop_center_next/app_manage/product?app_id=${record?.ApplicationBranch?.Application?.UUID}&app_name=${record.ApplicationName}&select_branch=${record.ApplicationBranch?.UUID}`,
      },
      fixed: 'left',
    },
    {
      dataIndex: 'ApplicationBranch.BranchName',
      title: '应用版本',
      width: '10%',
    },
    {
      dataIndex: ['ProductVersionInfo', 'ProductInfo', 'Name'],
      title: '产品名称',
      width: '10%',
    },
    {
      dataIndex: 'ProductVersion',
      title: '产品版本',
      width: '10%',
    },
    {
      dataIndex: 'LatestRegisteredApplicationPackage',
      title: '提测制品版本',
      width: '15%',
      render(_, record) {
        const { LatestRegisteredApplicationPackage } = record;
        const text = LatestRegisteredApplicationPackage ? getAppVersion(LatestRegisteredApplicationPackage) : '-';
        return <span>{text}</span>;
      },
    },
    {
      dataIndex: 'ReleaseApplicationPackageApplicationVersion',
      title: '验证通过版本',
      width: '15%',
      render(_, record) {
        const { ReleaseApplicationPackage } = record;
        const text = ReleaseApplicationPackage ? getAppVersion(ReleaseApplicationPackage) : '-';
        return <span>{text}</span>;
      },
    },
    {
      dataIndex: 'ValidatedPackagesPackageApplicationVersion',
      title: '提交通过版本',
      width: '15%',
      render(_, record) {
        const { ReleaseApplicationPackage } = record;
        const releaseText = ReleaseApplicationPackage ? getAppVersion(ReleaseApplicationPackage) : '';
        if (isDisabled && initialData) {
          const validatedPackage =
            initialData?.ValidatedPackages?.filter(
              (item) => item?.ApplicationBranchUUID === record?.ApplicationBranchUUID,
            )?.[0] || {};
          const text = getAppVersion(validatedPackage) || '';
          return text ? <span style={{ color: text != releaseText ? 'red' : 'inherit' }}>{text}</span> : '-';
        }
        const { LatestRegisteredApplicationPackage } = record;
        const text = LatestRegisteredApplicationPackage ? getAppVersion(LatestRegisteredApplicationPackage) : '';
        return text ? <span style={{ color: text != releaseText ? 'red' : 'inherit' }}>{text}</span> : '-';
      },
    },
  ];
  return (
    <TcsModal
      bodyStyle={{ maxHeight: 500, overflowY: 'scroll' }}
      title="填写验证结论"
      visible
      onCancel={onClose}
      width={1200}
      footer={
        isDisabled ? null : (
          <>
            <TcsButton type="primary" onClick={hanleConfirm} loading={loading}>
              确定
            </TcsButton>
            <TcsButton onClick={onClose}>取消</TcsButton>
          </>
        )
      }
    >
      <TcsSpin spinning={formLoading}>
        <TcsTable
          scroll={{ x: 1200 }}
          headerTitle="应用制品验证情况"
          columns={getColumns()}
          dataSource={record.IssueAppRel || []}
          pagination={{ pageSize: 10 }}
        />
        <ResulteForm
          isDisabled={isDisabled}
          initialData={initialData}
          record={record}
          dataSource={record.IssueAppRel || []}
          siteList={siteList}
          form={form}
        />
      </TcsSpin>
    </TcsModal>
  );
};
export default TestResult;
