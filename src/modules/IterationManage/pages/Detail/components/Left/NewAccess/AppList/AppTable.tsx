/*
 * @Author: lucyfang
 * @Date: 2025-05-29 10:35:42
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-06-05 17:12:51
 * @Description: 请输入注释信息
 */
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { TcsButton, TcsTable, TcsColumns } from '@tencent/tcs-component';
import { Checkbox, message } from '@tencent/tea-component';
import { IterationManage } from '@/common/api/iterationManage.api';

import AddRefAppModal from '../AddRefAppModal';
import VersionChangeModal from '../VersionChangeModal';
import { getUrlParams } from '@/common/utils';
interface AppData {
  UUID?: string;
  ApplicationName: string;
  ApplicationVersion: string;
  Architecture: string;
  ProductName: string;
  ProductVersion?: string;
  ProductVersionUUID?: string;
  ApplicationBranchUUID?: string;
}

interface IProps {
  tapdInfo: any;
}

const AppTable: React.FC<IProps> = ({ tapdInfo }) => {
  console.log('tapdInfo', tapdInfo);
  const [dataSource, setDataSource] = useState<AppData[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  // 复选框状态
  const [dbsqlEvaluated, setDbsqlEvaluated] = useState(false);
  // 修改产品版本模态框状态
  const [versionChangeVisible, setVersionChangeVisible] = useState(false);
  const [versionChangeRecord, setVersionChangeRecord] = useState<any>({});
  const urlParams = getUrlParams();
  // issueType: bug | story
  const isIssueBug = urlParams.obj_type === 'bug';

  // 提取公共变量，避免重复定义
  const { workspace_id: workspaceId, obj_type: objType, obj_id: objId } = urlParams;

  // 构建TapdUrl - 在多个函数中使用
  const tapdUrl = useMemo(
    () => `http://tapd.oa.com/${workspaceId}/${objType === 'bug' ? 'bugtrace/bugs' : 'prong/stories'}/view/${objId}`,
    [workspaceId, objType, objId],
  );

  // 判断是否为特定版本
  const isSpecialVersion = useMemo(() => {
    const specialVersions = ['TCE3.8.0', 'TCE3.6.0', 'TCS2.3.0'];
    const solutionVersionCode = tapdInfo?.SolutionVersion || '';
    return specialVersions.includes(solutionVersionCode);
  }, [tapdInfo?.SolutionVersion]);

  // 判断是否为TCE3.10.0版本
  const isTCE3100Version = useMemo(() => {
    const solutionVersionCode = tapdInfo?.SolutionVersion || '';
    return solutionVersionCode === 'TCE3.10.0';
  }, [tapdInfo?.SolutionVersion]);

  // 公共数据转换函数 - 避免重复代码
  const transformTapdData = useCallback(
    (componentList: any[]): AppData[] =>
      (componentList || []).map((item: any) => ({
        ApplicationName: item.ApplicationName || '',
        ApplicationVersion: item.Version || '',
        Architecture: item.Arch || '',
        ProductName: '',
      })),
    [],
  );

  const transformIssueData = useCallback(
    (issueAppRel: any[]): AppData[] =>
      (issueAppRel || []).map((item: any) => ({
        UUID: item.UUID || '',
        ApplicationName: item.ApplicationName || '',
        ApplicationVersion: item?.ApplicationBranch?.BranchName || '',
        Architecture: item.Arch || '',
        ProductName: item?.ProductVersionInfo?.ProductInfo?.Name || '',
        ProductVersion: item?.ProductVersion || '',
        ProductVersionUUID: item?.ProductVersionUUID || '',
        ApplicationBranchUUID: item?.ApplicationBranchUUID || '',
      })),
    [],
  );

  // 公共去重合并函数
  const mergeDataWithPriority = useCallback((tapdData: AppData[], issueData: AppData[]): AppData[] => {
    const dataMap = new Map<string, AppData>();

    // 先添加tapdData
    tapdData.forEach((item) => {
      dataMap.set(item.ApplicationName, item);
    });

    // 再添加issueData，如果ApplicationName相同则覆盖（以issueData为主）
    issueData.forEach((item) => {
      dataMap.set(item.ApplicationName, item);
    });

    // 转换为数组
    const combinedData: AppData[] = [];
    dataMap.forEach((value) => {
      combinedData.push(value);
    });
    console.log('combinedData:', combinedData);

    return combinedData;
  }, []);

  // 获取表格数据的函数
  const fetchTableData = useCallback(async () => {
    if (!tapdInfo?.SolutionVersion) {
      return;
    }

    try {
      setLoading(true);

      if (isIssueBug) {
        // 缺陷的表格数据逻辑
        if (isSpecialVersion) {
          // 特定版本 (TCE3.8.0, TCE3.6.0, TCS2.3.0) 使用 GetComponentListForTAPD
          if (!tapdInfo?.TAPDUrl) {
            message.warning({ content: '缺少TAPD URL，无法获取数据' });
            return;
          }

          const res = await IterationManage.GetComponentListForTAPD({
            TAPD: tapdInfo?.TAPDUrl,
          });

          if (res?.ComponentList?.length) {
            const tableData = transformTapdData(res.ComponentList);
            setDataSource(tableData);
            message.success({ content: `成功获取 ${tableData.length} 个应用数据 (GetComponentListForTAPD)` });
          } else {
            setDataSource([]);
            message.warning({ content: '未获取到应用数据' });
          }
        } else if (isTCE3100Version) {
          // TCE3.10.0 使用 GetComponentListForTAPD + ListIssueSolutionRelsWithDetail 并集
          const [tapdRes, issueRes] = await Promise.all([
            IterationManage.GetComponentListForTAPD({ TAPD: tapdInfo?.TAPDUrl || '' }),
            IterationManage.ListIssueSolutionRelsWithDetail({
              TapdUrl: tapdUrl,
            }),
          ]);

          const tapdData = transformTapdData(tapdRes?.ComponentList || []);
          const issueData = transformIssueData(issueRes?.data?.[0]?.IssueAppRel || []);
          const combinedData = mergeDataWithPriority(tapdData, issueData);

          setDataSource(combinedData);
          message.success({
            content: `成功获取 ${combinedData.length} 个应用数据 (TCE3.10.0并集: TAPD ${tapdData.length}个 + Issue ${issueData.length}个)`,
          });
        } else {
          // 其余情况使用 ListIssueSolutionRelsWithDetail
          const res = await IterationManage.ListIssueSolutionRelsWithDetail({
            TapdUrl: tapdUrl,
          });

          const tableData = transformIssueData(res?.data?.[0]?.IssueAppRel || []);
          console.log('tableData', tableData);
          setDataSource(tableData);
          message.success({ content: `成功获取 ${tableData.length} 个应用数据 (ListIssueSolutionRelsWithDetail)` });
        }
      } else {
        // 需求的表格数据逻辑：直接使用 ListIssueSolutionRelsWithDetail
        const res = await IterationManage.ListIssueSolutionRelsWithDetail({
          TapdUrl: tapdUrl,
        });

        const tableData = transformIssueData(res?.data?.[0]?.IssueAppRel || []);
        setDataSource(tableData);
        message.success({ content: `成功获取 ${tableData.length} 个应用数据 (需求-ListIssueSolutionRelsWithDetail)` });
      }
    } catch (error) {
      console.error('获取表格数据失败:', error);
      message.error({ content: '获取表格数据失败，请重试' });
      setDataSource([]);
    } finally {
      setLoading(false);
    }
  }, [
    isIssueBug,
    isSpecialVersion,
    isTCE3100Version,
    tapdInfo,
    tapdUrl,
    transformTapdData,
    transformIssueData,
    mergeDataWithPriority,
  ]);

  // 当数据变化时，自动获取表格数据
  useEffect(() => {
    fetchTableData();
  }, [fetchTableData]);

  const handleAddApp = () => {
    setModalVisible(true);
  };

  const handleModalClose = () => {
    setModalVisible(false);
  };

  const handleModalConfirm = async (selectedApps: any[]) => {
    console.log('添加应用:', selectedApps);
    try {
      if (isIssueBug) {
        // 缺陷的添加应用逻辑
        if (isSpecialVersion) {
          // 特定版本使用 AddTAPDRelatedComponents
          await addBugSpecialVersionApps(selectedApps);
        } else {
          // 其他版本使用 CreateIssueRelatedApps
          await addBugNormalVersionApps(selectedApps);
        }
      } else {
        // 需求的添加应用逻辑：直接使用 CreateIssueRelatedApps
        await addStoryApps(selectedApps);
      }

      // 添加成功后刷新表格数据
      await fetchTableData();
      setModalVisible(false);
    } catch (error) {
      console.error('添加应用失败:', error);
      message.error({ content: '添加应用失败，请重试' });
    }
  };

  // 缺陷特定版本添加应用
  const addBugSpecialVersionApps = async (selectedApps: any[]) => {
    console.log('缺陷特定版本 - 调用AddTAPDRelatedComponents接口:', selectedApps);

    // 调用 AddTAPDRelatedComponents 接口
    const res = await IterationManage.AddTAPDRelatedComponents({
      TapdURL: tapdInfo?.TAPDUrl || '',
      ComponentNames: selectedApps.map((app) => app.ApplicationName),
    });

    if (res?.Error) {
      throw new Error(res.Error.Message || '添加应用失败');
    }

    message.success({ content: `成功添加 ${selectedApps.length} 个应用 (AddTAPDRelatedComponents)` });
  };

  // 缺陷普通版本添加应用
  const addBugNormalVersionApps = async (selectedApps: any[]) => {
    console.log('缺陷普通版本 - 调用CreateIssueRelatedApps接口:', selectedApps);

    // TODO: 调用 CreateIssueRelatedApps 接口
    await IterationManage.CreateIssueRelatedApps({ IssueAppRel: selectedApps });

    // 模拟API调用
    message.success({ content: `成功添加 ${selectedApps.length} 个应用` });
  };

  // 需求添加应用
  const addStoryApps = async (selectedApps: any[]) => {
    console.log('需求 - 调用CreateIssueRelatedApps接口:', selectedApps);

    // TODO: 调用 CreateIssueRelatedApps 接口
    // const res = await IterationManage.CreateIssueRelatedApps({
    //   /* 参数 */
    // });

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000));
    message.success({ content: `成功添加 ${selectedApps.length} 个应用 (需求-CreateIssueRelatedApps)` });
  };

  // 删除应用处理函数
  const handleDeleteApp = async (record: AppData) => {
    try {
      if (isIssueBug) {
        // 缺陷的删除应用逻辑
        if (isSpecialVersion) {
          // 特定版本使用 DeleteTAPDRelatedComponents
          await deleteBugSpecialVersionApp(record);
        } else {
          // 其他版本使用 DeleteIssueRelatedApp
          await deleteBugNormalVersionApp(record);
        }
      } else {
        // 需求的删除应用逻辑：直接使用 DeleteIssueRelatedApp
        await deleteStoryApp(record);
      }

      // 删除成功后刷新表格数据
      await fetchTableData();
    } catch (error) {
      console.error('删除应用失败:', error);
      message.error({ content: '删除应用失败，请重试' });
    }
  };

  // 缺陷特定版本删除应用
  const deleteBugSpecialVersionApp = async (record: AppData) => {
    console.log('缺陷特定版本 - 调用DeleteTAPDRelatedComponents接口:', record);

    // 调用 DeleteTAPDRelatedComponents 接口
    const res = await IterationManage.DeleteTAPDRelatedComponents({
      TapdURL: data?.TapdUrl || '',
      ComponentNames: [record.ApplicationName],
    });

    if (res?.Error) {
      throw new Error(res.Error.Message || '删除应用失败');
    }

    message.success({ content: `成功删除应用 ${record.ApplicationName} (DeleteTAPDRelatedComponents)` });
  };

  // 缺陷普通版本删除应用
  const deleteBugNormalVersionApp = async (record: AppData) => {
    console.log('缺陷普通版本 - 调用DeleteIssueRelatedApp接口:', record);

    // TODO: 调用 DeleteIssueRelatedApp 接口
    // const res = await IterationManage.DeleteIssueRelatedApp({
    //   /* 参数 */
    // });

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000));
    message.success({ content: `成功删除应用 ${record.ApplicationName} (DeleteIssueRelatedApp)` });
  };

  // 需求删除应用
  const deleteStoryApp = async (record: AppData) => {
    console.log('需求 - 调用DeleteIssueRelatedApp接口:', record);

    // TODO: 调用 DeleteIssueRelatedApp 接口
    // const res = await IterationManage.DeleteIssueRelatedApp({
    //   /* 参数 */
    // });

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000));
    message.success({ content: `成功删除应用 ${record.ApplicationName} (需求-DeleteIssueRelatedApp)` });
  };

  // 修改产品版本处理函数
  const handleVersionChange = (record: AppData) => {
    setVersionChangeRecord(record);
    setVersionChangeVisible(true);
  };

  const handleVersionChangeConfirm = () => {
    setVersionChangeVisible(false);
    // 刷新表格数据
    fetchTableData();
  };

  const handleVersionChangeCancel = () => {
    setVersionChangeVisible(false);
  };

  const columns: TcsColumns[] = [
    {
      title: '应用名称',
      dataIndex: 'ApplicationName',
      width: '20%',
    },
    {
      title: '应用版本',
      dataIndex: 'ApplicationVersion',
      width: '15%',
    },
    {
      title: '应用架构',
      dataIndex: 'Architecture',
      width: '15%',
    },
    {
      title: '产品名称',
      dataIndex: 'ProductName',
      width: '20%',
    },
    {
      title: '产品版本',
      dataIndex: 'ProductVersion',
      width: '20%',
      render: (text, record) => {
        if (text) {
          return (
            <>
              {text}
              {/* 只有非特殊版本才显示修改按钮 */}
              {!isSpecialVersion && (
                <TcsButton
                  icon="pencil"
                  type="link"
                  onClick={() => handleVersionChange(record)}
                  style={{ marginLeft: 8 }}
                />
              )}
            </>
          );
        }
        return '-';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: '30%',
      render: (_, record) => (
        <>
          <TcsButton type="link" onClick={() => handleDeleteApp(record)}>
            删除
          </TcsButton>
        </>
      ),
    },
  ];

  return (
    <>
      {/* 评估表单 */}
      <div style={{ marginBottom: 16 }}>
        <Checkbox
          display="block"
          value={dbsqlEvaluated}
          onChange={(checked) => setDbsqlEvaluated(checked)}
          style={{ marginBottom: 8 }}
        >
          已确定评估完整依赖的 dbsql 应用
        </Checkbox>
      </div>

      <TcsButton type="primary" onClick={handleAddApp} style={{ marginBottom: 16 }}>
        添加应用
      </TcsButton>
      <TcsTable
        columns={columns}
        rowKey={(record) => record.UUID || record.ApplicationName}
        dataSource={dataSource}
        loading={loading}
      />
      {modalVisible && (
        <AddRefAppModal onClose={handleModalClose} onConfirm={handleModalConfirm} isIssueBug={isIssueBug} />
      )}
      {versionChangeVisible && (
        <VersionChangeModal
          data={tapdInfo}
          record={versionChangeRecord}
          onConfirm={handleVersionChangeConfirm}
          onCancel={handleVersionChangeCancel}
        />
      )}
    </>
  );
};

export default AppTable;
