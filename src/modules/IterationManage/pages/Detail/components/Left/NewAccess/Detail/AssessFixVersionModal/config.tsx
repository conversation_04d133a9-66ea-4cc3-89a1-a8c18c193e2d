/*
 * @Author: fang<PERSON><PERSON><PERSON>
 * @Date: 2025-05-01 10:00:00
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-06-03 14:51:40
 * @Description: 评估待修复版本弹窗表格配置
 */
import React from 'react';
import { Checkbox } from '@tencent/tea-component';

interface GetColumnsParams {
  shouldBeDefaultSelected: (version: string) => boolean;
  selectedArchs: Record<string, string[]>;
  selectedVersions: any[];
  onArchChange: (version: string, selectedArchs: string[]) => void;
}

export const getColumns = ({
  shouldBeDefaultSelected,
  selectedArchs,
  selectedVersions,
  onArchChange,
}: GetColumnsParams) => [
  {
    title: '解决方案版本',
    dataIndex: 'SolutionVersionName',
    width: '50%',
    render: (_: any, record: any) => {
      const versionName = record.SolutionVersionName;
      const isDefaultSelected = shouldBeDefaultSelected(versionName);
      return (
        <span>
          {versionName}
          {isDefaultSelected && (
            <span style={{ color: '#1890ff', marginLeft: 8, fontSize: '12px' }}>(当前及以上版本，必选)</span>
          )}
        </span>
      );
    },
  },
  {
    title: '复制Tapd单策略',
    dataIndex: 'Archs',
    width: '50%',
    render: (_, record) => {
      // 根据当前记录的Archs数据动态生成选项
      const availableArchs = record.Archs || [];
      const versionName = record.SolutionVersionName;
      const isDefaultSelected = shouldBeDefaultSelected(versionName);
      const currentSelectedArchs = selectedArchs[versionName] || [];

      // 检查当前版本是否被选中
      const isVersionSelected = selectedVersions.some((v) => v.SolutionVersionName === versionName);

      return (
        <Checkbox.Group
          value={isVersionSelected ? currentSelectedArchs : []}
          onChange={(values) => onArchChange(versionName, values)}
        >
          {availableArchs.map((arch: string) => (
            <Checkbox key={arch} name={arch} disabled={isDefaultSelected}>
              {arch.toUpperCase()}
            </Checkbox>
          ))}
        </Checkbox.Group>
      );
    },
  },
];
