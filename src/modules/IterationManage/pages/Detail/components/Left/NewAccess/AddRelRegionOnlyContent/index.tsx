import React, { useEffect, useMemo, useState } from 'react';
import {
  Alert,
  Bubble,
  DropdownBox,
  List,
  Popover,
  SearchBox,
  Switch,
  Text,
  Button,
  Checkbox,
} from '@tencent/tea-component';
import { DefectManagementRoutePath } from '@/common/routePath';
import { IDeliveryInfo } from '@/common/api/deliveryManage';
import styles from './AddRelRegion.module.less';
import { TcsCard, ProColumns, TcsTable, TcsSpace } from '@tencent/tcs-component';
import { IListProjectClientSiteApi, IterationManage } from '@/common/api/iterationManage.api';

export interface IProps {
  deliveryInfo?: IDeliveryInfo;
  disabledAllBeforeSelected?: boolean;
  maxHeight?: number;
}

const AddRelRegionOnlyContent: React.FC<IProps> = ({ deliveryInfo, maxHeight = 550 }) => {
  const [loading, setLoading] = useState(false);
  const [originalClientList, setOriginalClientList] = useState<IListProjectClientSiteApi.IClientSites[]>([
    {
      ClientName: '中国民航信息集团有限公司',
      ClientUUID: 'c9b3166fdda84caeb94067cbf1263575',
      ProjectSite: [
        {
          BugfixType: 'bugfix',
          DawnPlan: '',
          ForceDawnPlan: '',
          MultipleArch: '',
          OperationTool: '产品市场',
          PushPackageType: 'Antool',
          SiteArch: '',
          SiteAzConfig: null,
          SiteName: '北京-后沙峪-结算公司机房',
          SitePlanSolutionVersionUUID: 'bab5f66fea5c4ff1912262342a7973c6',
          SiteType: 'next',
          SiteUUID: '7f57661ef72f4568804d33c617b96f14',
          SolutionVersionArchs: null,
        },
        {
          BugfixType: 'patch',
          DawnPlan: '',
          ForceDawnPlan: '',
          MultipleArch: '',
          OperationTool: '产品市场',
          PushPackageType: 'Antool',
          SiteArch: '',
          SiteAzConfig: null,
          SiteName: '分布式云广州棠下1',
          SitePlanSolutionVersionUUID: 'bab5f66fea5c4ff1912262342a7973c61',
          SiteType: 'next',
          SiteUUID: 'b19f8094dfa34ba59a1f8867fe8ab1091',
          SolutionVersionArchs: null,
        },
      ],
    },
    {
      ClientName: '南方电网',
      ClientUUID: '9405916f191942afa22bf32cc7991e75',
      ProjectSite: [
        {
          BugfixType: 'patch',
          DawnPlan: '',
          ForceDawnPlan: '',
          MultipleArch: '',
          OperationTool: '产品市场',
          PushPackageType: 'Antool',
          SiteArch: '',
          SiteAzConfig: null,
          SiteName: '分布式云广州棠下',
          SitePlanSolutionVersionUUID: 'bab5f66fea5c4ff1912262342a7973c6',
          SiteType: 'next',
          SiteUUID: 'b19f8094dfa34ba59a1f8867fe8ab109',
          SolutionVersionArchs: null,
        },
      ],
    },
    {
      ClientName: '深圳证券通信有限公司',
      ClientUUID: '956defea0091489d8747f9b8c22a08e9',
      ProjectSite: [
        {
          BugfixType: 'patch',
          DawnPlan: '',
          ForceDawnPlan: '',
          MultipleArch: '',
          OperationTool: '产品市场',
          PushPackageType: 'Antool',
          SiteArch: '',
          SiteAzConfig: null,
          SiteName: '行业云',
          SitePlanSolutionVersionUUID: 'bab5f66fea5c4ff1912262342a7973c6',
          SiteType: 'next',
          SiteUUID: '2ef63b8b546a403781d48203e3ecd1e4',
          SolutionVersionArchs: null,
        },
      ],
    },
  ]);
  const [clientList, setClientList] = useState<IListProjectClientSiteApi.IClientSites[]>(originalClientList);
  const [checkedClientSites, setCheckedClientSites] = useState<Record<string, string[]>>({});
  const [releatedSites, setReleatedSites] = useState<any[]>([]);
  const [searchValue, setSearchValue] = useState<{ clientName?: string; siteName?: string }>({});
  const [isFullSites, setIsFullSites] = useState(false);

  const handleSearch = (value: string) => {
    if (!value) {
      setClientList(originalClientList);
      return;
    }

    const filteredClients = originalClientList.filter((client) => {
      const clientNameMatch = client.ClientName.includes(value);
      const siteMatch = client.ProjectSite?.some((site) => site.SiteName.includes(value));
      return clientNameMatch || siteMatch;
    });

    setClientList(filteredClients);
  };

  const handleClientCheck = (clientUUID: string, checked: boolean) => {
    const client = clientList.find((c) => c.ClientUUID === clientUUID);
    if (!client) return;

    const siteUUIDs = client.ProjectSite?.map((site) => site.SiteUUID) || [];
    setCheckedClientSites((prev) => {
      const newState = { ...prev };
      if (checked) {
        newState[clientUUID] = siteUUIDs;
      } else {
        delete newState[clientUUID];
      }
      return newState;
    });
  };

  const handleSiteCheck = (clientUUID: string, siteUUID: string, checked: boolean) => {
    setCheckedClientSites((prev) => {
      const newState = { ...prev };
      if (!newState[clientUUID]) {
        newState[clientUUID] = [];
      }
      if (checked) {
        newState[clientUUID].push(siteUUID);
      } else {
        newState[clientUUID] = newState[clientUUID].filter((id) => id !== siteUUID);
      }
      return newState;
    });
  };

  const handleSelectAll = () => {
    const newCheckedState: Record<string, string[]> = {};

    clientList.forEach((client) => {
      if (client.ClientUUID && client.ProjectSite) {
        newCheckedState[client.ClientUUID] = client.ProjectSite.map((site) => site.SiteUUID);
      }
    });

    setCheckedClientSites(newCheckedState);
  };

  const handleInvertSelection = () => {
    const newCheckedState: Record<string, string[]> = {};

    clientList.forEach((client) => {
      if (client.ClientUUID && client.ProjectSite) {
        const currentSelected = checkedClientSites[client.ClientUUID] || [];
        const allSiteUUIDs = client.ProjectSite.map((site) => site.SiteUUID);

        newCheckedState[client.ClientUUID] = allSiteUUIDs.filter((uuid) => !currentSelected.includes(uuid));
      }
    });

    setCheckedClientSites(newCheckedState);
  };

  const columns: ProColumns[] = [
    {
      title: '客户名称',
      dataIndex: 'ClientName',
      mergeRowSpan: true,
      render: (text, record) => {
        const clientUUID = clientList.find((c) => c.ClientName === text)?.ClientUUID;
        if (!clientUUID) return text;

        const isChecked =
          checkedClientSites[clientUUID]?.length ===
          clientList.find((c) => c.ClientUUID === clientUUID)?.ProjectSite?.length;
        const isIndeterminate = checkedClientSites[clientUUID]?.length > 0 && !isChecked;

        return (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Checkbox
              value={isChecked}
              indeterminate={isIndeterminate}
              onChange={(checked) => handleClientCheck(clientUUID, checked)}
            />
            <span style={{ marginLeft: 8 }}>{text}</span>
          </div>
        );
      },
    },
    {
      title: '局点名称',
      dataIndex: ['ProjectSite', 'SiteName'],
      render: (text, record) => {
        const clientUUID = clientList.find((c) => c.ClientName === record.ClientName)?.ClientUUID;
        if (!clientUUID) return text;

        const isChecked = checkedClientSites[clientUUID]?.includes(record.ProjectSite.SiteUUID);

        return (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Checkbox
              value={isChecked}
              onChange={(checked) => handleSiteCheck(clientUUID, record.ProjectSite.SiteUUID, checked)}
            />
            <span style={{ marginLeft: 8 }}>{text}</span>
          </div>
        );
      },
    },
    {
      title: '局点andonID',
      dataIndex: ['ProjectSite', 'SiteUUID'],
    },
    {
      title: '局点应用架构',
      dataIndex: ['ProjectSite', 'SiteArch'],
    },
    {
      title: '局点缺陷修复方式',
      dataIndex: ['ProjectSite', 'BugfixType'],
    },
    {
      title: '变更工具',
      dataIndex: ['ProjectSite', 'OperationTool'],
    },
    {
      title: '局点状态',
      dataIndex: ['ProjectSite', 'SiteType'],
    },
  ];

  const dataSource = useMemo(
    () =>
      clientList.flatMap(
        (client) =>
          client.ProjectSite?.map((site) => ({
            ClientName: client.ClientName,
            ProjectSite: site,
          })) || [],
      ),
    [clientList],
  );

  return (
    <TcsCard loading={loading}>
      <Alert
        type="warning"
        extra={
          releatedSites.length > 0 && (
            <Popover
              trigger={'click'}
              placement="bottom"
              overlay={
                <DropdownBox style={{ maxHeight: 200, overflowY: 'auto' }}>
                  <List type="option">
                    {(releatedSites || []).map((item) => (
                      <List.Item
                        key={item?.SiteUUID}
                        onClick={() => {
                          window.open(
                            `${DefectManagementRoutePath.DELIVERY_DETAIL_PAGE}?uuid=${item?.DeliveryID}`,
                            '_blank',
                          );
                        }}
                      >
                        {item?.SiteName}
                      </List.Item>
                    ))}
                  </List>
                </DropdownBox>
              }
            >
              <a target="_blank" style={{ textDecoration: 'underline', color: '#006eff' }}>
                查看关联交付单已出包局点
              </a>
            </Popover>
          )
        }
      >
        <div>可用局点状态：此处仅支持处于交付中和售后的局点</div>
        <div>
          迁移局点说明：当前极光Next的局点分为两类，一类是Next局点，另一类是从2.0迁移至Next的局点。对于后者，我们会在局点名称后添加【迁移】标识以示区分。
        </div>
        <div>
          缺陷修复方式说明：当前极光Next的缺陷修复方式分为两类，分别是bugfix和patch,
          我们会在局点名称后面添加【bugfix】或 【patch】来以示区分。
        </div>
        <div />
      </Alert>
      <div style={{ marginBottom: 5, display: 'flex', justifyContent: 'space-between', flexDirection: 'row-reverse' }}>
        <SearchBox size="l" onSearch={handleSearch} placeholder="可输入客户名称/局点名称进行搜索" />
      </div>

      <div style={{ maxHeight, overflow: 'auto' }}>
        <TcsTable
          cardBordered
          columns={columns}
          dataSource={dataSource}
          bordered={'all'}
          rowKey={(record) => record.ProjectSite.SiteUUID}
          headerTitle={
            <TcsSpace>
              <Button type="weak" onClick={handleSelectAll}>
                全选
              </Button>
              <Button type="weak" onClick={handleInvertSelection}>
                反选
              </Button>
            </TcsSpace>
          }
          toolBarRender={() => (
            <>
              <Bubble title="打开表示查看全部可交付局点，关闭表示进行精准过滤，只查询出过指定应用架构组合的可交付局点">
                <Switch value={isFullSites} onChange={(value) => setIsFullSites(value)}>
                  查看全部局点
                </Switch>
                <div>
                  {isFullSites && <Text theme="danger">请确认好选择的局点的出包架构与局点现场的架构信息是否匹配</Text>}
                </div>
              </Bubble>
            </>
          )}
        />
      </div>
    </TcsCard>
  );
};

export default AddRelRegionOnlyContent;
