import React, { useEffect, useState, useMemo } from 'react';
import { TcsTabs, TcsButton, TcsSpin } from '@tencent/tcs-component';
import { Modal } from '@tencent/tea-component';
import Operation from './Operation';
import Detail from './Detail';
import { IPropsBug } from '@/common/api/interationManage';
import { IterationManage } from '@/common/api/iterationManage.api';
import { useParams } from 'react-router-dom';
import { getUrlParams, isAdmin } from '@/common/utils';
import { IssueTypeConfig } from '@/modules/IterationManage/config';
import AssessFixVersionModal from './Detail/AssessFixVersionModal';
export interface IProps {
  data: IPropsBug;
  id?: string;
}

const NewAssess: React.FC<IProps> = ({ data }) => {
  const { pagePath } = useParams<{ pagePath: 'story' | 'defect' }>();
  console.log('data', data);
  console.log('pagePath', pagePath);
  const issueType = IssueTypeConfig[pagePath]; // 动态获取issueType
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalHandle, setModalHandle] = useState<any>(null);
  const [isAssessFixVersionModal, setIsAssessFixVersionModal] = useState(false);
  const [isStarted, setIsStarted] = useState(false);
  const [record, setRecord] = useState<any[]>([]);
  const [tapdInfo, setTapdInfo] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const urlParams = getUrlParams();
  const workspaceId = urlParams.workspace_id;
  const objId = urlParams.obj_id;
  const objType = urlParams.obj_type;
  console.log(data, 'data');
  // 判断是否为管理员或负责人（与Access.tsx一致）
  const isManager = useMemo(
    () =>
      data?.DevOwners?.includes((window as any).jiguang_username) ||
      data?.Owner?.includes((window as any).jiguang_username) ||
      data?.TestOwners?.includes((window as any).jiguang_username) ||
      isAdmin(),
    [data],
  );

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 1140 && !isModalOpen) {
        setIsModalOpen(true);
        const handle = Modal.show({
          caption: '提示',
          children: '当前窗口过小，请在大屏幕上操作',
          disableCloseIcon: true,
        });
        setModalHandle(handle);
      } else if (window.innerWidth >= 1140 && isModalOpen) {
        modalHandle?.destroy();
        setIsModalOpen(false);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      modalHandle?.destroy();
    };
  }, [isModalOpen, modalHandle]);

  // 获取tapd单数据
  useEffect(() => {
    if (objId && objType && workspaceId) {
      setLoading(true);
      IterationManage.GetTAPDContent({
        TapdURL: `http://tapd.oa.com/${workspaceId}/${
          objType === 'bug' ? 'bugtrace/bugs' : 'prong/stories'
        }/view/${objId}`,
      })
        .then((res) => {
          setTapdInfo(res?.Content);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [objId, objType, workspaceId]);

  const handleStart = () => {
    // 打开评估待修复版本弹窗
    setIsAssessFixVersionModal(true);
  };
  // 弹窗关闭后的回调
  const handleModalClose = () => {
    // 如果需要在弹窗关闭后执行某些操作，可以在这里添加
    // setIsStarted(true);
    setIsAssessFixVersionModal(false);
  };
  const handleModalConfirm = () => {
    setIsStarted(true);
    setIsAssessFixVersionModal(false);
  };

  return (
    <TcsSpin spinning={loading}>
      <div style={{ background: 'white', paddingTop: 10, minHeight: '100vh' }}>
        {!isStarted ? (
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
            <TcsButton type="primary" onClick={handleStart}>
              启动评估
            </TcsButton>
          </div>
        ) : (
          <TcsTabs>
            <TcsTabs.TabPane id="operation" key="operation" tab="操作">
              <Operation tapdInfo={tapdInfo} pagePath={pagePath} IssueID={data.IssueID} isManager={isManager} />
            </TcsTabs.TabPane>
            <TcsTabs.TabPane id="detail" key="detail" tab="详情">
              <Detail isManager={isManager} pagePath={pagePath} IssueID={data.IssueID} data={data} />
            </TcsTabs.TabPane>
          </TcsTabs>
        )}

        {/* 评估待修复版本弹窗 */}
        <AssessFixVersionModal
          visible={isAssessFixVersionModal}
          onClose={handleModalClose}
          tapdInfo={tapdInfo}
          onConfirm={handleModalConfirm}
        />
      </div>
    </TcsSpin>
  );
};

export default NewAssess;
