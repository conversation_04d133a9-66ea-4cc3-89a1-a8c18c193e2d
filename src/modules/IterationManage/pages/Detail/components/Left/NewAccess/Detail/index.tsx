import React, { useEffect } from 'react';
import { TcsTabs, TcsSpin } from '@tencent/tcs-component';
import BasicInfo from './BasicInfo';
import AppInfo from './AppInfo';
import SiteInfo from './SiteInfo';
import ChangeInfo from './ChangeInfo';
import { IPropsBug } from '@/common/api/interationManage';
import { IterationManage, IssueSolutionRelsWithDetail } from '@/common/api/iterationManage.api';
import { IssueTypeConfig } from '@/modules/IterationManage/config';

export interface IProps {
  data: IPropsBug;
  pagePath: 'story' | 'defect';
  IssueID: string;
  isManager: boolean;
}

const Detail: React.FC<IProps> = ({ pagePath, data }) => {
  const issueType = IssueTypeConfig[pagePath]; // 动态获取issueType
  const [loading, setLoading] = React.useState(false);
  const [otherRecordList, setOtherRecordList] = React.useState<IssueSolutionRelsWithDetail[]>([]);
  const [currentRecord, setCurrentRecord] = React.useState<IssueSolutionRelsWithDetail>();
  useEffect(() => {
    if (data?.SolutionVersionUUID) {
      setLoading(true);
      IterationManage.ListIssueSolutionRelsWithDetail({
        IssueType: issueType,
        IssueID: data.IssueID,
      })
        .then((res) => {
          const records = res.data || [];
          // 匹配当前记录
          const matchedRecord = records.find(
            (item) => item.Arch === data.Arch && item.SolutionVersionUUID === data.SolutionVersionUUID,
          );
          setCurrentRecord(matchedRecord);
          // 剩余记录放到 recordList
          setOtherRecordList(records.filter((item) => item.UUID !== matchedRecord?.UUID));
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [data?.SolutionVersionUUID, data?.IssueID, issueType, data?.Arch]);

  return (
    <TcsSpin spinning={loading}>
      <TcsTabs tabPosition="left">
        {/* 基础信息页签 */}
        <TcsTabs.TabPane id="basic" key="basic" tab="基础信息">
          <BasicInfo currentRecord={currentRecord} otherRecordList={otherRecordList} />
        </TcsTabs.TabPane>

        {/* 应用信息页签 */}
        <TcsTabs.TabPane id="app" key="app" tab="应用信息">
          <AppInfo currentRecord={currentRecord} />
        </TcsTabs.TabPane>

        {/* 局点信息页签 */}
        <TcsTabs.TabPane id="site" key="site" tab="局点信息">
          <SiteInfo record={currentRecord} />
        </TcsTabs.TabPane>

        {/* 变更单信息页签 */}
        <TcsTabs.TabPane id="change" key="change" tab="变更单信息">
          <ChangeInfo />
        </TcsTabs.TabPane>
      </TcsTabs>
    </TcsSpin>
  );
};

export default Detail;
