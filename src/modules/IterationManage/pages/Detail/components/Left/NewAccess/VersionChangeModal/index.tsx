import React, { useEffect, useState } from 'react';
import { TcsModal, TcsTable } from '@tencent/tcs-component';
import { getAppSolutionProductRel } from '@/common/api/interationManage';
import { IterationManage } from '@/common/api/iterationManage.api';
import { message } from '@tencent/tea-component';
interface IProps {
  onConfirm: () => void;
  record: any;
  onCancel: () => void;
  data: any;
}
const VersionChangeModal: React.FC<IProps> = ({ record, onConfirm, onCancel, data }) => {
  console.log('record', record);
  console.log('data', data);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[] | number[]>([]);
  const [productList, setProductList] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const handleRequest = () => {
    if (!record && !data) {
      return Promise.resolve({
        data: [],
      });
    }
    return getAppSolutionProductRel({
      ApplicationNames: [record?.ApplicationName],
      SolutionVersionUUID: data?.SolutionVersionUUID,
    }).then((res) => {
      if (res.Error) {
        message.error({
          content: res.Error.Message,
        });
        return {
          data: [],
        };
      }
      const list =
        res?.SolutionProductAppRels?.map((item) => ({
          ...item,
          key: `${item?.ApplicationBranchUUID}-${item?.ProductVersionUUID}`,
        })) || [];
      setProductList(list);
      return {
        data: list,
        total: list.length,
      };
    });
  };

  const getColumns = () => [
    {
      title: '产品',
      dataIndex: 'ProductName',
      with: '20%',
    },
    {
      title: '产品Code',
      dataIndex: 'ProductCode',
      with: '10%',
    },
    {
      title: '产品版本名',
      dataIndex: 'ProductVersionName',
      with: '20%',
    },
    {
      title: '应用分支',
      dataIndex: 'ApplicationBranchName',
      with: '20%',
    },
  ];
  useEffect(() => {
    if (record?.ProductVersionUUID && record?.ApplicationBranchUUID) {
      setSelectedRowKeys([`${record.ApplicationBranchUUID}-${record.ProductVersionUUID}`]);
    }
  }, [record]);
  const handleConfirm = () => {
    if (!selectedRowKeys?.length) {
      return message.error({ content: '请选择产品' });
    }
    const item = productList?.filter((item) => item?.key === selectedRowKeys[0])?.[0];
    setLoading(true);
    IterationManage.UpdateIssueAppRel({
      UUID: record?.UUID,
      ApplicationBranchUUID: item?.ApplicationBranchUUID,
      ProductVersionUUID: item?.ProductVersionUUID,
    })
      .then((res) => {
        if (res?.Message) {
          message.success({ content: res.Message });
        }
      })
      .finally(() => {
        setLoading(false);
        onConfirm();
      });
  };
  return (
    <TcsModal
      title="修改关联的产品版本"
      visible
      onCancel={onCancel}
      width={700}
      onOk={handleConfirm}
      confirmLoading={loading}
    >
      <TcsTable
        rowKey="key"
        request={handleRequest}
        columns={getColumns()}
        rowSelection={{
          selectedRowKeys,
          onChange: (keys: string[] | number[]) => {
            setSelectedRowKeys(keys);
          },
          type: 'radio',
        }}
        tableAlertRender={false}
      />
    </TcsModal>
  );
};
export default VersionChangeModal;
