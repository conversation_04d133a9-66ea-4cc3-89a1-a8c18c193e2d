import React, { useState, forwardRef, useImperativeHandle } from 'react';
import { TcsModal, TcsTable, TcsButton, TcsSpace } from '@tencent/tcs-component';
import { getColumns } from './config';
import DependentModal from './components/DependentModal';
import { getAppDependencies, evaluateDependencies, getDependencyEvaluations } from '@/common/api/iterationManage.api';
import { message, Alert } from '@tencent/tea-component';

interface IProps {
  issueID: string;
  record: any;
  onDataChange?: (data: { tableData: any[]; selectedValuesMap: any }) => void; // 新增数据变化回调
}

export interface DependAssessModalRef {
  getConfirmData: () => any;
}

const DependAssessModal = forwardRef<DependAssessModalRef, IProps>((props, ref) => {
  const { issueID, record, onDataChange } = props;
  console.log('issueID', issueID);
  const [dependenciesMap, setDependenciesMap] = useState({});
  const [dependentOnMap, setDependentOnMap] = useState({});
  const [selectLoading, setSelectLoading] = useState(false);
  const [tableData, setTableData] = useState([]);
  const [selectedValuesMap, setSelectedValuesMap] = useState({});
  const [visibleModal, setVisibleModal] = useState(false);
  const [applicationUUID, setApplicationUUID] = useState('');
  const [target, setTarget] = useState('');

  // 查看依赖关系,type区分依赖类型
  function handleDepend(record, type) {
    setApplicationUUID(record?.ApplicationUUID || '');
    setTarget(type);
    setVisibleModal(true);
  }

  async function handleRequest() {
    const apiParams = {
      IssueID: record?.IssueID,
      TAPDUrl: record?.TapdUrl,
      SolutionVersionUUID: record?.SolutionVersionUUID,
      ApplicationUUIDs: record?.IssueAppRel?.map((item) => item?.ApplicationBranch?.Application?.UUID),
    };

    try {
      const responseData = await getDependencyEvaluations(apiParams);
      if (responseData?.Code !== 0) {
        return message.error({ content: responseData?.Data?.Message });
      }
      const dependencyRelations = responseData?.Data?.DependencyRelations || [];
      const initialSelectedValuesMap = {};
      dependencyRelations.forEach((relation) => {
        initialSelectedValuesMap[relation.ApplicationUUID] = {
          dependencies: relation.DependentApplications,
          dependentOn: relation.AdaptedApplications,
        };
      });
      setTableData(dependencyRelations);
      setSelectedValuesMap(initialSelectedValuesMap);

      // 通知父组件数据变化
      if (onDataChange) {
        onDataChange({
          tableData: dependencyRelations,
          selectedValuesMap: initialSelectedValuesMap,
        });
      }

      return {
        data: dependencyRelations,
        success: true,
        total: dependencyRelations.length,
      };
    } catch (error) {
      message.error({ content: error.message });
      return {
        data: [],
        success: false,
        total: 0,
      };
    }
  }

  async function handleTagSelectFocus(recordInfo, type) {
    const appId = recordInfo.ApplicationUUID;
    const solutionVersionUUID = record.SolutionVersionUUID;
    const target = type === 'dependencies' ? 'upstream' : 'downstream';
    if ((type === 'dependencies' && !dependenciesMap[appId]) || (type === 'dependentOn' && !dependentOnMap[appId])) {
      const params = {
        ApplicationUUID: appId,
        SolutionVersionUUID: solutionVersionUUID,
        Target: target,
      };
      setSelectLoading(true);

      try {
        const res = await getAppDependencies(params);
        if (res?.Code !== 0) {
          return message.error({ content: res?.Data?.Message });
        }
        const data = res?.Data;
        if (type === 'dependencies') {
          setDependenciesMap((prev) => ({
            ...prev,
            [appId]: data || [],
          }));
        } else if (type === 'dependentOn') {
          setDependentOnMap((prev) => ({
            ...prev,
            [appId]: data || [],
          }));
        }
      } catch (error) {
        message.error({ content: error.message });
      } finally {
        setSelectLoading(false);
      }
    }
  }

  // 获取确认数据的函数，供外部调用
  const getConfirmData = () => {
    if (tableData.length === 0) {
      return null;
    }

    const requestData = {
      IssueID: issueID,
      TAPDUrl: record?.TapdUrl,
      SolutionVersionUUID: record?.SolutionVersionUUID,
      DependencyRelations: tableData.map((relation: any) => ({
        Application: relation?.Application,
        ApplicationUUID: relation?.ApplicationUUID,
        DependentApplications:
          selectedValuesMap[relation?.ApplicationUUID]?.dependencies.map((item: any) => ({
            ApplicationUUID: item?.ApplicationUUID,
            Application: item?.Application,
          })) || [],
        AdaptedApplications:
          selectedValuesMap[relation?.ApplicationUUID]?.dependentOn.map((item: any) => ({
            ApplicationUUID: item?.ApplicationUUID,
            Application: item?.Application,
          })) || [],
      })),
      IssueSolutionRelUUID: record?.UUID,
    };

    return requestData;
  };

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    getConfirmData,
  }));

  return (
    <>
      <Alert type="warning">注意：如果有新增对其他应用依赖，请先将应用增加TAPD涉及的应用列表，否则无法进行评估。</Alert>
      <TcsTable
        rowKey="ApplicationUUID"
        request={handleRequest}
        columns={getColumns({
          handleDepend,
          handleTagSelectFocus,
          dependenciesMap,
          dependentOnMap,
          selectLoading,
          tableData,
          selectedValuesMap,
          setSelectedValuesMap,
        })}
        options={false}
      />
      {visibleModal && (
        <DependentModal
          solutionVersionUUID={record?.SolutionVersionUUID}
          target={target}
          applicationUUID={applicationUUID}
          onClose={() => setVisibleModal(false)}
        />
      )}
    </>
  );
});

export default DependAssessModal;
