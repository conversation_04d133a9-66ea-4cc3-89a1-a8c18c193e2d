import React, { useState } from 'react';
import { Button, H1, Layout, Stepper } from '@tencent/tea-component';
import { TcsSpace, TcsModal } from '@tencent/tcs-component';
// import AppTable from '../../AccessItems/AppList/AppTable';
import AppTable from '../AppList/AppTable';
import { IPropsIssueSolutionRel } from '@/common/api/interationManage';
import DependAssessModal from '../DependAssessModal';
import AppListMr from '../AppListMr';
import AddRelRegionOnlyContent from '../AddRelRegionOnlyContent';
import OperationTools from '../OperationTools';
import VerifyExport from '../VerifyExport';

const { Body, Sider, Content } = Layout;

export interface IProps {
  pagePath: 'story' | 'defect';
  onReload?: () => void;
  IssueID: string;
  onLoading?: (status: boolean) => void;
  tableDataSource?: IPropsIssueSolutionRel[];
  isManager: boolean;
  tapdInfo: any;
}

interface Step {
  id: string;
  label: string;
  component?: React.ComponentType<IProps>; // 改为可选属性
  props?: IProps;
}

const Operation: React.FC<IProps> = ({ pagePath, IssueID, isManager, tapdInfo }) => {
  const steps: Step[] = [
    {
      id: 'evaluateApp',
      label: '评估应用',
      component: AppTable,
      props: {
        isManager,
        pagePath,
        onReload: () => {},
        IssueID,
        onLoading: () => {},
        tapdInfo,
      },
    },
    {
      id: 'evaluateDependencies',
      label: '评估依赖关系',
      component: DependAssessModal,
      props: { IssueID, tapdInfo },
    },
    { id: 'mergeApp', label: '合流应用', component: AppListMr },
    { id: 'evaluateSite', label: '评估局点', component: AddRelRegionOnlyContent },
    { id: 'createChangeOrder', label: '制作变更单', component: OperationTools },
    { id: 'fillConclusion', label: '填写验证结论', component: VerifyExport },
  ];

  const [currentStep, setCurrentStep] = useState(0);
  const [tapdStatus, setTapdStatus] = useState('待评估修复');

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      if (steps[currentStep].id === 'evaluateSite' && tapdStatus === '待评估修复') {
        TcsModal.warning({
          title: '提示',
          content: '当前TAPD单状态为待评估修复，请先扭转状态至已评估待合流，再进行操作。',
        });
        setTapdStatus('已评估待合流');
        return;
      }
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrev = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  return (
    <Layout style={{ height: 720 }}>
      <Body>
        <Sider style={{ width: '150px' }}>
          <Stepper style={{ marginTop: 10 }} type="vertical" steps={steps} current={steps[currentStep].id} />
        </Sider>
        <Content>
          <Content.Body full>
            {steps[currentStep].component ? (
              React.createElement(steps[currentStep].component, steps[currentStep].props || {})
            ) : (
              <H1>{steps[currentStep].label}</H1>
            )}
          </Content.Body>
        </Content>
      </Body>
      <Layout.Footer style={{ height: 40, marginTop: 10, textAlign: 'center' }}>
        <TcsSpace>
          <Button>暂存</Button>
          <Button onClick={handlePrev}>上一步</Button>
          <Button onClick={handleNext}>下一步</Button>
        </TcsSpace>
      </Layout.Footer>
    </Layout>
  );
};

export default Operation;
