import React, { useState, useEffect, useRef } from 'react';
import { TcsModal } from '@tencent/tcs-component';
import { ProForm } from '@/common/components';
import { TagSelect, StatusTip, message } from '@tencent/tea-component';
import { listAllApplicationNames, GeneratedApi } from '@/common/api/interationManage';
import { getUrlParams } from '@/common/utils';

interface AppData {
  ApplicationName: string;
  ProductVersionName: string;
  ProductName: string;
  Archs: string;
  ProductBranch: string;
}

interface IProps {
  // visible: boolean;
  onClose: () => void;
  onConfirm: (selectedApps: AppData[]) => void;
  tapdInfo: any;
  isIssueBug?: boolean; // 是否为缺陷类型
}

const AddRefAppModal: React.FC<IProps> = ({ onClose, onConfirm, tapdInfo, isIssueBug = true }) => {
  console.log('tapdInfo', tapdInfo);
  // const [selectedApps, setSelectedApps] = useState<AppData[]>([]); // 注释掉，不再需要
  const [appOptions, setAppOptions] = useState<Array<{ value: string; text: string; componentName?: string }>>([]);
  const [loading, setLoading] = useState(false);
  const [selectLoading, setSelectLoading] = useState(false);
  // const [tableData, setTableData] = useState<AppData[]>([]); // 注释掉，不再需要表格
  const formRef = useRef<any>(null);

  // 获取应用列表的函数
  const fetchAppList = async () => {
    setSelectLoading(true);
    setLoading(true);
    try {
      if (isIssueBug) {
        // 缺陷类型使用 ListBugfixApplications 接口
        const urlParams = getUrlParams();
        const res = await GeneratedApi.ListBugfixApplications({
          TapdWorkspaceID: urlParams.workspace_id,
        });
        if (res?.Applications?.length) {
          // 生成 options：value 取 ApplicationName，text 取 ApplicationName (ComponentName)，同时存储 componentName
          const options = (res.Applications || []).map((app: any) => ({
            value: app.ApplicationName,
            text: app.ComponentName ? `${app.ApplicationName} (${app.ComponentName})` : app.ApplicationName,
            componentName: app.ComponentName || '',
          }));
          setAppOptions(options);
        } else {
          message.error({
            content: '获取应用列表失败',
          });
        }
      } else {
        // 需求类型使用 ListAllApplicationNames 接口
        const res = await listAllApplicationNames();
        if (res.Error) {
          message.error({
            content: res.Error.Message || '获取应用列表失败',
          });
        } else {
          // 生成 options：value 和 text 都取 ApplicationName，componentName 为空字符串
          const options = (res.ApplicationNames || []).map((name: string) => ({
            value: name,
            text: name,
            componentName: '',
          }));
          setAppOptions(options);
        }
      }
    } catch (error) {
      console.error('获取应用列表失败:', error);
      message.error({
        content: '获取应用列表失败',
      });
    } finally {
      setSelectLoading(false);
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAppList();
  }, []);

  // 添加应用的API接口函数
  const addApplications = async (apps: string[]) => {
    try {
      setLoading(true);

      // 获取URL参数，判断当前空间
      const urlParams = getUrlParams();
      const isProductionSpace = urlParams.workspace_id === '20419092';

      // 获取SolutionVersionDetail.Code用于判断调用哪个接口
      const solutionVersionCode = tapdInfo?.SolutionVersion || '';
      console.log('SolutionVersionCode:', solutionVersionCode);

      // 判断是否为特定版本，需要调用特殊接口
      const specialVersions = ['TCE3.8.0', 'TCE3.6.0', 'TCS2.3.0'];
      const isSpecialVersion = specialVersions.includes(solutionVersionCode);

      const applications = apps.map((appName: string) => {
        // 在现网空间中，从appOptions中查找对应的组件名
        const appOption = appOptions.find((option) => option.value === appName);
        const componentName = appOption?.componentName || '';

        return {
          ApplicationName: appName,
          ComponentName: componentName,
        };
      });

      if (isSpecialVersion) {
        // 特定版本调用特殊接口
        // TODO: 调用特定版本的添加应用接口
        console.log('现网空间特定版本添加应用:', {
          solutionVersionCode,
          applications,
          tapdInfo,
        });

        // 模拟API调用
        await new Promise((resolve) => setTimeout(resolve, 1000));

        message.success({ content: `成功添加 ${apps.length} 个应用 (特定版本: ${solutionVersionCode})` });
      } else {
        // 其他版本调用CreateIssueRelatedApps接口
        // TODO: 调用CreateIssueRelatedApps接口
        console.log('现网空间调用CreateIssueRelatedApps:', {
          solutionVersionCode,
          applications,
          tapdInfo,
        });

        // 模拟API调用
        await new Promise((resolve) => setTimeout(resolve, 1000));

        message.success({ content: `成功添加 ${apps.length} 个应用 (CreateIssueRelatedApps)` });
      }

      // 调用父组件的回调函数
      const appData: AppData[] = apps.map((appName) => ({
        ApplicationName: appName,
        ProductVersionName: '',
        ProductName: '',
        Archs: '',
        ProductBranch: '',
      }));

      onConfirm(appData);
      onClose();
    } catch (error) {
      console.error('添加应用失败:', error);
      message.error({ content: '添加应用失败，请重试' });
    } finally {
      setLoading(false);
    }
  };

  // 确认按钮点击事件 - 直接执行添加应用逻辑
  const handleConfirm = async () => {
    try {
      const formValues = await formRef.current?.validateFields();
      const apps = formValues?.ApplicationNames || [];

      if (apps.length === 0) {
        message.error({ content: '请选择应用' });
        return;
      }

      await addApplications(apps);
    } catch (error) {
      console.error('表单验证失败:', error);
      message.error({ content: '请检查表单输入' });
    }
  };

  // 注释掉表格列配置 - 不再需要表格
  // const columns: TcsColumns[] = [
  //   {
  //     title: '应用名称',
  //     dataIndex: 'ApplicationName',
  //     width: '20%',
  //   },
  //   {
  //     title: '应用版本',
  //     dataIndex: 'ProductVersionName',
  //     width: '15%',
  //   },
  //   {
  //     title: '应用所属产品',
  //     dataIndex: 'ProductName',
  //     width: '20%',
  //   },
  //   {
  //     title: '应用架构',
  //     dataIndex: 'Archs',
  //     width: '15%',
  //   },
  //   {
  //     title: '应用模式',
  //     dataIndex: 'ProductBranch',
  //     width: '15%',
  //   },
  // ];

  return (
    <TcsModal title="添加应用信息" visible onCancel={onClose} onOk={handleConfirm} confirmLoading={loading} width={800}>
      <ProForm style={{ width: '100%' }} grid formRef={formRef}>
        <ProForm.Item
          label="选择关联应用"
          dataIndex="ApplicationNames"
          tooltip="支持粘贴使用英文分号(;)分隔的多个应用名称字符串"
          colProps={{ span: 24 }}
          rules={{
            required: {
              value: true,
              message: '请选择应用',
            },
          }}
        >
          <TagSelect
            placeholder="请选择应用"
            options={appOptions}
            autoClearSearchValue
            optionsOnly
            tips={selectLoading ? <StatusTip.LoadingTip /> : undefined}
            onPaste={(e) => {
              const text = e.clipboardData.getData('text');
              const appNames = text.split(';').map((item) => item.trim());
              if (text) {
                const newOptions = appNames.map((name) => ({
                  value: name,
                  text: name,
                  componentName: '',
                }));
                setAppOptions(Array.from(new Set([...appOptions, ...newOptions])));
              }
            }}
          />
        </ProForm.Item>
        {/* <Col span={4}>
          <Button type="primary" onClick={() => formRef.current?.submit()}>
            确认
          </Button>
        </Col> */}
      </ProForm>
      {/* <TcsTable
        columns={columns}
        dataSource={tableData}
        rowSelection={{
          type: 'checkbox',
          onChange: (_, selectedRows) => {
            setSelectedApps(selectedRows);
          },
        }}
        rowKey="ApplicationName"
        loading={loading}
      /> */}
    </TcsModal>
  );
};

export default AddRefAppModal;
