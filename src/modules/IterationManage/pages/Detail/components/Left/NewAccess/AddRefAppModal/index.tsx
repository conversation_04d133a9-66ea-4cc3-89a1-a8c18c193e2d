import React, { useState, useEffect, useRef } from 'react';
import { TcsModal, TcsTable, TcsColumns } from '@tencent/tcs-component';
import { ProForm } from '@/common/components';
import { TagSelect, StatusTip, message, Button, Col } from '@tencent/tea-component';
import { listAllApplicationNames, GeneratedApi, getAppSolutionProductRel } from '@/common/api/interationManage';
import { getUrlParams } from '@/common/utils';

interface IProps {
  // visible: boolean;
  onClose: () => void;
  onConfirm: (selectedApps: any[]) => void;
  tapdInfo: any;
  isIssueBug?: boolean; // 是否为缺陷类型
}

const AddRefAppModal: React.FC<IProps> = ({ onClose, onConfirm, tapdInfo, isIssueBug = true }) => {
  console.log('tapdInfo', tapdInfo);
  const [appOptions, setAppOptions] = useState<Array<{ value: string; text: string; componentName?: string }>>([]);
  const [loading, setLoading] = useState(false);
  const [selectLoading, setSelectLoading] = useState(false);
  const [tableData, setTableData] = useState<any[]>([]);
  const [selectedApps, setSelectedApps] = useState<any[]>([]);
  const formRef = useRef<any>(null);

  // 获取应用列表的函数
  const fetchAppList = async () => {
    setSelectLoading(true);
    setLoading(true);
    try {
      if (isIssueBug) {
        // 缺陷类型使用 ListBugfixApplications 接口
        const urlParams = getUrlParams();
        const res = await GeneratedApi.ListBugfixApplications({
          TapdWorkspaceID: urlParams.workspace_id,
        });
        if (res?.Applications?.length) {
          // 生成 options：value 取 ApplicationName，text 取 ApplicationName (ComponentName)，同时存储 componentName
          const options = (res.Applications || []).map((app: any) => ({
            value: app.ApplicationName,
            text: app.ComponentName ? `${app.ApplicationName} (${app.ComponentName})` : app.ApplicationName,
            componentName: app.ComponentName || '',
          }));
          setAppOptions(options);
        } else {
          message.error({
            content: '获取应用列表失败',
          });
        }
      } else {
        // 需求类型使用 ListAllApplicationNames 接口
        const res = await listAllApplicationNames();
        if (res.Error) {
          message.error({
            content: res.Error.Message || '获取应用列表失败',
          });
        } else {
          // 生成 options：value 和 text 都取 ApplicationName，componentName 为空字符串
          const options = (res.ApplicationNames || []).map((name: string) => ({
            value: name,
            text: name,
            componentName: '',
          }));
          setAppOptions(options);
        }
      }
    } catch (error) {
      console.error('获取应用列表失败:', error);
      message.error({
        content: '获取应用列表失败',
      });
    } finally {
      setSelectLoading(false);
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAppList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 获取表格数据的函数
  const fetchTableData = async (apps: string[]) => {
    try {
      setLoading(true);

      // 获取SolutionVersionDetail.Code用于判断调用哪个接口
      const solutionVersionCode = tapdInfo?.SolutionVersion || '';
      console.log('SolutionVersionCode:', solutionVersionCode);

      // 判断是否为特定版本，需要调用特殊接口
      const specialVersions = ['TCE3.8.0', 'TCE3.6.0', 'TCS2.3.0'];
      const isSpecialVersion = specialVersions.includes(solutionVersionCode);

      if (isSpecialVersion) {
        // 特定版本调用特殊接口 - 先空出来
        console.log('特定版本获取表格数据:', {
          solutionVersionCode,
          apps,
          tapdInfo,
        });

        // TODO: 调用特定版本的获取表格数据接口
        // 暂时设置空数据
        setTableData([]);
        message.warning({ content: `特定版本 ${solutionVersionCode} 的接口待实现` });
      } else {
        // 其他版本调用 GetAppSolutionProductRel 接口
        console.log('调用 GetAppSolutionProductRel 获取表格数据:', {
          solutionVersionCode,
          apps,
          tapdInfo,
        });

        const res = await getAppSolutionProductRel({
          ApplicationNames: apps,
          SolutionVersionUUID: tapdInfo?.SolutionVersionUUID || '3c2f24ca08a04671a418ac63fe5b9b2c',
        });

        if (res?.SolutionProductAppRels?.length) {
          setTableData(res?.SolutionProductAppRels || []);
          message.success({ content: `成功获取 ${res?.SolutionProductAppRels?.length} 条应用数据` });
        } else {
          setTableData([]);
          message.warning({ content: '未获取到应用数据' });
        }
      }
    } catch (error) {
      console.error('获取表格数据失败:', error);
      message.error({ content: '获取表格数据失败，请重试' });
      setTableData([]);
    } finally {
      setLoading(false);
    }
  };

  // 应用选择旁边的确认按钮 - 获取表格数据
  const handleSearchTableData = async () => {
    try {
      const formValues = await formRef.current?.validateFields();
      const apps = formValues?.ApplicationNames || [];

      if (apps.length === 0) {
        message.error({ content: '请选择应用' });
        return;
      }

      await fetchTableData(apps);
    } catch (error) {
      console.error('表单验证失败:', error);
      message.error({ content: '请检查表单输入' });
    }
  };

  // 模态框底部的确认按钮 - 保存选中的数据
  const handleConfirm = async () => {
    if (selectedApps.length === 0) {
      message.error({ content: '请选择要添加的应用' });
      return;
    }

    // 调用父组件的回调函数
    onConfirm(selectedApps);
    // onClose();
  };

  // 表格列配置
  const columns: TcsColumns[] = [
    {
      title: '应用名称',
      dataIndex: 'ApplicationName',
      width: '20%',
    },
    {
      title: '应用版本',
      dataIndex: 'ProductVersionName',
      width: '15%',
    },
    {
      title: '应用所属产品',
      dataIndex: 'ProductName',
      width: '20%',
    },
    {
      title: '应用架构',
      dataIndex: 'Archs',
      width: '15%',
      render: (_, record) => record?.Archs?.join(';'),
    },
    {
      title: '应用模式',
      dataIndex: 'ProductBranch',
      width: '15%',
    },
  ];

  return (
    <TcsModal
      title="添加应用信息"
      visible
      onCancel={onClose}
      onOk={handleConfirm}
      confirmLoading={loading}
      width={1000}
    >
      <ProForm style={{ width: '100%' }} grid formRef={formRef}>
        <ProForm.Item
          label="选择关联应用"
          dataIndex="ApplicationNames"
          tooltip="支持粘贴使用英文分号(;)分隔的多个应用名称字符串"
          colProps={{ span: 20 }}
          rules={{
            required: {
              value: true,
              message: '请选择应用',
            },
          }}
        >
          <TagSelect
            placeholder="请选择应用"
            options={appOptions}
            autoClearSearchValue
            optionsOnly
            tips={selectLoading ? <StatusTip.LoadingTip /> : undefined}
            onPaste={(e) => {
              const text = e.clipboardData.getData('text');
              const appNames = text.split(';').map((item) => item.trim());
              if (text) {
                const newOptions = appNames.map((name) => ({
                  value: name,
                  text: name,
                  componentName: '',
                }));
                setAppOptions(Array.from(new Set([...appOptions, ...newOptions])));
              }
            }}
          />
        </ProForm.Item>

        <Col span={4}>
          <Button type="primary" onClick={handleSearchTableData}>
            搜索
          </Button>
        </Col>
      </ProForm>
      <div style={{ marginTop: 20 }}>
        <TcsTable
          columns={columns}
          dataSource={tableData}
          rowKey="ApplicationName"
          loading={loading}
          pagination={false}
          options={false}
          rowSelection={{
            type: 'checkbox',
            selectedRowKeys: selectedApps.map((v) => v?.ApplicationName),
            onChange: (_, selectedRows) => {
              console.log(11111, _, selectedRows);
              setSelectedApps(selectedRows);
            },
          }}
        />
      </div>
    </TcsModal>
  );
};

export default AddRefAppModal;
