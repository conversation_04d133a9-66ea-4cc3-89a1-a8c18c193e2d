import React, { useRef } from 'react';
import { TcsColumns, TcsTable, TcsButton } from '@tencent/tcs-component';
import { IssueSolutionRelsWithDetail, IssueApplicationRelation } from '@/common/api/iterationManage.api';
import { getAppVersion } from '@/common/utils';
import ViewGitMRModal from '../../ViewGitMRModal';

interface AppInfoProps {
  currentRecord?: IssueSolutionRelsWithDetail;
}

const AppInfo: React.FC<AppInfoProps> = ({ currentRecord }) => {
  const columns = [
    {
      title: '应用名称',
      dataIndex: ['ApplicationName'],
      key: 'ApplicationName',
      fixed: 'left',
      width: '15%',
    },
    {
      title: '应用版本',
      dataIndex: ['ApplicationBranch', 'BranchName'],
      key: 'ApplicationVersion',
      width: '10%',
    },
    {
      title: '研发状态',
      dataIndex: 'Status',
      width: '10%',
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'IssueSolutionArchStatus',
        showType: 'tag',
      },
    },
    {
      title: '已合流版本',
      dataIndex: 'ReleaseApplicationPackage',
      width: '15%',
      disable: true,
      render(_, record) {
        let text = '-';
        const releasePackage = record?.LatestRegisteredApplicationPackage;
        if (releasePackage) {
          text = getAppVersion(releasePackage);
        }
        return text;
      },
    },
    {
      title: '正式制品版本',
      dataIndex: ['PackageVersion'],
      key: 'PackageVersion',
      width: '15%',
      render(text, record: IssueApplicationRelation) {
        return record.ReleaseApplicationPackage ? getAppVersion(record.ReleaseApplicationPackage) : '-';
      },
    },
    {
      title: '应用架构',
      dataIndex: ['Arch'],
      key: 'Arch',
      width: '8%',
    },
    {
      title: '产品名称',
      dataIndex: ['ProductVersionInfo', 'ProductInfo', 'Name'],
      key: 'ProductVersion',
      width: '10%',
    },
    {
      title: '产品版本',
      dataIndex: ['ProductVersion'],
      key: 'ProductVersion',
      width: '10%',
    },
    {
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      width: '10%',
      render: (text, record) => (
        <TcsButton type="link" onClick={() => handleViewGitMR(record)}>
          查看关联MR信息
        </TcsButton>
      ),
    },
  ] as TcsColumns[];

  const viewGitMrRef = useRef<any>();

  function handleViewGitMR(record: IssueApplicationRelation) {
    viewGitMrRef?.current?.show(record, currentRecord?.IssueID);
  }

  return (
    <>
      <TcsTable
        columns={columns}
        options={false}
        dataSource={currentRecord?.IssueAppRel || []}
        rowKey={(record) => record.ApplicationBranchUUID}
        pagination={{}}
        bordered
      />
      <ViewGitMRModal ref={viewGitMrRef} />
    </>
  );
};

export default AppInfo;
