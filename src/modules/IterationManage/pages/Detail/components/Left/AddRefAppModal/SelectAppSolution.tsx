/*
 * @Author: superfeng
 * @Date: 2023-03-21 17:04:45
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-10-28 17:59:32
 * @Description: 请输入注释信息
 */
import {
  listAllApplicationNames,
  ISolutionProductAppRels,
  getAppSolutionProductRel,
  IPropsIssueSolutionRel,
} from '@/common/api/interationManage';
import { ProForm } from '@/common/components';
import styles from './index.module.less';
import useLookup from '@/common/hookups/useLookup';
import {
  Button,
  TagSelect,
  Table,
  message,
  Col,
  Icon,
  Bubble,
  Text,
  Select,
  Form,
  StatusTip,
  Alert,
} from '@tencent/tea-component';
import React, { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useParams } from 'react-router-dom';

export interface IProps {
  // 缺陷单uuid
  issueID: string;
  defectType: string;
  issueSolutions: IPropsIssueSolutionRel[];
  issueInfo: any;
  onConfirm?: () => void;
  onCancel?: () => void;
  evaluateVerision?: { SolutionVersionName: string; SolutionVersionUUID: string }[];
}

const ALL_VALUE = '__ALL__';

const getColumns = ({ getLookupByCode }: { getLookupByCode: (type, value) => any }) => [
  {
    header: '解决方案名称',
    key: 'Solution',
    width: 200,
    render(record) {
      return `${record?.Solution || '-'}(${record?.SolutionEnName || '-'})`;
    },
  },
  {
    header: '解决方案版本',
    key: 'SolutionVersion',
    width: 200,
  },
  {
    header: '架构',
    key: 'Arch',
    width: 100,
    render(record) {
      const { Arch } = record;
      const lookup = getLookupByCode('PackageArch', Arch);
      return lookup?.Name || '-';
    },
  },
];

const deliverTypeColumn = ({ lookups, selectedSolutionArchs, control, onSelectChange }) => [
  {
    header: '出包类型',
    key: 'DeliverType',
    width: 200,
    render: (_record, rowKey) => (
      <Controller
        // 这里对应setValue(key,value) name对应key
        name={`${rowKey.replaceAll('.', '$')}`}
        control={control}
        rules={{
          required: selectedSolutionArchs.includes(rowKey)
            ? {
                value: true,
                message: '请选择出包类型',
              }
            : undefined,
        }}
        render={({ field, fieldState }) => (
          <Form.Item
            message={fieldState?.error?.message}
            style={{ width: '100%', display: 'block' }}
            status={fieldState?.error ? 'error' : undefined}
            showStatusIcon={false}
          >
            <Select
              {...field}
              appearance="button"
              searchable
              size="full"
              matchButtonWidth
              options={
                lookups?.DeliverType?.map((item) => ({
                  value: item.Code,
                  text: item.Name,
                })) || []
              }
              disabled={_record.disabled}
              onChange={(value, context) => {
                context?.event?.stopPropagation();
                field?.onChange(value);
                onSelectChange?.(value, _record);
              }}
            />
          </Form.Item>
        )}
      />
    ),
  },
];

const SelectAppSolution: React.ForwardRefRenderFunction<any, IProps> = (
  { issueID, issueSolutions, defectType, issueInfo, evaluateVerision = [] },
  ref,
) => {
  const formRef = useRef<any>(null);
  const [applicationNames, setApplicationNames] = useState<string[]>([]);
  const [selectLoading, setSelectLoading] = useState(false);
  const [selectedAppNames, setSelectedAppNames] = useState<string[]>([]);
  const [filterSolutionNames, setFilterSolutionNames] = useState<string[]>([]);
  const [filterSolutionVersionName, setFilterSolutionVersionName] = useState<string>();
  const [loading, setLoading] = useState(false);
  const { pagePath } = useParams<{ pagePath: 'story' | 'defect' }>();
  const { lookups, getLookupByCode } = useLookup(['DeliverType']);
  // record the deliver type in table
  const { control, trigger, getValues, setValue } = useForm({
    mode: 'all',
  });
  // 后端接口返回的解决方案版本应用关联数据，用于生成解决方案架构组合及反推应用列表
  const [solutionProductAppRels, setSolutionProductAppRels] = useState<ISolutionProductAppRels[]>([]);

  // 选中的架构组合
  const [selectedSolutionArchs, setSelectedSolutionArchs] = useState<string[]>([]);
  const solutionArchList = useMemo(() => {
    // 接口获取应用下的解决方案
    if (solutionProductAppRels?.length) {
      const list: string[] = [];
      const appMaps: Record<string, string[]> = {};
      solutionProductAppRels.forEach((item) => {
        const { Archs, SolutionName, SolutionVersionName, ApplicationName, SolutionVersion } = item;
        if (Archs?.length) {
          Archs.forEach((arch) => {
            const key = `${SolutionName}~${SolutionVersionName}~${arch}~${SolutionVersion?.Solution?.NameEN || ''}`;
            if (!list.includes(key)) {
              list.push(key);
            }
            if (!appMaps[key]) {
              appMaps[key] = [];
            }
            appMaps[key].push(ApplicationName);
          });
        }
      });
      const checkDisabled = (item, issueSolutions) => {
        const issueKeys = issueSolutions.map(
          (issue) =>
            `${issue.SolutionVersionDetail.Solution.Name}~${issue.SolutionVersion}~${issue.Arch}~${
              issue.SolutionVersionDetail.Solution.NameEN || ''
            }`,
        );
        return issueKeys.includes(item);
      };

      const updatedList = list.map((item) => {
        const [Solution, SolutionVersionName, Arch, NameEN] = item.split('~');
        const disabled = issueSolutions?.length > 0 ? checkDisabled(item, issueSolutions) : false;
        return {
          key: item,
          Solution,
          SolutionVersion: SolutionVersionName,
          SolutionEnName: NameEN,
          Arch,
          ApplicationNames: appMaps[item],
          disabled,
        };
      });

      return updatedList;
    }
    return [];
  }, [solutionProductAppRels, issueSolutions]);

  useEffect(() => {
    if (pagePath === 'defect' && solutionArchList.length > 0) {
      if (issueSolutions.length === 0) {
        // 没有选择应用，默认筛选条件为源单对应的解决方案名称
        setFilterSolutionNames([issueInfo?.Solution?.Name]);
      } else {
        // 选择了应用，默认筛选条件为用户选择的应用列表里面的解决方案名称
        const defaultSolutionNames = issueSolutions.map((item) => item?.SolutionVersionDetail?.Solution?.Name);
        setFilterSolutionNames(Array.from(new Set(defaultSolutionNames)));
      }
    }
  }, [issueSolutions, issueInfo?.Solution?.Name, solutionArchList]);

  const tableData = useMemo(() => {
    // 解决方案和版本拥有过滤，filterSolutionNames，filterSolutionVersionName
    let data = solutionArchList;
    if (filterSolutionNames.length && !filterSolutionNames.includes(ALL_VALUE)) {
      data = data.filter((item) => filterSolutionNames.includes(item.Solution));
    }

    if (filterSolutionVersionName && filterSolutionVersionName !== ALL_VALUE) {
      data = data.filter((item) => item.SolutionVersion === filterSolutionVersionName);
    }
    return data;
  }, [filterSolutionNames, filterSolutionVersionName, solutionArchList]);

  useEffect(() => {
    if (selectedAppNames.length) {
      // 点击确认表单有应用名数据之后，紧接着调用接口获取该应用下的数据
      setLoading(true);
      getAppSolutionProductRel({
        ApplicationNames: selectedAppNames,
      })
        .then((res) => {
          if (res.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            const result = res.SolutionProductAppRels || [];
            // 缺陷直接展示，需求只展示对应解决方案版本的数据
            const combinedVersions = [
              ...evaluateVerision,
              {
                SolutionVersionName: issueInfo.SolutionVersion,
                SolutionVersionUUID: issueInfo.SolutionVersionUUID,
              },
            ];
            const solutionProductAppRels =
              pagePath === 'defect'
                ? result
                : result.filter((item) =>
                    combinedVersions.some((version) => version.SolutionVersionUUID === item.SolutionVersionUUID),
                  );
            // 赋值给SolutionAppRels
            setSolutionProductAppRels(solutionProductAppRels);

            const selectedRows: string[] = [];
            const deliverTypeData: any = {};

            // 区分缺陷单和需求单
            // 缺陷单非第一次新增，默认选中issueSolutions
            if (issueSolutions?.length && pagePath === 'defect') {
              // 区分是不是第一次新增
              issueSolutions.forEach((item) => {
                const key = `${item.SolutionVersionDetail.Solution.Name}~${item.SolutionVersion}~${item.Arch}~${item.SolutionVersionDetail.Solution.NameEN}`;
                selectedRows.push(key);
                // 出包类型赋值给对应value 回显用
                deliverTypeData[`${key.replaceAll('.', '$')}`] = item.DeliverType;
              });
            } else {
              // 缺陷单第一次新增，默认选中当前解决方案名称的数据
              // 需求单默认选中所有，非第一次新增则回显

              // 默认当前架构下的选中x86和arm
              const archs = ['rhel.amd64', 'rhel.arm64'];
              if (!archs.includes(issueInfo.Arch)) {
                archs.push(issueInfo.Arch);
              }
              const finterApps = solutionProductAppRels?.filter((item) =>
                item.Archs?.some((arch) => arch === 'rhel.amd64' || arch === 'rhel.arm64'),
              );
              // 不需要rhel.power架构
              const newSelectedRows =
                finterApps?.flatMap((item) =>
                  item.Archs?.filter((arch) => arch !== 'rhel.power').map((arch) =>
                    pagePath === 'defect'
                      ? `${issueInfo.Solution.Name}~${issueInfo.SolutionVersion}~${arch}~${issueInfo.Solution.NameEN}`
                      : `${item.SolutionName}~${item.SolutionVersion.Code}~${arch}~${item.SolutionVersion.Solution.NameEN}`,
                  ),
                ) || [];
              selectedRows.push(...Array.from(new Set(newSelectedRows)));

              // 回显
              if (issueSolutions?.length) {
                issueSolutions.forEach((item) => {
                  const key = `${item.SolutionVersionDetail.Solution.Name}~${item.SolutionVersion}~${item.Arch}~${item.SolutionVersionDetail.Solution.NameEN}`;
                  // 出包类型赋值给对应value 回显用
                  deliverTypeData[`${key.replaceAll('.', '$')}`] = item.DeliverType;
                });
              }
            }
            // 默认选中
            setSelectedSolutionArchs(selectedRows);
            setTimeout(() => {
              Object.entries(deliverTypeData).forEach(([key, value]) => {
                // 给对应的key值value
                setValue(key, value);
              });
            }, 100);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      setSelectedSolutionArchs([]);
      setSolutionProductAppRels([]);
    }
  }, [selectedAppNames, issueInfo?.SolutionVersionUUID, pagePath, issueSolutions, setValue, issueInfo]);

  const fetchSolutionData = (selectedSolutionArchs: string[], solutionProductAppRels: ISolutionProductAppRels[]) => {
    const issueApps = selectedSolutionArchs.map((item) => {
      const [solution, solutionVersion, arch] = item.split('~');
      const rels = solutionProductAppRels.filter(
        (item) =>
          item.SolutionName === solution && item.SolutionVersionName === solutionVersion && item.Archs.includes(arch),
      );
      // 拿到对应的key
      const recordKey = item.replaceAll('.', '$');
      // 拿到key值对应的value
      const deliverType = getValues(recordKey);
      return {
        Arch: arch,
        Solution: solution,
        SolutionVersion: solutionVersion,
        SolutionVersionUUID: rels[0]?.SolutionVersionUUID,
        IssueType: pagePath === 'defect' ? 'bug' : 'story',
        IssueID: issueID,
        DeliverType: deliverType,
        IssueAppRel: rels.map((rel) => ({
          // 应用版本的UUID
          ApplicationBranchUUID: rel.ApplicationBranchUUID,
          // 对应的应用名
          ApplicationName: rel.ApplicationName,
          // // 应用类型，tad 表示tad应用 ted表示老版本的组件
          // ApplicationType: rel.ApplicationType,
          // 架构，x86/arm/power等
          Arch: arch,
          // 关联的缺陷单，对应story表中的一个需求单或者bug表中的一个缺陷单
          IssueID: issueID,
          // 产品版本
          ProductVersion: rel.ProductVersionName,
          // 产品版本UUID
          ProductVersionUUID: rel.ProductVersionUUID,
        })),
      };
    });
    return issueApps;
  };

  useImperativeHandle(
    ref,
    () => ({
      getData() {
        if (selectedSolutionArchs?.length > 0) {
          return trigger().then((valid) => {
            if (valid) {
              return fetchSolutionData(selectedSolutionArchs, solutionProductAppRels);
            }
            message.error({ content: '请选择出包类型' });
            // throw new Error('请选择出包类型');
          });
        }
        message.error({ content: '请选择要添加的应用和解决方案版本架构' });
        return Promise.reject('请选择要添加的应用和解决方案版本架构');
      },
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [pagePath, issueID, getValues, selectedSolutionArchs, solutionProductAppRels, trigger],
  );

  useEffect(() => {
    setSelectLoading(true);
    listAllApplicationNames()
      .then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          setApplicationNames(res.ApplicationNames || []);
        }
      })
      .finally(() => {
        setSelectLoading(false);
      });
  }, []);
  // 点击确认时调用的方法
  async function handleSelectedApps(formData) {
    setSelectedAppNames(formData.ApplicationNames || []);
  }

  const solutionOptions = useMemo(() => {
    const options: string[] = [];
    solutionArchList.forEach((item) => {
      if (!options.includes(item.Solution)) {
        options.push(item.Solution);
      }
    });
    return options.map((text) => ({
      text,
      value: text,
    }));
  }, [solutionArchList]);

  const solutionVersionOptions = useMemo(() => {
    let data = solutionArchList;
    if (filterSolutionNames.length && !filterSolutionNames.includes(ALL_VALUE)) {
      data = solutionArchList.filter((item) => filterSolutionNames.includes(item.Solution));
    }
    const options: string[] = [];
    data.forEach((item) => {
      if (!options.includes(item.SolutionVersion)) {
        options.push(item.SolutionVersion);
      }
    });
    return options.map((text) => ({
      text,
      value: text,
    }));
  }, [solutionArchList, filterSolutionNames]);

  const tableColumns = useMemo(() => {
    const columns = getColumns({
      getLookupByCode,
    });
    if (defectType === 'OnlineBug' || defectType === 'OnlineStory') {
      // 只有现网需求和缺陷需要选择出包类型
      columns.push(
        ...(deliverTypeColumn({
          lookups,
          selectedSolutionArchs,
          control,
          onSelectChange: handleSelectChange,
        }) as any[]),
      );
    }

    return columns;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defectType, getLookupByCode, control, selectedSolutionArchs, lookups]);

  // 在出包类型发生变化后，自动勾选这一行
  function handleSelectChange(value: string, record: any = {}) {
    const { key } = record;
    if (value && !selectedSolutionArchs.includes(key)) {
      setSelectedSolutionArchs((keys) => [...keys, key]);
    }
  }

  return (
    <>
      <div>
        <ProForm formRef={formRef} style={{ width: '100%' }} grid onFinish={handleSelectedApps}>
          <ProForm.Item
            label="选择关联应用"
            dataIndex="ApplicationNames"
            tooltip="支持粘贴使用英文分号(;)分隔的多个应用名称字符串"
            colProps={{ span: 20 }}
            rules={{
              required: {
                value: true,
                message: '请选择应用',
              },
            }}
          >
            <TagSelect
              placeholder="请选择应用"
              // value={selecteTemporaryAppNames}
              options={applicationNames.map((item) => ({
                value: item,
                text: item,
              }))}
              autoClearSearchValue
              optionsOnly
              onPaste={(e) => {
                const text = e.clipboardData.getData('text');
                const originSelectedAppNames = formRef.current.getFieldValue('ApplicationNames') || [];
                let appNames = Array.from(
                  new Set([...originSelectedAppNames, ...text.split(';').map((item) => item.trim())]),
                );
                appNames = appNames.filter((item) => applicationNames.includes(item));
                if (text) {
                  formRef.current.setFieldsValue({
                    ApplicationNames: appNames,
                  });
                }
              }}
              tips={
                // eslint-disable-next-line no-nested-ternary
                selectLoading ? <StatusTip.LoadingTip /> : applicationNames.length ? undefined : <StatusTip.EmptyTip />
              }
              overlayClassName={styles.tagselect}
            />
          </ProForm.Item>

          <Col span={4}>
            <Button type="primary" htmlType="submit">
              确认
            </Button>
          </Col>
        </ProForm>
        {pagePath === 'defect' && (
          <Alert>默认仅显示当前缺陷单关联解决方案的数据，如果选择其他解决方案，请在解决方案名称列进行筛选</Alert>
        )}
        {applicationNames.length ? (
          <form>
            <Table
              recordKey="key"
              columns={tableColumns}
              records={tableData}
              rowDisabled={(row) => {
                const { SolutionVersion, Arch, disabled } = row;
                if (issueSolutions?.length) {
                  const find = issueSolutions.find(
                    (item) => item.SolutionVersion === SolutionVersion && item.Arch === Arch,
                  );
                  if (disabled) {
                    return true;
                  }

                  // 如果找到对应记录，判断对应记录是否已交付，如果有交付单，则不能修改
                  if (find) {
                    return !!find.DeliveryUUID;
                  }
                }
                return false;
              }}
              addons={[
                Table.addons.selectable({
                  value: selectedSolutionArchs,
                  rowSelect: false,
                  onChange: (value, context) => {
                    setSelectedSolutionArchs(context.selectedRecords.map((item) => item.key));
                  },
                  width: 50,
                  render(element, { record }) {
                    if (new Set(record.ApplicationNames || []).size !== selectedAppNames.length) {
                      const unRelApps = selectedAppNames.filter(
                        (item) => !(record.ApplicationNames || []).includes(item),
                      );
                      return (
                        <div style={{ width: 45 }}>
                          <span>{element}</span>
                          <span>
                            <Bubble
                              content={
                                <>
                                  <div>
                                    <Text theme="warning">以下组件未关联当前架构组合:</Text>
                                  </div>
                                  <div>
                                    {unRelApps.map((item) => (
                                      <div key={item}>{item}</div>
                                    ))}
                                  </div>
                                </>
                              }
                            >
                              <Icon type="warning" />
                            </Bubble>
                          </span>
                        </div>
                      );
                    }
                    return element;
                  },
                }),
                Table.addons.scrollable({
                  maxHeight: 400,
                }),
                Table.addons.autotip({
                  isLoading: loading,
                }),
                Table.addons.rowtooltip({
                  tooltip: (record) =>
                    record?.disabled
                      ? '仅支持查看，如需修改局点或修改出包类型，请到对应解决方案版本架构的评估记录中通过点击操作列修改出包类型按钮进行操作，或直接点击批量修改出包类型'
                      : null,
                }),

                // 需求单不需要过滤的能力，只会显示当前需求单相同的解决方案版本的数据
                ...(pagePath === 'defect'
                  ? [
                      Table.addons.filterable({
                        type: 'multiple',
                        column: 'Solution',
                        value: filterSolutionNames,
                        // 增加 "全部" 选项
                        all: {
                          value: ALL_VALUE,
                          text: '全部',
                        },
                        searchable: true,
                        options: solutionOptions,
                        onChange: (value) => setFilterSolutionNames(value),
                      }),
                      Table.addons.filterable({
                        type: 'single',
                        column: 'SolutionVersion',
                        searchable: true,
                        value: filterSolutionVersionName,
                        boxStyle: { width: 260 },
                        // 增加 "全部" 选项
                        all: {
                          value: ALL_VALUE,
                          text: '全部',
                        },
                        options: solutionVersionOptions,
                        onChange: (value) => setFilterSolutionVersionName(value),
                      }),
                    ]
                  : []),
              ]}
            />
          </form>
        ) : undefined}
      </div>
    </>
  );
};

export default forwardRef(SelectAppSolution);
