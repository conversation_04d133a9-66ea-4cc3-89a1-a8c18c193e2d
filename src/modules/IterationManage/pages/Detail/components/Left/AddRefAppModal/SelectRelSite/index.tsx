import { Tcs<PERSON>ollapse, TcsSpin } from '@tencent/tcs-component';
import { Bubble, Checkbox, SearchBox, StatusTip, Switch, Button, Alert, Text, message } from '@tencent/tea-component';
import React, { forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import styles from './index.module.less';
import useLookup from '@/common/hookups/useLookup';
import { DELIVER_TYPE } from '@/common/constants';
import {
  ICreateIssueRelatedAppsApi,
  IListDeliverySitesForIssueSolutionVersionApi,
  IListProjectClientSiteApi,
  IterationManage,
} from '@/common/api/iterationManage.api';
import { IPropsIssueSolutionRel } from '@/common/api/interationManage';
export interface IProps {
  issueApps: any[];
  issueSolutions: IPropsIssueSolutionRel[];
}

const SelectRelSite: React.ForwardRefRenderFunction<any, IProps> = ({ issueApps, issueSolutions }, ref) => {
  const [loading, setLoading] = useState(false);
  const [clientSitesMap, setClientSitesMap] = useState<Record<string, IListProjectClientSiteApi.IClientSites[]>>({});
  const [checkedClientSites, setCheckedClientSites] = useState<Record<string, string[]>>({});
  const [searchValue, setSearchValue] = useState('');
  const [isFullSites, setIsFullSites] = useState(false);
  // const [issueSolutionRelSites, setIssueSolutionRelSites] =
  //   useState<IListDeliverySitesForIssueSolutionVersionApi.IResponse['IssueSolutionRelSites']>();
  const { getLookupByCode } = useLookup(['PackageArch', 'DeliverType']);
  useEffect(() => {
    setLoading(true);
    // // 获取选中的局点清单
    // if (issueSolutions?.length) {
    //   IterationManage.ListDeliverySitesForIssueSolutionVersion({
    //     IssueSolutionRelUUIDs: issueSolutions.map((item) => item.UUID),
    //   }).then((res) => {
    //     setIssueSolutionRelSites(res.IssueSolutionRelSites);
    //   });
    // }
    const promises: Promise<any>[] = [
      issueSolutions?.length
        ? IterationManage.ListDeliverySitesForIssueSolutionVersion({
            IssueSolutionRelUUIDs: issueSolutions.map((item) => item.UUID),
          })
        : Promise.resolve(undefined),
    ];

    promises.push(
      ...issueApps?.map((item) =>
        IterationManage.ListProjectClientSite({
          SolutionVersionID: item.SolutionVersionUUID,
          Arch: item.Arch,
          Applications: item.IssueAppRel.map((app) => app.ApplicationName),
          IsFullSites: isFullSites,
          ShowPatchSites: true,
        }),
      ),
    );
    Promise.all(promises)
      .then(([issueSolutionRelSites, ...res]) => {
        const clientSitesMap: Record<string, IListProjectClientSiteApi.IClientSites[]> = {};
        let siteIds: string[] = [];
        // res 接口调用会根据当前的解决方案版本和架构调用多个，res即为多个调用后的集合数据[]
        // 拿到当前的所有局点的数据。按照IssueSolutionRelUUID 为数组
        if (issueSolutionRelSites?.IssueSolutionRelSites) {
          const solutionSites = (issueSolutionRelSites as IListDeliverySitesForIssueSolutionVersionApi.IResponse)
            .IssueSolutionRelSites;
          siteIds = solutionSites.map((item) => item.Sites?.map((site) => site.SiteUUID) || []).flat();
        }

        const checked = {};
        res?.forEach((item, index) => {
          const issueApp = issueApps[index];
          const key = `${issueApp.SolutionVersionUUID}-${issueApp.Arch}`;
          clientSitesMap[key] = item.ClientInfos.filter((item) => item.ProjectSite?.length);
          // 指定客户出包
          if (issueApp.DeliverType === DELIVER_TYPE.ALL_PROJECTS) {
            clientSitesMap[key]?.forEach((item) => {
              const clientKey = `${issueApp.SolutionVersionUUID}-${issueApp.Arch}-${item.ClientUUID}`;
              checked[clientKey] = item.ProjectSite?.map((item) => item.SiteUUID) || [];
            });
          } else if (issueApp.DeliverType === DELIVER_TYPE.SPECIFIED_PROJECTS) {
            clientSitesMap[key]?.forEach((item) => {
              const clientKey = `${issueApp.SolutionVersionUUID}-${issueApp.Arch}-${item.ClientUUID}`;
              const findSiteIds =
                item.ProjectSite?.filter((item) => siteIds.includes(item.SiteUUID))?.map((item) => item.SiteUUID) || [];
              if (findSiteIds.length) {
                checked[clientKey] = findSiteIds;
              }
            });
          }
        });

        setCheckedClientSites(checked);
        setClientSitesMap(clientSitesMap);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [issueApps, issueSolutions, isFullSites]);

  useImperativeHandle(
    ref,
    () => ({
      getData() {
        const result = issueApps?.map((item) => {
          const key = `${item.SolutionVersionUUID}-${item.Arch}`;
          const resultItem = {
            SolutionVersionUUID: item.SolutionVersionUUID,
            Arch: item.Arch,
            DeliverType: item.DeliverType,
            SiteInfos: [] as ICreateIssueRelatedAppsApi.ISiteInfo[],
          };
          if (item.DeliverType === DELIVER_TYPE.NO_NEED) {
            return resultItem;
          }
          const clientSites = clientSitesMap[key];
          if (!clientSites) {
            return resultItem;
          }
          const siteInfos: ICreateIssueRelatedAppsApi.ISiteInfo[] = [];
          clientSites?.forEach((client) => {
            const checkedSites = checkedClientSites[`${key}-${client.ClientUUID}`] || [];
            siteInfos.push(
              ...(client.ProjectSite?.filter((item) => checkedSites.includes(item.SiteUUID)) || []).map((item) => ({
                SiteUUID: item.SiteUUID,
                ClientName: client.ClientName,
                SiteName: item.SiteName,
                SiteType: item.SiteType,
              })),
            );
          });

          if (item.DeliverType === DELIVER_TYPE.SPECIFIED_PROJECTS && siteInfos.length === 0) {
            message.error({
              content: `${item.Solution}(${item.SolutionVersion})-${
                getLookupByCode('PackageArch', item.Arch)?.Name || item.Arch
              }未选择局点，请选择局点后再提交`,
            });
          }
          resultItem.SiteInfos = siteInfos;
          return resultItem;
        });
        return result;
      },
    }),
    [checkedClientSites, issueApps, clientSitesMap],
  );

  const filterSitesMap = useMemo(() => {
    if (!searchValue) {
      return clientSitesMap;
    }
    const filterClientSitesMap: Record<string, IListProjectClientSiteApi.IClientSites[]> = {};
    issueApps?.forEach((item) => {
      const key = `${item.SolutionVersionUUID}-${item.Arch}`;
      filterClientSitesMap[key] = clientSitesMap[key].filter((client) => {
        if (client.ClientName.includes(searchValue)) {
          return true;
        }

        const includeSite = client.ProjectSite?.filter?.((site) => site.SiteName.includes(searchValue)) || [];
        return includeSite.length > 0;
      });
    });
    return filterClientSitesMap;
  }, [searchValue, clientSitesMap, issueApps]);

  function handleSearch(value?: string) {
    setSearchValue(value || '');
  }

  function handleChangeGroup(
    solutionVersionUUID: string,
    arch: string,
    client: IListProjectClientSiteApi.IClientSites,
    oldChecked: boolean,
  ) {
    // 如果之前是全选，则改为全不选
    const key = `${solutionVersionUUID}-${arch}-${client.ClientUUID}`;
    if (oldChecked) {
      setCheckedClientSites((sites) => {
        const selectedSites = sites[key] || [];
        const unSelectedSites = client?.ProjectSite?.map((item) => item.SiteUUID) || [];
        return {
          ...sites,
          [key]: selectedSites.filter((item) => !unSelectedSites.includes(item)),
        };
      });
    } else {
      // 如果之前是半选或者未选，则改为全选
      setCheckedClientSites((sites) => {
        const selectedSites = sites[key] || [];
        const list = Array.from(
          new Set([...(client.ProjectSite?.map((item) => item.SiteUUID) || []), ...selectedSites]),
        );
        return {
          ...sites,
          [key]: list,
        };
      });
    }
  }

  function handleChangeSiteStatus(
    solutionVersionUUID: string,
    arch: string,
    clientUUID: string,
    siteUUID: string,
    checked: boolean,
  ) {
    const key = `${solutionVersionUUID}-${arch}-${clientUUID}`;
    setCheckedClientSites((sites) => {
      let arr = sites[key] || [];
      if (checked) {
        arr = [...arr, siteUUID];
      } else {
        arr = arr.filter((item) => item !== siteUUID);
      }
      return {
        ...sites,
        [key]: arr,
      };
    });
  }

  // 全选当前客户端的所有局点
  function handleSelectAllSites(
    solutionVersionUUID: string,
    arch: string,
    clientUUID: string,
    projectSites: IListProjectClientSiteApi.ISiteBaseInfo[],
  ) {
    const key = `${solutionVersionUUID}-${arch}-${clientUUID}`;
    setCheckedClientSites((prevChecked) => ({
      ...prevChecked,
      [key]: projectSites.map((site) => site.SiteUUID),
    }));
  }

  // 反选当前客户端的所有局点
  function handleInverseSelectSites(
    solutionVersionUUID: string,
    arch: string,
    clientUUID: string,
    projectSites: IListProjectClientSiteApi.ISiteBaseInfo[],
  ) {
    const key = `${solutionVersionUUID}-${arch}-${clientUUID}`;
    setCheckedClientSites((prevChecked) => {
      const currentChecked = prevChecked[key] || [];
      const newChecked = projectSites
        .filter((site) => !currentChecked.includes(site.SiteUUID))
        .map((site) => site.SiteUUID);
      return {
        ...prevChecked,
        [key]: newChecked,
      };
    });
  }

  const defaultActiveIds = useMemo(
    () => issueApps?.map((item) => `${item.SolutionVersionUUID}-${item.Arch}`).slice(0, 1),
    [issueApps],
  );

  return (
    <TcsSpin spinning={loading}>
      <Alert type="warning">
        <p>可用局点状态：此处仅支持处于交付中和售后的局点</p>
        <p>
          迁移局点说明：当前极光Next的局点分为两类，一类是Next局点，另一类是从2.0迁移至Next的局点。对于后者，我们会在局点名称后添加【迁移】标识以示区分。
        </p>
      </Alert>
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 5 }}>
        <Bubble title="打开表示查看全量可交付局点，关闭表示进行精准过滤，只查询出过指定应用架构组合的可交付局点">
          <Switch value={isFullSites} onChange={(value) => setIsFullSites(value)}>
            查看全部局点
          </Switch>
          {isFullSites && <Text theme="danger">请确认好选择的局点的出包架构与局点现场的架构信息是否匹配</Text>}
        </Bubble>
        <SearchBox size="l" onSearch={handleSearch} placeholder="可输入客户名称/局点名称进行搜索" />
      </div>
      <div style={{ maxHeight: 800, minHeight: 640, overflow: 'auto' }}>
        <TcsCollapse accordion bordered background defaultActiveIds={defaultActiveIds}>
          {issueApps?.map((item) => {
            const key = `${item.SolutionVersionUUID}-${item.Arch}`;
            const clients = filterSitesMap[key];

            let content: JSX.Element;
            if (item.DeliverType === DELIVER_TYPE.NO_NEED) {
              content = <StatusTip.EmptyTip emptyText="仅后续新增客户出包无需选择局点" />;
            } else {
              // 检查是否需要显示全选和反选按钮
              const showSelectButtons = item.DeliverType !== DELIVER_TYPE.ALL_PROJECTS;
              content =
                clients && clients.length > 0 ? (
                  <>
                    {showSelectButtons && (
                      <>
                        <Button
                          onClick={() =>
                            clients.forEach((client) =>
                              handleSelectAllSites(
                                item.SolutionVersionUUID,
                                item.Arch,
                                client.ClientUUID,
                                client.ProjectSite,
                              ),
                            )
                          }
                          className={styles.select_button}
                          type="weak"
                          style={{ marginRight: 5, marginBottom: 5 }}
                        >
                          全选
                        </Button>
                        <Button
                          onClick={() =>
                            clients.forEach((client) =>
                              handleInverseSelectSites(
                                item.SolutionVersionUUID,
                                item.Arch,
                                client.ClientUUID,
                                client.ProjectSite,
                              ),
                            )
                          }
                          className={styles.select_button}
                          type="weak"
                          style={{ marginRight: 5, marginBottom: 5 }}
                        >
                          反选
                        </Button>
                      </>
                    )}
                    {clients?.map((client) => {
                      let value = false;
                      let indeterminate = false;
                      const clientKey = `${item.SolutionVersionUUID}-${item.Arch}-${client.ClientUUID}`;
                      const checkedList = checkedClientSites[clientKey] || [];
                      if (checkedList.length === client.ProjectSite?.length) {
                        value = true;
                      } else if (checkedList.length > 0) {
                        indeterminate = true;
                      }
                      const disabled = item.DeliverType === DELIVER_TYPE.ALL_PROJECTS;
                      return (
                        <div key={client.ClientUUID} className={styles.add_rel_region__group}>
                          <div className={styles.add_rel_region__group_title}>
                            <Checkbox
                              indeterminate={indeterminate}
                              value={value}
                              disabled={disabled}
                              onChange={() => {
                                handleChangeGroup(item.SolutionVersionUUID, item.Arch, client, value);
                              }}
                            >
                              {client.ClientName}
                            </Checkbox>
                          </div>
                          <Checkbox.Group value={checkedList}>
                            {client.ProjectSite?.map((projectSite) => (
                              <Checkbox
                                name={projectSite.SiteUUID}
                                key={projectSite.SiteUUID}
                                disabled={disabled}
                                onChange={(checked) =>
                                  handleChangeSiteStatus(
                                    item.SolutionVersionUUID,
                                    item.Arch,
                                    client.ClientUUID,
                                    projectSite.SiteUUID,
                                    checked,
                                  )
                                }
                              >
                                {projectSite.SiteName}
                                {projectSite?.SiteArch?.split(';').length > 1 && (
                                  <Text style={{ fontWeight: 600 }}>【一云多芯局点】</Text>
                                )}
                                <Text style={{ fontWeight: 600 }}>
                                  {projectSite.SiteType === 'gray' ? '【迁移】' : ''}
                                </Text>
                              </Checkbox>
                            ))}
                          </Checkbox.Group>
                        </div>
                      );
                    })}
                  </>
                ) : (
                  <StatusTip.EmptyTip emptyText="暂无可勾选的局点，请打开全部局点开关，查看是否有可选局点" />
                );
            }

            return (
              <TcsCollapse.Panel
                key={key}
                id={key}
                title={`${item.Solution}(${item.SolutionVersion})-${
                  getLookupByCode('PackageArch', item.Arch)?.Name || item.Arch
                }【${getLookupByCode('DeliverType', item.DeliverType)?.Name || item.DeliverType}】`}
              >
                <div style={{ maxHeight: 500, overflow: 'auto' }}>{content}</div>
              </TcsCollapse.Panel>
            );
          })}
        </TcsCollapse>
      </div>
    </TcsSpin>
  );
};

export default forwardRef(SelectRelSite);
