/*
 * @Author: superfeng
 * @Date: 2023-03-21 17:04:45
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-11-04 11:25:35
 * @Description: 请输入注释信息
 */
import { IPropsIssueSolutionRel } from '@/common/api/interationManage';
import { TcsButton, TcsModal, TcsSpace } from '@tencent/tcs-component';

import React, { useRef, useState, useEffect } from 'react';
import SelectAppSolution from './SelectAppSolution';
import SelectRelSite from './SelectRelSite';
import { message, Alert } from '@tencent/tea-component';
import { DELIVER_TYPE } from '@/common/constants';
import EditRelSiteModal from '../EditRelSiteModal';
import { ICreateIssueRelatedAppsApi, IterationManage } from '@/common/api/iterationManage.api';
import DoubleConfirmModal from './SelectRelSite/components/DoubleConfirmModal';
import { useParams } from 'react-router-dom';
import styles from './index.module.less';

export interface IProps {
  // 缺陷单uuid
  issueID: string;
  defectType: string;
  issueSolutions: IPropsIssueSolutionRel[];
  issueInfo: any;
  onConfirm?: () => void;
  onCancel?: () => void;
  tapdUrl?: string;
}

export enum Step {
  One = 'one',
  Two = 'two',
}

const AddRefAppModal: React.FC<IProps> = ({
  onConfirm,
  onCancel,
  issueID,
  issueSolutions,
  defectType,
  issueInfo,
  tapdUrl,
}) => {
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [step, setStep] = useState(Step.One);
  const [selectedIssueApps, setSelectedIssueApps] = useState<any[]>([]);
  const selectAppRef = useRef<any>();
  const selectRelSiteRef = useRef<any>();
  const [addRelSiteModalVisible, setAddRelSiteModalVisible] = useState<boolean>(false);
  const [doubleConfirmVisible, setDoubleConfirmVisible] = useState<boolean>(false);
  const [evaluateVerision, setEvaluateVerision] = useState<
    { SolutionVersionName: string; SolutionVersionUUID: string }[]
  >([]);
  const { pagePath } = useParams<{ pagePath: 'story' | 'defect' }>();

  useEffect(() => {
    if (issueID && tapdUrl && pagePath === 'story') {
      IterationManage.ListEvaluatedSolutionVersions({ IssueID: issueID, TAPDUrl: tapdUrl }).then((res) => {
        const SolutionVersions = res?.SolutionVersions?.length ? res.SolutionVersions : [];
        setEvaluateVerision(SolutionVersions);
      });
    }
  }, [issueID, tapdUrl, pagePath]);

  // function handleOK() {
  //   selectAppRef.current
  //     .getData()
  //     .then((res) => {
  //       setSelectedIssueApps(res);
  //     })
  //     .catch((error) => {
  //       console.log(error);
  //     });
  //   setAddRelSiteModalVisible(true);
  // }

  const handleAddIuuseApp = async () => {
    try {
      const formData = await selectAppRef.current.getData();

      // 原始的值
      if (issueSolutions?.length) {
        const currentIssueSolutions = issueSolutions?.map((item) => ({
          Arch: item.Arch,
          Solution: item.SolutionVersionDetail?.Solution?.Name,
          SolutionVersion: item.SolutionVersion,
          SolutionVersionUUID: item.SolutionVersionUUID,
          IssueType: item.IssueType,
          DeliverType: item.DeliverType,
          IssueAppRel: item?.IssueAppRel?.map((rel) => ({
            ApplicationBranchUUID: rel.ApplicationBranchUUID,
            ApplicationName: rel.ApplicationName,
            Arch: rel.Arch,
            IssueID: item.IssueID,
            ProductVersion: rel.ProductVersion,
            ProductVersionUUID: rel.ProductVersionUUID,
          })),
        }));
        const resultDataUUID = currentIssueSolutions?.map((item) => `${item.SolutionVersionUUID}-${item.Arch}`);
        const result = formData?.some((item) => {
          const uuidWithArch = `${item.SolutionVersionUUID}-${item.Arch}`;
          const findIndex = resultDataUUID.findIndex((data) => data === uuidWithArch);
          return findIndex === -1 && item.DeliverType !== DELIVER_TYPE.NO_NEED;
        });
        if (result) {
          setDoubleConfirmVisible(true);
        } else {
          handleConfirm();
        }
      }
    } catch (error: any) {
      message.warning({
        content: error,
      });
    }
  };

  const handleConfirm = async () => {
    const params: ICreateIssueRelatedAppsApi.IRequest = {
      IssueApps: [],
      Sites: [],
    };
    try {
      const res = await selectAppRef.current?.getData();
      if (res) {
        if (issueInfo.Type === 'Other') {
          params.IssueApps = res;
        } else {
          const sites = selectRelSiteRef.current?.getData() || [];
          params.IssueApps = res;
          params.Sites = sites;
          if (
            step === Step.Two &&
            sites?.some(
              (item) => item?.DeliverType === DELIVER_TYPE.SPECIFIED_PROJECTS && item?.SiteInfos?.length === 0,
            )
          ) {
            return;
          }
        }
        setConfirmLoading(true);
        IterationManage.CreateIssueRelatedApps({
          ...params,
        })
          .then(({ Error }) => {
            if (Error) {
              setConfirmLoading(false);
              return message.error({ content: Error.Message });
            }
            message.success({
              content: '新增成功',
            });
            onConfirm?.();
            reset();
            setConfirmLoading(false);
          })
          .finally(() => {
            setConfirmLoading(false);
          });
      }
    } catch (error) {
      message.error({ content: error });
    }
  };

  const handleCancel = () => {
    onCancel?.();
    reset();
  };

  function reset() {
    setStep(Step.One);
    setSelectedIssueApps([]);
  }

  // const handleNext = () => {
  //   selectAppRef.current
  //     .getData()
  //     .then((res) => {
  //       setSelectedIssueApps(res);
  //       setStep(Step.Two);
  //     })
  //     .catch((error) => {
  //       console.log(error);
  //     });
  // };

  // const handlePrevious = () => {
  //   setStep(Step.One);
  // };
  const handleNotRelate = async () => {
    const data = await selectAppRef.current.getData();
    IterationManage.CreateIssueRelatedApps({
      IssueApps: data,
      Sites: [],
    })
      .then(({ Error }) => {
        if (Error) {
          return message.error({ content: Error.Message });
        }
        message.success({
          content: '新增成功',
        });
        setDoubleConfirmVisible(false);
        onConfirm?.();
        reset();
      })
      .finally(() => {});
  };
  const handleRelate = async () => {
    const formData = (await selectAppRef.current.getData()) || [];
    setDoubleConfirmVisible(false);
    if (formData?.length) {
      setStep(Step.Two);
      setSelectedIssueApps(formData);
    }
  };
  const handleAddIuuseAndSites = () => {
    handleRelate();
  };

  return (
    <>
      <TcsModal
        title="新增关联应用"
        visible
        onCancel={handleCancel}
        width={1100}
        wrapClassName={styles.modal}
        style={{ color: 'red' }}
        destroyOnClose
        zIndex={999}
        confirmLoading={confirmLoading}
        onOk={handleConfirm}
        footer={
          issueInfo?.Type !== 'Other' ? (
            <TcsSpace>
              {step === Step.One && (
                <>
                  <TcsButton type="primary" onClick={handleAddIuuseAndSites}>
                    添加应用并关联局点
                  </TcsButton>
                  {issueSolutions?.length !== 0 && (
                    <TcsButton type="primary" onClick={handleAddIuuseApp} loading={confirmLoading}>
                      添加应用
                    </TcsButton>
                  )}
                </>
              )}
              {step === Step.Two && (
                <TcsButton type="primary" onClick={handleConfirm} loading={confirmLoading}>
                  确认
                </TcsButton>
              )}
              <TcsButton onClick={handleCancel}>取消</TcsButton>
            </TcsSpace>
          ) : undefined
        }
      >
        <div style={{ display: Step.One === step ? 'block' : 'none' }}>
          {pagePath === 'story' && (
            <Alert>
              <p>
                <span>经PM评估，当前需求需要回合以下版本:</span>
                <span>{issueInfo.SolutionVersion}</span>
                {evaluateVerision?.length
                  ? evaluateVerision.map((item, index) => (
                      <span key={index}>
                        {index < evaluateVerision.length && '，'}
                        {item?.SolutionVersionName}
                      </span>
                    ))
                  : null}
                <span>，应用评估时请尽量一次性评估。</span>
              </p>
              <p>
                此外，TCE3.6.0，TCE3.8.0，TCE3.6.0stable版本不支持在此新增关联应用，如需添加，请前往极光2.0进行操作。
              </p>
            </Alert>
          )}
          <SelectAppSolution
            issueID={issueID}
            defectType={defectType}
            issueSolutions={issueSolutions}
            issueInfo={issueInfo}
            ref={selectAppRef}
            evaluateVerision={evaluateVerision}
          />
        </div>
        {step === Step.Two && (
          <SelectRelSite issueSolutions={issueSolutions} issueApps={selectedIssueApps} ref={selectRelSiteRef} />
        )}

        <EditRelSiteModal
          visible={addRelSiteModalVisible}
          onClose={() => setAddRelSiteModalVisible(false)}
          issueSolutions={issueSolutions}
          issueApps={selectedIssueApps}
        />
      </TcsModal>
      <DoubleConfirmModal
        visible={doubleConfirmVisible}
        handleCancel={() => setDoubleConfirmVisible(false)}
        handleNotRelate={handleNotRelate}
        handleRelate={handleRelate}
      />
    </>
  );
};

export default AddRefAppModal;
