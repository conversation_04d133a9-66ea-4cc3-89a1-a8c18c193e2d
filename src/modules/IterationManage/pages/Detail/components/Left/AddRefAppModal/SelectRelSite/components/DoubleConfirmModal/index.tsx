import { TcsButton, TcsModal } from '@tencent/tcs-component';
import React from 'react';
export interface IProps {
  visible: boolean;
  handleRelate: () => void;
  handleNotRelate: () => void;
  handleCancel: () => void;
}
const DoubleConfirmModal = ({ visible, handleRelate, handleNotRelate, handleCancel }: IProps) => (
  <TcsModal
    visible={visible}
    title="提示"
    onCancel={handleCancel}
    zIndex={1000}
    destroyOnClose
    footer={
      <>
        <TcsButton type="primary" onClick={handleRelate}>
          去关联
        </TcsButton>
        <TcsButton type="primary" onClick={handleNotRelate}>
          暂不关联
        </TcsButton>
        <TcsButton onClick={handleCancel}>取消</TcsButton>
      </>
    }
  >
    您勾选了新的解决方案版本及架构，可选择关联新的局点，请确认是否前往关联？
  </TcsModal>
);
export default DoubleConfirmModal;
