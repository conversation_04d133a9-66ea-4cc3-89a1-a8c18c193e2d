/*
 * @Author: superfeng
 * @Date: 2023-05-09 11:36:07
 * @LastEditors: superfeng
 * @LastEditTime: 2023-05-12 14:27:32
 * @Description: 请输入注释信息
 */
import ExEditor from '@/common/components/ExEditor';
import { Card, Col, Modal, Row } from '@tencent/tea-component';
import React from 'react';

export interface IProps {
  visible: boolean;
  originValue: string;
  newValue: string;
  onClose: () => void;
}

const ViewRichTextModal: React.FC<IProps> = ({ visible, originValue, newValue, onClose }) => (
  <Modal visible={visible} caption="查看详情" size={1250} onClose={onClose}>
    <Row>
      <Col span={12}>
        <Card>
          <Card.Body title="变更前">{originValue ? <ExEditor value={originValue} readonly /> : '-'}</Card.Body>
        </Card>
      </Col>
      <Col span={12}>
        <Card>
          <Card.Body title="变更后">{newValue ? <ExEditor value={newValue} readonly /> : '-'}</Card.Body>
        </Card>
      </Col>
    </Row>
  </Modal>
);

export default ViewRichTextModal;
