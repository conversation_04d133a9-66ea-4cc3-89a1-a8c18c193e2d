/*
 * @Author: superfeng
 * @Date: 2023-05-08 09:49:47
 * @LastEditors: superfeng
 * @LastEditTime: 2023-05-16 15:08:03
 * @Description: 请输入注释信息
 */
import { listIssueChangeHistories, IIssueChangeHistories } from '@/common/api/interationManage';
import { Button, Table, message } from '@tencent/tea-component';
import React, { useEffect, useState } from 'react';
import styles from './index.module.less';
import useLookup from '@/common/hookups/useLookup';
import ViewRichTextModal from './ViewRichTextModal';
import dayjs from 'dayjs';

export interface IProps {
  issueId: string;
}

const getColumns = ({
  getLookupByCode,
  onViewRichText,
}: {
  getLookupByCode: (type, code) => any;
  onViewRichText: (originValue: string, newValue: string) => void;
}) => [
  {
    header: '序号',
    key: 'recordIndex',
    width: 50,
    fixed: 'left',
  },
  {
    header: '变更时间',
    key: 'CreatedAt',
    width: 120,
    render(record) {
      return dayjs(record.CreatedAt)
        .format('YYYY-MM-DD HH:mm:ss')
        .split(' ')
        .map((item) => <div key={item}>{item}</div>);
    },
  },
  {
    header: '变更人',
    key: 'Creator',
    width: 100,
  },
  {
    header: '变更类型',
    key: 'OperationType',
    width: 80,
    render(record) {
      const { OperationType } = record;
      const lookup = getLookupByCode('IssueUpdateType', OperationType);
      return lookup?.Name || OperationType || '-';
    },
  },
  {
    header: '变更方式',
    key: 'UpdateMethod',
    width: '8%',
    render(record) {
      const { UpdateMethod } = record;
      const lookup = getLookupByCode('IssueUpdateMethod', UpdateMethod);
      return lookup?.Name || UpdateMethod || '-';
    },
  },
  {
    header: '变更内容',
    key: 'UpdateEntity',
    width: '8%',
    render(record) {
      const { UpdateEntity } = record;
      const lookup = getLookupByCode('IssueUpdateEntity', UpdateEntity);
      return lookup?.Name || UpdateEntity || '-';
    },
  },
  {
    header: (
      <table className={styles.history_header_table}>
        <thead>
          <tr>
            <td className={`${styles.history_header_table__td} ${styles.history_header_table__td_field}`}>变更字段</td>
            <td className={`${styles.history_header_table__td} ${styles.history_header_table__td_value}`}>变更前</td>
            <td className={`${styles.history_header_table__td} ${styles.history_header_table__td_value}`}>变更后</td>
          </tr>
        </thead>
      </table>
    ),
    key: 'UpdateInfo',
    render(record: IIssueChangeHistories) {
      const { UpdateColumns = [], RichTextColumns = [], NewValue, OriginValue } = record;
      return (
        <table className={styles.history_body_table}>
          <tbody>
            {UpdateColumns.map((column, index) => {
              const isRichText = RichTextColumns.includes(column);
              return (
                <tr key={column}>
                  <td className={`${styles.history_body_table__td} ${styles.history_body_table__td_field}`}>
                    <div>{column}</div>
                  </td>
                  <td className={`${styles.history_body_table__td} ${styles.history_body_table__td_value}`}>
                    {isRichText ? (
                      <Button type="link" onClick={() => onViewRichText(OriginValue[index], NewValue[index])}>
                        查看详情
                      </Button>
                    ) : (
                      <div>{OriginValue[index] || '-'}</div>
                    )}
                  </td>
                  <td className={`${styles.history_body_table__td} ${styles.history_body_table__td_value}`}>
                    {isRichText ? (
                      <Button type="link" onClick={() => onViewRichText(OriginValue[index], NewValue[index])}>
                        查看详情
                      </Button>
                    ) : (
                      <div>{NewValue[index] || '-'}</div>
                    )}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      );
    },
  },
];

const History: React.FC<IProps> = ({ issueId }) => {
  const [list, setList] = useState<IIssueChangeHistories[]>([]);

  const [loading, setLoading] = useState(false);
  const { getLookupByCode } = useLookup([]);
  const columns = getColumns({ getLookupByCode, onViewRichText: handleViewRichText });
  const [richTextInfo, setRichTextInfo] = useState({
    visible: false,
    originValue: '',
    newValue: '',
  });

  useEffect(() => {
    if (issueId) {
      setLoading(true);
      listIssueChangeHistories({
        IssueID: issueId,
      })
        .then((res) => {
          if (res.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            setList(
              res.ListIssueChangeHistories.map((item, index) => ({
                ...item,
                recordIndex: index + 1,
              })),
            );
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [issueId]);

  function handleViewRichText(originValue, newValue) {
    setRichTextInfo({
      visible: true,
      originValue,
      newValue,
    });
  }

  function handleCloseViewRichText() {
    setRichTextInfo({
      visible: false,
      originValue: '',
      newValue: '',
    });
  }

  return (
    <div>
      <Table
        columns={columns}
        records={list}
        bordered
        compact
        addons={[
          Table.addons.pageable(),
          Table.addons.autotip({
            isLoading: loading,
          }),
          Table.addons.scrollable({
            minWidth: 1200,
          }),
        ]}
      />
      <ViewRichTextModal {...richTextInfo} onClose={handleCloseViewRichText} />
    </div>
  );
};

export default History;
