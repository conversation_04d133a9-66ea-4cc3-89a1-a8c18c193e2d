import { TcsModal, TcsTable } from '@tencent/tcs-component';
import React, { useImperativeHandle, useRef, useState } from 'react';
import { getColumns } from './config';
import { UnlinkApplicationPackageFromIssues, listApplicationPackageIssueRels } from '@/common/api/interationManage';
import { Alert, Button, message } from '@tencent/tea-component';
import VerifyStatusModal from './VerifyStatusModal';
import RelateVerifyPackage from '../RelateVerifyPackage';

export interface IProps {
  onConfirm: () => void;
  isSolutionVersionIncluded?: boolean;
  isAppRulesConfigLimitUsersIncluded?: boolean;
  AppRulesConfigLimitUsersArray?: string[];
}

const MergeVersionModal: React.ForwardRefRenderFunction<any, IProps> = (
  { onConfirm, isSolutionVersionIncluded, isAppRulesConfigLimitUsersIncluded, AppRulesConfigLimitUsersArray },
  ref,
) => {
  const verifyStatusRef = useRef<any>();
  const actionRef = useRef<any>();
  const relateVerifyRef = useRef<any>();

  const [visible, setVisible] = useState(false);
  const [data, setData] = useState<{
    IssueID?: string;
    ApplicationBranchUUID?: string;
    Arch?: string;
    record?: any;
    isSolutionVersionIncluded?: boolean;
    isAppRulesConfigLimitUsersIncluded?: boolean;
  }>({});
  const columns = getColumns({
    onDisassociate: handleDisassociate,
    // onUpdateStatus: handleUpdateStatus,
    isSolutionVersionIncluded: isSolutionVersionIncluded,
    isAppRulesConfigLimitUsersIncluded: isAppRulesConfigLimitUsersIncluded,
    AppRulesConfigLimitUsersArray,
  });

  useImperativeHandle(
    ref,
    () => ({
      show(record: any) {
        setVisible(true);
        setData(record);
      },
    }),
    [],
  );
  function handleRequest({ IssueID, ApplicationBranchUUID, Arch }) {
    if (!IssueID) {
      return Promise.resolve({
        data: [],
      });
    }
    return listApplicationPackageIssueRels({
      IssueID,
      ApplicationBranchUUID,
      Arch,
    }).then((res) => {
      if (res.Error) {
        message.error({
          content: res.Error.Message,
        });
        return {
          data: [],
        };
      }
      return {
        data: res.Items,
      };
    });
  }

  function handleDisassociate(record) {
    UnlinkApplicationPackageFromIssues({
      PackageUUID: record.PackageUUID,
      IssueIDs: [record.IssueID],
    })
      .then((res) => {
        if (res.Error) {
          return message.error({ content: res.Error?.Message });
        }
        actionRef?.current?.reload();
        message.success({ content: '解除关联成功' });
      })
      .catch((error) => {
        message.error({ content: error });
      });
  }

  // function handleUpdateStatus(record, status) {
  //   verifyStatusRef.current.show({
  //     ...record,
  //     Status: status,
  //   });
  // }

  function handleCancel() {
    setVisible(false);
    onConfirm();
  }

  function handleRelease() {
    relateVerifyRef.current.show(data?.record);
  }

  return (
    <TcsModal
      title="已合流版本列表"
      visible={visible}
      width={1200}
      footer={null}
      onCancel={handleCancel}
      destroyOnClose
    >
      <Alert>关联的制品版本会由测试同学测试后转为正式制品Tag</Alert>
      <TcsTable
        actionRef={actionRef}
        headerTitle={
          <Button type="primary" onClick={handleRelease}>
            关联制品列表
          </Button>
        }
        columns={columns}
        request={handleRequest}
        params={data}
        pagination={{}}
        scroll={{
          x: 1300,
        }}
      />
      <VerifyStatusModal
        ref={verifyStatusRef}
        onConfirm={() => {
          actionRef?.current?.reload();
        }}
      />
      <RelateVerifyPackage
        issueID={data?.IssueID}
        ref={relateVerifyRef}
        onRelateChange={() => {
          actionRef?.current?.reload();
        }}
        isSolutionVersionIncluded={isSolutionVersionIncluded}
        isAppRulesConfigLimitUsersIncluded={isAppRulesConfigLimitUsersIncluded}
        AppRulesConfigLimitUsersArray={AppRulesConfigLimitUsersArray}
      />
    </TcsModal>
  );
};

export default React.forwardRef(MergeVersionModal);
