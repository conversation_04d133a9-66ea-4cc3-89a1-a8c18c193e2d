import React from 'react';
import { getAppVersion } from '@/common/utils';
import { TcsButtonGroup } from '@tencent/tcs-component';
import { ExternalLink, Text } from '@tencent/tea-component';
import { PackageTestStatus } from '@/common/constants';
export const getColumns = ({
  onDisassociate,
  // onUpdateStatus,
  isSolutionVersionIncluded = false,
  isAppRulesConfigLimitUsersIncluded = false,
  AppRulesConfigLimitUsersArray,
}: {
  // 解除关联
  onDisassociate: (record: any) => void;
  // 修改状态
  // onUpdateStatus: (record: any, status: string) => void;
  isSolutionVersionIncluded?: boolean;
  isAppRulesConfigLimitUsersIncluded?: boolean;
  AppRulesConfigLimitUsersArray?: string[];
}) => [
  {
    title: '制品版本',
    dataIndex: 'PackageUUID',
    fixed: 'left',
    width: '20%',
    render: (text, record: any) => getAppVersion(record.ApplicationPackage),
  },
  {
    title: '源码分支',
    dataIndex: ['ApplicationPackage', 'CodeBranch'],
    width: '10%',
  },
  {
    title: '源码仓库',
    dataIndex: ['ApplicationPackage', 'CodeRepo'],
    width: '10%',
    render(text: string) {
      const CodeRepo = text;
      if (!CodeRepo) {
        return '-';
      }
      return (
        <Text copyable={{ text: CodeRepo }}>
          <ExternalLink href={CodeRepo}>点击查看</ExternalLink>
        </Text>
      );
    },
  },
  {
    title: '代码提交人',
    dataIndex: ['ApplicationPackage', 'CodeCommitter'],
    width: '10%',
  },
  {
    title: '验证状态',
    dataIndex: 'Status',
    width: '10%',
    valueType: 'dictSelect',
    fieldProps: {
      dictType: 'PackageTestStatus',
    },
  },
  {
    title: '验证说明',
    dataIndex: 'Comments',
    width: '10%',
    ellipsis: true,
  },
  {
    title: '验证人',
    dataIndex: 'LastModifier',
    width: '10%',
  },
  {
    title: '创建人',
    dataIndex: 'Creator',
    width: '10%',
  },
  {
    title: '创建时间',
    dataIndex: 'CreatedAt',
    width: '10%',
    valueType: 'dateTime',
  },
  {
    title: '操作',
    dataIndex: 'option',
    width: '16%',
    valueType: 'option',
    fixed: 'right',
    render(text, record) {
      const isRoot = record?.Creator === 'root';
      const isLimit = isRoot && !isAppRulesConfigLimitUsersIncluded && isSolutionVersionIncluded;
      return (
        <TcsButtonGroup
          type="link"
          items={[
            {
              text: '解除关联',
              confirm: true,
              confirmProps: {
                title: '确认解除关联？',
                onConfirm: () => onDisassociate(record),
              },
              disabled: isLimit,
              tooltip: isLimit
                ? `当前合流版本为自动关联，且解决方案版本为正式版本，相关应用/制品不允许解除关联，如需操作，请联系${AppRulesConfigLimitUsersArray?.join(
                    ';',
                  )}`
                : '',
            },
            {
              // 测试人员灰度
              text: '验证通过',
              hidden: record.Status === PackageTestStatus.TEST_SUCCEED,
              disabled: true,
              tooltip: '当前功能已下线，请关闭弹窗至填写验证结论处操作',
            },
            {
              text: '验证失败',
              hidden: record.Status === PackageTestStatus.TEST_FAILED,
              disabled: true,
              tooltip: '当前功能已下线，请关闭弹窗至填写验证结论处操作',
            },
          ]}
        />
      );
    },
  },
];
