/*
 * @Author: lucyfang
 * @Date: 2024-08-22 11:11:33
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-12-19 10:46:12
 * @Description: 请输入注释信息
 */
import React from 'react';
import { TcsModal, TcsTable } from '@tencent/tcs-component';
import { getColumns } from './config';
import { getAppDependencies } from '@/common/api/iterationManage.api';
import { message } from '@tencent/tea-component';

interface IProps {
  onClose: () => void;
  target: string;
  applicationUUID: string;
  solutionVersionUUID: string;
}

const DependentModal: React.FC<IProps> = ({ onClose, target, applicationUUID, solutionVersionUUID }) => {
  // 修改请求函数以适应 TcsTable 的 request 属性
  const handleRequest = async () => {
    const apiParams = {
      ApplicationUUID: applicationUUID,
      SolutionVersionUUID: solutionVersionUUID,
      Target: target,
    };

    try {
      const res = await getAppDependencies(apiParams);
      if (res?.Code !== 0) {
        return message.error({ content: res?.Data?.Message });
      }
      const data = res?.Data || [];
      return {
        data,
        success: true,
        total: data.length,
      };
    } catch (error) {
      message.error({ content: error.message });
      return {
        data: [],
        success: false,
        total: 0,
      };
    }
  };

  return (
    <TcsModal
      width={1200}
      visible={true}
      title={target === 'upstream' ? '我依赖谁' : '谁依赖我'}
      onCancel={onClose}
      footer={false}
    >
      <TcsTable
        request={handleRequest}
        columns={getColumns({ target })}
        scroll={{ x: 800 }}
        pagination={{ defaultPageSize: 5 }}
        options={false}
      />
    </TcsModal>
  );
};

export default DependentModal;
