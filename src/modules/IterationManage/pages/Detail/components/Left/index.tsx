/*
 * @Author: superfeng
 * @Date: 2023-03-20 10:08:25
 * @LastEditors: superfeng
 * @LastEditTime: 2023-05-15 16:27:13
 * @Description: 请输入注释信息
 */
import { Card, TabPanel, Tabs } from '@tencent/tea-component';
import React from 'react';
import Description from './Description';
import styles from './index.module.less';
import Access from './Access';
import History from './History';
import Attachment from './Attachment';
import Comment from './Comment';
import { RelatedTypeMap } from '@/modules/IterationManage/config';
import RelatedIssueList from './RelatedIssueList';

export interface IProps {
  data: any;
  pagePath: string;
  update: any;
}

const Left: React.FC<IProps> = ({ data, pagePath, update }) => {
  const tabs = [
    {
      id: 'desc',
      label: '详情',
    },
    {
      id: 'access',
      label: '评估',
    },
    // {
    //   id: RelatedTypeMap[pagePath],
    //   label: RelatedTypeMap[pagePath],
    // },
    // {
    //   id: 'metadatas',
    //   label: '元数据',
    // },
    {
      id: 'history',
      label: '变更历史',
    },
  ];

  return (
    <>
      <div className={styles.detail_left}>
        <Card bordered>
          <Card.Body style={{ padding: 10 }}>
            <Tabs tabs={tabs} defaultActiveId="access">
              <TabPanel id="desc">
                <Description data={data} />
              </TabPanel>
              <TabPanel id="access">
                <Access data={data} update={update} />
              </TabPanel>
              <TabPanel id={RelatedTypeMap[pagePath]}>
                <RelatedIssueList data={data} />
              </TabPanel>
              {/* <TabPanel id="metadatas">
                <Metadatas issueId={data?.IssueID}></Metadatas>
              </TabPanel> */}
              <TabPanel id="history">
                <History issueId={data?.IssueID} />
              </TabPanel>
            </Tabs>
          </Card.Body>
        </Card>
        <Attachment issueId={data?.IssueID} />
        <Comment issueId={data?.IssueID} />
      </div>
    </>
  );
};

export default Left;
