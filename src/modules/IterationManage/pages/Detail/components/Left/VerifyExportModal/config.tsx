import { Button, ExternalLink, Text } from '@tencent/tea-component';
import React from 'react';
export const getColumns = () => [
  {
    title: '创建人',
    dataIndex: 'Creator',
    width: '10%',
  },
  {
    title: '创建时间',
    valueType: 'dateTime',
    dataIndex: 'CreatedAt',
    width: '20%',
  },
  {
    title: '状态',
    dataIndex: 'Status',
    width: '10%',
    valueType: 'dictSelect',
    fieldProps: {
      dictType: 'VerificationStatus',
    },
  },
  {
    title: '测试用户',
    dataIndex: 'ClientName',
    width: '20%',
  },
  {
    title: '测试局点',
    dataIndex: 'SiteName',
    width: '20%',
  },
  {
    title: '物料下载',
    dataIndex: 'MaterialURL',
    width: '10%',
    render(text, record) {
      if (!record.MaterialURL || record.MaterialURL === '-') {
        return '-';
      }
      return (
        <Text
          copyable={{
            text: record.MaterialURL,
          }}
        >
          <ExternalLink href={record.MaterialURL}>点击下载</ExternalLink>
        </Text>
      );
    },
  },
  {
    title: '操作',
    dataIndex: 'option',
    valueType: 'option',
    render: (text, record) => (
      <Button
        type="link"
        onClick={() => {
          window.open(
            `/page/flow-design/flow-publish/exec?instance_id=${record.WorkflowInstanceID}&workflow_id=${record.WorkflowID}`,
            '_blank',
          );
        }}
      >
        查看出包详情
      </Button>
    ),
  },
];
