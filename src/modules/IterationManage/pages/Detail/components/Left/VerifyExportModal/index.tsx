import { TcsModal, TcsSpace, TcsTable } from '@tencent/tcs-component';
import React, { useImperativeHandle, useMemo, useState } from 'react';
import { getColumns } from './config';
import { ListPackageVerifications } from '@/common/api/interationManage';
import { Button, message } from '@tencent/tea-component';
import { generateWorkflowInstance } from '@/common/api/deliveryManage';
import { VERIFY_EXPORT_WORKFLOW_UUID } from '@/common/config';

import styles from '../index.module.less';

const VerifyExportModal: React.ForwardRefRenderFunction<any, any> = (props, ref) => {
  const [visible, setVisible] = useState(false);
  const [record, setRecord] = useState<any>();
  const columns = getColumns();
  const [loading, setLoading] = useState(false);

  const isPublished = useMemo(() => record?.OperationSheet?.Status === 'Published', [record?.OperationSheet?.Status]);

  useImperativeHandle(
    ref,
    () => ({
      show(record: any) {
        setVisible(true);
        setRecord(record);
      },
    }),
    [],
  );

  function handleRequest() {
    if (visible && record) {
      return ListPackageVerifications.requestData({
        SourceKey: record.UUID,
      }).then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
          return {
            data: [],
          };
        }
        return {
          data: res.ListPackageVerifications || [],
          success: true,
        };
      });
    }
    return Promise.resolve({
      data: [],
    });
  }

  function handleStartFlow(workflowId: string) {
    setLoading(true);
    generateWorkflowInstance({
      WorkflowUUID: workflowId,
      GlobalValues: {
        SourceKey: record.UUID,
        SourceType: 'IssueSolution',
      },
    })
      .then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          message.success({
            content: '流程启动成功',
          });

          window.open(
            `/page/flow-design/flow-publish/exec?instance_id=${res.WorkflowInstanceID}&workflow_id=${res.WorkflowID}`,
            '_blank',
          );
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }

  const is3100Story = useMemo(() => {
    if (!record) {
      return false;
    }
    return record.SolutionVersion === 'TCE3.10.0' && record.IssueType === 'story';
  }, [record]);

  return (
    <TcsModal
      title="验证记录"
      visible={visible}
      width={1200}
      footer={null}
      wrapClassName={styles.dialog_max_width}
      confirmLoading={loading}
      onCancel={() => {
        setVisible(false);
      }}
    >
      <TcsTable
        columns={columns}
        headerTitle={
          <TcsSpace>
            <Button
              type="primary"
              tooltip={isPublished ? '' : '当前解决方案版本架构关联变更单未发布，验证出包仅支持出制品包，请知悉'}
              onClick={() => {
                handleStartFlow(VERIFY_EXPORT_WORKFLOW_UUID.ProductMarket);
              }}
              loading={loading}
            >
              启动验证出包流程
            </Button>
            {is3100Story && (
              <Button
                onClick={() => {
                  handleStartFlow(VERIFY_EXPORT_WORKFLOW_UUID.Tops);
                }}
                loading={loading}
              >
                启动2.0验证出包流程
              </Button>
            )}
          </TcsSpace>
        }
        request={handleRequest}
        params={{
          SourceKey: record?.UUID,
        }}
      />
    </TcsModal>
  );
};

export default React.forwardRef(VerifyExportModal);
