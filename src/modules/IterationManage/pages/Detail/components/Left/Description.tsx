/*
 * @Author: <PERSON><PERSON>
 * @Date: 2023-03-20 10:22:53
 * @LastEditors: superfeng
 * @LastEditTime: 2023-03-28 18:01:50
 * @Description: 请输入注释信息
 */
import ExEditor from '@/common/components/ExEditor';
import React from 'react';

export interface IProps {
  data: any;
}

const Description: React.FC<IProps> = ({ data }) => (
  <>{data ? <ExEditor readonly value={data.Description} placeholder="" /> : ''} </>
);

export default Description;
