import { TcsModal } from '@tencent/tcs-component';
import React, { useEffect, useRef, useState } from 'react';
import { IPropsIssueSolutionRel } from '@/common/api/interationManage';
import { IterationManage } from '@/common/api/iterationManage.api';
import { message } from '@tencent/tea-component';
import BatchEditRelSite from './components/BatchEditRelsite';

export interface IProps {
  visible: boolean;
  issueSolutions: IPropsIssueSolutionRel[];
  onClose: () => void;
  // 是否是批量出包，还是当前选中的出包
  batchEdit?: boolean;
  issueApps?: any[];
  onConfirm?: () => void;
}
const EditRelSiteModal = ({ visible, issueSolutions, onClose, onConfirm, issueApps, batchEdit }: IProps) => {
  const selectRelSiteRef = useRef<any>();
  const [batchIssueApps, setBacthIssueApps] = useState<any>([]);
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  const handleCancel = () => {
    onClose();
  };
  const fetchSolutionData = (solutionProductAppRels: IPropsIssueSolutionRel[]) => {
    const batchIssueApps = solutionProductAppRels.map((item) => {
      const { Arch, SolutionVersion, SolutionVersionUUID, IssueID, DeliverType, IssueType } = item;
      return {
        Arch,
        Solution: item.SolutionVersionDetail.Solution.Name,
        SolutionVersion,
        SolutionVersionUUID,
        IssueType,
        IssueID,
        DeliverType,
        IssueAppRel: item.IssueAppRel?.map((rel) => ({
          // 应用版本的UUID
          ApplicationBranchUUID: rel.ApplicationBranchUUID,
          // 对应的应用名
          ApplicationName: rel.ApplicationName,
          // // 应用类型，tad 表示tad应用 ted表示老版本的组件
          // ApplicationType: rel.ApplicationType,
          // 架构，x86/arm/power等
          Arch: rel.Arch,
          // 关联的缺陷单，对应story表中的一个需求单或者bug表中的一个缺陷单
          IssueID,
          // 产品版本
          ProductVersion: rel.ProductVersion,
          // 产品版本UUID
          ProductVersionUUID: rel.ProductVersionUUID,
        })),
      };
    });
    return batchIssueApps;
  };

  const handleConfirm = () => {
    const data = selectRelSiteRef?.current?.getData();
    setConfirmLoading(true);
    const updatePromises = data?.issue?.map((item, index) =>
      IterationManage.UpdateIssueSolutionArchDeliveryType({
        UUID: item.UUID,
        DeliverType: item.DeliverType,
        SiteInfos: data?.site[index]?.SiteInfos,
      }),
    );

    Promise.all(updatePromises)
      .then(() => {
        message.success({
          content: '批量修改成功',
        });
        setConfirmLoading(false);
        handleCancel();
      })
      .catch((error) => {
        setConfirmLoading(false);
        message.error({
          content: error?.message || '批量修改失败',
        });
        onConfirm?.();
      });
  };
  useEffect(() => {
    if (visible) {
      setBacthIssueApps(fetchSolutionData(issueSolutions));
    }
  }, [visible, issueSolutions]);

  return (
    <TcsModal
      visible={visible}
      title="批量修改出包局点"
      onCancel={handleCancel}
      width="xl"
      onOk={handleConfirm}
      confirmLoading={confirmLoading}
      destroyOnClose
    >
      <BatchEditRelSite
        issueSolutions={issueSolutions}
        issueApps={batchEdit ? batchIssueApps : issueApps}
        ref={selectRelSiteRef}
        batchEdit={batchEdit}
        // handleConfirm={handleConfirm}
      />
    </TcsModal>
  );
};
export default EditRelSiteModal;
