import { TcsSpin, TcsTable } from '@tencent/tcs-component';
import { Bubble, Checkbox, SearchBox, StatusTip, Switch, Button, Alert, Text } from '@tencent/tea-component';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import styles from '../../../AddRefAppModal/SelectRelSite/index.module.less';
import useLookup from '@/common/hookups/useLookup';
import { DELIVER_TYPE } from '@/common/constants';
import {
  ICreateIssueRelatedAppsApi,
  IListDeliverySitesForIssueSolutionVersionApi,
  IListProjectClientSiteApi,
  IterationManage,
} from '@/common/api/iterationManage.api';
import { IPropsIssueSolutionRel } from '@/common/api/interationManage';
import { message } from 'antd';
import { SOLUTION_ARCH_STATUS } from '@/common/config';
export interface IProps {
  issueApps: any[];
  issueSolutions: IPropsIssueSolutionRel[];
  batchEdit?: boolean;
}

const BatchEditRelSite: React.ForwardRefRenderFunction<any, IProps> = (
  { issueApps, issueSolutions, batchEdit = false },
  ref,
) => {
  // 拿的是getData的数据
  const [loading, setLoading] = useState(false);
  const [clientSitesMap, setClientSitesMap] = useState<Record<string, IListProjectClientSiteApi.IClientSites[]>>({});
  const [checkedClientSites, setCheckedClientSites] = useState<Record<string, string[]>>({});
  const [tableData, setTableData] = useState<any>([]);
  //   const currentTableDataRef = useRef<any>();
  const [editableKeys, setEditableKeys] = useState<string[] | undefined>([]);

  // const [issueSolutionRelSites, setIssueSolutionRelSites] =
  //   useState<IListDeliverySitesForIssueSolutionVersionApi.IResponse['IssueSolutionRelSites']>();

  const { getLookupByCode } = useLookup(['PackageArch', 'DeliverType']);
  const [listDeliverySites, setListDeliverySites] = useState<IListDeliverySitesForIssueSolutionVersionApi.IResponse[]>(
    [],
  );
  const [expandRowKeys, setExpandRowKeys] = useState<string[]>([]);
  useEffect(() => {
    setLoading(true);
    let issueSolutionRelSitesPromise;
    if (issueSolutions?.length) {
      issueSolutionRelSitesPromise = Promise.all(
        issueSolutions.map((item) =>
          IterationManage.ListDeliverySitesForIssueSolutionVersion({
            IssueSolutionRelUUIDs: [item.UUID],
          }),
        ),
      );
    }
    const listProjectClientSitePromises = Promise.all(
      issueApps.map((item) =>
        IterationManage.ListProjectClientSite({
          SolutionVersionID: item.SolutionVersionUUID,
          Arch: item.Arch,
          Applications: item.IssueAppRel?.map((app) => app.ApplicationName) || [],
          IsFullSites: true,
          ShowPatchSites: true,
        }),
      ),
    );

    const promises: any = [];
    if (issueSolutionRelSitesPromise) {
      promises.push(issueSolutionRelSitesPromise);
    }
    promises.push(listProjectClientSitePromises);

    Promise.all(promises)
      .then(([issueSolutionRelSites, res]) => {
        const clientSitesMap: Record<string, IListProjectClientSiteApi.IClientSites[]> = {};
        const checked = {};
        setListDeliverySites(issueSolutionRelSites);
        res?.forEach((item, index) => {
          const issueApp = issueApps[index];
          const key = `${issueApp.SolutionVersionUUID}-${issueApp.Arch}`;
          clientSitesMap[key] = item.ClientInfos.filter((item) => item.ProjectSite?.length);
          if (issueApp.DeliverType === DELIVER_TYPE.ALL_PROJECTS) {
            clientSitesMap[key]?.forEach((item) => {
              const clientKey = `${issueApp.SolutionVersionUUID}-${issueApp.Arch}-${item.ClientUUID}`;
              checked[clientKey] = item.ProjectSite?.map((item) => item.SiteUUID) || [];
            });
          } else if (issueApp.DeliverType === DELIVER_TYPE.SPECIFIED_PROJECTS) {
            const nowIssueSolutionRelSite = issueSolutionRelSites?.[index].IssueSolutionRelSites?.[0]?.Sites?.map(
              (item) => item.SiteUUID,
            );

            clientSitesMap[key]?.forEach((item) => {
              const clientKey = `${issueApp.SolutionVersionUUID}-${issueApp.Arch}-${item.ClientUUID}`;
              // 批量修改出包局点，要按照当前的出包类型去展示局点
              const findSiteIds =
                item?.ProjectSite?.filter((item) => nowIssueSolutionRelSite?.includes(item.SiteUUID))?.map(
                  (item) => item.SiteUUID,
                ) || [];

              if (findSiteIds.length) {
                checked[clientKey] = findSiteIds;
              }
            });
          }
        });
        setCheckedClientSites(checked);
        setClientSitesMap(clientSitesMap);
        const tableDataSource: { [key: string]: any } =
          issueSolutions
            ?.filter((item) => {
              const clientSitesMapKey = Object.keys(clientSitesMap);
              const key = `${item.SolutionVersionUUID}-${item.Arch}`;
              return clientSitesMapKey?.includes(key);
            })
            ?.map((item) => {
              const key = `${item.SolutionVersionUUID}-${item.Arch}`;

              return {
                SolutionName: item.SolutionVersionDetail.Solution.Name,
                SolutionVersion: item.SolutionVersion,
                Arch: item.Arch,
                SolutionVersionDetail: item.SolutionVersionDetail,
                SolutionVersionUUID: item.SolutionVersionUUID,
                IssueAppRel: item.IssueAppRel,
                IssueID: item.IssueID,
                IssueType: item.IssueType,
                // 如果有更改出包类型
                DeliverType: item.DeliverType,
                clientSites: clientSitesMap[key],
                Key: key,

                isFullSites: true,
                UUID: item.UUID,
                Status: item.Status,
              };
            })
            ?.filter(
              (item) => item?.Status === SOLUTION_ARCH_STATUS.DEVELOPING || item?.DeliverType === DELIVER_TYPE.NO_NEED,
            )
            ?.map((item, index) => ({
              ...item,
              index,
            })) || [];

        setTableData(tableDataSource);
        setExpandRowKeys([tableDataSource?.[0]?.Key]);
        const keys: any = tableDataSource?.map((item) => item?.Key) || [];
        setEditableKeys(keys);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [issueApps, issueSolutions, batchEdit]);

  useImperativeHandle(
    ref,
    () => ({
      getData() {
        const siteResult = tableData.map((item) => {
          const key = `${item.SolutionVersionUUID}-${item.Arch}`;
          const result = {
            SolutionVersionUUID: item.SolutionVersionUUID,
            Arch: item.Arch,
            DeliverType: item.DeliverType,
            SiteInfos: [] as ICreateIssueRelatedAppsApi.ISiteInfo[],
          };
          // 仅后续新增客户出包
          if (item.DeliverType === DELIVER_TYPE.NO_NEED) {
            return result;
          }
          const clientSites = clientSitesMap[key];
          const siteInfos: ICreateIssueRelatedAppsApi.ISiteInfo[] = [];
          clientSites?.forEach((client) => {
            const checkedSites = checkedClientSites[`${key}-${client.ClientUUID}`] || [];
            siteInfos.push(
              ...(client.ProjectSite?.filter((item) => checkedSites.includes(item.SiteUUID)) || []).map((item) => ({
                SiteUUID: item.SiteUUID,
                ClientName: client.ClientName,
                SiteName: item.SiteName,
                SiteType: item.SiteType,
              })),
            );
          });
          if (item.DeliverType === DELIVER_TYPE.SPECIFIED_PROJECTS && item.clientSites?.length === 0) {
            message.error({
              content: `${item.SolutionName}(${item.SolutionVersion})-${
                getLookupByCode('PackageArch', item.Arch)?.Name || item.Arch
              }未选择局点，请选择局点后再提交`,
            });
            throw new Error(
              `${item.SolutionName}(${item.SolutionVersion})-${
                getLookupByCode('PackageArch', item.Arch)?.Name || item.Arch
              }未选择局点，请选择局点后再提交`,
            );
          }
          result.SiteInfos = siteInfos;

          return result;
        });
        return { site: siteResult, issue: tableData };
      },
    }),

    // eslint-disable-next-line react-hooks/exhaustive-deps
    [checkedClientSites, issueApps, clientSitesMap, tableData],
  );

  function handleChangeGroup(
    solutionVersionUUID: string,
    arch: string,
    client: IListProjectClientSiteApi.IClientSites,
    oldChecked: boolean,
  ) {
    // 如果之前是全选，则改为全不选
    const key = `${solutionVersionUUID}-${arch}-${client.ClientUUID}`;
    if (oldChecked) {
      setCheckedClientSites((sites) => {
        const selectedSites = sites[key] || [];
        const unSelectedSites = client?.ProjectSite?.map((item) => item.SiteUUID) || [];
        return {
          ...sites,
          [key]: selectedSites.filter((item) => !unSelectedSites.includes(item)),
        };
      });
    } else {
      // 如果之前是半选或者未选，则改为全选
      setCheckedClientSites((sites) => {
        const selectedSites = sites[key] || [];
        const list = Array.from(
          new Set([...(client.ProjectSite?.map((item) => item.SiteUUID) || []), ...selectedSites]),
        );
        return {
          ...sites,
          [key]: list,
        };
      });
    }
  }

  function handleChangeSiteStatus(
    solutionVersionUUID: string,
    arch: string,
    clientUUID: string,
    siteUUID: string,
    checked: boolean,
  ) {
    const key = `${solutionVersionUUID}-${arch}-${clientUUID}`;
    setCheckedClientSites((sites) => {
      let arr = sites[key] || [];
      if (checked) {
        arr = [...arr, siteUUID];
      } else {
        arr = arr.filter((item) => item !== siteUUID);
      }
      return {
        ...sites,
        [key]: arr,
      };
    });
  }

  // 全选当前客户端的所有局点
  function handleSelectAllSites(
    solutionVersionUUID: string,
    arch: string,
    clientUUID: string,
    projectSites: IListProjectClientSiteApi.ISiteBaseInfo[],
  ) {
    const key = `${solutionVersionUUID}-${arch}-${clientUUID}`;
    setCheckedClientSites((prevChecked) => ({
      ...prevChecked,
      [key]: projectSites.map((site) => site.SiteUUID),
    }));
  }

  // 反选当前客户端的所有局点
  function handleInverseSelectSites(
    solutionVersionUUID: string,
    arch: string,
    clientUUID: string,
    projectSites: IListProjectClientSiteApi.ISiteBaseInfo[],
  ) {
    const key = `${solutionVersionUUID}-${arch}-${clientUUID}`;
    setCheckedClientSites((prevChecked) => {
      const currentChecked = prevChecked[key] || [];
      const newChecked = projectSites
        .filter((site) => !currentChecked.includes(site.SiteUUID))
        .map((site) => site.SiteUUID);
      return {
        ...prevChecked,
        [key]: newChecked,
      };
    });
  }

  function changeCheckedClients(value: string, record, clientSites, listDeliverySites) {
    const checked = { ...checkedClientSites };
    if (value === DELIVER_TYPE.ALL_PROJECTS) {
      clientSites?.forEach((item) => {
        const clientKey = `${record.SolutionVersionUUID}-${record.Arch}-${item.ClientUUID}`;
        checked[clientKey] = item?.ProjectSite.map((item) => item.SiteUUID);
      });
    } else if (value === DELIVER_TYPE.SPECIFIED_PROJECTS) {
      const currentSites = listDeliverySites?.IssueSolutionRelSites?.[0]?.Sites;
      const currentSiteIds = currentSites?.map((site) => site.SiteUUID);
      clientSites?.forEach((client) => {
        client.ProjectSite?.forEach((site) => {
          const clientKey = `${record.SolutionVersionUUID}-${record.Arch}-${client.ClientUUID}`;
          if (currentSiteIds?.includes(site.SiteUUID)) {
            checked[clientKey] = [site.SiteUUID];
          } else {
            checked[clientKey] = [];
          }
        });
      });
    }
    setCheckedClientSites(checked);
  }

  function handleDeliverType(value, record) {
    setTableData(
      tableData?.map((item, tableIndex) => (tableIndex === record.index ? { ...item, DeliverType: value } : item)),
    );
    const recordData = record?.record;
    changeCheckedClients(value, recordData, recordData?.clientSites, listDeliverySites?.[record?.index]);
  }

  function handleChangeFullSites(value, record) {
    setLoading(true);
    const promise1 = IterationManage.ListDeliverySitesForIssueSolutionVersion({
      IssueSolutionRelUUIDs: [record?.UUID],
    });

    const promise2 = IterationManage.ListProjectClientSite({
      SolutionVersionID: record.SolutionVersionUUID,
      Arch: record.Arch,
      Applications: record.IssueAppRel.map((app) => app.ApplicationName),
      IsFullSites: value,
      ShowPatchSites: true,
    });
    Promise.all([promise1, promise2])
      .then(([listDeliverySites, clientSites]) => {
        setTableData(
          tableData?.map((item, tableIndex) =>
            tableIndex === record.index ? { ...item, isFullSites: value, clientSites: clientSites?.ClientInfos } : item,
          ),
        );

        changeCheckedClients(record.DeliverType, record, clientSites?.ClientInfos, listDeliverySites);
      })
      .finally(() => {
        setLoading(false);
      });
  }

  function handleSearch(value?: string, record?: Record<string, any>) {
    if (!value) {
      setTableData(
        tableData?.map((item) => ({
          ...item,
          clientSites: clientSitesMap[`${item.SolutionVersionUUID}-${item.Arch}`],
        })),
      );
      return;
    }
    const result = tableData?.map((item, index) => {
      if (index === record?.index) {
        const filteredClients = item.clientSites?.filter((client) => {
          const matchesClientName = client.ClientName.includes(value);
          const matchesSiteName = client.ProjectSite?.some((site) => site?.SiteName?.includes(value));
          return matchesClientName || matchesSiteName;
        });
        return {
          ...item,
          clientSites: filteredClients,
        };
      }
      return item;
    });

    setTableData(result);
  }

  const columns = [
    {
      title: '解决方案',
      dataIndex: 'SolutionName',
    },
    {
      title: '解决方案版本',
      dataIndex: 'SolutionVersion',
    },
    {
      title: '架构',
      dataIndex: 'Arch',
    },
    {
      title: '出包类型',
      dataIndex: 'DeliverType',
      editable: true,
      valueType: 'dictSelect',
      fieldProps: (text, record) => ({
        dictType: 'DeliverType',
        onChange: (value) => {
          handleDeliverType(value, record);
        },
      }),
    },
  ];

  return (
    <>
      <Alert type="warning">
        <div>可用局点状态：此处仅支持处于交付中和售后的局点</div>
        <div>
          迁移局点说明：当前极光Next的局点分为两类，一类是Next局点，另一类是从2.0迁移至Next的局点。对于后者，我们会在局点名称后添加【迁移】标识以示区分。
        </div>
        <div>修改范围：仅支持修改修复状态为【研发中】或出包类型为【仅新增后续出包】的评估记录关联的局点</div>
      </Alert>

      <TcsTable
        options={false}
        cardBordered
        columns={columns}
        dataSource={tableData}
        rowKey="Key"
        editable={{
          editableKeys,
        }}
        loading={loading}
        expandable={{
          expandedRowKeys: expandRowKeys,
          onExpandedRowsChange: (values) => setExpandRowKeys(values),
          expandedRowRender(record) {
            console.log(record, 'IsFullSites');
            let content: JSX.Element = <div />;

            const clients = record.clientSites;
            if (record.DeliverType === DELIVER_TYPE.NO_NEED) {
              content = <StatusTip.EmptyTip emptyText="仅后续新增客户出包无需选择局点" />;
            } else {
              // 检查是否需要显示全选和反选按钮
              const showSelectButtons = record.DeliverType !== DELIVER_TYPE.ALL_PROJECTS;
              content = (
                <div>
                  {clients && clients.length > 0 ? (
                    <>
                      {showSelectButtons && (
                        <>
                          <Button
                            onClick={() =>
                              clients.forEach((client) =>
                                handleSelectAllSites(
                                  record.SolutionVersionUUID,
                                  record.Arch,
                                  client.ClientUUID,
                                  client.ProjectSite,
                                ),
                              )
                            }
                            className={styles.select_button}
                            type="weak"
                            style={{ marginRight: 5, marginBottom: 5 }}
                          >
                            全选
                          </Button>
                          <Button
                            onClick={() =>
                              clients.forEach((client) =>
                                handleInverseSelectSites(
                                  record.SolutionVersionUUID,
                                  record.Arch,
                                  client.ClientUUID,
                                  client.ProjectSite,
                                ),
                              )
                            }
                            className={styles.select_button}
                            type="weak"
                            style={{ marginRight: 5, marginBottom: 5 }}
                          >
                            反选
                          </Button>
                        </>
                      )}
                      <div style={{ maxHeight: 300, overflow: 'auto' }}>
                        {clients?.map((client) => {
                          let value = false;
                          let indeterminate = false;
                          const clientKey = `${record.SolutionVersionUUID}-${record.Arch}-${client.ClientUUID}`;
                          const checkedList = checkedClientSites[clientKey] || [];
                          if (checkedList.length === client.ProjectSite?.length) {
                            value = true;
                          } else if (checkedList.length > 0) {
                            indeterminate = true;
                          }
                          const disabled = record.DeliverType === DELIVER_TYPE.ALL_PROJECTS;
                          return (
                            <div key={client.ClientUUID} className={styles.add_rel_region__group}>
                              <div className={styles.add_rel_region__group_title}>
                                <Checkbox
                                  indeterminate={indeterminate}
                                  value={value}
                                  disabled={disabled}
                                  onChange={() => {
                                    handleChangeGroup(record.SolutionVersionUUID, record.Arch, client, value);
                                  }}
                                >
                                  {client.ClientName}
                                </Checkbox>
                              </div>
                              <Checkbox.Group value={checkedList}>
                                {client.ProjectSite?.map((projectSite) => (
                                  <Checkbox
                                    name={projectSite.SiteUUID}
                                    key={projectSite.SiteUUID}
                                    disabled={disabled}
                                    onChange={(checked) =>
                                      handleChangeSiteStatus(
                                        record.SolutionVersionUUID,
                                        record.Arch,
                                        client.ClientUUID,
                                        projectSite.SiteUUID,
                                        checked,
                                      )
                                    }
                                  >
                                    {projectSite.SiteName}
                                    {projectSite?.SiteArch?.split(';').length > 1 && (
                                      <Text style={{ fontWeight: 600 }}>【一云多芯局点】</Text>
                                    )}
                                    <Text style={{ fontWeight: 600 }}>
                                      {projectSite.SiteType === 'gray' ? '【迁移】' : ''}
                                    </Text>
                                  </Checkbox>
                                ))}
                              </Checkbox.Group>
                            </div>
                          );
                        })}
                      </div>
                    </>
                  ) : (
                    <StatusTip.EmptyTip emptyText="暂无可勾选的局点" />
                  )}
                </div>
              );
            }

            return (
              <TcsSpin spinning={loading}>
                {record?.DeliverType !== DELIVER_TYPE.NO_NEED && (
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 5 }}>
                    <Bubble content="打开表示查看全量可交付局点，关闭表示进行精准过滤，只查询出过指定应用架构组合的可交付局点">
                      <Switch value={record?.isFullSites} onChange={(value) => handleChangeFullSites(value, record)}>
                        {record.isFullSites ? '全量可交付局点' : '应用架构组合局点'}
                      </Switch>
                      <div>
                        {record?.isFullSites && (
                          <Text theme="danger">请确认好选择的局点的出包架构与局点现场的架构信息是否匹配</Text>
                        )}
                      </div>
                    </Bubble>
                    <SearchBox
                      size="l"
                      onSearch={(value) => handleSearch(value, record)}
                      placeholder="可输入客户名称/局点名称进行搜索"
                    />
                  </div>
                )}
                <div>{content}</div>
              </TcsSpin>
            );
          },
        }}
      />
    </>
  );
};

export default forwardRef(BatchEditRelSite);
