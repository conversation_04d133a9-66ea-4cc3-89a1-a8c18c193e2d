/*
 * @Author: superfeng
 * @Date: 2023-04-19 15:44:38
 * @LastEditors: superfeng
 * @LastEditTime: 2023-05-30 11:05:04
 * @Description: 请输入注释信息
 */
import { DefectManagementRoutePath } from '@/common/routePath';
import { TcsModal, TcsTable } from '@tencent/tcs-component';

import React, { useImperativeHandle, useState } from 'react';

const getColumns = () => [
  {
    title: '交付单名称',
    dataIndex: 'Name',
    linkable: true,
    linkProps: {
      linkUrl(text, record) {
        return `${DefectManagementRoutePath.DELIVERY_DETAIL_PAGE}?uuid=${record.UUID}`;
      },
      target: 'blank',
    },
  },
  {
    title: '交付单状态',
    dataIndex: 'Status',
    valueType: 'dictSelect',
    fieldProps: {
      dictType: 'DeliveryStatus',
      showType: 'tag',
    },
  },
];

const DeliveryListModal: React.ForwardRefRenderFunction<any, any> = (props, ref) => {
  const [visible, setVisible] = useState(false);
  const [dataSource, setDataSource] = useState<any[]>([]);

  useImperativeHandle(
    ref,
    () => ({
      show(dataSource) {
        setDataSource(dataSource);
        setVisible(true);
      },
    }),
    [],
  );

  function handleClose() {
    setVisible(false);
  }

  return (
    <TcsModal footer={null} title="关联交付单" visible={visible} onCancel={handleClose}>
      <TcsTable ghost options={false} columns={getColumns()} dataSource={dataSource} />
    </TcsModal>
  );
};

export default DeliveryListModal;
