import { Alert, Justify, Modal, Table, TagSearchBox, message } from '@tencent/tea-component';
import React, { useEffect, useImperativeHandle, useMemo, useState, useRef } from 'react';
import {
  IPropsIssueSolutionRel,
  LinkApplicationPackageToIssues,
  ListApplicationPackageIssueRels,
  ListApplicationPackages,
} from '@/common/api/interationManage';
import { unstable_batchedUpdates } from 'react-dom';
import { TcsModal, TcsButtonGroup } from '@tencent/tcs-component';
import { getAppVersion } from '@/common/utils';
import { injectable, scrollable } from '@tencent/tea-component/lib/table/addons';
import { PackageTestStatus } from '@/common/constants';
import VerifyStatusModal from '../MergeVersionModal/VerifyStatusModal';

export interface IProps {
  issueID: string;
  defectType: string;
  onRelateChange: () => void;
  tableDataSource: any[];
}
const { pageable, autotip } = Table.addons;

const RelatePackage: React.ForwardRefRenderFunction<any, IProps> = (
  { issueID, defectType, onRelateChange, tableDataSource: outerTableDataSource },
  ref,
) => {
  const [visible, setVisible] = useState(false);
  const [searchParams, setSearchParams] = useState<any>();
  const [applicationPackageIssueRels, setApplicationPackageIssueRels] = useState<any[]>([]);

  const [record, setRecord] = useState<any>();
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [pageInfo, setPageInfo] = useState({
    pageSize: 10,
    pageIndex: 1,
    total: 0,
  });
  const verifyStatusRef = useRef<any>();
  // 当用户在弹框里面修改关联制品后，需要获取最新的effectiveVersion,只能从外部表格数据获取
  const effectiveVersion = useMemo(() => {
    let newRecord: any = undefined;
    outerTableDataSource?.forEach((item) => {
      item?.IssueAppRel?.forEach((app) => {
        if (app.UUID === record?.UUID) {
          newRecord = app;
        }
      });
    });
    return newRecord?.ReleaseApplicationPackage ? getAppVersion(newRecord.ReleaseApplicationPackage) : undefined;
  }, [record, outerTableDataSource]);

  const [loading, setLoading] = useState(false);
  const [update, setUpdate] = useState<any>({});

  useImperativeHandle(
    ref,
    () => ({
      show(record: IPropsIssueSolutionRel) {
        setRecord(record);
        setVisible(true);
      },
    }),
    [],
  );

  function handleCancel() {
    setVisible(false);
    unstable_batchedUpdates(() => {
      setPageInfo((info) => ({
        ...info,
        pageIndex: 1,
      }));
    });
  }

  const columns = [
    {
      header: '制品版本',
      key: 'version',
      width: '45%',
      render(row) {
        return getAppVersion(row);
      },
    },
    {
      header: '源代码分支',
      key: 'CodeBranch',
    },
    {
      header: '代码提交人',
      key: 'CodeCommitter',
    },
    {
      header: '验证状态',
      key: 'Status',
      fieldProps: {
        dictType: 'PackageTestStatus',
      },
      render(record) {
        switch (record.StatusText) {
          case PackageTestStatus.WAITING_FOR_TESTING:
            return '待验证';
          case PackageTestStatus.TEST_FAILED:
            return '验证不通过';
          case PackageTestStatus.TEST_SUCCEED:
            return '验证通过';
          default:
            return '-';
        }
      },
    },
    {
      header: '操作',
      key: 'options',
      render(record) {
        return (
          <TcsButtonGroup
            items={[
              {
                text: '关联',
                hidden: !(record.StatusText === PackageTestStatus.NO_MERGE),
                confirm: true,
                confirmProps: {
                  title: '确定关联此制品？',
                  onConfirm: () => hanldeRelatePackage(record),
                },
                tooltip: '关联会自动将制品加入合流版本并验证通过',
              },
              // {
              //   text: '验证通过',
              //   hidden:
              //     record.StatusText === PackageTestStatus.TEST_SUCCEED ||
              //     record.StatusText === PackageTestStatus.NO_MERGE,
              //   onClick: () => {
              //     handleUpdateStatus(record, PackageTestStatus.TEST_SUCCEED);
              //   },
              // },
              // {
              //   text: '验证失败',
              //   hidden:
              //     record.StatusText === PackageTestStatus.TEST_FAILED ||
              //     record.StatusText === PackageTestStatus.NO_MERGE,
              //   onClick: () => {
              //     handleUpdateStatus(record, PackageTestStatus.TEST_FAILED);
              //   },
              // },
            ]}
          />
        );
      },
    },
  ];
  // function handleUpdateStatus(record, status) {
  //   verifyStatusRef.current.show({
  //     ...record,
  //     Status: status,
  //   });
  // }
  function hanldeRelatePackage(value) {
    LinkApplicationPackageToIssues({
      PackageUUID: value.UUID,
      IssueIDs: [issueID],
      IssueType: defectType,
      Status: PackageTestStatus.TEST_SUCCEED,
    }).then((res) => {
      if (res.Error) {
        return message.error({ content: res.Error?.Message });
      }
      message.success({ content: res.Msg });
      setUpdate({});
      onRelateChange();
    });
  }
  // 加载当前已关联制品列表
  useEffect(() => {
    if (visible && issueID) {
      ListApplicationPackageIssueRels({
        IssueID: issueID,
        ApplicationBranchUUID: record.ApplicationBranchUUID,
      }).then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          setApplicationPackageIssueRels(
            res.ListApplicationPackageIssueRels?.filter((item) => item.ApplicationPackage) || [],
          );
        }
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible, issueID, update]);

  useEffect(() => {
    if (visible && issueID) {
      setLoading(true);
      const params: any = {
        ApplicationUUID: record?.ApplicationBranch?.ApplicationUUID,
        ApplicationBranch: record?.ApplicationBranch?.BranchName,
        Arch: record?.Arch,
        // 默认都为正式制品
        PackageType: 1,
        pageNo: pageInfo.pageIndex,
        pageSize: pageInfo.pageSize,
        // 关联正式制品 只获取已验证通过的版本
        Status: PackageTestStatus.TEST_SUCCEED,
      };
      if (searchParams?.length) {
        searchParams.forEach((item) => {
          if (item.attr && item.values?.length) {
            params[item.attr.key] = item.values[0].name;
          } else {
            message.warning({
              content: '不支持使用单个关键字进行搜索',
            });
          }
        });
      }
      ListApplicationPackages(params)
        .then((res) => {
          if (res.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            setDataSource(res.ApplicationPackages || []);
            setPageInfo((page) => ({
              ...page,
              total: res.Total || 0,
            }));
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [visible, update, issueID, record, pageInfo.pageSize, pageInfo.pageIndex, searchParams]);

  const tableData = useMemo((): any[] => {
    if (dataSource && applicationPackageIssueRels) {
      return dataSource.map((item) => {
        const foundValue = applicationPackageIssueRels.find((value) => value?.PackageUUID === item.UUID);
        const statusText = foundValue ? foundValue.Status : 'no_merge';
        const RelateState = foundValue?.Status === PackageTestStatus.TEST_SUCCEED;
        return {
          ...item,
          IssueID: foundValue?.IssueID,
          PackageUUID: foundValue?.PackageUUID,
          RelateState,
          StatusText: statusText,
        };
      });
    }

    return dataSource || [];
  }, [dataSource, applicationPackageIssueRels]);

  function handleQuery(params: any) {
    setSearchParams(params);
  }

  return (
    <TcsModal
      visible={visible}
      title={
        <span style={{ display: 'flex' }}>
          关联正式制品
          <span style={{ flex: 1, marginTop: -4, marginLeft: 10 }}>
            <Modal.Message
              description={`应用：${record?.ApplicationName}；版本：${record?.ApplicationBranch?.BranchName}； 架构：${record?.Arch}`}
            />
          </span>
        </span>
      }
      onCancel={handleCancel}
      footer={null}
      width={950}
    >
      {effectiveVersion && (
        <Alert>
          <div>{`当前缺陷单已关联【${applicationPackageIssueRels.length}】个制品版本，当前生效制品版本为【${effectiveVersion}】`}</div>
          <div>
            注意，此页面如果新增关联制品，会跳过测试步骤，直接标记为验证通过版本，并可以直接用于发布，请谨慎操作！如您期望关联一个已合流的制品交由测试同学验证，请点击“合流版本”列的“查看详情”，在弹窗中关联对应的制品。
          </div>
        </Alert>
      )}
      <Table.ActionPanel>
        <Justify
          right={
            <TagSearchBox
              attributes={[
                {
                  type: 'input',
                  key: 'CommitID',
                  name: 'CommitID',
                },
                {
                  type: 'input',
                  key: 'CodeBranch',
                  name: '源代码分支',
                },
                {
                  type: 'input',
                  key: 'CodeCommitter',
                  name: '代码提交人',
                },
              ]}
              onChange={handleQuery}
            />
          }
        />
      </Table.ActionPanel>
      <Table
        columns={columns}
        records={tableData}
        recordKey="ID"
        addons={[
          pageable({
            recordCount: pageInfo.total,
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            onPagingChange: (query) => {
              setPageInfo((pageInfo) => ({
                ...pageInfo,
                ...query,
              }));
            },
          }),
          autotip({
            isLoading: loading,
          }),
          injectable({
            cell: (props, context) => {
              if (context.record && getAppVersion(context.record) === effectiveVersion) {
                return {
                  style: {
                    color: 'rgb(6, 119, 74)',
                    background: 'rgb(214, 244, 228)',
                  },
                };
              }

              return {};
            },
          }),
          scrollable({
            maxHeight: 'calc(100vh - 380px)',
          }),
        ]}
      />
      <VerifyStatusModal
        ref={verifyStatusRef}
        onConfirm={() => {
          setUpdate({});
          onRelateChange();
        }}
      />
    </TcsModal>
  );
};

export default React.forwardRef(RelatePackage);
