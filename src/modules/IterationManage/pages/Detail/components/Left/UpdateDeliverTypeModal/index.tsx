/*
 * @Author: superfeng
 * @Date: 2023-06-25 16:29:32
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-01-14 11:28:44
 * @Description: 请输入注释信息
 */
import { IPropsIssueSolutionRel } from '@/common/api/interationManage';
import {
  ICreateIssueRelatedAppsApi,
  IListProjectClientSiteApi,
  IterationManage,
} from '@/common/api/iterationManage.api';
import { DELIVER_TYPE } from '@/common/constants';
import { TcsCard, TcsForm, TcsFormDictSelect, TcsFormInstance, TcsModal, TcsSpin } from '@tencent/tcs-component';
import { Alert, Bubble, Button, Checkbox, message, SearchBox, Switch, Text } from '@tencent/tea-component';
import React, { useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { unstable_batchedUpdates } from 'react-dom';
import styles from './index.module.less';

export interface IProps {
  onConfirm: () => void;
}

const UpdateDeliverTypeModal: React.ForwardRefRenderFunction<any, IProps> = ({ onConfirm }, ref) => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isFullSites, setIsFullSites] = useState(false);
  const [clientSites, setClientSites] = useState<IListProjectClientSiteApi.IClientSites[]>([]);
  const [checkedClientSites, setCheckedClientSites] = useState<Record<string, string[]>>({});
  const [record, setRecord] = useState<IPropsIssueSolutionRel>();
  const [deliverCheckedSites, setDeliverCheckedSites] = useState<ICreateIssueRelatedAppsApi.ISiteInfo[]>([]);
  const formRef = useRef<TcsFormInstance>();
  const [newDeliverType, setNewDeliverType] = useState('');
  const [searchValue, setSearchValue] = useState<{ clientName?: string; siteName?: string }>({});
  const filteredClientSites = useMemo(() => {
    if (!searchValue.clientName && !searchValue.siteName) {
      return clientSites; // 如果没有搜索值，返回原始列表
    }
    return clientSites.filter((client) => {
      const matchClientName = client.ClientName.includes(searchValue.clientName || '');
      const matchSiteName = client.ProjectSite?.some((site) => site.SiteName.includes(searchValue.siteName || ''));
      return matchClientName || matchSiteName;
    });
  }, [clientSites, searchValue]);

  useImperativeHandle(
    ref,
    () => ({
      show(record: IPropsIssueSolutionRel) {
        setRecord(record);
        setNewDeliverType(record.DeliverType);
        setVisible(true);
      },
    }),
    [],
  );

  useEffect(() => {
    if (visible && record) {
      setLoading(true);
      Promise.all([
        IterationManage.ListProjectClientSite({
          SolutionVersionID: record.SolutionVersionUUID,
          Arch: record.Arch,
          Applications: record.IssueAppRel?.map((app) => app.ApplicationName),
          IsFullSites: isFullSites,
          ShowPatchSites: true,
        }),
        record.DeliverType === DELIVER_TYPE.SPECIFIED_PROJECTS
          ? IterationManage.ListDeliverySitesForIssueSolutionVersion({
              IssueSolutionRelUUIDs: [record.UUID],
            })
          : Promise.resolve({ IssueSolutionRelSites: undefined }),
      ])
        .then(([clientSites, solutionRelSites]) => {
          unstable_batchedUpdates(() => {
            setClientSites(clientSites.ClientInfos.filter((item) => item.ProjectSite?.length));
            if (solutionRelSites) {
              setDeliverCheckedSites(solutionRelSites?.IssueSolutionRelSites?.[0]?.Sites || []);
            }
          });
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [visible, record, isFullSites]);

  useEffect(() => {
    // 仅后续新增客户出包将所有的局点都选中

    if (newDeliverType === DELIVER_TYPE.ALL_PROJECTS) {
      const checked: Record<string, string[]> = {};
      clientSites.forEach((client) => {
        checked[client.ClientUUID] = client.ProjectSite?.map((item) => item.SiteUUID);
      });
      setCheckedClientSites(checked);
    } else if (newDeliverType === DELIVER_TYPE.NO_NEED) {
      setCheckedClientSites({});
    } else {
      if (deliverCheckedSites?.length) {
        const checked: Record<string, string[]> = {};
        const siteIds = deliverCheckedSites?.map((site) => site.SiteUUID);
        clientSites?.forEach((client) => {
          client.ProjectSite?.forEach((site) => {
            if (siteIds.includes(site.SiteUUID)) {
              if (!checked[client.ClientUUID]) {
                checked[client.ClientUUID] = [];
              }
              checked[client.ClientUUID].push(site.SiteUUID);
            }
          });
        });
        setCheckedClientSites(checked);
      }
    }
  }, [newDeliverType, clientSites, deliverCheckedSites]);

  function handleConfirm() {
    formRef.current?.validateFields().then((values: any) => {
      const siteInfos: ICreateIssueRelatedAppsApi.ISiteInfo[] = [];
      if (values.DeliverType === DELIVER_TYPE.ALL_PROJECTS) {
        clientSites.forEach((client) => {
          client.ProjectSite?.forEach((site) => {
            siteInfos.push({
              ...site,
              ClientName: client.ClientName,
            });
          });
        });
      } else if (values.DeliverType === DELIVER_TYPE.SPECIFIED_PROJECTS) {
        Object.keys(checkedClientSites).forEach((clientUUID) => {
          const client = clientSites.find((item) => item.ClientUUID === clientUUID);
          if (client) {
            client.ProjectSite?.forEach((site) => {
              if (checkedClientSites[clientUUID].includes(site.SiteUUID)) {
                siteInfos.push({
                  ...site,
                  ClientName: client.ClientName,
                });
              }
            });
          }
        });
        if (siteInfos.length === 0) {
          message.warning({
            content: '请选择要评估的局点',
          });
          return;
        }
      }
      setLoading(true);
      IterationManage.UpdateIssueSolutionArchDeliveryType({
        UUID: record!.UUID,
        DeliverType: values.DeliverType,
        SiteInfos: siteInfos,
      })
        .then(() => {
          message.success({
            content: '修复成功',
          });
          onConfirm();
          setVisible(false);
          reset();
        })
        .catch((error) => {
          message.error({
            content: error?.message,
          });
        })
        .finally(() => {
          setLoading(false);
        });
    });
  }

  function handleCancel() {
    setVisible(false);
    reset();
  }

  const reset = () => {
    formRef.current?.resetFields();
    setCheckedClientSites({});
    setDeliverCheckedSites([]);
    setClientSites([]);
  };

  function handleChangeGroup(client: IListProjectClientSiteApi.IClientSites, oldChecked: boolean) {
    // 如果之前是全选，则改为全不选
    if (oldChecked) {
      setCheckedClientSites((sites) => {
        const selectedSites = sites[client.ClientUUID] || [];
        const unSelectedSites = client?.ProjectSite.map((item) => item.SiteUUID) || [];
        return {
          ...sites,
          [client.ClientUUID]: selectedSites.filter((item) => !unSelectedSites.includes(item)),
        };
      });
    } else {
      // 如果之前是半选或者未选，则改为全选
      setCheckedClientSites((sites) => {
        const selectedSites = sites[client.ClientUUID] || [];
        const list = Array.from(
          new Set([...(client.ProjectSite.map((item) => item.SiteUUID) || []), ...selectedSites]),
        );
        return {
          ...sites,
          [client.ClientUUID]: list,
        };
      });
    }
  }

  function handleChangeSiteStatus(
    client: IListProjectClientSiteApi.IClientSites,
    site: IListProjectClientSiteApi.ISiteBaseInfo,
    checked: boolean,
  ) {
    setCheckedClientSites((sites) => {
      let arr = sites[client.ClientUUID];
      if (arr) {
        if (checked) {
          arr.push(site.SiteUUID);
        } else {
          arr = arr.filter((item) => item !== site.SiteUUID);
        }
        return {
          ...sites,
          [client.ClientUUID]: arr,
        };
      }
      if (checked) {
        return {
          ...sites,
          [client.ClientUUID]: [site.SiteUUID],
        };
      }
      return sites;
    });
  }

  function handleSearch(value?: string) {
    setSearchValue({
      clientName: value,
      siteName: value,
    });
  }

  // 全选
  function handleSelectAll() {
    const newCheckedClientSites = filteredClientSites.reduce((acc, client) => {
      acc[client.ClientUUID] = client.ProjectSite.map((site) => site.SiteUUID);
      return acc;
    }, {});
    setCheckedClientSites(newCheckedClientSites);
  }

  // 反选
  function handleInverseSelect() {
    const newCheckedClientSites = { ...checkedClientSites };
    filteredClientSites.forEach((client) => {
      const currentChecked = newCheckedClientSites[client.ClientUUID] || [];
      const newChecked = client.ProjectSite.map((site) => site.SiteUUID)
        .filter((siteUUID) => !currentChecked.includes(siteUUID))
        .concat(
          currentChecked.filter((siteUUID) => !client.ProjectSite.map((site) => site.SiteUUID).includes(siteUUID)),
        );
      newCheckedClientSites[client.ClientUUID] = newChecked;
    });
    setCheckedClientSites(newCheckedClientSites);
  }

  return (
    <TcsModal
      visible={visible}
      title="修改出包类型"
      onCancel={handleCancel}
      onOk={handleConfirm}
      confirmLoading={loading}
      destroyOnClose
      width={1000}
    >
      <TcsSpin spinning={loading}>
        <TcsCard title="出包类型" collapsible bordered>
          <TcsForm
            formRef={formRef}
            initialValues={record}
            onValuesChange={(values) => {
              setNewDeliverType(values.DeliverType);
            }}
          >
            <TcsFormDictSelect
              label="修改出包类型"
              name="DeliverType"
              rules={[
                {
                  required: true,
                  message: '请选择出包类型',
                },
              ]}
              fieldProps={{
                dictType: 'DeliverType',
              }}
            />
          </TcsForm>
        </TcsCard>
        <TcsCard
          title={
            <>
              <span>评估局点</span>
              <span style={{ fontSize: 12, color: 'red' }}>(修改出包类型需重新评估局点)</span>
            </>
          }
          bordered
        >
          {newDeliverType !== DELIVER_TYPE.NO_NEED && (
            <Alert type="warning">
              <p>可用局点状态：此处仅支持处于交付中和售后的局点</p>
              <p>
                迁移局点说明：当前极光Next的局点分为两类，一类是Next局点，另一类是从2.0迁移至Next的局点。对于后者，我们会在局点名称后添加【迁移】标识以示区分。
              </p>
            </Alert>
          )}
          <div style={{ maxHeight: 500, overflow: 'auto' }}>
            {newDeliverType === DELIVER_TYPE.NO_NEED && <Alert type="info">仅后续新增客户出包无需选择局点</Alert>}
            {newDeliverType === DELIVER_TYPE.ALL_PROJECTS && (
              <Alert type="info">存量与新增客户均需出包，所有的局点都将被勾选上，无法取消</Alert>
            )}
            {newDeliverType !== DELIVER_TYPE.NO_NEED && (
              <div style={{ marginBottom: 10, display: 'flex', justifyContent: 'space-between' }}>
                <Bubble content="打开表示查看全量可交付局点，关闭表示进行精准过滤，只查询出过指定应用架构组合的可交付局点">
                  <Switch value={isFullSites} onChange={(value) => setIsFullSites(value)}>
                    {isFullSites ? '全量可推送局点' : '应用架构组合局点'}
                  </Switch>
                  <div>
                    {isFullSites && (
                      <Text theme="danger">请确认好选择的局点的出包架构与局点现场的架构信息是否匹配</Text>
                    )}
                  </div>
                </Bubble>
                <SearchBox size="l" onSearch={handleSearch} placeholder="可输入客户名称/局点名称进行搜索" />
              </div>
            )}
            {newDeliverType !== DELIVER_TYPE.NO_NEED && (
              <>
                {newDeliverType === DELIVER_TYPE.SPECIFIED_PROJECTS && (
                  <>
                    <Button onClick={handleSelectAll} type="weak" style={{ marginRight: 5, marginBottom: 5 }}>
                      全选
                    </Button>
                    <Button onClick={handleInverseSelect} type="weak" style={{ marginRight: 5, marginBottom: 5 }}>
                      反选
                    </Button>
                  </>
                )}

                {filteredClientSites?.map((client) => {
                  let value = false;
                  let indeterminate = false;
                  const checkedList = checkedClientSites[client.ClientUUID] || [];
                  if (checkedList.length === client.ProjectSite?.length) {
                    value = true;
                  } else if (checkedList.length > 0) {
                    indeterminate = true;
                  }
                  const disabled = newDeliverType === DELIVER_TYPE.ALL_PROJECTS;
                  return (
                    <div key={client.ClientUUID} className={styles.add_rel_region__group}>
                      <div className={styles.add_rel_region__group_title}>
                        <Checkbox
                          indeterminate={indeterminate}
                          value={value}
                          disabled={disabled}
                          onChange={() => {
                            handleChangeGroup(client, value);
                          }}
                        >
                          {client.ClientName}
                        </Checkbox>
                      </div>
                      <Checkbox.Group value={checkedList}>
                        {client.ProjectSite?.map((projectSite) => (
                          <Checkbox
                            name={projectSite.SiteUUID}
                            key={projectSite.SiteUUID}
                            disabled={disabled}
                            onChange={(checked) => handleChangeSiteStatus(client, projectSite, checked)}
                          >
                            {projectSite.SiteName}
                            {projectSite?.SiteArch?.split(';').length > 1 && (
                              <Text style={{ fontWeight: 600 }}>【一云多芯局点】</Text>
                            )}
                            <Text style={{ fontWeight: 600 }}>{projectSite.SiteType === 'gray' ? '【迁移】' : ''}</Text>
                          </Checkbox>
                        ))}
                      </Checkbox.Group>
                    </div>
                  );
                })}
              </>
            )}
          </div>
        </TcsCard>
      </TcsSpin>
    </TcsModal>
  );
};

export default React.forwardRef(UpdateDeliverTypeModal);
