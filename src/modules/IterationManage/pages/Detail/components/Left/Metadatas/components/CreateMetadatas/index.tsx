import { CreateIssueMetadata, UpdateIssueMetadata } from '@/common/api/interationManage';
import { ProForm } from '@/common/components';
import { Button, Input, Modal, message } from '@tencent/tea-component';
import React, { useImperativeHandle, useRef, useState } from 'react';
export interface IProps {
  IssueSolutionData: any;
  onAddSuccess: () => void;
}
const CreateMetadatas: React.ForwardRefRenderFunction<any, IProps> = ({ IssueSolutionData, onAddSuccess }, ref) => {
  const [metadataVisible, setMetadataVisible] = useState<boolean>(false);
  const [record, setRecord] = useState<any>();
  const formRef = useRef<any>();

  useImperativeHandle(
    ref,
    () => ({
      show(value) {
        setMetadataVisible(true);
        setRecord(value ? { ...value, MetadataValue: JSON.stringify(value.MetadataValue, null, 2) } : undefined);
      },
    }),
    [],
  );
  const handleConfirm = () => {
    formRef.current.validateFields().then((values: any) => {
      if (record) {
        UpdateIssueMetadata({
          IssueMetadataUUID: record.UUID,
          MetadataValue: JSON.parse(values?.MetadataValue),
        })
          .then((res) => {
            if (res.Error) {
              return message.error({ content: res.Error.Message });
            }
            onAddSuccess?.();
          })
          .finally(() => {
            setMetadataVisible(false);
          });
      } else {
        CreateIssueMetadata({
          ...values,
          IssueID: IssueSolutionData.IssueID,
          IssueSolutionRelUUID: IssueSolutionData.UUID,
          MetadataValue: JSON.parse(values?.MetadataValue),
        })
          .then((res) => {
            if (res.Error) {
              return message.error({ content: res.Error.Message });
            }
            onAddSuccess();
          })
          .finally(() => {
            setMetadataVisible(false);
          });
      }
    });
  };
  function isJsonString(str) {
    try {
      JSON.parse(str);
      return true;
    } catch (err) {
      return false;
    }
  }

  return (
    <Modal
      caption={`${record ? '编辑' : '新建'}元数据`}
      visible={metadataVisible}
      onClose={() => setMetadataVisible(false)}
      destroyOnClose
    >
      <Modal.Body>
        <ProForm formRef={formRef} initialValues={record}>
          <ProForm.Item
            label="元数据名"
            dataIndex="MetadataKey"
            rules={{
              required: {
                value: true,
                message: '请输入元数据名',
              },
            }}
          >
            <Input readOnly={record} size="l" />
          </ProForm.Item>
          <ProForm.Item
            label="元数据值"
            dataIndex="MetadataValue"
            rules={{
              validate: (value) => {
                if (!isJsonString(value)) {
                  return '请输入正确的json格式';
                }
                return undefined;
              },
            }}
          >
            <Input.TextArea style={{ height: 200 }} size="l" />
          </ProForm.Item>
        </ProForm>
      </Modal.Body>
      <Modal.Footer>
        <Button type="primary" onClick={handleConfirm}>
          确定
        </Button>
        <Button type="weak" onClick={() => setMetadataVisible(false)}>
          取消
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
export default React.forwardRef(CreateMetadatas);
