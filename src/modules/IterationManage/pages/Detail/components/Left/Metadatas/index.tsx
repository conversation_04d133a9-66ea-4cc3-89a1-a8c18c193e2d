/*
 * @Author: superfeng
 * @Date: 2023-03-17 10:47:27
 * @LastEditors: superfeng
 * @LastEditTime: 2023-06-25 18:44:26
 * @Description: 请输入注释信息
 */
import { Button, Justify, Modal, PopConfirm, Table, message } from '@tencent/tea-component';
import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';
import { DeleteIssueMetadata, ListIssueMetadatasWithDetail } from '@/common/api/interationManage';
import useLookup from '@/common/hookups/useLookup';
import MetaDatasDetail from './components/MetaDatasDetail';
import CreateMetadatas from './components/CreateMetadatas';
import dayjs from 'dayjs';

const { pageable, autotip } = Table.addons;
// export interface IProps {
//   // issueId: string;
// }
export const getColumns = ({
  getLookupByCode,
  handleViewMetaDatas,
  handleDelete,
  handleEditMetadata,
}: {
  getLookupByCode: (type, code) => any;
  handleViewMetaDatas: (value) => void;
  handleEditMetadata: (value) => void;
  handleDelete: (value) => void;
}) => [
  {
    header: '元数据名',
    key: 'MetadataKey',
    width: 80,
    render(record) {
      const { MetadataKey } = record;
      const lookup = getLookupByCode('IssueMetadataKey', MetadataKey);
      if (lookup) {
        return lookup.Name;
      }
      return MetadataKey || '-';
    },
  },
  {
    header: '元数据值',
    key: 'MetadataValue',
    width: 50,
    fixed: 'left',
    render: (record) => <a onClick={() => handleViewMetaDatas(record)}>查看元数据</a>,
  },
  {
    header: '创建用户',
    key: 'Creator',
    width: 50,
    fixed: 'left',
  },
  {
    header: '创建日期',
    key: 'CreatedAt',
    width: 50,
    fixed: 'left',
    render(record) {
      if (record.CreatedAt) {
        return dayjs(record.CreatedAt)
          .format('YYYY-MM-DD HH:mm:ss')
          .split(' ')
          .map((item) => <div key={item}>{item}</div>);
      }
      return '-';
    },
  },
  {
    header: '修改日期',
    key: 'UpdateAt',
    width: 50,
    fixed: 'left',
    render(record) {
      if (record.UpdateAt) {
        return dayjs(record.UpdateAt)
          .format('YYYY-MM-DD HH:mm:ss')
          .split(' ')
          .map((item) => <div key={item}>{item}</div>);
      }
      return '-';
    },
  },

  {
    header: '操作',
    key: 'options',
    width: 80,
    fixed: 'left',
    render: (record) => (
      <>
        <Button onClick={() => handleEditMetadata(record)} type="link">
          编辑
        </Button>

        <PopConfirm
          title="确定要删除？"
          message="删除后，元数据将不再生效！"
          footer={(close) => (
            <>
              <Button
                type="link"
                onClick={() => {
                  handleDelete(record);
                  close();
                }}
              >
                删除
              </Button>
              <Button
                type="text"
                onClick={() => {
                  close();
                }}
              >
                取消
              </Button>
            </>
          )}
          placement="top-start"
        >
          <Button type="link">删除</Button>
        </PopConfirm>
      </>
    ),
  },
];
const Metadatas: React.ForwardRefRenderFunction<any, any> = ({}, ref) => {
  const [record, setRecord] = useState<any>();
  const [visible, setVisible] = useState<boolean>(false);
  useImperativeHandle(
    ref,
    () => ({
      show(value) {
        setVisible(true);
        setRecord(value);
      },
    }),
    [],
  );
  const [dataSource, setDataSource] = useState<any[]>([]);

  const [loading, setLoading] = useState(false);
  const [pageInfo, setPageInfo] = useState({
    pageSize: 10,
    pageIndex: 1,
    total: 0,
  });
  const metaDataRef = useRef<any>();
  const createMetadatasRef = useRef<any>();

  const { getLookupByCode } = useLookup([]);

  const handleEditMetadata = (value) => {
    // metaDataRef?.current?.show(value.MetadataValue);
    createMetadatasRef.current.show(value);
  };
  const handleViewMetaDatas = (value) => {
    metaDataRef?.current?.show(value);
  };

  const handleDelete = async (value) => {
    try {
      const { Error } = await DeleteIssueMetadata({
        IssueMetadataUUID: value.UUID,
      });
      if (Error) {
        return message.error({ content: Error.Message });
      }
      message.success({ content: '删除成功' });
      handleSearch();
    } catch (error) {
      console.error(error);
    }
  };
  const handleSearch = () => {
    setLoading(true);
    ListIssueMetadatasWithDetail({
      IssueSolutionRelUUID: record?.UUID,
    })
      .then((res) => {
        setDataSource(res.ListIssueMetadatas || []);
        setPageInfo((page) => ({
          ...page,
          total: res.Total!,
        }));
      })
      .finally(() => {
        setLoading(false);
      });
  };
  const handleAdd = () => {
    createMetadatasRef.current.show();
  };
  useEffect(() => {
    if (visible) {
      handleSearch();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  return (
    <>
      <Modal visible={visible} caption="元数据查看" size="xl" onClose={() => setVisible(false)}>
        <Modal.Body>
          <Table.ActionPanel>
            <Justify
              left={
                <Button type="primary" onClick={handleAdd}>
                  新建
                </Button>
              }
            />
          </Table.ActionPanel>
          <Table
            records={dataSource}
            recordKey="ID"
            bordered
            addons={[
              pageable({
                recordCount: pageInfo.total,
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                onPagingChange: (query) => {
                  setPageInfo((pageInfo) => ({
                    ...pageInfo,
                    ...query,
                  }));
                },
              }),
              autotip({
                isLoading: loading,
              }),
            ]}
            // @ts-ignore
            columns={getColumns({ getLookupByCode, handleViewMetaDatas, handleDelete, handleEditMetadata })}
          />
        </Modal.Body>
      </Modal>

      <MetaDatasDetail ref={metaDataRef} />
      <CreateMetadatas IssueSolutionData={record} ref={createMetadatasRef} onAddSuccess={handleSearch} />
    </>
  );
};

export default React.forwardRef(Metadatas);
