import { ProForm, QueryFilter } from '@/common/components';
import useLookup from '@/common/hookups/useLookup';
import { Card, Select } from '@tencent/tea-component';

import React from 'react';

export interface IProps {
  onSearch: (values: any) => void;
  initialValues?: any;
  //   pagePath: string;
}

const Search: React.FC<IProps> = ({ onSearch, initialValues }) => {
  const handleSearch = (values) => {
    onSearch(values);
  };
  const { lookups } = useLookup(['PackageArch', 'IssueMetadataKey', 'IssueMetadataStatus']);

  return (
    <Card style={{ marginBottom: 20 }}>
      <Card.Body>
        <QueryFilter onSearch={handleSearch} initialValues={initialValues}>
          <ProForm.Item label="数据类型" dataIndex="MetadataKey">
            <Select
              placeholder="请选择数据类型"
              appearance="button"
              size="m"
              matchButtonWidth
              clearable
              options={
                lookups.IssueMetadataKey?.map((item) => ({
                  value: item.Code,
                  text: item.Name,
                })) || []
              }
            />
          </ProForm.Item>
          <ProForm.Item label="架构" dataIndex="Arch">
            <Select
              placeholder="请选择架构"
              appearance="button"
              size="m"
              matchButtonWidth
              clearable
              options={
                lookups?.PackageArch?.map((item) => ({
                  value: item.Code,
                  text: item.Name,
                })) || []
              }
            />
          </ProForm.Item>

          <ProForm.Item label="状态" dataIndex="Status">
            <Select
              placeholder="请选择状态"
              appearance="button"
              size="m"
              matchButtonWidth
              clearable
              options={
                lookups.IssueMetadataStatus?.map((item) => ({
                  value: item.Code,
                  text: item.Name,
                })) || []
              }
            />
          </ProForm.Item>
        </QueryFilter>
      </Card.Body>
    </Card>
  );
};

export default Search;
