import { Button, ExternalLink, Modal, Text } from '@tencent/tea-component';
import React, { useImperativeHandle, useMemo, useState } from 'react';

const MetaDatasDetail: React.ForwardRefRenderFunction<any, any> = ({}, ref) => {
  const [visible, setVisible] = useState(false);
  const [record, setRecord] = useState<any>();
  useImperativeHandle(
    ref,
    () => ({
      show(value) {
        setVisible(true);
        setRecord(value);
      },
    }),
    [],
  );

  const detail = useMemo(() => {
    if (!record || !record?.MetadataValue) {
      return '-';
    }
    const metadataValue = record.MetadataValue;
    // 打制品tag流水线
    if (record?.MetadataKey === 'IssueSolutionVersionArchTagWorkflow') {
      const workflowLink = `${window.location.origin}/page/flow-design/flow-publish/exec?workflow_id=${metadataValue.WorkflowID}&instance_id=${metadataValue.WorkflowInstanceID}`;
      return (
        <span>
          制品Tag流水线:
          <Text copyable={{ text: workflowLink }}>
            <ExternalLink href={workflowLink}>点击访问</ExternalLink>
          </Text>
        </span>
      );
    }
    return <pre>{JSON.stringify(metadataValue, null, 2)}</pre>;
  }, [record]);

  const close = () => setVisible(false);
  return (
    <Modal visible={visible} caption="元数据详情" onClose={close} size="l">
      <Modal.Body>{detail}</Modal.Body>
      <Modal.Footer>
        <Button type="primary" onClick={close}>
          关闭
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default React.forwardRef(MetaDatasDetail);
