import { TcsForm, TcsModal } from '@tencent/tcs-component';
import React, { useEffect, useState, useRef } from 'react';
import useLookup from '@/common/hookups/useLookup';
import { TagSelect, TagOption, message, Alert } from '@tencent/tea-component';
import { IterationManage } from '@/common/api/iterationManage.api';
import { TCE_VERSION_ORDER, TCS_VERSION_ORDER } from './config';
import styles from './index.module.less';

export interface IProps {
  onClose: () => void;
  info: any;
}

const findMinVersionByOrder = (versions: string[], versionOrder: string[]): string =>
  versions.reduce(
    (min, version) => (versionOrder.indexOf(version) < versionOrder.indexOf(min) ? version : min),
    versions[0],
  );

const VersionAssessModal = ({ onClose, info }: IProps) => {
  const { IssueID, TapdUrl, SolutionVersion } = info;
  const [options, setOptions] = useState<TagOption[]>([]);
  const [tagSelectValue, setTagSelectValue] = useState<string[]>([]);
  const [evaluatedVersions, setEvaluatedVersions] = useState<string[]>([]);
  const [nonDeletableTags, setNonDeletableTags] = useState<string[]>([]);
  const formRef = useRef<any>();
  const [loading, setLoading] = useState(false);
  const { lookups } = useLookup(['SolutionVersionInfo']);

  useEffect(() => {
    const fetchData = async () => {
      const evaluatedSolutionVersionsRes = await IterationManage.ListEvaluatedSolutionVersions({
        IssueID,
        TAPDUrl: TapdUrl,
      });

      const firstDictionary = lookups?.SolutionVersionInfo?.[0] || {};
      const extra = firstDictionary?.Extra || {};
      const versions = [...(extra.TCE || []), ...(extra.TCS || [])];
      const optionsData = versions.map((version) => ({ text: version, value: version }));
      // 下拉列表，不包括原本的解决方案版本
      const filteredOptionsData = optionsData.filter((option) => option?.value !== SolutionVersion);

      const SolutionVersions = evaluatedSolutionVersionsRes?.SolutionVersions?.length
        ? evaluatedSolutionVersionsRes.SolutionVersions.map((item) => item.SolutionVersionName)
        : [];

      setEvaluatedVersions(SolutionVersions);

      let baseTCEVersion = info?.Solution?.Code === 'tce' ? SolutionVersion : null;
      let baseTCSVersion = info?.Solution?.Code === 'tcs-paas' ? SolutionVersion : null;

      if (SolutionVersions.length > 0) {
        // 已评估版本列表区分tce，tcs
        const tceVersions = SolutionVersions.filter((version) => version.startsWith('TCE'));
        const tcsVersions = SolutionVersions.filter((version) => version.startsWith('TCS'));

        // 将原本解决方案加到已选列表
        baseTCEVersion ? tceVersions.push(baseTCEVersion) : tcsVersions.push(baseTCSVersion);

        // 找最小基线版本
        if (tceVersions.length > 0) {
          baseTCEVersion = findMinVersionByOrder(tceVersions, TCE_VERSION_ORDER);
        }
        if (tcsVersions.length > 0) {
          baseTCSVersion = findMinVersionByOrder(tcsVersions, TCS_VERSION_ORDER);
        }
      }

      const initialVersions = [];
      if (baseTCEVersion) {
        const startIndex = TCE_VERSION_ORDER.indexOf(baseTCEVersion);
        initialVersions.push(
          ...TCE_VERSION_ORDER.slice(startIndex).filter(
            (version) =>
              version !== SolutionVersion &&
              !SolutionVersions.includes(version) &&
              filteredOptionsData.some((option) => option.value === version),
          ),
        );
      }
      if (baseTCSVersion) {
        const startIndex = TCS_VERSION_ORDER.indexOf(baseTCSVersion);
        initialVersions.push(
          ...TCS_VERSION_ORDER.slice(startIndex).filter(
            (version) =>
              version !== SolutionVersion &&
              !SolutionVersions.includes(version) &&
              filteredOptionsData.some((option) => option.value === version),
          ),
        );
      }

      // 更新待选项，排除已评估的版本
      const remainingOptions = filteredOptionsData.filter((option) => !SolutionVersions.includes(option.value));
      setOptions(remainingOptions);

      setTagSelectValue(initialVersions);
      setNonDeletableTags(initialVersions);
      formRef.current?.setFieldsValue({
        tagSelectValue: initialVersions,
        evaluatedVersions: SolutionVersions,
      });
    };

    fetchData();
  }, []);

  const handleTagChange = (tags: string[]) => {
    const newTags = [...tags];
    const addVersions = (versionOrder, selectedVersion) => {
      const startIndex = versionOrder.indexOf(selectedVersion);
      const additionalVersions = versionOrder
        .slice(startIndex)
        .filter((version) => options.some((option) => option.value === version));
      additionalVersions.forEach((version) => {
        if (!newTags.includes(version)) {
          newTags.push(version);
        }
      });
    };

    tags.forEach((tag) => {
      if (tag.startsWith('TCE')) {
        addVersions(TCE_VERSION_ORDER, tag);
      } else if (tag.startsWith('TCS')) {
        addVersions(TCS_VERSION_ORDER, tag);
      }
    });

    const uniqueTags = [...new Set(newTags)];
    setTagSelectValue(uniqueTags);
    formRef.current.setFieldsValue({ tagSelectValue: uniqueTags });
  };

  const handleDeleteTag = (tag: TagOption) => {
    if (nonDeletableTags.includes(tag.value)) {
      return false;
    }

    // 找已选版本中的最小版本
    const isMinVersion = (versionOrder, version) => {
      const selectedVersions = tagSelectValue.filter((tag) => versionOrder.includes(tag));
      const minVersion = findMinVersionByOrder(selectedVersions, versionOrder);
      return version === minVersion;
    };

    if (tag.value.startsWith('TCE') && !isMinVersion(TCE_VERSION_ORDER, tag.value)) {
      return false;
    }

    if (tag.value.startsWith('TCS') && !isMinVersion(TCS_VERSION_ORDER, tag.value)) {
      return false;
    }

    const remainingTags = tagSelectValue.filter((value) => value !== tag.value);
    if (remainingTags.length < tagSelectValue.length) {
      setTagSelectValue(remainingTags);
      formRef.current.setFieldsValue({ tagSelectValue: remainingTags });
      return true;
    }
    return false;
  };

  const handleSave = () => {
    formRef.current?.validateFields()?.then(() => {
      setLoading(true);
      IterationManage.EvaluateStorySolutionVersions({
        TAPDUrl: TapdUrl,
        SourceSolutionVersion: SolutionVersion,
        SolutionVersions: tagSelectValue,
      })
        .then((res: any) => {
          onClose();
          if (res?.Message) {
            message.success({
              content: res?.Message,
            });
          }
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  return (
    <TcsModal
      wrapClassName={styles.modal}
      visible
      title="回合版本评估"
      onCancel={onClose}
      width={900}
      onOk={handleSave}
      confirmLoading={loading}
    >
      {SolutionVersion && (
        <Alert>
          <p>当前需求单解决方案版本：{SolutionVersion}</p>
          <p>回合版本评估不支持删除，若要删除已评估的版本复制单请前往对应TAPD单进行手动拒绝。</p>
        </Alert>
      )}

      <TcsForm formRef={formRef}>
        {evaluatedVersions?.length > 0 ? (
          <TcsForm.Item label="已评估的解决方案版本" name="evaluatedVersions">
            <TagSelect open={false} value={evaluatedVersions} hideCloseButton={true} onDeleteTag={() => false} />
          </TcsForm.Item>
        ) : null}
        <TcsForm.Item
          label="待回合评估的解决方案版本"
          name="tagSelectValue"
          rules={[
            {
              required: true,
              message: '请选择解决方案版本',
            },
          ]}
        >
          <TagSelect
            options={options}
            value={tagSelectValue}
            onChange={handleTagChange}
            onDeleteTag={handleDeleteTag}
            hideCloseButton={false}
            placeholder="请选择解决方案版本"
          />
        </TcsForm.Item>
      </TcsForm>
    </TcsModal>
  );
};

export default VersionAssessModal;
