/*
 * @Author: superfeng
 * @Date: 2023-05-10 17:13:22
 * @LastEditors: superfeng
 * @LastEditTime: 2023-05-15 15:53:37
 * @Description: 请输入注释信息
 */
import { Button, Icon, List, PopConfirm, Progress, Text } from '@tencent/tea-component';
import React from 'react';
import styles from './FileList.module.less';
import dayjs from 'dayjs';

export interface IFile {
  filename: string;
  id: string;
  url: string;
  status: 'success' | 'error' | 'uploading';
  percent: number;
  // 创建人
  creator: string;
  // 创建时间
  createAt: string;
}
export interface IProps {
  fileList: IFile[];
  onRemoveFile: (file: IFile) => void;
}

const FileList: React.FC<IProps> = ({ fileList = [], onRemoveFile }) => {
  function handleDownload(item: IFile) {
    if (item.url) {
      window.open(item.url, '_blank');
    }
  }
  function handleRemove(item: IFile) {
    onRemoveFile(item);
  }
  return (
    <List type="divide">
      {fileList.map((item) => {
        if (item.status === 'uploading') {
          return (
            <List.Item key={item.id}>
              <div className={styles.file_item}>
                <Progress percent={item.percent} text={item.filename} />
              </div>
            </List.Item>
          );
        }
        return (
          <List.Item key={item.id}>
            <div className={styles.file_item}>
              <div className={styles.file_item__title}>
                <Text>{item.filename}</Text>
              </div>
              <div className={styles.file_item__info}>
                <Text theme="label" className={styles.file_item__info_item}>
                  {item.creator}
                </Text>
                <Text theme="label" className={styles.file_item__info_item}>
                  {dayjs(item.createAt).format('YYYY-MM-DD HH:mm:ss')}
                </Text>
                {item.status === 'success' && (
                  <>
                    <Text theme="label" className={styles.file_item__info_item}>
                      <Icon type="download" onClick={() => handleDownload(item)} style={{ cursor: 'pointer' }} />
                    </Text>
                    <Text theme="label">
                      <PopConfirm
                        title="确认要删除附件？"
                        footer={(close) => (
                          <>
                            <Button
                              type="link"
                              onClick={() => {
                                close();
                                handleRemove(item);
                              }}
                            >
                              删除
                            </Button>
                            <Button
                              type="text"
                              onClick={() => {
                                close();
                              }}
                            >
                              取消
                            </Button>
                          </>
                        )}
                      >
                        <Icon type="close" style={{ cursor: 'pointer' }} />
                      </PopConfirm>
                    </Text>
                  </>
                )}
              </div>
            </div>
          </List.Item>
        );
      })}
    </List>
  );
};

export default FileList;
