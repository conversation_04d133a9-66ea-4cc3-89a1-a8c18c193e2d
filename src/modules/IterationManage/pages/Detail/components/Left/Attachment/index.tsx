/*
 * @Author: superfeng
 * @Date: 2023-05-09 19:34:02
 * @LastEditors: superfeng
 * @LastEditTime: 2023-05-16 14:54:36
 * @Description: 请输入注释信息
 */
// import { getUploadCosUrl } from '@/common/api/uploadFile';
import { Card, message } from '@tencent/tea-component';
import React, { useEffect, useRef, useState } from 'react';
// import { UploadEventContext, ProgressInfo } from '@tencent/tea-component/lib/upload/uploadFile';
import FileList, { IFile } from './FileList';
// import dayjs from 'dayjs';
import { deleteIssueAttachment, listIssueAttachments } from '@/common/api/interationManage';
import Loading from '@/common/components/Loading';

export interface IProps {
  issueId: string;
}

const Attachment: React.FC<IProps> = ({ issueId }) => {
  const [fileList, setFileList] = useState<IFile[]>([]);
  const [loading, setLoading] = useState(false);
  const fileListRef = useRef<IFile>();
  fileListRef.current = fileList;

  useEffect(() => {
    if (issueId) {
      setLoading(true);
      listIssueAttachments({
        IssueID: issueId,
        DeletedAt: 0,
      })
        .then((res) => {
          if (res.Error) {
            message.error({
              content: `上传失败:${res.Error.Message}`,
            });
          } else {
            setFileList(
              res.ListIssueAttachments?.map(
                (item): IFile => ({
                  id: item.ID,
                  filename: item.Name,
                  creator: item.Creator,
                  createAt: item.CreatedAt,
                  url: item.DownloadUrl,
                  status: 'success',
                }),
              ) || [],
            );
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [issueId]);

  // const updateFileInfo = (id, info: Partial<IFile>) => {
  //   setFileList((fileList) =>
  //     fileList.map((item) => {
  //       if (item.id === id) {
  //         return {
  //           ...item,
  //           ...info,
  //         };
  //       }
  //       return item;
  //     }),
  //   );
  // };

  // 从fileList中删除某一项
  const deleteFileList = (id) => {
    setFileList((fileList) => fileList.filter((item) => item.id !== id));
  };

  // const getActionUrl = (file) => {
  //   setFileList((fileList) => [
  //     ...(fileList || []),
  //     {
  //       filename: file.name,
  //       id: file.id,
  //       percent: 0,
  //       status: 'uploading',
  //       creator: 'superfeng',
  //       createAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
  //     },
  //   ]);
  //   return getUploadCosUrl({
  //     fileName: file.name,
  //   });
  // };

  // const handleUploadSuccess = useCallback(
  //   (file) => {
  //     createIssueAttachment({
  //       DownloadUrl: file.url,
  //       Name: file.filename,
  //       IssueID: issueId,
  //     }).then((res) => {
  //       if (res.Error) {
  //         message.error({
  //           content: `文件上传失败,失败原因为:${res.Error.Message}`,
  //         });
  //         deleteFileList(file.id);
  //       } else {
  //         updateFileInfo(file.id, {
  //           status: 'success',
  //           percent: 100,
  //           id: res.Attachment.ID,
  //           name: res.Attachment.Name,
  //           createAt: res.Attachment.CreatedAt,
  //           creator: res.Attachment.Creator,
  //         });
  //       }
  //     });
  //   },
  //   [issueId],
  // );

  // function handleUploadError(result, context) {
  //   // updateFileInfo(context.file.id, {
  //   //   status: 'error',
  //   // });
  //   deleteFileList(context.file.id);
  //   message.error({
  //     content: '文件上传失败',
  //   });
  // }

  // function handleProgress(progress: ProgressInfo, context: UploadEventContext) {
  //   updateFileInfo(context.file.id, {
  //     percent: progress.percent,
  //   });
  // }

  function handleRemoveFile(file: IFile) {
    setLoading(true);
    deleteIssueAttachment({
      AttachmentID: file.id as number,
    })
      .then((res) => {
        if (res.Error) {
        } else {
          message.success({
            content: '附件删除成功',
          });
          deleteFileList(file.id);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }

  // function handleBeforeUpload(file) {
  //   const filename = file.name;
  //   const size = file.size / 1024 / 1024 / 1024;
  //   const xhr = new XMLHttpRequest();
  //   if (size > 5) {
  //     message.error({
  //       content: '上传文件不能大于5G',
  //     });

  //     return Promise.reject(new Error('上传文件不能大于5G'));
  //   }
  //   // onChange?.({
  //   //   name: filename,
  //   //   percent: 0,
  //   //   status: 'warning',
  //   // });
  //   setLoading(true);
  //   // onLoading?.(true);
  //   getActionUrl(file)!
  //     .then((result) => {
  //       if (result.Error) {
  //         deleteFileList(file.id);
  //         message.error({
  //           content: result.Error.Message,
  //         });
  //         throw new Error(result.Error.Message);
  //       } else {
  //         updateFileInfo(file.id, {
  //           url: getDownloadUrl(result.Response.Key),
  //         });
  //       }

  //       if (result.Response.Url) {
  //         const url = new URL(result.Response.Url);
  //         xhr.open('PUT', `${window.location.protocol}//${url.hostname}${url.pathname}${url.search}`, true);
  //         xhr.onreadystatechange = () => {
  //           if (xhr.readyState === 4) {
  //             const res = {
  //               id: file.id,
  //               url: getDownloadUrl(result.Response.Key),
  //               filename,
  //               status: 'success',
  //             };
  //             message.success({
  //               content: '上传成功',
  //             });
  //             setLoading(false);
  //             handleUploadSuccess(res);
  //           }
  //         };

  //         xhr.upload.onprogress = function (e) {
  //           updateFileInfo(file.id, {
  //             percent: (e.loaded / e.total) * 100,
  //           });
  //         };

  //         xhr.onerror = function () {
  //           message.error({
  //             content: `${file.name}上传失败`,
  //           });
  //           // onChange?.(undefined);
  //           setLoading(false);
  //           // updateFileInfo(context.file.id, {
  //           //   status: 'error',
  //           // });
  //           deleteFileList(file.id);
  //           message.error({
  //             content: '文件上传失败',
  //           });
  //         };
  //         xhr.send(file);
  //       }
  //     })
  //     .catch(() => {
  //       message.error({
  //         content: `${file.name}上传失败`,
  //       });
  //       deleteFileList(file.id);
  //       // onChange?.(undefined);
  //       setLoading(false);
  //       // onLoading?.(false);
  //     });

  //   return Promise.reject();
  // }

  return (
    <Card bordered>
      <Card.Header>
        <h3 style={{ padding: 10, margin: 0 }}>
          <span style={{ marginRight: 10 }}>附件</span>
          {/* <Upload
            multiple
            beforeUpload={handleBeforeUpload}
            // action={handleAction}
            // method="PUT"
            // onSuccess={handleUploadSuccess}
            // onError={handleUploadError}
            // onProgress={handleProgress}
          >
            {({ open }) => (
              <>
                <Button onClick={open} type="text">
                  <div>
                    <Icon type="plus" />
                    上传
                  </div>
                </Button>
              </>
            )}
          </Upload> */}
        </h3>
      </Card.Header>
      <Card.Body>
        <Loading loading={loading}>
          <div style={{ maxWidth: 600, marginTop: 10 }}>
            <FileList fileList={fileList} onRemoveFile={handleRemoveFile} />
          </div>
        </Loading>
      </Card.Body>
    </Card>
  );
};

export default Attachment;
