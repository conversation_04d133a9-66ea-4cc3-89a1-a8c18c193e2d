import { getStoryWithDetail, listStoryToBugs } from '@/common/api/interationManage';
import useLookup from '@/common/hookups/useLookup';
import { RelatedTypeMap } from '@/modules/IterationManage/config';
import { Table, Tag, message } from '@tencent/tea-component';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

export interface IProps {
  data: any;
}
export const getColumns = ({
  onDetail,
  getLookupByCode,
  pagePath,
}: {
  onDetail: (record) => void;
  getLookupByCode: (type, code) => any;
  pagePath: string;
}) => [
  {
    key: 'IssueID',
    header: `${RelatedTypeMap[pagePath]}ID`,
    fixed: 'left',
  },
  {
    key: 'Title',
    header: '标题',
    width: '35%',
    render(record: any) {
      return <a onClick={() => onDetail(record)}>{record.Title}</a>;
    },
  },
  {
    key: 'Priority',
    header: '优先级',
    render(record) {
      const { Priority } = record;
      const lookup = getLookupByCode('Priority', Priority);
      if (lookup) {
        return <Tag style={{ color: lookup.Extra?.Color }}>{lookup.Name}</Tag>;
      }
      return Priority || '-';
    },
  },
  {
    key: 'Status',
    header: '状态',
    render(record) {
      const { Status } = record;
      const lookup = getLookupByCode('IssueStatus', Status);
      if (lookup) {
        return <Tag style={{ color: lookup.Extra?.Color }}>{lookup.Name}</Tag>;
      }
      return Status || '-';
    },
  },
  {
    key: 'Owner',
    header: '处理人',
  },
  {
    key: 'Creator',
    header: '创建人',
  },
  {
    key: 'CreatedAt',
    header: '创建时间',
    align: 'center',

    render(record) {
      return dayjs(record.CreatedAt)
        .format('YYYY-MM-DD HH:mm:ss')
        .split(' ')
        .map((item) => <div key={item}>{item}</div>);
    },
  },
];
const RelatedIssueList: React.FC<IProps> = ({ data }) => {
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const { pagePath } = useParams<{ pagePath: 'story' | 'defect' }>();
  const { getLookupByCode } = useLookup([]);

  useEffect(() => {
    if (data?.ParentStoryIssueID) {
      if (pagePath === 'defect') {
        setLoading(true);

        getStoryWithDetail({
          IssueID: data?.ParentStoryIssueID,
        }).then((res) => {
          if (res.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            setDataSource(res.GetStory ? [res.GetStory] : []);
            setLoading(false);
          }
        });
      }
    }
    if (pagePath === 'story') {
      setLoading(true);
      listStoryToBugs({
        ParentStoryIssueID: data?.IssueID,
      }).then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          setDataSource(res.ListBugs || []);
          setLoading(false);
        }
      });
    }
  }, [data, pagePath]);
  function handleDetail(record: any) {
    if (pagePath === 'story') {
      window.open(
        `/page/defect_manage/__develop_center_next/iteration_manage/defect/detail?issue_id=${record.IssueID}`,
        '_blank',
      );
    } else {
      window.open(
        `/page/defect_manage/__develop_center_next/iteration_manage/story/detail?issue_id=${record.IssueID}`,
        '_blank',
      );
    }
  }

  return (
    <Table
      columns={getColumns({ onDetail: handleDetail, getLookupByCode, pagePath })}
      records={dataSource}
      addons={[
        Table.addons.autotip({
          isLoading: loading,
        }),
      ]}
    />
  );
};

export default RelatedIssueList;
