/*
 * @Author: superfeng
 * @Date: 2023-03-20 19:35:30
 * @LastEditors: superfeng
 * @LastEditTime: 2023-06-26 10:14:24
 * @Description: 请输入注释信息
 */
import React, { useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';

import PageDesignBoard from '@/common/components/PageDesignBoard';
import { listTemplate } from '@/common/api/interationManage';
import { message } from '@tencent/tea-component';
import { IssueTypeConfig } from '@/modules/IterationManage/config';

export interface IProps {
  data: any;
  pagePath: string;
  isEdit?: boolean;
}

const Right: React.ForwardRefRenderFunction<any, IProps> = ({ data: formData, pagePath, isEdit = false }, ref) => {
  const boardRef = useRef<any>(null);
  const [templateList, setTemplateList] = useState<any[]>([]);
  const issueType = IssueTypeConfig[pagePath];
  const inputParams = useMemo(
    () => ({
      readonly: !isEdit,
      formData: {
        ...formData,
        DeliverOwners: formData?.DeliverOwners ? formData.DeliverOwners.split(';') : [],
        DevOwners: formData?.DevOwners ? formData?.DevOwners.split(';') : [],
        Owner: formData?.Owner ? formData?.Owner.split(';') : [],
        TestOwners: formData?.TestOwners ? formData?.TestOwners.split(';') : [],
      },
    }),
    [formData, isEdit],
  );

  useImperativeHandle(
    ref,
    () => ({
      getFormData: async () => {
        const [result] = boardRef.current?.pageTriggerEmit?.('getFormData');
        if (result instanceof Promise) {
          const formData = await result;
          if (!formData) {
            throw new Error('');
          }
          return formData;
        }
        return result;
      },
    }),
    [],
  );

  useEffect(() => {
    listTemplate({
      IssueType: issueType,
    }).then((res) => {
      if (res.Error) {
        message.error({
          content: res.Error.Message,
        });
      } else {
        setTemplateList(res.ListIssueTemplates);
        return res.ListIssueTemplates;
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const template = useMemo(() => {
    if (formData && templateList) {
      if (!formData.IssueTemplateUUID) {
        return templateList[0];
      }
      return templateList.find((item) => item.UUID === formData.IssueTemplateUUID);
    }
  }, [formData, templateList]);

  return template ? (
    <PageDesignBoard ref={boardRef} key={isEdit ? 1 : 2} inputParams={inputParams} list={template?.Template} />
  ) : (
    <div>暂无模板</div>
  );
};

export default React.forwardRef(Right);
