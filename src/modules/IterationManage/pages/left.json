{"page": {"id": "c11320be-0b57-42ee-9546-607dfef02a8a", "name": "页面", "code": "__page__", "properties": {"pageWidth": "420", "pageAlign": "center", "showPageHeader": false, "inputParams": {"value": "return {\n  readonly:false,\n  formData: {}\n}", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return {\n      readonly: false,\n      formData: {}\n    };\n  } catch (error) {\n    console.error('表达式解析错误，错误原因: ', error, error.message);\n    if ($utils) {\n      $utils.ui.message.error('表达式执行错误，详情请查看浏览器控制台日志');\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": []}}, "events": {"eventList": [{"eventName": "onOpen", "eventType": "custom", "eventCode": "// 工具类\nfunction requestCloudApi(options) {\n  const {\n    action,\n    paramVar,\n    resultCode,\n    outerParamName,\n    isPaging = false\n  } = options\n  return $utils.api.requestCloudApi(action, {\n    [outerParamName]: paramVar\n  }).then(res => {\n    if (res.Error) {\n      throw new Error(res.Error.Message)\n    } else {\n      // 如果有分页，则返回数据和分页\n      if (isPaging) {\n        return {\n          data: res[resultCode],\n          total: res.Total\n        }\n      }\n      return res[resultCode]\n    }\n  }).catch(error => {\n    $utils.ui.message.error(error.message);\n    throw error;\n  })\n}\n\nasync function requestLookups(types, useTenantID) {\n return await requestCloudApi({\n    action: 'ListProductDictionaryDetails',\n    paramVar: $utils.api.transformCloudApiParams({\n      Type: {\n        Op: 'in',\n        Value: types || []\n      },\n      TenantID: useTenantID ? window.jiguang_currentNs: 0\n    }, {\n      useEqFields: ['TenantID']\n    }),\n    resultCode: 'ListProductDictionaries',\n    outerParamName: 'ProductDictionary'\n  })\n}\n\nfunction arrayToObject(array, key) {\n  const object = {}\n  array.forEach(item => {\n    if (!object[item[key]]) {\n      object[item[key]] = []\n    }\n    object[item[key]].push(item)\n  })\n  return object;\n}\n\n// 加载数据字典值\ntry {\n  const lookupResult_OSArch_Severity_Priority = await requestLookups([\"OSArch\",\n    \"Severity\",\n    \"Priority\"],\n    false);\n    debugger\n  $utils.state.updateState(state => {\n    state.lookupMaps = {\n      ...state.lookupMaps,\n      ...arrayToObject(lookupResult_OSArch_Severity_Priority || [], 'Type')\n    }\n    \n  })\n\n} catch (error) {}", "codeGenerateInfo": "[{\"id\":\"requestLookup\",\"uuid\":\"c12cfa62-9e8a-49a9-934b-6a523530a6ad\",\"title\":\"加载数据字典值\",\"properties\":{\"type\":\"OSArch,Severity,Priority\",\"useTenantID\":false,\"mountGlobalState\":true,\"globalStateName\":\"lookupMaps\"},\"catchList\":[],\"finallyList\":[],\"chosen\":false,\"selected\":false,\"name\":\"\"}]", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = async function () {\n  try {\n    // 工具类\n    function requestCloudApi(options) {\n      const {\n        action,\n        paramVar,\n        resultCode,\n        outerParamName,\n        isPaging = false\n      } = options;\n      return $utils.api.requestCloudApi(action, {\n        [outerParamName]: paramVar\n      }).then(res => {\n        if (res.Error) {\n          throw new Error(res.Error.Message);\n        } else {\n          // 如果有分页，则返回数据和分页\n          if (isPaging) {\n            return {\n              data: res[resultCode],\n              total: res.Total\n            };\n          }\n          return res[resultCode];\n        }\n      }).catch(error => {\n        $utils.ui.message.error(error.message);\n        throw error;\n      });\n    }\n    async function requestLookups(types, useTenantID) {\n      return await requestCloudApi({\n        action: 'ListProductDictionaryDetails',\n        paramVar: $utils.api.transformCloudApiParams({\n          Type: {\n            Op: 'in',\n            Value: types || []\n          },\n          TenantID: useTenantID ? window.jiguang_currentNs : 0\n        }, {\n          useEqFields: ['TenantID']\n        }),\n        resultCode: 'ListProductDictionaries',\n        outerParamName: 'ProductDictionary'\n      });\n    }\n    function arrayToObject(array, key) {\n      const object = {};\n      array.forEach(item => {\n        if (!object[item[key]]) {\n          object[item[key]] = [];\n        }\n        object[item[key]].push(item);\n      });\n      return object;\n    }\n\n    // 加载数据字典值\n    try {\n      const lookupResult_OSArch_Severity_Priority = await requestLookups([\"OSArch\", \"Severity\", \"Priority\"], false);\n      debugger;\n      $utils.state.updateState(state => {\n        state.lookupMaps = {\n          ...state.lookupMaps,\n          ...arrayToObject(lookupResult_OSArch_Severity_Priority || [], 'Type')\n        };\n      });\n    } catch (error) {}\n  } catch (error) {\n    console.error('表达式解析错误，错误原因: ', error, error.message);\n    if ($utils) {\n      $utils.ui.message.error('表达式执行错误，详情请查看浏览器控制台日志');\n    }\n  }\n}();\n      return __result__;\n  })()"}]}, "list": [{"properties": {"initialValues": {"value": "return $context.inputParams.formData || {}", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return $context.inputParams.formData || {};\n  } catch (error) {\n    console.error('表达式解析错误，错误原因: ', error, error.message);\n    if ($utils) {\n      $utils.ui.message.error('表达式执行错误，详情请查看浏览器控制台日志');\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.inputParams.formData"]}, "colNum": 1, "layout": "horizontal", "disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "labelWrap": true, "labelWidth": 110, "dataMonitor": true, "submitterButtons": {"showCount": 3, "preRender": {"useExpr": true, "wrapFunction": false, "dependencyVariableList": []}, "postRender": {"useExpr": true, "wrapFunction": false, "dependencyVariableList": []}}, "readonly": {"value": "return $context.inputParams.readonly", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return $context.inputParams.readonly;\n  } catch (error) {\n    console.error('表达式解析错误，错误原因: ', error, error.message);\n    if ($utils) {\n      $utils.ui.message.error('表达式执行错误，详情请查看浏览器控制台日志');\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.inputParams.readonly"]}}, "id": "fe387b01-158f-42ce-8113-d056ddce9e9c", "type": "form", "category": "container", "list": [{"properties": {"formItemProps": {"label": {"value": "解决方案", "useExpr": false, "wrapFunction": false}, "name": "Solution", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"value": "", "useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"useExpr": false, "wrapFunction": false}, "tag": false, "showSearch": true, "searchCaseSensitive": false, "defaultValue": {"useExpr": false, "wrapFunction": false}, "placeholder": {"value": "请选择解决方案", "useExpr": false, "wrapFunction": false}, "labelInValue": false, "propLoading": {"value": false, "useExpr": false, "wrapFunction": false}, "dataSource": {"sourceType": "request", "requestDependParams": {"value": "return {}", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return {};\n  } catch (error) {\n    console.error('表达式解析错误，错误原因: ', error, error.message);\n    if ($utils) {\n      $utils.ui.message.error('表达式执行错误，详情请查看浏览器控制台日志');\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": []}, "requestCode": {"value": "// 工具类\nfunction requestCloudApi(options) {\n  const {\n    action,\n    paramVar,\n    resultCode,\n    outerParamName,\n    isPaging = false\n  } = options\n  return $utils.api.requestCloudApi(action, {\n    [outerParamName]: paramVar\n  }).then(res => {\n    if (res.Error) {\n      throw new Error(res.Error.Message)\n    } else {\n      // 如果有分页，则返回数据和分页\n      if (isPaging) {\n        return {\n          data: res[resultCode],\n          total: res.Total\n        }\n      }\n      return res[resultCode]\n    }\n  }).catch(error => {\n    $utils.ui.message.error(error.message);\n    throw error;\n  })\n}\n\n// 请求数据引擎数据\ntry {\n  return await requestCloudApi({\n    action: 'ListSolutions',\n    paramVar: undefined,\n    resultCode: 'ListSolutions',\n    outerParamName: 'Solution',\n    isPaging: false\n  });\n\n} catch (error) {}", "useExpr": true, "wrapFunction": false, "codeGenerateInfo": "[{\"id\":\"requestCloud\",\"uuid\":\"2c927616-713a-4122-b3f1-d4722e66b5c7\",\"title\":\"请求数据引擎数据\",\"properties\":{\"action\":\"ListSolutions\",\"resultCode\":\"ListSolutions\",\"outerParamName\":\"Solution\",\"isPaging\":false,\"autoReturn\":true},\"catchList\":[],\"finallyList\":[],\"chosen\":false,\"selected\":false,\"name\":\"\"}]", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = async function () {\n  try {\n    // 工具类\n    function requestCloudApi(options) {\n      const {\n        action,\n        paramVar,\n        resultCode,\n        outerParamName,\n        isPaging = false\n      } = options;\n      return $utils.api.requestCloudApi(action, {\n        [outerParamName]: paramVar\n      }).then(res => {\n        if (res.Error) {\n          throw new Error(res.Error.Message);\n        } else {\n          // 如果有分页，则返回数据和分页\n          if (isPaging) {\n            return {\n              data: res[resultCode],\n              total: res.Total\n            };\n          }\n          return res[resultCode];\n        }\n      }).catch(error => {\n        $utils.ui.message.error(error.message);\n        throw error;\n      });\n    }\n\n    // 请求数据引擎数据\n    try {\n      return await requestCloudApi({\n        action: 'ListSolutions',\n        paramVar: undefined,\n        resultCode: 'ListSolutions',\n        outerParamName: 'Solution',\n        isPaging: false\n      });\n    } catch (error) {}\n  } catch (error) {\n    console.error('表达式解析错误，错误原因: ', error, error.message);\n    if ($utils) {\n      $utils.ui.message.error('表达式执行错误，详情请查看浏览器控制台日志');\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": []}}, "autoTransformData": true, "changeDataSelectValue": "nothing", "transformDataType": "object", "labelField": "Name", "valueField": "ID"}}, "id": "9d73b6d5-a92a-45de-922f-9a21d31d110c", "type": "select", "category": "component", "events": {"eventList": []}, "name": "下拉框", "code": "select_1679302866543", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "下拉框", "code": "select_1679302866543", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "发现版本", "useExpr": false, "wrapFunction": false}, "name": "SolutionVersion", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"value": "", "useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"useExpr": false, "wrapFunction": false}, "tag": false, "showSearch": true, "searchCaseSensitive": false, "defaultValue": {"useExpr": false, "wrapFunction": false}, "placeholder": {"value": "请选择解决方案版本", "useExpr": false, "wrapFunction": false}, "labelInValue": false, "propLoading": {"value": false, "useExpr": false, "wrapFunction": false}, "dataSource": {"sourceType": "request", "requestDependParams": {"value": "return {\n  SolutionID:$context.formData.Solution\n}", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return {\n      SolutionID: $context.formData.Solution\n    };\n  } catch (error) {\n    console.error('表达式解析错误，错误原因: ', error, error.message);\n    if ($utils) {\n      $utils.ui.message.error('表达式执行错误，详情请查看浏览器控制台日志');\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.formData.Solution"]}, "requestCode": {"value": "// 工具类\nfunction requestCloudApi(options) {\n  const {\n    action,\n    paramVar,\n    resultCode,\n    outerParamName,\n    isPaging = false\n  } = options\n  return $utils.api.requestCloudApi(action, {\n    [outerParamName]: paramVar\n  }).then(res => {\n    if (res.Error) {\n      throw new Error(res.Error.Message)\n    } else {\n      // 如果有分页，则返回数据和分页\n      if (isPaging) {\n        return {\n          data: res[resultCode],\n          total: res.Total\n        }\n      }\n      return res[resultCode]\n    }\n  }).catch(error => {\n    $utils.ui.message.error(error.message);\n    throw error;\n  })\n}\n\n// 将常规参数转换为数据引擎参数(将依赖参数转换为数据引擎可识别参数)\nconst requestDependParams = $utils.api.transformCloudApiParams(\n  $context.requestDependParams || {},\n  {\n    //TODO: 搜索条件默认会使用like模糊搜索，可以在这里将精准搜索的字段进行指定\n    useEqFields: [],\n    //排序字段，格式为 {Key:'Name',Sort:'DESC'}\n    order: [],\n  }\n)\n// 编码模块\nif (!$context.requestDependParams?.SolutionID) {\n  return []\n}\n// 请求数据引擎数据(请求下拉框数据,请完善配置信息)\ntry {\n  const selectData = await requestCloudApi({\n    action: 'ListSolutionVersionWithMarketFlag',\n    paramVar: requestDependParams,\n    resultCode: 'ListSolutionVersions',\n    outerParamName: 'SolutionVersion',\n    isPaging: false\n  });\n  return selectData;\n\n} catch (error) {}", "useExpr": true, "wrapFunction": false, "codeGenerateInfo": "[{\"id\":\"transformCloudApiParams\",\"uuid\":\"352f7ae5-a193-49e8-92ed-91c03dca9ab6\",\"title\":\"将常规参数转换为数据引擎参数\",\"properties\":{\"paramVar\":\"$context.requestDependParams || {}\",\"returnVarName\":\"requestDependParams\"},\"catchList\":[],\"finallyList\":[],\"name\":\"将依赖参数转换为数据引擎可识别参数\",\"chosen\":false,\"selected\":false},{\"id\":\"customCode\",\"uuid\":\"dff3a833-0707-4f72-9566-5a71a6cdee8c\",\"title\":\"编码模块\",\"properties\":{\"code\":\"if(!$context.requestDependParams?.SolutionID){\\n  return []\\n}\",\"closure\":false},\"catchList\":[],\"finallyList\":[],\"chosen\":false,\"selected\":false,\"name\":\"\"},{\"id\":\"requestCloud\",\"uuid\":\"f3688ce0-f244-42e0-a0c4-a6445abda4fa\",\"title\":\"请求数据引擎数据\",\"properties\":{\"action\":\"ListSolutionVersionWithMarketFlag\",\"resultCode\":\"ListSolutionVersions\",\"outerParamName\":\"SolutionVersion\",\"paramVar\":\"requestDependParams\",\"isPaging\":false,\"returnVarName\":\"selectData\",\"autoReturn\":true},\"catchList\":[],\"finallyList\":[],\"chosen\":false,\"name\":\"请求下拉框数据,请完善配置信息\",\"selected\":false}]", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = async function () {\n  try {\n    var _$context$requestDepe;\n    // 工具类\n    function requestCloudApi(options) {\n      const {\n        action,\n        paramVar,\n        resultCode,\n        outerParamName,\n        isPaging = false\n      } = options;\n      return $utils.api.requestCloudApi(action, {\n        [outerParamName]: paramVar\n      }).then(res => {\n        if (res.Error) {\n          throw new Error(res.Error.Message);\n        } else {\n          // 如果有分页，则返回数据和分页\n          if (isPaging) {\n            return {\n              data: res[resultCode],\n              total: res.Total\n            };\n          }\n          return res[resultCode];\n        }\n      }).catch(error => {\n        $utils.ui.message.error(error.message);\n        throw error;\n      });\n    }\n\n    // 将常规参数转换为数据引擎参数(将依赖参数转换为数据引擎可识别参数)\n    const requestDependParams = $utils.api.transformCloudApiParams($context.requestDependParams || {}, {\n      //TODO: 搜索条件默认会使用like模糊搜索，可以在这里将精准搜索的字段进行指定\n      useEqFields: [],\n      //排序字段，格式为 {Key:'Name',Sort:'DESC'}\n      order: []\n    });\n    // 编码模块\n    if (!((_$context$requestDepe = $context.requestDependParams) !== null && _$context$requestDepe !== void 0 && _$context$requestDepe.SolutionID)) {\n      return [];\n    }\n    // 请求数据引擎数据(请求下拉框数据,请完善配置信息)\n    try {\n      const selectData = await requestCloudApi({\n        action: 'ListSolutionVersionWithMarketFlag',\n        paramVar: requestDependParams,\n        resultCode: 'ListSolutionVersions',\n        outerParamName: 'SolutionVersion',\n        isPaging: false\n      });\n      return selectData;\n    } catch (error) {}\n  } catch (error) {\n    console.error('表达式解析错误，错误原因: ', error, error.message);\n    if ($utils) {\n      $utils.ui.message.error('表达式执行错误，详情请查看浏览器控制台日志');\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.requestDependParams", "$context.requestDependParams.SolutionID"]}}, "autoTransformData": true, "changeDataSelectValue": "nothing", "transformDataType": "object", "labelField": "Code", "valueField": "Code"}}, "id": "deda44fd-8ed9-4a1e-a61a-317e9ab87042", "type": "select", "category": "component", "events": {"eventList": []}, "name": "下拉框", "code": "select_1679296528351", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "下拉框", "code": "select_1679296528351", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "发现架构", "useExpr": false, "wrapFunction": false}, "name": "Arch", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"useExpr": false, "wrapFunction": false}, "tag": false, "showSearch": true, "defaultValue": {"useExpr": false, "wrapFunction": false}, "placeholder": {"useExpr": false, "wrapFunction": false}, "labelInValue": false, "propLoading": {"value": false, "useExpr": false, "wrapFunction": false}, "dataSource": {"sourceType": "context", "contextInfo": {"source": "globalState", "key": "lookupMaps.OSArch"}}, "autoTransformData": true, "changeDataSelectValue": "nothing", "transformDataType": "object", "labelField": "Name", "valueField": "Code"}}, "id": "1c555efc-b5e1-4b37-a3b1-07ab8f690fd2", "type": "select", "category": "component", "events": {"eventList": []}, "name": "下拉框", "code": "select_1679296568942", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "下拉框", "code": "select_1679296568942", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "产品模块", "useExpr": false, "wrapFunction": false}, "name": "ProductModule", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"useExpr": false, "wrapFunction": false}, "tag": false, "showSearch": true, "defaultValue": {"useExpr": false, "wrapFunction": false}, "placeholder": {"useExpr": false, "wrapFunction": false}, "labelInValue": false, "propLoading": {"value": false, "useExpr": false, "wrapFunction": false}, "dataSource": {"sourceType": "request", "requestDependParams": {"value": "return {\n  TenantID:{\n    Value:window.jiguang_currentNs,\n    Op:'='\n  }\n}\n", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return {\n      TenantID: {\n        Value: window.jiguang_currentNs,\n        Op: '='\n      }\n    };\n  } catch (error) {\n    console.error('表达式解析错误，错误原因: ', error, error.message);\n    if ($utils) {\n      $utils.ui.message.error('表达式执行错误，详情请查看浏览器控制台日志');\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": []}, "requestCode": {"value": "// 工具类\nfunction requestCloudApi(options) {\n  const {\n    action,\n    paramVar,\n    resultCode,\n    outerParamName,\n    isPaging = false\n  } = options\n  return $utils.api.requestCloudApi(action, {\n    [outerParamName]: paramVar\n  }).then(res => {\n    if (res.Error) {\n      throw new Error(res.Error.Message)\n    } else {\n      // 如果有分页，则返回数据和分页\n      if (isPaging) {\n        return {\n          data: res[resultCode],\n          total: res.Total\n        }\n      }\n      return res[resultCode]\n    }\n  }).catch(error => {\n    $utils.ui.message.error(error.message);\n    throw error;\n  })\n}\n\n// 将常规参数转换为数据引擎参数(将依赖参数转换为数据引擎可识别参数)\nconst requestDependParams = $utils.api.transformCloudApiParams(\n  $context.requestDependParams || {},\n  {\n    //TODO: 搜索条件默认会使用like模糊搜索，可以在这里将精准搜索的字段进行指定\n    useEqFields: [],\n    //排序字段，格式为 {Key:'Name',Sort:'DESC'}\n    order: [],\n  }\n)\n// 请求数据引擎数据(请求下拉框数据,请完善配置信息)\ntry {\n  const selectData = await requestCloudApi({\n    action: 'ListProductInfoDetails',\n    paramVar: requestDependParams,\n    resultCode: 'ListProductInfos',\n    outerParamName: 'ProductInfo',\n    isPaging: false\n  });\n  return selectData;\n\n} catch (error) {}", "useExpr": true, "wrapFunction": false, "codeGenerateInfo": "[{\"id\":\"transformCloudApiParams\",\"uuid\":\"dda7a7e4-76c5-4659-aad7-61e9d1a80440\",\"title\":\"将常规参数转换为数据引擎参数\",\"properties\":{\"paramVar\":\"$context.requestDependParams || {}\",\"returnVarName\":\"requestDependParams\"},\"catchList\":[],\"finallyList\":[],\"name\":\"将依赖参数转换为数据引擎可识别参数\",\"chosen\":false,\"selected\":false},{\"id\":\"requestCloud\",\"uuid\":\"5cb12ce6-a3b4-467c-8aa8-9f1b69e0c588\",\"title\":\"请求数据引擎数据\",\"properties\":{\"action\":\"ListProductInfoDetails\",\"resultCode\":\"ListProductInfos\",\"outerParamName\":\"ProductInfo\",\"paramVar\":\"requestDependParams\",\"isPaging\":false,\"returnVarName\":\"selectData\",\"autoReturn\":true},\"catchList\":[],\"finallyList\":[],\"chosen\":false,\"name\":\"请求下拉框数据,请完善配置信息\",\"selected\":false}]", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = async function () {\n  try {\n    // 工具类\n    function requestCloudApi(options) {\n      const {\n        action,\n        paramVar,\n        resultCode,\n        outerParamName,\n        isPaging = false\n      } = options;\n      return $utils.api.requestCloudApi(action, {\n        [outerParamName]: paramVar\n      }).then(res => {\n        if (res.Error) {\n          throw new Error(res.Error.Message);\n        } else {\n          // 如果有分页，则返回数据和分页\n          if (isPaging) {\n            return {\n              data: res[resultCode],\n              total: res.Total\n            };\n          }\n          return res[resultCode];\n        }\n      }).catch(error => {\n        $utils.ui.message.error(error.message);\n        throw error;\n      });\n    }\n\n    // 将常规参数转换为数据引擎参数(将依赖参数转换为数据引擎可识别参数)\n    const requestDependParams = $utils.api.transformCloudApiParams($context.requestDependParams || {}, {\n      //TODO: 搜索条件默认会使用like模糊搜索，可以在这里将精准搜索的字段进行指定\n      useEqFields: [],\n      //排序字段，格式为 {Key:'Name',Sort:'DESC'}\n      order: []\n    });\n    // 请求数据引擎数据(请求下拉框数据,请完善配置信息)\n    try {\n      const selectData = await requestCloudApi({\n        action: 'ListProductInfoDetails',\n        paramVar: requestDependParams,\n        resultCode: 'ListProductInfos',\n        outerParamName: 'ProductInfo',\n        isPaging: false\n      });\n      return selectData;\n    } catch (error) {}\n  } catch (error) {\n    console.error('表达式解析错误，错误原因: ', error, error.message);\n    if ($utils) {\n      $utils.ui.message.error('表达式执行错误，详情请查看浏览器控制台日志');\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.requestDependParams"]}}, "autoTransformData": true, "changeDataSelectValue": "nothing", "transformDataType": "object", "labelField": "Name", "valueField": "Code"}}, "id": "fec398dc-e2a4-4a86-b1d9-f0225b3e9f49", "type": "select", "category": "component", "events": {"eventList": []}, "name": "下拉框", "code": "select_1679296602333", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "下拉框", "code": "select_1679296602333", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "问题来源", "useExpr": false, "wrapFunction": false}, "name": "Source", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"showType": "textarea", "disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "trim": false, "placeholder": {"useExpr": false, "wrapFunction": false}}}, "id": "bee58669-5eef-4c89-8f58-21dfd8b9d3dd", "type": "input", "category": "component", "events": {"eventList": []}, "name": "问题来源", "code": "input_1679296666395", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "问题来源", "code": "input_1679296666395", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "优先级", "useExpr": false, "wrapFunction": false}, "name": "Priority", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": true, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}, "requiredMessage": {"value": "该项为必填项", "useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"useExpr": false, "wrapFunction": false}, "tag": false, "showSearch": true, "searchCaseSensitive": false, "defaultValue": {"useExpr": false, "wrapFunction": false}, "placeholder": {"value": "请选择优先级", "useExpr": false, "wrapFunction": false}, "labelInValue": false, "propLoading": {"value": false, "useExpr": false, "wrapFunction": false}, "dataSource": {"sourceType": "context", "contextInfo": {"source": "globalState", "key": "lookupMaps.Priority"}}, "autoTransformData": true, "changeDataSelectValue": "nothing", "transformDataType": "object", "labelField": "Name", "valueField": "Code"}}, "id": "3a77e3e1-c0b8-453a-ac51-611642258724", "type": "select", "category": "component", "events": {"eventList": []}, "name": "下拉框", "code": "select_1679296679697", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "下拉框", "code": "select_1679296679697", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "严重程度", "useExpr": false, "wrapFunction": false}, "name": "SeverityLevel", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": true, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}, "requiredMessage": {"value": "该项为必填项", "useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"useExpr": false, "wrapFunction": false}, "tag": false, "showSearch": true, "searchCaseSensitive": false, "defaultValue": {"useExpr": false, "wrapFunction": false}, "placeholder": {"value": "请选择严重程度", "useExpr": false, "wrapFunction": false}, "labelInValue": false, "propLoading": {"value": false, "useExpr": false, "wrapFunction": false}, "dataSource": {"sourceType": "context", "contextInfo": {"source": "globalState", "key": "lookupMaps.Severity"}}, "autoTransformData": true, "changeDataSelectValue": "nothing", "transformDataType": "object", "labelField": "Name", "valueField": "Code"}}, "id": "1f4d35f0-c34b-44a6-ac23-4f6163ad5e00", "type": "select", "category": "component", "events": {"eventList": []}, "name": "严重程度", "code": "select_1679296719777", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "严重程度", "code": "select_1679296719777", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "处理人", "useExpr": false, "wrapFunction": false}, "name": "Owner", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"value": true, "useExpr": false, "wrapFunction": false}, "tag": true}}, "id": "e09f1769-8b21-47ae-a718-e585b629f7c4", "type": "userSelect", "category": "component", "events": {"eventList": []}, "name": "人员选择框", "code": "userSelect_1679311054781", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "人员选择框", "code": "userSelect_1679311054781", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "研发负责人", "useExpr": false, "wrapFunction": false}, "name": "DevOwners", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"value": true, "useExpr": false, "wrapFunction": false}, "tag": true}}, "id": "637bf748-4054-4b1f-8b01-c9e81bbed0a6", "type": "userSelect", "category": "component", "events": {"eventList": []}, "name": "人员选择框", "code": "userSelect_1679311134565", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "人员选择框", "code": "userSelect_1679311134565", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "测试负责人", "useExpr": false, "wrapFunction": false}, "name": "TestOwners", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"value": true, "useExpr": false, "wrapFunction": false}, "tag": true}}, "id": "6b3f306f-8354-4d30-8bac-889ef623b6c3", "type": "userSelect", "category": "component", "events": {"eventList": []}, "name": "人员选择框", "code": "userSelect_1679311163482", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "人员选择框", "code": "userSelect_1679311163482", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "交付负责人", "useExpr": false, "wrapFunction": false}, "name": "DeliverOwners", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"value": true, "useExpr": false, "wrapFunction": false}, "tag": true}}, "id": "4fc6e89f-a2d8-49ba-8530-d24a2ebcd938", "type": "userSelect", "category": "component", "events": {"eventList": []}, "name": "人员选择框", "code": "userSelect_1679311184972", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "人员选择框", "code": "userSelect_1679311184972", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}], "events": {"eventList": []}, "name": "常规表单", "code": "form_1679296507834", "extra": {"targetGroupName": "container", "groupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "常规表单", "code": "form_1679296507834", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}], "type": "page", "category": "container", "advancedProperties": {"triggerList": [{"type": "manualTrigger", "condition": {"useExpr": true, "wrapFunction": false, "dependencyVariableList": []}, "triggerCode": {"useExpr": true, "wrapFunction": false, "value": "// 工具类\nasync function getFormData(formCode) {\n  const formRef = $context.pageMapRef[formCode]\n  if (formRef) {\n    return await formRef.current.validateFields()\n  } else {\n    $utils.ui.message.warn('表单不存在，请检查组件code是否正确')\n    throw new Error('表单不存在，请检查表单code是否正确')\n  }\n}\n\n// 读取表单数据\ntry {\n  const formData = await getFormData('form_1679296507834');\n  return formData;\n\n} catch (error) {}", "codeGenerateInfo": "[{\"id\":\"formValidateField\",\"uuid\":\"8b0a36c8-e17e-472e-974c-3d1802e12410\",\"title\":\"读取表单数据\",\"properties\":{\"isBatch\":false,\"formCode\":\"form_1679296507834\",\"returnVarName\":\"formData\",\"autoReturn\":true},\"catchList\":[],\"finallyList\":[]}]", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = async function () {\n  try {\n    // 工具类\n    async function getFormData(formCode) {\n      const formRef = $context.pageMapRef[formCode];\n      if (formRef) {\n        return await formRef.current.validateFields();\n      } else {\n        $utils.ui.message.warn('表单不存在，请检查组件code是否正确');\n        throw new Error('表单不存在，请检查表单code是否正确');\n      }\n    }\n\n    // 读取表单数据\n    try {\n      const formData = await getFormData('form_1679296507834');\n      return formData;\n    } catch (error) {}\n  } catch (error) {\n    console.error('表达式解析错误，错误原因: ', error, error.message);\n    if ($utils) {\n      $utils.ui.message.error('表达式执行错误，详情请查看浏览器控制台日志');\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.pageMapRef[formCode]"]}, "title": "获取当前表单数据", "code": "getFormData"}]}}, "modalPage": []}