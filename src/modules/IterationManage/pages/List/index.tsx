/*
 * @Author: superfeng
 * @Date: 2022-08-16 12:56:05
 * @LastEditors: superfeng
 * @LastEditTime: 2023-05-15 16:07:18
 * @Description: 请输入注释信息
 */
import React from 'react';

import IterationTable from './components/IterationTable';
import { useParams } from 'react-router-dom';
import { listPageTitleMap } from '../../config';
import { TcsLayout } from '@tencent/tcs-component';
import { ExternalLink } from '@tencent/tea-component';

const List = () => {
  const params = useParams();
  const { pagePath } = params;
  const { history } = TcsLayout.useHistory();

  return (
    <TcsLayout
      fullHeight
      title={listPageTitleMap[pagePath]}
      history={history}
      customizeCard
      operation={
        pagePath === 'story' ? (
          <ExternalLink
            onClick={() => {
              window.open('https://jiguang.woa.com/page/tcsc_document/143093000366030848', '_blank');
            }}
          >
            操作指南
          </ExternalLink>
        ) : undefined
      }
    >
      <IterationTable pagePath={pagePath} />
    </TcsLayout>
  );
};
export default List;
