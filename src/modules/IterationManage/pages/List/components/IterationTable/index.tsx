/*
 * @Author: superfeng
 * @Date: 2023-03-17 10:47:27
 * @LastEditors: superfeng
 * @LastEditTime: 2023-06-25 18:44:26
 * @Description: 请输入注释信息
 */
import { withRouteBasename } from '@/common/routePath';
import { Dropdown, Icon, List, SelectOptionWithGroup, Switch, message } from '@tencent/tea-component';
import React, { useMemo, useRef, useState } from 'react';
import SyncTapdModal from '../SyncTapdModal';
import { getColumns } from './config';
// import { getUrlParams } from '@/common/utils';
import { ListIteration } from '@/common/api/interationManage';
// import useLookup from '@/common/hookups/useLookup';
import { toListParamsCApi } from '@/common/api/api';
import {
  CreateButtonEnterMap,
  // CreateButtonEnterMap,
  EditPageTitleMap,
  IssueTypeConfig,
  createButtonTextMap,
} from '@/modules/IterationManage/config';
import _ from 'lodash';
import { omit } from 'lodash-es';
import styles from './index.modules.less';

import {
  AddMRWorkItemCheckWhitelist,
  DeleteMRWorkItemCheckWhitelist,
  ListIterationsWithDetail,
} from '@/common/api/requirementIteration';
import { checkCurrentEnvState } from '@/common/utils/tools';
import { IPlanProps } from '@/modules/RequirementIterationManage/pages/Detail';
import AddIssueToIteration from '@/modules/RequirementIterationManage/pages/Detail/components/AddIssueToIteration';
import { TcsActionType, TcsButton, TcsLayout, TcsSpace, TcsSpin, TcsTable } from '@tencent/tcs-component';
import { TcscExcelExportButton, TcscExcelExportWrap } from '@tencent/tcsc-base';
import moment from 'moment';
import { UpdateFunMap } from '../../../Edit';

const IterationTable: React.FC<any> = ({
  pagePath,
  iterationManage = false,
  iterationUUID,
  onAddToIteration,
  isManage,
  iterationPlanStatus,
  mrStatus,
  releasePlanUUID,
  releasePlanID,
}: {
  pagePath: string;
  iterationManage: boolean;
  iterationUUID: string;
  onAddToIteration?: () => void;
  isManage?: boolean;
  iterationPlanStatus?: IPlanProps;
  mrStatus?: boolean;
  releasePlanUUID: string;
  releasePlanID?: string;
}) => {
  const relatedCache = localStorage.getItem('defect_manage/iteration_manage/RelatedToMe');
  const [related, setRelated] = useState(relatedCache ? relatedCache === 'true' : false);
  // const [mrWork, setMrWork] = useState<boolean>(false);

  // const [queryParams, setQueryParams] = useState<any>(initialParams);
  const [loading, setLoading] = useState(false);
  const { history } = TcsLayout.useHistory();
  const [visible, setVisible] = useState(false);

  // const [searchData, setSearchData] = useState<any>();
  const [addVisible, setAddVisible] = useState<boolean>(false);
  const [iterationList, setIterationList] = useState<SelectOptionWithGroup[]>();
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [iterationToIssueType, setIterationToIssueType] = useState<string>('');

  // const { getLookupByCode } = useLookup([]);
  const actionRef = useRef<TcsActionType>();

  const [editRowIndex, setEditRowIndex] = useState(-1);

  const columns = getColumns({
    // onDetail: handleDetail,
    // getLookupByCode,
    pagePath,
    handleBlurToIteration,
    handleFocusToIteration,
    iterationList,
    handleChangeValue,
    handleToIteration,
    iterationManage,
    handleRemoveIterationToIssue,
    isManage,
    iterationPlanStatus,
    editRowIndex,
    mrStatus,
    handleAddMrList,
    handleDeleteMrList,
  });
  function handleRemoveIterationToIssue(value) {
    setAddVisible(true);
    setIterationToIssueType('remove');
    setSelectedKeys([value.IssueID]);
  }

  function loadIssueData(queryParams) {
    const {
      current: pageIndex,
      pageSize,
      TAPD,
      CreatedAt,
      UpdateAt,
      CategoryID,
      RelatedApplications,
      pagePath,
      __breadcrumbCode__,
      ...otherParams
    } = queryParams;
    // const { current: pageIndex, pageSize } = page || pageInfo;
    const iterationManageSearch = omit(otherParams, 'iteration_id');
    const timeFilter: any = [];
    if (CreatedAt?.length && CreatedAt[0] && CreatedAt[1]) {
      const beginTime = moment(CreatedAt?.[0])?.format('YYYY-MM-DD 00:00:00');
      const endTime = moment(CreatedAt?.[1])?.format('YYYY-MM-DD 23:59:59');
      timeFilter.push({
        Key: 'CreatedAt',
        Op: '>=',
        Value: beginTime,
      });
      timeFilter.push({
        Key: 'CreatedAt',
        Op: '<=',
        Value: endTime,
      });
    }
    const updateTimeFilter: any = [];
    if (UpdateAt?.length && UpdateAt[0] && UpdateAt[1]) {
      const beginUpdateTime = moment(UpdateAt?.[0])?.format('YYYY-MM-DD 00:00:00');
      const endUpdateTime = moment(UpdateAt?.[1])?.format('YYYY-MM-DD 23:59:59');
      updateTimeFilter.push({
        Key: 'UpdateAt',
        Op: '>=',
        Value: beginUpdateTime,
      });
      updateTimeFilter.push({
        Key: 'UpdateAt',
        Op: '<=',
        Value: endUpdateTime,
      });
    }
    const params = {
      TAPD,
      IssueType: IssueTypeConfig[pagePath],
      RelatedToMe: iterationManage ? false : related,
      CategoryID: !queryParams?.CategoryID?.[1] ? queryParams?.CategoryID?.[0] : queryParams?.CategoryID?.[1],
      RelatedApplications: RelatedApplications || undefined,
      TenantUUID: (window as any).jiguang_currentNs,
    };
    if (iterationManage) {
      const listIteration = ListIteration(
        {
          ...params,
          FilterExpr: toListParamsCApi(
            {
              IterationUUID: iterationUUID || undefined,
              ReleaseID: releasePlanID || undefined,
              ...iterationManageSearch,
              SolutionVersionUUID: queryParams.SolutionVersionUUID?.[1] || undefined,
            },
            {
              useEqFields: ['IssueID', 'Type', 'IterationUUID', 'SolutionVersionUUID', 'Status', 'ReleaseID'],
              page: false,
            },
          )
            ._Filter?.concat(...timeFilter, ...updateTimeFilter)
            ?.filter((item) => item.Value !== '' && item.Value !== '%%'),
          PageNo: pageIndex,
          PageSize: pageSize || 20,
        },
        pagePath,
      );

      return Promise.all([listIteration]).then(([iteration]) => {
        if (iteration.Error) {
          return message.error({ content: iteration.Error.Message });
        }
        const items = iteration.Items || [];

        return {
          data: items || [],
          success: true,
          total: iteration.Total,
        };
      });
    }
    return ListIteration(
      {
        ...params,
        FilterExpr: toListParamsCApi(
          {
            ...otherParams,
            SolutionVersionUUID: queryParams.SolutionVersionUUID?.[1] || undefined,
          },
          {
            useEqFields: ['IssueID', 'Type', 'SolutionVersionUUID', 'Status', 'SeverityLevel', 'Priority'],
            page: false,
          },
        )
          ._Filter?.concat(...timeFilter, ...updateTimeFilter)
          ?.filter((item) => item.Value !== '' && item.Value !== '%%'),

        PageNo: pageIndex,
        PageSize: pageSize || 20,
      },
      pagePath,
    ).then((res) => {
      if (res.Error) {
        message.error({
          content: res.Error.Message,
        });
        return {
          success: true,
          data: [],
          total: 0,
        };
      }
      return {
        success: true,
        data: res.Items || [],
        total: res.Total,
      };
    });
  }

  function handleToIteration(record: any) {
    const url = `${withRouteBasename(`/requirement_iteration_manage/detail`)}?iteration_id=${record.IterationUUID}`;
    window.open(url, '_blank');
  }
  function handleFocusToIteration(index: number) {
    fetchData();
    setEditRowIndex(index);
  }

  function handleBlurToIteration() {
    setEditRowIndex(-1);
  }
  const fetchData = async () => {
    const res = await ListIterationsWithDetail({
      pageSize: 9999,
      current: 1,
    });
    if (res.Error) {
      return message.error({
        content: res.Error.Message,
      });
    }
    res.ListIterations?.unshift({ UUID: '', Name: '- 空 -' });
    setIterationList(
      res.ListIterations?.map((item) => ({
        value: item.UUID,
        text: item.Name,
      })) || [],
    );
  };

  function handleChangeValue(value) {
    updateList(value);
    setEditRowIndex(-1);
  }
  const updateList = async (value) => {
    const updateData = _.omit(value, ['index', 'select', 'selectValue']);
    const updateFun = UpdateFunMap[pagePath];
    setLoading(true);
    try {
      const res = await updateFun?.({
        ...updateData,
        IterationUUID: value.selectValue || '',
      });
      if (!res.Error) {
        actionRef.current?.reload();
        message.success({ content: '更新迭代成功' });
      } else {
        message.error({
          content: res.Error.Message,
        });
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  function handleSyncTapd() {
    setVisible(true);
  }
  function handleSyncConfirm() {
    setVisible(false);
    actionRef.current?.reload();
  }
  const handleAddToIterationConfirm = () => {
    setAddVisible(false);
    actionRef.current?.reload();
    onAddToIteration?.();
    setSelectedKeys([]);
  };

  function handleSyncCancel() {
    setVisible(false);
  }

  function handleToDetail(type: 'create' | 'edit') {
    if (type === 'create') {
      history.push(`${withRouteBasename(`/iteration_manage/${pagePath}/edit`)}`);
    }
  }
  const handleAddToIteration = () => {
    setAddVisible(true);
    setIterationToIssueType('add');
  };
  const handleAddToIterationCancel = () => {
    setAddVisible(false);
  };
  async function handleAddMrList(row) {
    try {
      const { Error, Message } = await AddMRWorkItemCheckWhitelist({
        IterationUUID: iterationUUID,
        IssueIDs: [row.IssueID],
        TAPDUrls: [row.TapdUrl],
        ReleasePlanUUID: releasePlanUUID,
      });
      if (Error) {
        return message.error({ content: Error.Message });
      }
      message.success({ content: Message });
      actionRef.current?.reload();
    } catch (error) {
      console.error(error);
    }
  }
  async function handleDeleteMrList(row) {
    try {
      const { Error, Message } = await DeleteMRWorkItemCheckWhitelist({
        IterationUUID: iterationUUID,
        ReleasePlanUUID: releasePlanUUID,
        IssueIDs: [row.IssueID],
        TAPDUrls: [row.TapdUrl],
      });
      if (Error) {
        return message.error({ content: Error.Message });
      }
      message.success({ content: Message });
      actionRef.current?.reload();
    } catch (error) {
      console.error(error);
    }
  }

  // const buttonLeft = () => {
  //   if (pagePath === 'story') {
  //     return (
  //       <TcsButton
  //         type="primary"
  //         tooltip={iterationPlanStatus?.addStoryLock ? '当前迭代已锁定需求，无法添加需求' : ''}
  //         onClick={handleAddToIteration}
  //         disabled={iterationPlanStatus?.addStoryLock}
  //       >
  //         添加需求
  //       </TcsButton>
  //     );
  //   }
  //   if (pagePath === 'defect') {
  //     return (
  //       <TcsButton
  //         tooltip={iterationPlanStatus?.addBugLock ? '当前迭代已锁定缺陷，无法添加缺陷' : ''}
  //         type="primary"
  //         onClick={handleAddToIteration}
  //         disabled={iterationPlanStatus?.addBugLock}
  //       >
  //         添加缺陷
  //       </TcsButton>
  //     );
  //   }
  // };
  const buttonLeft = useMemo(() => {
    const text = pagePath === 'story' ? '需求' : '缺陷';
    const isLock = pagePath === 'story' ? iterationPlanStatus?.addStoryLock : iterationPlanStatus?.addBugLock;
    return (
      <TcsButton
        type="primary"
        tooltip={isLock ? `当前迭代已锁定${text}，无法添加${text}` : ''}
        onClick={handleAddToIteration}
        disabled={isLock}
      >
        {`添加${text}`}
      </TcsButton>
    );
  }, [pagePath, iterationPlanStatus]);

  const params = useMemo(() => ({ pagePath }), [pagePath]);

  const headerTitle = useMemo(() => {
    if (iterationManage) {
      if (iterationUUID) {
        return buttonLeft;
      }
      return null;
    }
    if (checkCurrentEnvState()) {
      return (
        <Dropdown
          button={
            <span>
              <Icon type="plus" className={styles.icon_text} />
              创建{CreateButtonEnterMap[pagePath]}
            </span>
          }
          appearance="button"
          trigger="hover"
          className={styles.dropdown_button}
        >
          <List type="option">
            <List.Item onClick={() => handleToDetail('create')}>
              <Icon type="and" />
              <span style={{ marginLeft: 5 }}>{createButtonTextMap[pagePath]}</span>
            </List.Item>

            <List.Item onClick={handleSyncTapd}>
              <Icon type="and" />
              <span style={{ marginLeft: 5 }}>从TAPD同步{EditPageTitleMap[pagePath]}</span>
            </List.Item>
          </List>
        </Dropdown>
      );
    }
    return (
      <TcsButton type="primary" onClick={handleSyncTapd}>
        从TAPD同步{EditPageTitleMap[pagePath]}
      </TcsButton>
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [iterationManage, pagePath, buttonLeft, iterationUUID]);

  return (
    <TcsSpin full spinning={loading}>
      <TcscExcelExportWrap>
        <TcsTable
          key={pagePath}
          columns={columns}
          search={{
            labelWidth: 80,
          }}
          actionRef={actionRef}
          request={(...rest) => {
            const [params] = rest;
            return loadIssueData(params);
          }}
          params={params}
          pagination={{
            defaultPageSize: 20,
            showSizeChanger: true,
          }}
          scroll={{ x: 2500 }}
          scrollInTable={!iterationManage}
          syncToUrl={!iterationManage}
          rowKey="IssueID"
          columnsState={{
            // 缓存用户修改的列的key
            persistenceKey: `defect_manage/iteration_manage/${pagePath}/columns_state`,
          }}
          headerTitle={headerTitle}
          toolBarRender={() =>
            // eslint-disable-next-line no-nested-ternary
            !iterationManage ? (
              <TcsSpace>
                <TcscExcelExportButton fieldName="缺陷单列表" />
                <Switch
                  style={{ paddingTop: 5 }}
                  defaultChecked
                  value={related}
                  onChange={(value) => {
                    localStorage.setItem('defect_manage/iteration_manage/RelatedToMe', String(value));
                    // unstable_batchedUpdates(() => {
                    //   setRelated(value);
                    // });
                    setRelated(value);
                    setTimeout(() => {
                      actionRef.current?.reload(true);
                    });
                  }}
                >
                  与我相关
                </Switch>
              </TcsSpace>
            ) : mrStatus ? (
              // <TcsSpace>
              //   <TcscExcelExportButton fieldName="缺陷单列表" />
              //   <Switch
              //     style={{ paddingTop: 5 }}
              //     defaultChecked
              //     value={mrWork}
              //     onChange={(value) => {
              //       // unstable_batchedUpdates(() => {
              //       //   setRelated(value);
              //       // });
              //       // setRelated(value);
              //       setMrWork(value);
              //       setTimeout(() => {
              //         actionRef.current?.reload(true);
              //       });
              //     }}
              //   >
              //     只查看合流白名单
              //   </Switch>
              // </TcsSpace>
              ''
            ) : (
              ''
            )
          }
        />
      </TcscExcelExportWrap>

      <SyncTapdModal visible={visible} onCancel={handleSyncCancel} onConfirm={handleSyncConfirm} />
      <AddIssueToIteration
        addVisible={addVisible}
        pagePath={pagePath}
        onCancal={handleAddToIterationCancel}
        onConfirm={handleAddToIterationConfirm}
        type={iterationToIssueType}
        releaseType={!iterationUUID}
        IterationSelectKey={selectedKeys}
      />
    </TcsSpin>
  );
};

export default IterationTable;
