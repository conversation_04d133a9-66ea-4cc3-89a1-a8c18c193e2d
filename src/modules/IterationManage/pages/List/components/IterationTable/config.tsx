/*
 * @Author: superfeng
 * @Date: 2023-03-17 11:12:43
 * @LastEditors: superfeng
 * @LastEditTime: 2023-06-05 11:29:43
 * @Description: 请输入注释信息
 */
import { SolutionVersionSelect } from '@/common/components';
import CategorySelect from '@/common/components/CategorySelect';
import { withRouteBasename } from '@/common/routePath';
import { TcscExcelExportColumns } from '@/common/utils/excelUtils';
import { IssueIDHeaderMap, IterationPathConfig } from '@/modules/IterationManage/config';
import { IPlanProps } from '@/modules/RequirementIterationManage/pages/Detail';
import { TcsButtonGroup } from '@tencent/tcs-component';
import { SelectOptionWithGroup } from '@tencent/tea-component';
import React from 'react';
import AppSearchInput from '../AppSearchInput';

export const getColumns = ({
  pagePath,
  iterationManage,
  handleRemoveIterationToIssue,
  isManage,
  iterationPlanStatus,
  // handleToIteration,
  // handleFocusToIteration,
  // iterationList,
  // handleChangeValue,
  // handleBlurToIteration,
  // editRowIndex,
  mrStatus,
  handleAddMrList,
  handleDeleteMrList,
}: {
  pagePath: string;
  iterationManage: boolean;
  handleRemoveIterationToIssue: (value) => void;
  isManage?: boolean;
  iterationPlanStatus?: IPlanProps;
  handleToIteration: (value) => void;
  handleFocusToIteration: (value) => void;
  handleBlurToIteration: (value) => void;
  iterationList?: SelectOptionWithGroup[];
  handleChangeValue: (value) => void;
  editRowIndex: number;
  mrStatus?: boolean;
  handleAddMrList: (value) => void;
  handleDeleteMrList: (value) => void;
}): TcscExcelExportColumns[] =>
  [
    {
      title: IssueIDHeaderMap[pagePath],
      dataIndex: 'IssueID',
      fixed: 'left',
      disable: true,
      width: '5%',
      order: 3,
      copyable: true,
      fieldProps: {
        allClear: true,
      },
    },
    ...(pagePath === IterationPathConfig.Defect
      ? [
          {
            dataIndex: 'Type',
            title: '缺陷类型',
            width: '5%',
            order: 4,
            valueType: 'dictSelect',
            fieldProps: {
              dictType: 'BugType',
              showType: 'tag',
            },
          },
        ]
      : [
          {
            dataIndex: 'Type',
            title: '需求类型',
            width: '5%',
            order: 4,
            valueType: 'dictSelect',
            fieldProps: {
              dictType: 'StoryType',
              showType: 'tag',
            },
          },
          {
            dataIndex: 'CategoryID',
            title: '需求分类',
            width: '6%',
            search: false,
            valueType: 'dictSelect',
            fieldProps: {
              dictType: 'StoryCategory',
              showType: 'tag',
            },
          },
          {
            dataIndex: 'CategoryID',
            title: '需求分类',
            order: 4,
            hideInTable: true,
            renderFormItem: () => <CategorySelect />,
          },
        ]),
    {
      dataIndex: 'Title',
      title: '标题',
      disable: true,
      width: '15%',
      valueType: 'text',
      order: 2,
      linkable: true,
      linkProps: {
        linkUrl: (text, record) =>
          `${withRouteBasename(`/iteration_manage/${pagePath}/detail`)}?issue_id=${record.IssueID}`,
      },
    },
    {
      dataIndex: ['Solution', 'Name'],
      title: '解决方案名称',
      width: '10%',
      order: 4,
      search: false,
      render: (text, record) => `${record?.Solution?.Name || '-'}(${record?.Solution?.NameEN || '-'})`,
    },
    {
      dataIndex: 'SolutionVersion',
      title: '解决方案版本',
      width: '6%',
      order: 4,
      search: false,
    },
    {
      dataIndex: 'SolutionVersionUUID',
      title: '解决方案版本',
      hideInTable: true,
      order: 4,
      renderFormItem() {
        return <SolutionVersionSelect />;
      },
    },
    {
      dataIndex: 'AppNameList',
      title: '应用/引入版本/解决版本',
      width: '15%',
      order: 4,
      search: false,
      renderExcelText: (text, record) => {
        const { IssueSolutionRel = [] } = record;
        let appList = IssueSolutionRel.map((item) => {
          const { IssueAppRel = [] } = item;
          return IssueAppRel.map(
            (app) =>
              `${app.ApplicationName} / ${app.BasePackageVersion || '-'}/ ${
                app?.ReleaseApplicationPackage?.ApplicationVersion || '-'
              }`,
          );
        }).flat();

        appList = Array.from(new Set(appList));
        return appList?.length ? appList.map((appName) => appName).join(',\n') : '-';
      },
      render: (text, record) => {
        const { IssueSolutionRel = [] } = record;
        let appList = IssueSolutionRel.map((item) => {
          const { IssueAppRel = [] } = item;
          return IssueAppRel.map(
            (app) =>
              `${app.ApplicationName} / ${app.BasePackageVersion || '-'}/ ${
                app?.ReleaseApplicationPackage?.ApplicationVersion || '-'
              }`,
          );
        }).flat();

        appList = Array.from(new Set(appList));
        return appList?.length ? appList.map((appName) => <div key={appName}>{appName}</div>) : '-';
      },
    },
    {
      title: '关联应用',
      dataIndex: 'RelatedApplications',
      order: 4,
      hideInTable: true,
      renderFormItem: () => <AppSearchInput />,
    },

    {
      dataIndex: ['Iteration', 'Name'],
      title: '迭代',
      width: '10%',
      valueType: 'text',
      linkable: true,
      order: 4,
      search: false,
      linkProps: {
        linkUrl: (text, record) => {
          if (!record.IterationUUID) {
            return undefined;
          }
          return `${withRouteBasename(`/requirement_iteration_manage/detail`)}?iteration_id=${record.IterationUUID}`;
        },
      },

      // renderExcelText(text, record) {
      //   return record.Iteration?.Name;
      // },
      // render(text, record, index) {
      //   if (editRowIndex !== index) {
      //     return (
      //       <span>
      //         {record?.IterationUUID ? <a onClick={() => handleToIteration(record)}>{record.Iteration?.Name}</a> : '-'}
      //         {!iterationManage && (
      //           <Icon type="pencil" onClick={() => handleFocusToIteration(index)} style={{ marginLeft: 5 }} />
      //         )}
      //       </span>
      //     );
      //   }
      //   return (
      //     <span>
      //       <Select
      //         appearance="button"
      //         defaultValue={record?.IterationUUID}
      //         style={{ width: '80%' }}
      //         options={iterationList}
      //         searchable
      //         onChange={(value) => handleChangeValue({ ...record, selectValue: value, index })}
      //         clearable
      //       />
      //       <Icon type="dismiss" onClick={() => handleBlurToIteration(index)} style={{ marginLeft: 5 }} size="s" />
      //     </span>
      //   );
      // },
    },
    {
      dataIndex: ['ReleasePlan', 'ReleaseName'],
      title: '发布计划',
      width: '10%',
      valueType: 'text',
      linkable: true,
      order: 4,
      search: false,
      linkProps: {
        linkUrl: (text, record) => {
          if (!record.IterationUUID) {
            return undefined;
          }
          return `${withRouteBasename(`/requirement_iteration_manage/detail`)}?iteration_id=${record.IterationUUID}`;
        },
      },
    },

    pagePath === IterationPathConfig.Defect
      ? {
          dataIndex: 'SeverityLevel',
          title: '严重程度',
          width: '6%',
          order: 4,
          valueType: 'dictSelect',
          fieldProps: {
            dictType: 'Severity',
            showType: 'tag',
            mode: 'multiple',
            showSelectAll: true,
            showSearch: true,
          },
        }
      : undefined,
    {
      dataIndex: 'Priority',
      title: '优先级',
      width: '5%',
      order: 4,
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'Priority',
        showType: 'tag',
        mode: 'multiple',
        showSelectAll: true,
        showSearch: true,
      },
    },
    {
      dataIndex: 'Status',
      title: '状态',
      width: '5%',
      valueType: 'dictSelect',
      order: 4,
      fieldProps: {
        dictType: 'IssueStatus',
        showType: 'dot',
        mode: 'multiple',
        showSelectAll: true,
        showSearch: true,
      },
    },
    {
      dataIndex: 'TAPD',
      title: '关联TAPD',
      hideInTable: true,
      order: 1,
      fieldProps: {
        placeholder: '请输入TAPD链接或ID',
      },
    },
    {
      dataIndex: 'Owner',
      title: '处理人',
      order: 4,
      width: '10%',
      renderText: (text) => (text ?? '').split(';'),
      valueType: 'staffSelect',
      fieldProps: {
        multiple: true,
      },
    },
    {
      dataIndex: 'Creator',
      title: '创建人',
      order: 4,
      width: '5%',
    },
    {
      dataIndex: 'CreatedAt',
      title: '创建时间',
      valueType: 'dateTime',
      width: '5%',
      search: false,
    },
    {
      dataIndex: 'CreatedAt',
      title: '创建时间',
      valueType: 'dateRange',
      hideInTable: true,
      order: 4,
      width: '5%',
    },
    {
      dataIndex: 'UpdateAt',
      title: '最后修改时间',
      width: '5%',
      search: false,
      valueType: 'dateTime',
    },
    {
      dataIndex: 'UpdateAt',
      title: '最后修改时间',
      width: '5%',
      valueType: 'dateRange',
      order: 4,
      hideInTable: true,
      fieldProps: {
        clearable: true,
      },
    },
    {
      dataIndex: 'operation',
      title: '操作',
      fixed: 'right',
      width: '13%',
      disable: true,
      valueType: 'option',
      render(text, record) {
        const result: any = [];
        const noPermissionTooltip = '您当前不是迭代管理员，无权限操作';
        if (record.TapdUrl) {
          result.push({
            text: ' 跳转至Tapd单',
            onClick: () => {
              window.open(record.TapdUrl, '_blank');
            },
          });
        }
        if (iterationManage) {
          result.push({
            text: '移出迭代',
            tooltip: isManage ? '' : noPermissionTooltip,
            disabled:
              !isManage &&
              ((pagePath === IterationPathConfig.Story && iterationPlanStatus?.addStoryLock) ||
                (pagePath === IterationPathConfig.Defect && iterationPlanStatus?.addBugLock)),
            onClick: () => {
              handleRemoveIterationToIssue(record);
            },
          });
          const mrActionText = record?.MRWhiteID ? '移出合流白名单' : '加入合流白名单';
          const mrActionTitle = record?.MRWhiteID ? '确定移出合流白名单？' : '确定加入合流白名单？';
          const mrActionHandler = record?.MRWhiteID ? handleDeleteMrList : handleAddMrList;

          if (mrStatus) {
            result.push({
              text: mrActionText,
              disabled: !isManage,
              tooltip: isManage ? '' : noPermissionTooltip,
              confirm: true,
              confirmProps: {
                title: mrActionTitle,
                onConfirm() {
                  mrActionHandler(record);
                },
              },
            });
          }
        }
        return result?.length ? <TcsButtonGroup items={result} /> : '-';
      },
    },
  ].filter((item) => item) as TcscExcelExportColumns[];
