import { ListApplicationMeta } from '@/common/api/patchManage';
import { SelectMultiple, message } from '@tencent/tea-component';
import { useDebounceFn } from 'ahooks';
import React, { useState } from 'react';

export interface IProps {
  value?: string[];
  onChange?: (value: string[]) => void;
}

const AppSearchInput: React.FC<IProps> = ({ value = [], onChange }) => {
  const [applicationOptions, setApplicationOptions] = useState<any>([]);
  const { run } = useDebounceFn(
    async (value) => {
      const { Error, ApplicationMetaList } = await ListApplicationMeta({
        ApplicationNameKeyWord: value,
        PageSize: 50,
      });
      if (Error) {
        message.error({ content: Error.Message || '获取应用名称列表失败' });
      } else {
        setApplicationOptions(
          ApplicationMetaList?.map((item) => ({
            text: item.ApplicationName,
            value: item.ApplicationName,
          })) || [],
        );
      }
    },
    {
      wait: 300,
    },
  );

  return (
    <SelectMultiple
      options={applicationOptions}
      searchPlaceholder="请输入名称进行应用搜索"
      staging={false}
      appearance="button"
      size="m"
      onSearch={run}
      matchButtonWidth={true}
      searchable
      clearable
      value={value}
      onChange={onChange}
    />
  );
};

export default AppSearchInput;
