/*
 * @Author: superfeng
 * @Date: 2023-03-16 20:04:55
 * @LastEditors: superfeng
 * @LastEditTime: 2023-07-03 17:20:34
 * @Description: 请输入注释信息
 */
import { ListApplicationMeta } from '@/common/api/patchManage';
import { SolutionVersionSelect } from '@/common/components';
import CategorySelect from '@/common/components/CategorySelect';
import useLookup from '@/common/hookups/useLookup';
import { IterationPathConfig } from '@/modules/IterationManage/config';
import { TcsForm, TcsFormStaffSelect, TcsFormText, TcsQueryFilter } from '@tencent/tcs-component';
import { Card, Select, SelectMultiple, message } from '@tencent/tea-component';
import { RangePicker } from '@tencent/tea-component/lib/datepicker/RangePicker';
import { useDebounceFn } from 'ahooks';

import React, { useState } from 'react';

export interface IProps {
  onSearch: (values: any) => void;
  initialValues?: any;
  pagePath: string;
}

const Search: React.FC<IProps> = ({ onSearch, initialValues, pagePath }) => {
  const handleSearch = (values) => {
    onSearch(values);
  };
  const { lookups } = useLookup(['BugType', 'IssueStatus', 'StoryCategory', 'Severity', 'Priority']);
  const [applicationOptions, setApplicationOptions] = useState<any>([]);
  const { run } = useDebounceFn(
    async (value) => {
      const { Error, ApplicationMetaList } = await ListApplicationMeta({
        ApplicationNameKeyWord: value,
        PageSize: 50,
      });
      if (Error) {
        message.error({ content: Error.Message || '获取应用名称列表失败' });
      } else {
        setApplicationOptions(
          ApplicationMetaList?.map((item) => ({
            text: item.ApplicationName,
            value: item.ApplicationName,
          })) || [],
        );
      }
    },
    {
      wait: 300,
    },
  );

  return (
    <Card style={{ marginBottom: 5 }} className="tcs-layout__table_search">
      <Card.Body>
        <TcsQueryFilter onSearch={handleSearch} initialValues={initialValues} labelWidth={90}>
          <TcsFormText label="关联TAPD" name="TAPD" fieldProps={{ placeholder: '请输入TAPD链接或ID' }} />
          <TcsFormText label="标题" name="Title" fieldProps={{ placeholder: '请输入标题' }} />
          {pagePath === IterationPathConfig.Defect ? (
            <TcsFormText label="缺陷单ID" name="IssueID" fieldProps={{ placeholder: '请输入缺陷单ID' }} />
          ) : (
            <TcsFormText label="需求单ID" name="IssueID" fieldProps={{ placeholder: '请输入需求单ID' }} />
          )}
          <TcsForm.Item label="解决方案版本" name="SolutionVersionUUID">
            <SolutionVersionSelect />
          </TcsForm.Item>
          <TcsForm.Item label={pagePath === IterationPathConfig.Defect ? '缺陷状态' : '需求状态'} name="Status">
            <Select
              placeholder="请选择状态"
              appearance="button"
              size="m"
              matchButtonWidth
              clearable
              options={
                lookups.IssueStatus?.map((item) => ({
                  value: item.Code,
                  text: item.Name,
                })) || []
              }
            />
          </TcsForm.Item>

          <TcsForm.Item label="优先级" name="Priority">
            <Select
              placeholder="请选择优先级"
              appearance="button"
              size="m"
              matchButtonWidth
              clearable
              options={
                lookups.Priority?.map((item) => ({
                  value: item.Code,
                  text: item.Name,
                })) || []
              }
            />
          </TcsForm.Item>
          <TcsForm.Item label="关联应用" name="RelatedApplications" initialValue={[]}>
            <SelectMultiple
              options={applicationOptions}
              searchPlaceholder="请输入名称进行应用搜索"
              staging={false}
              appearance="button"
              size="m"
              onSearch={run}
              matchButtonWidth={true}
              searchable
              clearable
            />
          </TcsForm.Item>
          <TcsFormStaffSelect label="处理人" name="Owner" valueField="enName" fieldProps={{ size: 'm' }} />
          <TcsFormStaffSelect label="创建人" name="Creator" valueField="enName" fieldProps={{ size: 'm' }} />

          {pagePath === IterationPathConfig.Defect && (
            <TcsForm.Item label="严重程度" name="SeverityLevel">
              <Select
                placeholder="请选择严重程度"
                appearance="button"
                size="m"
                matchButtonWidth
                clearable
                options={
                  lookups.Severity?.map((item) => ({
                    value: item.Code,
                    text: item.Name,
                  })) || []
                }
              />
            </TcsForm.Item>
          )}
          {pagePath === IterationPathConfig.Defect && (
            <TcsForm.Item label="缺陷类型" name="Type">
              <Select
                placeholder="请选择缺陷类型"
                appearance="button"
                size="m"
                matchButtonWidth
                clearable
                options={
                  lookups.BugType?.map((item) => ({
                    value: item.Code,
                    text: item.Name,
                  })) || []
                }
              />
            </TcsForm.Item>
          )}

          {pagePath === IterationPathConfig.Story && (
            <TcsForm.Item label="需求分类" name="CategoryID">
              <CategorySelect />
            </TcsForm.Item>
          )}

          <TcsForm.Item label="创建时间" name="CreatedAt">
            <RangePicker />
          </TcsForm.Item>
          <TcsForm.Item label="更新时间" name="UpdateAt">
            <RangePicker />
          </TcsForm.Item>
        </TcsQueryFilter>
      </Card.Body>
    </Card>
  );
};
export default Search;
