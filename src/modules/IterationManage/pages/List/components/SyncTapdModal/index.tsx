import React, { useRef, useState } from 'react';
import { ProForm } from '@/common/components';
import { Button, Input, Modal, message } from '@tencent/tea-component';
import { syncTapdWithBug, syncTapdWithStory } from '@/common/api/interationManage';
import { IterationPathConfig } from '@/modules/IterationManage/config';
import { useParams } from 'react-router-dom';
import { withRouteBasename } from '@/common/routePath';
import { TcsLayout } from '@tencent/tcs-component';
import { syncTapdWithIteration } from '@/common/api/requirementIteration';
/*
 * @Author: superfeng
 * @Date: 2023-03-17 14:50:35
 * @LastEditors: superfeng
 * @LastEditTime: 2023-06-25 16:19:07
 * @Description: 请输入注释信息
 */
export interface IProps {
  visible: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  iterationManage?: boolean;
}

const SyncTapdFunMap = {
  [IterationPathConfig.Defect]: syncTapdWithBug,
  [IterationPathConfig.Story]: syncTapdWithStory,
};

const UpdateOrCreateResultCode = {
  [IterationPathConfig.Defect]: 'Bug',
  [IterationPathConfig.Story]: 'Story',
};

const SyncTapdModal: React.FC<IProps> = ({ visible, onCancel, onConfirm, iterationManage = false }) => {
  const formRef = useRef<any>(null);
  const [loading, setLoading] = useState(false);
  const { pagePath } = useParams<{ pagePath: string }>();
  const { history } = TcsLayout.useHistory();

  const handleConfirm = () => {
    formRef.current.validateFields().then((values: any) => {
      if (iterationManage) {
        setLoading(true);
        syncTapdWithIteration(values)
          .then((res) => {
            if (res.Error) {
              message.error({
                content: res.Error.Message,
              });
            } else {
              formRef.current.resetFields();
              history.push(
                `${withRouteBasename(`/requirement_iteration_manage/detail`)}?iteration_id=${res?.Iteration.UUID}`,
              );
              onConfirm();
            }
          })
          .finally(() => {
            setLoading(false);
          });
      } else {
        const syncTapdFun = SyncTapdFunMap[pagePath];

        setLoading(true);
        syncTapdFun(values)
          .then((res) => {
            if (res.Error) {
              message.error({
                content: res.Error.Message,
              });
            } else {
              formRef.current.resetFields();
              history.push(
                `${withRouteBasename(`/iteration_manage/${pagePath}/detail`)}?issue_id=${
                  res[UpdateOrCreateResultCode[pagePath]].IssueID
                }`,
              );
              onConfirm();
            }
          })
          .finally(() => {
            setLoading(false);
          });
      }
    });
  };
  const handleCancel = () => {
    formRef.current.resetFields();
    onCancel();
  };

  return (
    <Modal visible={visible} caption="同步Tapd" onClose={handleCancel}>
      <Modal.Body>
        <ProForm formRef={formRef}>
          <ProForm.Item
            label="请输入Tapd链接"
            dataIndex="TapdUrl"
            rules={{
              required: {
                value: true,
                message: '请输入Tapd链接',
              },
            }}
          >
            <Input.TextArea style={{ width: '100%' }} placeholder="请输入Tapd链接" />
          </ProForm.Item>
        </ProForm>
      </Modal.Body>
      <Modal.Footer>
        <Button type="primary" loading={loading} onClick={handleConfirm}>
          确定
        </Button>
        <Button type="weak" onClick={handleCancel}>
          取消
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default SyncTapdModal;
