/*
 * @Author: superfeng
 * @Date: 2023-03-17 15:41:01
 * @LastEditors: superfeng
 * @LastEditTime: 2023-06-27 17:07:25
 * @Description: 请输入注释信息
 */
import { Button, Card, Form, Select, message } from '@tencent/tea-component';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import styles from './index.module.less';
import Left from './components/Left';
import Right from './components/Right';
import {
  createBug,
  createStory,
  getBugWithDetail,
  getStoryWithDetail,
  listTemplate,
  updateBug,
  updateStory,
} from '@/common/api/interationManage';
import { getUrlParams } from '@/common/utils';
import { withRouteBasename } from '@/common/routePath';
import { EditPageTitleMap, IssueTypeConfig, IterationPathConfig } from '../../config';
import usePagePath from '../../hooks/usePagePath';
import { TcsLayout, TcsSpin } from '@tencent/tcs-component';

const DetailFunMap = {
  [IterationPathConfig.Defect]: getBugWithDetail,
  [IterationPathConfig.Story]: getStoryWithDetail,
};

export const UpdateFunMap = {
  [IterationPathConfig.Defect]: updateBug,
  [IterationPathConfig.Story]: updateStory,
};

const CreateFunMap = {
  [IterationPathConfig.Defect]: createBug,
  [IterationPathConfig.Story]: createStory,
};

const UpdateOrCreateResultCode = {
  [IterationPathConfig.Defect]: 'Bug',
  [IterationPathConfig.Story]: 'Story',
};

const Edit = () => {
  const [templateList, setTemplateList] = useState<any[]>([]);
  const [templateID, setTemplateID] = useState('');
  const [loading, setLoading] = useState(false);
  const rightRef = useRef<any>(null);
  const leftRef = useRef<any>(null);
  const { history } = TcsLayout.useHistory();
  const urlParams = getUrlParams();
  // const { pagePath } = useParams();
  const pagePath = usePagePath();
  const [editData, setEditData] = useState<any>(null);
  const isEdit = !!urlParams.issue_id;
  const issueType = IssueTypeConfig[pagePath];

  useEffect(() => {
    const promises = [
      listTemplate({
        IssueType: issueType,
      }).then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          setTemplateList(res.ListIssueTemplates);
          return res.ListIssueTemplates;
        }
      }),
    ];
    if (urlParams.issue_id) {
      const getDetailFun = DetailFunMap[pagePath];
      promises.push(
        getDetailFun({
          IssueID: urlParams.issue_id,
        }).then((res) => {
          if (res.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            const { DeliverOwners, DevOwners, Owner, TestOwners, ...otherData } = res?.GetBug || res?.GetStory;
            setEditData({
              ...otherData,
              DeliverOwners: DeliverOwners ? DeliverOwners.split(';') : [],
              DevOwners: DevOwners ? DevOwners.split(';') : [],
              Owner: Owner ? Owner.split(';') : [],
              TestOwners: TestOwners ? TestOwners.split(';') : [],
            });
            return res?.GetBug || res?.GetStory;
          }
        }),
      );
    }
    setLoading(true);
    Promise.all(promises)
      .then(([templateList, editData]) => {
        if (templateList?.length) {
          if (editData) {
            setTemplateID(editData.IssueTemplateUUID);
          } else {
            setTemplateID(templateList[0].UUID);
          }
        }
      })
      .finally(() => {
        setLoading(false);
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [urlParams.issue_id]);

  async function handleConfirm() {
    setLoading(true);
    try {
      const [leftFormData, rightFormData] = await Promise.all([
        leftRef.current?.getFormData(),
        rightRef.current?.getFormData(),
      ]);

      const { DeliverOwners, DevOwners, Owner, TestOwners, ...otherFormData } = rightFormData;

      const submitData = {
        ...leftFormData,
        ...otherFormData,
        DeliverOwners: DeliverOwners ? DeliverOwners.join(';') : '',
        DevOwners: DevOwners ? DevOwners.join(';') : '',
        Owner: Owner ? Owner.join(';') : '',
        TestOwners: TestOwners ? TestOwners.join(';') : '',
        IssueTemplateUUID: templateID,
      };

      const updateFun = UpdateFunMap[pagePath];
      const createFun = CreateFunMap[pagePath];

      const res = isEdit
        ? await updateFun({
            ...submitData,
            IssueID: urlParams.issue_id,
          })
        : await createFun(submitData);
      if (res.Error) {
        throw new Error(res.Error.Message);
      } else {
        if (isEdit) {
          history.go(-1);
        } else {
          // const issueId =
          history.replace(
            `${withRouteBasename(`/iteration_manage/${pagePath}/detail`)}?issue_id=${
              res[UpdateOrCreateResultCode[pagePath]].IssueID
            }`,
          );
        }
        // 跳转至详情页
        // history.replace(`${DefectManagementRoutePath.ITERATION_DETAIL_PAGE}?issue_id=${res.Bug.IssueID}`);
      }
    } catch (error) {
      console.error(error);
      if ((error as any)?.message) {
        message.error({
          content: (error as any)?.message,
        });
      }
    } finally {
      setLoading(false);
    }
  }

  function handleCancel() {
    history.go(-1);
  }

  const template = useMemo(() => {
    if (!templateID) {
      return templateList[0];
    }
    return templateList.find((item) => item.UUID === templateID);
  }, [templateID, templateList]);

  const titleAction = isEdit ? `编辑${EditPageTitleMap[pagePath]}` : `新增${EditPageTitleMap[pagePath]}`;

  return (
    <TcsLayout title={titleAction} customizeCard history={history} fullHeight>
      <TcsSpin spinning={loading} wrapperClassName={styles.iteration_edit_loading}>
        <Card className={styles.iteration_edit}>
          {!isEdit && (
            <Card.Header>
              <div className={styles.iteration_edit_template}>
                <Form>
                  <Form.Item label="请选择模板">
                    <Select
                      appearance="button"
                      placeholder="请选择模板"
                      matchButtonWidth
                      value={templateID}
                      options={templateList?.map((item) => ({
                        value: item.UUID,
                        text: item.Name,
                      }))}
                      style={{ width: 500 }}
                      onChange={(value) => setTemplateID(value)}
                    />
                  </Form.Item>
                </Form>
              </div>
            </Card.Header>
          )}
          <Card.Body className={styles.iteration_edit_body}>
            <div className={styles.iteration_edit_content}>
              <div className={styles.iteration_edit_content__left}>
                <Left ref={leftRef} editData={editData} pagePath={pagePath} />
              </div>
              <div className={styles.iteration_edit_content__right}>
                <Right ref={rightRef} template={template} formData={editData} />
              </div>
            </div>
          </Card.Body>
          <Card.Footer>
            <div className={styles.iteration_edit__buttons}>
              <Button type="primary" style={{ marginRight: 10 }} onClick={handleConfirm}>
                提交
              </Button>
              <Button onClick={handleCancel}>取消</Button>
            </div>
          </Card.Footer>
        </Card>
      </TcsSpin>
    </TcsLayout>
  );
};

export default Edit;
