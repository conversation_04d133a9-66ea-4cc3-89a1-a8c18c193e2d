/*
 * @Author: superfeng
 * @Date: 2023-03-17 16:29:16
 * @LastEditors: superfeng
 * @LastEditTime: 2023-06-28 10:18:44
 * @Description: 请输入注释信息
 */
import ExEditor from '@/common/components/ExEditor';
import { TcsForm, TcsFormText } from '@tencent/tcs-component';
import React, { useEffect, useImperativeHandle, useRef } from 'react';

export interface IProps {
  editData?: any;
  pagePath: 'store' | 'defect';
}

const DEFECT_DEFAULT_DESC = `<p>【问题描述】</p><p><span style="color: #b7b7b7">// 请描述清楚背景、问题现象、具体影响 </span></p><p>【重现步骤】</p><p><span style="color: #b7b7b7"> // 若有 请填写重现步骤 </span></p><p>【根本原因】</p><p><span style="color: #b7b7b7"> // 请说明根因内容 </span></p><p>【修复内容】</p><p><span style="color: #b7b7b7"> // 必须描述清楚范围</span></p>`;

const STORY_DEFAULT_DESC = `<p>【需求背景】</p><p><span style="color: #b7b7b7">//此需求产生的背景及需要解决的问题或主要目的 &nbsp; </span></p><p>【用户价值】</p><p><span style="color: #b7b7b7"><span style="background-color: #ffffff">//此需求给用户带领的价值，如解决用户的痛点</span></span></p><p>【需求描述】</p><p><span style="color: #b7b7b7">//需求详细说明，优化前后的描述。 </span>  </p><p>【验收标准】</p><p><span style="color: #b7b7b7">//需求验收条件，一些量化指标定义，如性能数据，是否提供用书手册  </span> </p><p>【期望上线时间】</p><p><span style="color: #b7b7b7">//希望此功能能支持的时间</span></p>`;

const Left: React.ForwardRefRenderFunction<any, any> = ({ editData, pagePath }, ref) => {
  const formRef = useRef<any>(null);

  useImperativeHandle(
    ref,
    () => ({
      getFormData() {
        return formRef.current.validateFields();
      },
    }),
    [],
  );

  useEffect(() => {
    // 如果是编辑页面，则回填编辑数据
    if (editData) {
      formRef.current?.setFieldsValue(editData);
    } else {
      // formRef.current?.setFieldsValue({
      //   Description: pagePath === 'story' ? STORY_DEFAULT_DESC : DEFECT_DEFAULT_DESC,
      // });
    }
  }, [editData]);

  return (
    <TcsForm formRef={formRef}>
      <TcsFormText name="Title" readonly />
      <TcsForm.Item name="Description" initialValue={pagePath === 'story' ? STORY_DEFAULT_DESC : DEFECT_DEFAULT_DESC}>
        <ExEditor readonly placeholder="请输入缺陷内容" />
      </TcsForm.Item>
    </TcsForm>
  );
};

export default React.forwardRef(Left);
