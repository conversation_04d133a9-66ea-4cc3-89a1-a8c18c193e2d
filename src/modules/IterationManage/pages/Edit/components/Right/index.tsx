/*
 * @Author: superfeng
 * @Date: 2023-03-20 19:35:30
 * @LastEditors: superfeng
 * @LastEditTime: 2023-03-28 14:42:38
 * @Description: 请输入注释信息
 */
import React, { useImperativeHandle, useMemo, useRef } from 'react';

import PageDesignBoard from '@/common/components/PageDesignBoard';

export interface IProps {
  template?: any;
  formData?: any;
}

const Right: React.ForwardRefRenderFunction<any, any> = ({ formData, template }, ref) => {
  const boardRef = useRef<any>(null);
  const inputParams = useMemo(
    () => ({
      readonly: false,
      formData: formData || {},
    }),
    [formData],
  );

  useImperativeHandle(
    ref,
    () => ({
      getFormData: async () => {
        const [result] = boardRef.current?.pageTriggerEmit?.('getFormData');
        if (result instanceof Promise) {
          const formData = await result;
          if (!formData) {
            throw new Error('');
          }
          return formData;
        }
        return result;
      },
    }),
    [],
  );

  return template ? (
    <PageDesignBoard inputParams={inputParams} list={template?.Template} ref={boardRef} />
  ) : (
    <div>暂无模板</div>
  );
};

export default React.forwardRef(Right);
