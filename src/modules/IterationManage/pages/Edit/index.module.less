.iteration_edit {
  display: flex;
  flex-direction: column;
  height: 100%;

  &_loading{
    height: 100% !important;
    &>div{
      height: 100% !important;
    }
  }

  &_body{
    flex: 1;
    height: 0;

    :global{
      .tea-card__content{
        height: 100%;
        overflow: auto;
      }
    }
  }

  &_template {
    padding: 20px;
  }

  &_content {
    width: 100%;
    display: flex;
    height: 100%;

    &__left {
      flex: 1;
      padding-top: 10px;
      padding-right: 20px;
      border: 1px solid #e8eaee;
      // min-height: 500px;
      width: 0;
      overflow: auto;
      height: 100%;
    
    }
    &__right {
      padding-top: 10px;
      width: 440px;
      border: 1px solid #e8eaee;
      border-left: 0;
      // min-height: 500px;
      height: 100%; 
      overflow: auto;
    }
  }
  &__buttons {
    margin-top: 10px;
    padding-bottom: 10px;
    text-align: center;
  }
}
