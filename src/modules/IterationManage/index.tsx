/*
 * @Author: super<PERSON>
 * @Date: 2023-03-17 15:25:49
 * @LastEditors: superfeng
 * @LastEditTime: 2023-03-20 10:27:37
 * @Description: 请输入注释信息
 */
import React from 'react';
import { Route } from 'react-router-dom';
import List from './pages/List';
import { DefectManagementRoutePath } from '@/common/routePath';
import Edit from './pages/Edit';
import Detail from './pages/Detail';
import TapdPage from './pages/TapdPage/index';

const Index = () => {
  const routesConfig = [
    {
      path: DefectManagementRoutePath.ITERATION_INDEX_PAGE,
      component: List,
      exact: true,
    },
    {
      path: DefectManagementRoutePath.ITERATION_EDIT_PAGE,
      component: Edit,
      exact: true,
    },
    {
      path: DefectManagementRoutePath.ITERATION_DETAIL_PAGE,
      component: Detail,
      exact: true,
    },
    {
      path: DefectManagementRoutePath.ITERATION_TAPD_PAGE,
      component: TapdPage,
      exact: true,
    },
  ];

  return (
    <>
      {routesConfig.map((item, index) => (
        <Route key={index} component={item.component} path={item.path} exact={item.exact} />
      ))}
    </>
  );
};
export default Index;
