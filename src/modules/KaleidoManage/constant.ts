/*
 * @Author: lucyfang
 * @Date: 2024-08-29 14:52:49
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-08-29 15:01:46
 * @Description:
 */
export enum CTRL_STATUS_TYPE {
  // 待完善
  TO_BE_ADVISED = 10,
  // 已完善
  ADVISED = 11,
  // 待提交
  TO_BE_SUBMIT = 20,
  // 已提交
  SUBMIT = 21,
}

// 变更控制表必填状态
export enum CTRL_REQUIRED_TYPE {
  // 必填
  REQUIRED = 1,
  // 非必填可删除
  NULLABLE = 2,
  // 非必填不可删除
  NULLABLE_NOT_DELETE = 3,
  // 必填但可使用默认
  REQUIRED_DEFAULT = 4,
}

export const CTRL_STATUS = [
  {
    value: CTRL_STATUS_TYPE.TO_BE_ADVISED,
    label: '待完善',
  },
  {
    value: CTRL_STATUS_TYPE.ADVISED,
    label: '已完善',
  },
  {
    value: CTRL_STATUS_TYPE.TO_BE_SUBMIT,
    label: '待提交',
  },
  {
    value: CTRL_STATUS_TYPE.SUBMIT,
    label: '已提交',
  },
];

// 是否为关键组件
export const CRITICAL_COMPONENT_OPTIONS = Object.freeze([
  {
    id: '是',
    name: '是',
  },
  {
    id: '否',
    name: '否',
  },
]);

// 租户是否有感知
export const LESSEE_FEEL_OPTIONS = Object.freeze([
  {
    id: '有',
    name: '有',
  },
  {
    id: '轻微',
    name: '轻微',
  },
  {
    id: '无',
    name: '无',
  },
]);

// 业务请求影响
export const REQUEST_AFFECT = Object.freeze([
  {
    id: '丢失',
    name: '丢失',
  },
  {
    id: '阻塞',
    name: '阻塞',
  },
  {
    id: '无影响',
    name: '无影响',
  },
]);

// 变更影响范围
export const CHANGE_AFFECT_PRODUCT = Object.freeze([
  {
    id: '单产品',
    name: '单产品',
  },
  {
    id: '多产品',
    name: '多产品',
  },
]);

// 变更优先级字段
export const SEVERITY_OPTIONS = Object.freeze([
  {
    id: 'high',
    name: '致命',
  },
  {
    id: 'middle',
    name: '严重',
  },
  {
    id: 'low',
    name: '一般及以下',
  },
]);

// 严重程度字段
export const CHANGE_PRIORITY_OPTIONS = Object.freeze([
  {
    id: 'highest',
    name: 'SVIP',
  },
  {
    id: 'high',
    name: '非常紧急',
  },
  {
    id: 'middle',
    name: '紧急',
  },
  {
    id: 'low',
    name: '一般',
  },
]);

// 变更控制表模板类型
export const TEMP_TYPE = {
  // 默认模板
  SOLUTION_TEMP: 'solution_temp',
  // 组件模板
  COMP_TEMP: 'comp_temp',
};

// 产品运维中心
export const PRODUCT_OPS = '产品运维中心';

// 私有化产品变更
export const PRIVATE_PRODUCT = '私有化产品变更';

// 变更工具
export const CHANGE_TOOL = [
  {
    id: 'kaleido',
    name: 'kaleido',
  },
  {
    id: PRODUCT_OPS,
    name: PRODUCT_OPS,
  },
  {
    id: PRIVATE_PRODUCT,
    name: PRIVATE_PRODUCT,
  },
];

export const NEW_CUSTOMER_EXPORT = '仅后续新增客户出包';

export const CCB_CLIENT_NAME = [
  '中国建设银行股份有限公司',
  '建行升级测试',
  '建行临时环境',
  '达烁高科(北京)信息技术有限公司',
  '建行',
];
