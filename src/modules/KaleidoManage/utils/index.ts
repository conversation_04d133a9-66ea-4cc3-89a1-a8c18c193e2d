/*
 * @Author: lucyfang
 * @Date: 2024-09-06 15:28:03
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-09-25 15:55:29
 * @Description: 请输入注释信息
 */
export function downloadFileByUrl(url: string) {
  const iframe = document.createElement('iframe');
  iframe.src = url;
  iframe.style.position = 'fixed';
  iframe.style.left = '10000px';

  document.body.appendChild(iframe);
  setTimeout(() => {
    iframe.remove();
  }, 100 * 1000);
}

export const getChangePriority = (assess: any, changeType: string) => {
  // 计算变更优先级
  let severity = 0;
  if (assess) {
    if (changeType === 'story') {
      severity = 0;
    } else if (changeType === 'security_bug') {
      severity = assess.sensitiveBug ? 1 : 0;
    } else {
      if (assess.dataImpact === '1') {
        severity = 2;
      } else if (assess.dataImpact === '2') {
        severity = 1;
      }
      if (severity < 2) {
        if (assess.businessImpact === '0') {
          if (assess.businessImpactItem?.length === 1) {
            severity = 1;
          } else if (assess.businessImpactItem?.length === 2) {
            severity = 2;
          }
        }
      }
    }
  }
  return ['low', 'middle', 'high'][severity];
};

export const parseTapd = (tapdUrl: string) => {
  const reg = /\/\/tapd\.w?oa\.com\/([a-zA-Z0-9_-]+)\/bugtrace\/bugs\/view\/(\d+)/g;
  const reg1 = /\/\/tapd\.w?oa\.com\/([a-zA-Z0-9_-]+)\/bugtrace\/bugs\/view\?bug_id=(\d+)/g;
  const reg2 = /\/\/tapd\.woa\.com\/tapd_fe\/([a-zA-Z0-9_-]+)\/bug\/detail\/(\d+)/g;
  const reg3 = /\/\/tapd\.woa\.com\/tapd_fe\/([a-zA-Z0-9_-]+)\/story\/detail\/(\d+)/g;
  const reg4 = /\/\/tapd\.w?oa\.com\/([a-zA-Z0-9_-]+)\/prong\/stories\/view\/(\d+)/g;

  let matches;
  for (const item of [reg, reg1, reg2, reg3, reg4]) {
    matches = item.exec(tapdUrl);
    if (matches && matches.length > 1) {
      break;
    }
  }
  if (matches && matches.length > 1) {
    return {
      workspace_id: matches![1] === 'private_tce_bug' ? '20419092' : matches![1],
      tapd_id: matches![2],
    };
  }
};
