/*
 * @Author: lucyfang
 * @Date: 2024-09-12 14:36:18
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-10-15 10:35:46
 * @Description: 请输入注释信息
 */
import React, { useState, useEffect, useCallback } from 'react';
import { TcsLayout, TcsSpin } from '@tencent/tcs-component';
import ChangeCtrl from './components/ChangeCtrl';
import { TapdListData } from './type';
import { message, Alert } from '@tencent/tea-component';
import { getUrlParams } from '@/common/utils';
import { getChangeOrders } from '@/common/api/kaleidoManage';
export interface IQueryParams {
  tapd_id: string;
  workspace_id: string;
  [key: string]: string;
}

const Kaleido = () => {
  const [urlParams, setUrlParams] = useState<IQueryParams>();
  const [data, setData] = useState<TapdListData>();
  const [tapdList, setTapdList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [update, setUpdate] = useState({});

  const [, setUrlState] = TcsLayout.useUrlState(urlParams || {}, { navigateMode: 'replace' });
  const addSyncKeys = useCallback(() => {
    setUpdate({});
  }, []);

  // 同步请求参数
  useEffect(() => {
    const queryParams = getUrlParams() as IQueryParams;
    setUrlParams(queryParams);
    setUrlState(queryParams);
  }, []);

  useEffect(() => {
    // 获取变更单的相关的变更单
    const fetchData = async () => {
      if (urlParams?.workspace_id && urlParams?.tapd_id) {
        setLoading(true);
        try {
          const response = await getChangeOrders({
            workspace_id: urlParams.workspace_id,
            bug_id: urlParams.tapd_id,
          });

          const data = response.data;

          if (response.code === 0) {
            const result = data.map((item) => ({
              ...item,
              key: `${item.tce_ver}-${item.arch}`,
            }));

            setData(result[0]);
            setTapdList(result);
          } else {
            message.error({ content: response.message });
          }
        } catch (error) {
          message.error({ content: error.message || '请求失败' });
        } finally {
          setLoading(false);
        }
      }
    };

    fetchData();
  }, [urlParams, update]);

  return (
    <TcsLayout title="变更控制表" customizeCard fullHeight>
      <Alert type="warning">当前是beat版本，有问题请联系lucyfang</Alert>
      {urlParams?.workspace_id ? (
        <TcsSpin spinning={loading}>
          <ChangeCtrl
            workspaceId={urlParams!.workspace_id!}
            bugId={urlParams!.tapd_id}
            currentTapd={data}
            addSyncKeys={addSyncKeys}
            tapdList={tapdList || []}
          />
        </TcsSpin>
      ) : (
        <></>
      )}
    </TcsLayout>
  );
};

export default Kaleido;
