/*
 * @Author: lucyfang
 * @Date: 2024-08-28 17:13:59
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-08-29 14:59:59
 * @Description: 请输入注释信息
 */
export interface DbsqlAmpDb {
  is_plan: boolean;
  is_qci: boolean;
  status: number;
}
export interface CompList {
  [key: string]: DbsqlAmpDb;
}
export interface TapdListData {
  workspace_id: string;
  bug_id: string;
  status: string;
  current_owner: string;
  tce_ver: string;
  arch: string;
  plan_comps: string[];
  qci_comps: string[];
  only_bug_id: number;
  ctrl_status: number;
  base_status: number;
  comp_list: CompList;
  key: string;
  is_qflow: boolean;
  package_type: string;
}
export interface IBaseInfo {
  sub_ctrl_id: number;
  status: number;
  rela_users: string;
  modify_user: string;
  modify_time: string;
  create_user: string;
  // 主导人
  ctrl_owner: string;
  // 变更组件
  comp_id: string;
  // 是否qflow
  is_qflow: boolean;

  tool?: string;
}

export interface ICompCtrl {
  sub_ctrl_id: number;
  name: string;
  seq: number;
  status: number;
  modify_user: string;
  modify_time: string;
  is_frontend: boolean;
  comp_id: string;
  is_qci: boolean;
  is_plan: boolean;
  yaml_url?: string;
}

export interface ISubmitInfo {
  // 提交信息
  create_time: string;
  modify_time: string;
  status: number;
  modify_user: string;
  // 主导人
  ctrl_owner: string;
}
