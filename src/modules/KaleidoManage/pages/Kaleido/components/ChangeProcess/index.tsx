/*
 * @Author: lucyfang
 * @Date: 2024-09-05 11:23:44
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-10-23 16:24:10
 * @Description: 完善变更流程组件
 */
import React, { useState, useEffect, useRef } from 'react';
import { TcsButton, TcsCard, TcsCollapse, TcsButtonGroup, TcsEditTableFormInstance } from '@tencent/tcs-component';
import { CTRL_STATUS_TYPE, TEMP_TYPE, CTRL_STATUS } from '../../../../constant';
import {
  listCtrlSubItem,
  removeTapdRel,
  queryTempList,
  getTempData,
  saveCtrlSubItem,
  orderCtrlSeq,
} from '@/common/api/kaleidoManage';
import ProcessTable from '../ProcessTable';
import styles from './index.module.less';
import { message, Icon } from '@tencent/tea-component';
import { downloadFileByUrl } from '../../../../utils';
import CtrlSyncModal from '../CtrlSyncModal';
import { TapdListData } from '../../type';

interface IProps {
  data?: any;
  ctrlId?: string;
  bugId?: string;
  workspaceId?: string;
  // 刷新数据
  resetCompCtrlData: (data: any) => void;
  updateCompCtrlList?: (sub_ctrl_id: number) => void;
  tapdParams: any;
  isReadonly: boolean;
  isDominator: boolean;
  status?: CTRL_STATUS_TYPE;
  baseInfoStatus?: CTRL_STATUS_TYPE;
  onEditablePanelsChange: (data: any) => void;
  addSyncKeys?: () => void;
  tapdList: TapdListData[];
  currentTapd: TapdListData;
}

const ChangeProcess: React.FC<IProps> = ({
  status,
  data,
  tapdParams,
  isReadonly,
  // 是否主导人
  isDominator,
  baseInfoStatus,
  resetCompCtrlData,
  onEditablePanelsChange,
  addSyncKeys,
  tapdList,
  currentTapd,
}) => {
  const [ctrlSyncVisible, setCtrlSyncVisible] = useState(false);
  // 用于管理面板的闭合状态
  const [activeIds, setActiveIds] = useState<string[]>([]);
  // 用于管理面板的数据
  const [panelData, setPanelData] = useState<Record<string, any>>({});
  // 用于管理面板的原始数据
  const [originalPanelData, setOriginalPanelData] = useState<Record<string, any>>({});
  // 用于管理面板的加载状态
  const [loadingPanels, setLoadingPanels] = useState<Record<string, boolean>>({});
  // 用于管理面板的编辑状态
  const [editablePanels, setEditablePanels] = useState<Record<string, boolean>>({});

  // 模板列表状态
  const [tempList, setTempList] = useState<any[]>([]);
  // 当前模板状态
  const [currentTemp, setCurrentTemp] = useState<Record<string, any>>({});
  // 默认模板数据
  const [defaultTempData, setDefaultTempData] = useState<Record<string, any[]>>({});
  // 面板编辑状态
  const [disableOtherEdit, setDisableOtherEdit] = useState<boolean>(false);

  const ref = useRef<TcsEditTableFormInstance>();

  useEffect(() => {
    onEditablePanelsChange(editablePanels);
  }, [editablePanels, onEditablePanelsChange]);

  const getListCtrlSubItem = async (sub_ctrl_id: number) => {
    setLoadingPanels((prevState) => ({ ...prevState, [sub_ctrl_id]: true }));
    try {
      // 查询变更控制表子表列表
      const response = await listCtrlSubItem({ sub_ctrl_id });
      // 可以修改的数据
      setPanelData((prevState) => ({ ...prevState, [sub_ctrl_id]: response.data }));
      // 复制原始数据作为备份
      setOriginalPanelData((prevState) => ({
        ...prevState,
        [sub_ctrl_id]: response.data,
      }));

      // 获取模版列表
      if (response?.data?.comp_id) {
        await fetchTempList(
          tapdParams?.solution_ver,
          response?.data.comp_id,
          Number(sub_ctrl_id),
          response?.data?.temp_name,
        );
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoadingPanels((prevState) => ({ ...prevState, [sub_ctrl_id]: false }));
    }
  };
  const loadTempData = (tempType: string, key: string, subCtrlId: number) => {
    setLoadingPanels((prevState) => ({ ...prevState, [subCtrlId]: true }));
    return getTempData({
      ctrlId: subCtrlId,
      tempType,
      // 如果是默认模板，则使用版本号，否则用id
      key,
    }).finally(() => {
      setLoadingPanels((prevState) => ({ ...prevState, [subCtrlId]: false }));
    });
  };

  // 获取模板列表
  const fetchTempList = async (solution_ver: string, comp_id: string, sub_ctrl_id: number, temp_name: string) => {
    try {
      // 加载模版列表
      const response = await queryTempList({ solution_ver, comp_id });
      if (response.code === 0) {
        // 设置当前模版列表
        setTempList((prevState) => ({ ...prevState, [sub_ctrl_id]: response.data.list }));
        // 设置当前的模版名称
        const defaultTemp = response.data.list.find((temp) => temp?.name === temp_name);
        if (defaultTemp) {
          setCurrentTemp((prevState) => ({ ...prevState, [sub_ctrl_id]: defaultTemp }));
        }

        // 加载默认模板数据
        const dataList = response?.data?.list || [];
        const defaultTempData = dataList.find((item) => item.temp_type === TEMP_TYPE.SOLUTION_TEMP);
        loadTempData(TEMP_TYPE.SOLUTION_TEMP, defaultTempData.solution_ver, sub_ctrl_id).then(({ code, data }) => {
          if (code === 0) {
            setDefaultTempData((prevState) => ({
              ...prevState,
              [sub_ctrl_id]: data.list.map((item: any) => {
                item.oper_id = item.id;
                return item;
              }),
            }));
          }
        });
      } else {
        message.error({ content: response.message });
      }
    } catch (error) {
      message.error({ content: '获取模板列表失败' });
    }
  };

  // 点击折叠面板
  const handlePanelChange = (newActiveIds: string[], { activeId, active }: { activeId: string; active: boolean }) => {
    setActiveIds(newActiveIds);
    if (active && !panelData[activeId]) {
      getListCtrlSubItem(Number(activeId));
    }
  };
  // 解除关联tapd单
  const handleRemoveTapdRel = async (sub_ctrl_id) => {
    try {
      const response = await removeTapdRel({ sub_ctrl_id });
      if (response.code === 0) {
        message.success({ content: '解除关联成功' });
        // 更新前端显示
        addSyncKeys?.();
      } else {
        message.error({ content: response.message });
      }
    } catch (error) {
      message.error({ content: '解除关联失败' });
    }
  };
  // 下载
  const handleDownload = async (row) => {
    try {
      const URL = window.location.origin;
      if (row.yaml_url) {
        downloadFileByUrl(row.yaml_url);
      } else {
        downloadFileByUrl(`${URL}/deliveryx/chgctrl/comp/download/${row.sub_ctrl_id}?time=${Date.now()}`);
      }
    } catch (error) {
      message.error({ content: error });
    }
  };

  // 编辑
  const handleEdit = async (sub_ctrl_id: string) => {
    if (baseInfoStatus === CTRL_STATUS_TYPE.TO_BE_ADVISED) {
      message.warning({ content: '请先完善基本信息后再编辑变更控制表子项' });
      return;
    }
    setEditablePanels((prevState) => ({ ...prevState, [sub_ctrl_id]: true }));
    setDisableOtherEdit(true);

    if (!activeIds.includes(sub_ctrl_id)) {
      setActiveIds([...activeIds, sub_ctrl_id]);
      if (!panelData[sub_ctrl_id]) {
        // 没有数据则请求数据
        await getListCtrlSubItem(Number(sub_ctrl_id));
      }
    }
  };
  // 取消编辑
  const handleCancelEdit = (sub_ctrl_id: string) => {
    setEditablePanels((prevState) => ({ ...prevState, [sub_ctrl_id]: false }));
    setDisableOtherEdit(false);

    // 恢复原始数据
    if (originalPanelData[sub_ctrl_id]) {
      const tempName = originalPanelData[sub_ctrl_id].temp_name;
      if (tempList[sub_ctrl_id]) {
        const defaultTemp = tempList[sub_ctrl_id].find((temp) => temp?.name === tempName);
        setCurrentTemp((prevState) => ({ ...prevState, [sub_ctrl_id]: defaultTemp }));
      }
      setPanelData((prevState) => ({
        ...prevState,
        [sub_ctrl_id]: originalPanelData[sub_ctrl_id],
      }));
    }
  };
  // 保存编辑
  const handleSaveEdit = (sub_ctrl_id: string) => {
    setDisableOtherEdit(false);
    ref.current?.validateRowsData().then((res) => {
      if (panelData[sub_ctrl_id]) {
        const params = panelData[sub_ctrl_id];
        params.list = panelData[sub_ctrl_id]?.list
          ?.map(({ rowSpan_stage, ...rest }) => rest)
          ?.map((item, index) => ({ ...item, ...res[index] }));

        saveCtrlSubItem({
          ...params,
          sub_ctrl_id: sub_ctrl_id,
          modify_user: (window as any).jiguang_username,
        })
          .then(({ code, message: msg }: { code: number; message: any }) => {
            if (code === 0) {
              message.success({ content: '保存成功' });
              setEditablePanels((prevState) => ({ ...prevState, [sub_ctrl_id]: false }));
              // 保存成功后清除原始数据备份
              setOriginalPanelData((prevState) => {
                const newState = { ...prevState };
                delete newState[sub_ctrl_id];
                return newState;
              });
              addSyncKeys?.();
            } else {
              message.error(msg);
            }
          })
          .finally(() => {});
      }
    });
  };
  // 切换模版数据
  const handleChangeTemp = (tempData: any, sub_ctrl_id: number, isReset: boolean) => {
    setCurrentTemp((prevState) => ({ ...prevState, [sub_ctrl_id]: tempData }));
    loadTempData(
      tempData.temp_type,
      tempData.temp_type === TEMP_TYPE.SOLUTION_TEMP ? tempData.solution_ver : tempData.temp_id,
      sub_ctrl_id,
    ).then(({ code, data }) => {
      if (code === 0) {
        const list = data.list.map((item: any) => {
          item.oper_id = item.id;
          return item;
        });
        setPanelData((prevState) => {
          const newState = { ...prevState };
          newState[sub_ctrl_id] = {
            ...newState[sub_ctrl_id],
            list: list,
            temp_name: tempData.name,
          };
          return newState;
        });

        message.warning({
          content: `${isReset ? '您已初始化模板' : '您已切换到新的模板'}，请检查当前模板是否需要修改`,
        });
      }
    });
  };
  // 重新加载模板列表，并选中传入的模板
  const handleReloadTempList = (val, sub_ctrl_id) => {
    fetchTempList(tapdParams?.solution_ver, panelData[sub_ctrl_id]?.comp_id, Number(sub_ctrl_id), val);
  };
  const handleDataUpdate = (val, sub_ctrl_id) => {
    setPanelData((prevState) => ({
      ...prevState,
      [sub_ctrl_id]: {
        ...prevState[sub_ctrl_id],
        list: val,
      },
    }));
  };
  const isEdit = (row: any): { isShow: boolean; message?: string } => {
    if (status !== CTRL_STATUS_TYPE.SUBMIT) {
      if (!isReadonly) {
        if (!row.is_plan) {
          return { isShow: false, message: '评估后才能编辑' };
        }
        if (!row.is_qci) {
          return { isShow: false, message: '合流后才能编辑' };
        }
        return { isShow: true };
      } else {
        return { isShow: false, message: '没有编辑权限' };
      }
    }
    return { isShow: false, message: '已提交不能编辑' };
  };
  const handleMove = async (upDown: 'up' | 'down', sub_ctrl_id: number) => {
    // 检查是否有任何面板处于编辑状态
    const isAnyPanelEditing = Object.values(editablePanels).some((isEditing) => isEditing);

    if (isAnyPanelEditing) {
      message.warning({ content: '编辑状态下不允许移动' });
      return;
    }
    try {
      const dt = data.find((item) => item.sub_ctrl_id === sub_ctrl_id);
      const index = data.findIndex((item) => item.sub_ctrl_id === sub_ctrl_id);
      const isUp = upDown === 'up';
      const otherIndex = isUp ? index - 1 : index + 1;
      if (otherIndex >= 0 && otherIndex < data.length) {
        const other = data[otherIndex];
        const currentSeq = isUp ? dt.seq - 1 : dt.seq + 1;
        const otherSeq = isUp ? other.seq + 1 : other.seq - 1;
        const compCtrl = [
          {
            sub_ctrl_id: dt.sub_ctrl_id,
            seq: currentSeq,
          },
          {
            sub_ctrl_id: other.sub_ctrl_id,
            seq: otherSeq,
          },
        ];
        const { code, message: msg } = await orderCtrlSeq({
          ctrl_id: dt.ctrl_id,
          comp_ctrl: compCtrl,
          modify_user: (window as any).jiguang_username,
        });
        if (code === 0) {
          const newData = data.map((item: any) => {
            if (item.sub_ctrl_id === dt.sub_ctrl_id) {
              return {
                ...item,
                seq: currentSeq,
              };
            }
            if (item.sub_ctrl_id === other.sub_ctrl_id) {
              return {
                ...item,
                seq: otherSeq,
              };
            }
            return item;
          });
          resetCompCtrlData(newData.sort((x: any, y: any) => x.seq - y.seq));
          message.success({ content: '移动成功' });
        } else {
          message.error(msg);
        }
      }
    } catch (error) {
      message.error({ content: '移动失败' });
    } finally {
      setLoadingPanels((prevState) => ({ ...prevState, [sub_ctrl_id]: false }));
    }
  };
  const handleCloseAll = () => {
    setActiveIds([]);
  };
  const handleCtrlSync = () => {
    setCtrlSyncVisible(true);
  };

  return (
    <TcsCard
      title="变更流程"
      extra={
        tapdList &&
        tapdList.length > 1 && (
          <TcsButton type="link" onClick={handleCtrlSync}>
            同步到其他版本
          </TcsButton>
        )
      }
    >
      <TcsButton style={{ marginBottom: 5, display: 'block' }} type="link" onClick={handleCloseAll}>
        一键折叠
      </TcsButton>
      <TcsCollapse activeIds={activeIds} onActive={handlePanelChange} background destroyInactivePanel>
        {data.map((item, index) => {
          return (
            <TcsCollapse.Panel
              className={styles.collapse}
              key={item.sub_ctrl_id}
              id={item.sub_ctrl_id}
              title={
                <>
                  <div style={{ display: 'flex', flex: 1, justifyContent: ' space-between' }}>
                    <div style={{ display: 'flex' }}>
                      {`组件${index + 1} 组件名：${item.name} (所属产品：${item.prod_code})`}【状态：
                      {CTRL_STATUS.find((status) => status.value === item?.status)?.label || item?.status}】【
                      {item.is_plan ? (
                        '已评估'
                      ) : (
                        <div>
                          <span style={{ marginRight: 5, color: 'red', fontWeight: 'bold' }}>未评估</span>
                          <Icon
                            type="warning"
                            color=""
                            tooltip="在涉及的产品/组件和名称处补充，英文分号间隔"
                            style={{ position: 'relative', top: '-1px', cursor: 'pointer' }}
                          />
                        </div>
                      )}
                      】【
                      {item.is_qci ? (
                        '已合流'
                      ) : (
                        <div>
                          <span style={{ marginRight: 5, color: 'red', fontWeight: 'bold' }}>待合流</span>
                          <Icon
                            type="warning"
                            tooltip="合流后方可编辑"
                            style={{ position: 'relative', top: '-1px', cursor: 'pointer' }}
                          />
                        </div>
                      )}
                      】
                    </div>
                    <div onClick={(e) => e?.stopPropagation()}>
                      <TcsButtonGroup
                        maxNum={4}
                        items={[
                          {
                            text: '编辑',
                            onClick: () => handleEdit(item.sub_ctrl_id),
                            hidden: editablePanels[item.sub_ctrl_id],
                            disabled: !isEdit(item).isShow || disableOtherEdit,
                            tooltip:
                              disableOtherEdit && !editablePanels[item.sub_ctrl_id]
                                ? '当前有其他编辑未保存，请先保存'
                                : isEdit(item).isShow || editablePanels[item.sub_ctrl_id]
                                ? ''
                                : isEdit(item).message,
                          },
                          {
                            text: '取消编辑',
                            confirm: true,
                            confirmProps: {
                              title: '确认要取消编辑吗',
                              onConfirm: () => handleCancelEdit(item.sub_ctrl_id),
                            },
                            hidden: !editablePanels[item.sub_ctrl_id],
                          },
                          {
                            text: '确定保存',
                            confirm: true,
                            confirmProps: {
                              title: '确认保存修改吗',
                              onConfirm: () => handleSaveEdit(item.sub_ctrl_id),
                            },
                            hidden: !editablePanels[item.sub_ctrl_id],
                          },
                          {
                            text: '解除tapd关联',
                            confirm: true,
                            confirmProps: {
                              title: '确认要解除tapd关联吗',
                              onConfirm: () => {
                                handleRemoveTapdRel(item?.sub_ctrl_id);
                              },
                            },
                            disabled: !(item.status !== CTRL_STATUS_TYPE.SUBMIT && item.is_qci && !item.is_plan),
                            tooltip:
                              item.status !== CTRL_STATUS_TYPE.SUBMIT && item.is_qci && !item.is_plan
                                ? ''
                                : '已合流待评估的可以手动解除tapd关联',
                          },
                          {
                            text: '上移',
                            onClick: () => {
                              handleMove('up', item?.sub_ctrl_id);
                            },
                            hidden: index === 0 || !(status !== CTRL_STATUS_TYPE.SUBMIT && isDominator),
                            tooltip: item.status !== CTRL_STATUS_TYPE.SUBMIT && isDominator ? '' : '只有主导人才可移动',
                          },
                          {
                            text: '下移',
                            onClick: () => {
                              handleMove('down', item?.sub_ctrl_id);
                            },
                            hidden: index === data.length - 1 || !(status !== CTRL_STATUS_TYPE.SUBMIT && isDominator),
                            tooltip: item.status !== CTRL_STATUS_TYPE.SUBMIT && isDominator ? '' : '只有主导人才可移动',
                          },
                          {
                            text: '下载',
                            onClick: () => handleDownload(item),
                            disabled: item.status === CTRL_STATUS_TYPE.TO_BE_ADVISED,
                            tooltip: item.status == CTRL_STATUS_TYPE.TO_BE_ADVISED ? '完善信息才可下载' : '',
                          },
                        ]}
                      />
                    </div>
                  </div>
                </>
              }
            >
              <ProcessTable
                data={panelData[item.sub_ctrl_id]}
                tableData={panelData[item.sub_ctrl_id]?.list || []}
                loading={loadingPanels[item.sub_ctrl_id]}
                editable={editablePanels[item.sub_ctrl_id]}
                tempList={tempList[item.sub_ctrl_id] || []} // 传递 tempList
                currentTemp={currentTemp[item.sub_ctrl_id]} // 传递 currentTemp
                onChangeTemp={(tempData, isReset) => handleChangeTemp(tempData, item.sub_ctrl_id, isReset)} // 更新 onChangeTemp 属性
                defaultTempData={defaultTempData[item.sub_ctrl_id]}
                reloadTempList={(val) => {
                  handleReloadTempList(val, item.sub_ctrl_id);
                }}
                ref={ref}
                onDataSourceChange={(val) => handleDataUpdate(val, item.sub_ctrl_id)}
              />
            </TcsCollapse.Panel>
          );
        })}
      </TcsCollapse>
      {ctrlSyncVisible && (
        <CtrlSyncModal
          onCancel={() => {
            setCtrlSyncVisible(false);
          }}
          onConfirm={() => setCtrlSyncVisible(false)}
          tapdList={tapdList}
          currentTapd={currentTapd}
          ctrlList={data}
        />
      )}
    </TcsCard>
  );
};
export default ChangeProcess;
