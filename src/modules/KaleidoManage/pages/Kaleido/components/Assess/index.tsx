/*
 * @Author: lucyfang
 * @Date: 2024-09-13 16:46:22
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-10-14 15:07:43
 * @Description: 请输入注释信息
 */
import React, { useState, useEffect } from 'react';
import { TcsCard } from '@tencent/tcs-component';
import { getChangePriority } from '../../../../utils';
import { CHANGE_PRIORITY_OPTIONS, SEVERITY_OPTIONS } from '../../../../constant';
import { Form, Radio, CheckboxGroup, Checkbox } from '@tencent/tea-component';

interface IProps {
  changeType: string;
  assess?: any;
  onChange?: (newAssess: any) => void;
  isEdit: boolean;
}

const Assess: React.FC<IProps> = ({ changeType, assess, onChange, isEdit }) => {
  const [priority, setPriority] = useState('');

  useEffect(() => {
    const val = assess || { dataImpact: '0', businessImpact: '1' };
    if (val && changeType) {
      const priorityKey = getChangePriority(val, changeType);
      const priorityData = CHANGE_PRIORITY_OPTIONS.find((item: any) => item.id === priorityKey);
      const severity = SEVERITY_OPTIONS.find((item: any) => item.id === priorityKey);
      if (priorityData) {
        const formattedPriority = `${priorityData.name}(${severity?.name})`;
        setPriority(formattedPriority);
      }
    }
  }, [changeType, assess]);

  // 业内敏感漏洞
  const handleSensitiveBugChange = (checked: boolean) => {
    // 调用外部onChange，通知父组件状态变更
    if (onChange) {
      onChange({ sensitiveBug: checked });
    }
  };
  // 数据影响评估
  const handleDataImpactChange = (val: string) => {
    triggerChange({
      businessImpact: assess?.businessImpact,
      businessImpactItem: assess?.businessImpactItem,
      dataImpact: val,
    });
  };
  // 业务影响评估
  const handleChangeBusinessImpact = (val: string) => {
    triggerChange({
      dataImpact: assess?.dataImpact,
      businessImpactItem: assess?.businessImpactItem,
      businessImpact: val,
    });
  };
  const handleChangeBusinessImpactItem = (val: string[]) => {
    triggerChange({
      businessImpact: assess?.businessImpact,
      dataImpact: assess?.dataImpact,
      businessImpactItem: val || [],
    });
  };

  function triggerChange(obj: any) {
    if (onChange) {
      onChange({
        businessImpact: obj.businessImpact ?? '1',
        dataImpact: obj.dataImpact ?? '0',
        businessImpactItem: obj.businessImpactItem ?? [],
      });
    }
  }

  return (
    <TcsCard bordered>
      {changeType === 'security_bug' && (
        <CheckboxGroup
          onChange={(val) => {
            const isChecked = val.includes('sensitiveBug');
            handleSensitiveBugChange(isChecked);
          }}
          value={assess.sensitiveBug ? ['sensitiveBug'] : []}
          disabled={isEdit}
        >
          <Checkbox name="sensitiveBug">业内敏感漏洞</Checkbox>
        </CheckboxGroup>
      )}
      {changeType === 'bugfix' && (
        <>
          <Form.Item label="数据影响评估">
            <Radio.Group
              defaultValue="0"
              value={assess?.dataImpact ?? '0'}
              onChange={(val) => handleDataImpactChange(val)}
              disabled={isEdit}
            >
              <Radio name="0">无数据丢失</Radio>
              <Radio name="1">业务数据丢失</Radio>
              <Radio name="2">监控或日志等运维数据丢失</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item label="业务影响评估">
            <Radio.Group
              disabled={isEdit}
              defaultValue="1"
              onChange={(val) => {
                handleChangeBusinessImpact(val);
              }}
              value={assess?.businessImpact ?? '1'}
            >
              <Radio name="0">核心业务中断</Radio>
              <Radio name="1">核心业务无影响</Radio>
            </Radio.Group>
          </Form.Item>
          {assess?.businessImpact === '0' ? (
            <Form.Item>
              <Checkbox.Group
                disabled={isEdit}
                onChange={(val) => {
                  handleChangeBusinessImpactItem(val);
                }}
                value={assess?.businessImpactItem ?? []}
              >
                <Checkbox name="0">业务中断超过0.5秒</Checkbox>
                <Checkbox name="1">影响两个产品及以上</Checkbox>
              </Checkbox.Group>
            </Form.Item>
          ) : undefined}
        </>
      )}
      <Form.Item label="变更优先级(严重程度)">
        <Form.Text>
          <div style={{ paddingTop: 6 }}>{priority}</div>
        </Form.Text>
      </Form.Item>
    </TcsCard>
  );
};
export default Assess;
