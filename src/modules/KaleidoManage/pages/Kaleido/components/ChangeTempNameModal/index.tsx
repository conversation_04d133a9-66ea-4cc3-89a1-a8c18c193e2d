/*
 * @Author: lucyfang
 * @Date: 2024-09-27 10:14:28
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-10-14 15:35:00
 * @Description: 请输入注释信息
 */
import React, { useState } from 'react';
import { TcsModal, TcsSpace, TcsButton } from '@tencent/tcs-component';
import { Alert, AutoComplete, Input, message } from '@tencent/tea-component';
import { TEMP_TYPE } from '../../../../constant';

interface IProps {
  onCancel: () => void;
  tempList: any[];
  onSaveTempName: (val: string) => void;
  saveLoading: boolean;
}

const ChangeTempNameModal: React.FC<IProps> = ({ onCancel, tempList, onSaveTempName, saveLoading }) => {
  const [inputValue, setInputValue] = useState('');

  function handleSave() {
    if (!inputValue) {
      return message.warning({ content: '请输入模板名称' });
    }
    // 查询新名字与默认模板是否重复了
    const find = tempList.find((item: any) => item.temp_type === TEMP_TYPE.SOLUTION_TEMP && item.name === inputValue);
    if (find) {
      return message.warning({ content: '模板名称不能与默认模板一致' });
    }
    if (tempList.find((item: any) => item.temp_type === TEMP_TYPE.COMP_TEMP && item.name === inputValue)) {
      TcsModal.confirm({
        title: '提示',
        content: '当前模板名称已存在，继续操作将覆盖同名模板，是否继续？',
        onOk() {
          onSaveTempName(inputValue);
        },
      });
    } else {
      onSaveTempName(inputValue);
    }
  }
  return (
    <TcsModal visible onCancel={onCancel} title="保存模版名称" width={700} footer={<></>}>
      <Alert type="warning">同一个组件的不同模板名称需唯一，若相同则覆盖保存</Alert>
      <TcsSpace>
        <span>保存模版名称：</span>
        <AutoComplete
          options={tempList.map((item) => ({ value: item.name, text: item.name }))}
          keyword={inputValue}
          onChange={(value) => {
            setInputValue(value);
          }}
        >
          {(ref) => <Input ref={ref} value={inputValue} onChange={(value) => setInputValue(value)} />}
        </AutoComplete>
        <TcsButton type="primary" onClick={handleSave} tooltip="点击按钮将当前内容保存为模板" loading={saveLoading}>
          保存
        </TcsButton>
      </TcsSpace>
    </TcsModal>
  );
};
export default ChangeTempNameModal;
