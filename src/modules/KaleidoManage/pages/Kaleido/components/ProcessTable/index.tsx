/*
 * @Author: lucyfang
 * @Date: 2024-09-05 16:30:59
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-10-17 16:10:26
 * @Description: 变更控制表子表
 */
import React, { useState, useEffect, useRef, useImperativeHandle } from 'react';
import { TcsTable, TcsButton, TcsActionType, TcsEditTableFormInstance, TcsButtonGroup } from '@tencent/tcs-component';
import { message } from '@tencent/tea-component';
import { saveTemp } from '@/common/api/kaleidoManage';

import SelectTemp from '../SelectTemp';
import AddExtraOperatorModal from '../AddExtraOperatorModal';
import { CTRL_REQUIRED_TYPE } from '../../../../constant';
interface IProps {
  data?: any; // 子表相关数据
  tableData?: any; // 表格数据
  loading?: boolean; // 表格是否处理加载状态
  editable?: boolean; // 是否可编辑
  tempList?: any[]; // 模版列表
  currentTemp?: any; // 当前选中的模版
  onChangeTemp: (tempData: any, isReset: boolean) => void; // 切换模板时的回调函数
  defaultTempData: any; // 默认模板数据
  reloadTempList: (tempName: string) => void; // 重新加载模版列表的回掉函数
  onDataSourceChange: (val: any) => void;
}

const ProcessTable: React.ForwardRefRenderFunction<any, IProps> = (props, ref) => {
  const {
    data,
    tableData,
    loading,
    editable = false,
    tempList,
    currentTemp,
    onChangeTemp,
    reloadTempList,
    onDataSourceChange,
  } = props;
  // 管理可编辑行的键值
  const [editableKeys, setEditableKeys] = useState<string[]>(tableData.map((item: any) => item.oper_id));
  // 管理表格的数据源
  const [dataSource, setDataSource] = useState<any[]>(tableData);
  // 表格操作相关的引用
  const actionRef = useRef<TcsActionType>();
  // 编辑表格表单相关的引用
  const editFormRef = useRef<TcsEditTableFormInstance>();
  // 控制是否只显示关键信息的开关状态
  // const [showOnlyVisible, setShowOnlyVisible] = useState(false);
  // 控制添加额外操弹窗的显示状态
  const [visible, setVisible] = useState(false);
  // 控制添加额外操弹窗的显示状态
  const [saveLoading, setSaveLoading] = useState(false);

  // 根据 editable 和 tableData 更新 editableKeys 状态
  useEffect(() => {
    if (editable) {
      setEditableKeys(tableData.map((item: any) => item.oper_id));
    } else {
      setEditableKeys([]);
    }
  }, [editable, tableData]);

  // 初始化 dataSource 和 originalData 状态
  useEffect(() => {
    if (tableData) {
      setDataSource(getProcessedData(tableData));
    }
  }, [tableData]);

  // 使用 useImperativeHandle 暴露组件方法
  useImperativeHandle(
    ref,
    () => {
      return {
        validateRowsData() {
          return editFormRef.current?.validateRowsData();
        },
        getRowsData() {
          return editFormRef.current?.getRowsData();
        },
      };
    },
    [],
  );

  // 处理表格数据的加工，包括阶段合并和序号添加
  const getProcessedData = (data: any[]) => {
    const processedData: any[] = [];
    let currentStage = '';
    let seq = 0;

    data.forEach((item) => {
      if (item.stage !== currentStage) {
        currentStage = item.stage;
        seq = 0;
      }
      // eslint-disable-next-line no-plusplus
      processedData.push({ ...item, seq: ++seq });
    });
    return processedData;
  };
  const handleRemoveRow = (row: any) => {
    const newData = dataSource.filter((item) => item.oper_id !== row.oper_id);
    setDataSource(newData);
    onDataSourceChange(newData);
  };
  // 表格的列配置
  const getColumns = () => {
    const columns: any[] = [
      {
        title: '阶段',
        dataIndex: 'stage',
        width: '8%',
        mergeRowSpan: true,
      },

      {
        title: '操作内容/目的',
        dataIndex: 'operation',
        width: '10%',
      },
      {
        title: '目标主机名',
        dataIndex: 'target_host',
        width: '10%',
        editable: true,
        required: true,
      },
      {
        title: '目标用户名',
        dataIndex: 'host_user',
        width: '10%',
        editable: true,
      },
      {
        title: '详细步骤描述（细化到命令行）',
        dataIndex: 'oper_detail',
        width: '20%',
        editable: true,
        valueType: 'textArea',
      },
      {
        title: '操作说明',
        dataIndex: 'oper_desc',
        width: '20%',
        editable: true,
        valueType: 'textArea',
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: '10%',
        fixed: 'right',
        render: (text: any, row: any, index: any) => {
          return (
            <>
              <TcsButtonGroup
                items={[
                  {
                    text: '上移',
                    type: 'link',
                    disabled: !editable || !isMoveUp(index),
                    tooltip: !editable ? '编辑状态下才可移动' : '',
                    onClick: () => handleMove(index, 'up'),
                  },
                  {
                    text: '下移',
                    type: 'link',
                    disabled: !editable || !isMoveDown(index),
                    tooltip: !editable ? '编辑状态下才可移动' : '',
                    onClick: () => handleMove(index, 'down'),
                  },
                  {
                    text: '删除',
                    type: 'link',
                    hidden: row.required !== CTRL_REQUIRED_TYPE.NULLABLE,
                    disabled: !editable,
                    tooltip: !editable ? '编辑状态下才可删除' : '',
                    confirm: true,
                    confirmProps: {
                      title: '确认要删除？',
                      onConfirm: () => handleRemoveRow(row),
                    },
                  },
                ]}
              />
            </>
          );
        },
      },
    ];
    // 如果不是编辑模式，则显示序号列
    if (!editable) {
      columns.splice(1, 0, {
        title: '序号',
        dataIndex: 'seq',
        width: '6%',
      });
    }

    return columns;
  };

  const isMoveUp = (index: number) => {
    if (index <= 0) return false;
    const currentStage = dataSource[index].stage;
    return dataSource[index - 1].stage === currentStage;
  };

  const isMoveDown = (index: number) => {
    if (index >= dataSource.length - 1) return false;
    const currentStage = dataSource[index].stage;
    return dataSource[index + 1].stage === currentStage;
  };

  const handleMove = (index: number, direction: 'up' | 'down') => {
    editFormRef.current?.validateRowsData().then((res) => {
      const updatedDataSource = dataSource.map((item, index) => {
        const resItem = res[index];
        return {
          ...item,
          ...resItem,
        };
      });
      const newData = [...updatedDataSource];
      if (direction === 'up' && index > 0) {
        [newData[index], newData[index - 1]] = [newData[index - 1], newData[index]];
      }
      if (direction === 'down' && index < newData.length - 1) {
        [newData[index], newData[index + 1]] = [newData[index + 1], newData[index]];
      }
      setDataSource(newData);
      onDataSourceChange(newData);
    });
  };

  // 展示关键信息开关操作
  // const handleToggleSwitch = (checked) => {
  //   // console.log(editFormRef?.current?.getRowsData());
  //   editFormRef?.current?.validateRowsData().then((res) => {
  //     console.log(res);
  //     setTimeout(() => {
  //       setShowOnlyVisible(checked);
  //     }, 2000);
  //   });
  // };
  const handleCancel = () => {
    setVisible(false);
  };
  const handleAddExtra = () => {
    setVisible(true);
  };

  // 确认插入元素
  const handleConfirm = (info: any) => {
    const [stage, posOperation] = info?.pos_operation.split('--');
    const find = dataSource.find((item) => item.stage === stage && item.operation === info.operation);
    if (find) {
      return message.warning({ content: `操作${info.operation}已存在` });
    }
    let targetIndex = dataSource.findIndex((item) => item.stage === stage && item.operation === posOperation);
    targetIndex = info.pos === 'before' ? targetIndex : targetIndex + 1;
    const newRow = {
      seq: targetIndex,
      operation: info.operation,
      oper_id: Date.now(),
      required: CTRL_REQUIRED_TYPE.NULLABLE,
      oper_desc: info.oper_desc,
      oper_detail: info.oper_detail,
      host_user: info.host_user,
      target_host: info.target_host,
      stage,
      show: 1,
    };

    editFormRef.current?.validateRowsData().then((res) => {
      const updatedDataSource = dataSource.map((item, index) => {
        const resItem = res[index];
        return {
          ...item,
          ...resItem,
        };
      });
      const newDataSourse = [...updatedDataSource];
      const newData = [...newDataSourse.slice(0, targetIndex), newRow, ...newDataSourse.slice(targetIndex)];

      setDataSource(newData);
      onDataSourceChange(newData);
      setVisible(false);
    });
  };
  // 保存修改的模版名称
  const handleSaveTempName = (val: string) => {
    editFormRef.current?.validateRowsData().then((res) => {
      const updatedDataSource = dataSource
        ?.map(({ rowSpan_stage, ...rest }) => rest)
        ?.map((item, index) => {
          const resItem = res[index];
          return {
            ...item,
            ...resItem,
          };
        });
      const newData = [...updatedDataSource];
      saveTemp({
        temp_name: val,
        comp_id: data?.comp_id,
        list: newData.map((item: any) => ({
          ...item,
          id: item.oper_id,
        })),
      })
        .then(({ code, message: msg }) => {
          if (code === 0) {
            reloadTempList(val);
            message.success({ content: '模板保存成功' });
          } else {
            message.error(msg);
          }
        })
        .catch((error) => {
          message.error(error);
        })
        .finally(() => {
          setSaveLoading(false);
        });
    });
    setSaveLoading(true);
  };

  const headerTitle = (
    <>
      <SelectTemp
        tempList={tempList}
        currentTemp={currentTemp}
        onChange={onChangeTemp}
        onSaveTempName={handleSaveTempName}
        saveLoading={saveLoading}
      />
    </>
  );
  // const handleDataSorce = () => {
  //   // console.log(editFormRef?.current?.)
  //   return dataSource?.filter((item) => (showOnlyVisible ? item.show === 1 : true));
  // };
  return (
    <>
      <TcsTable
        actionRef={actionRef}
        rowKey="oper_id"
        columns={getColumns()}
        dataSource={dataSource}
        loading={loading}
        options={false}
        editable={{
          type: 'multiple',
          editableKeys: editableKeys,
          onChange: setEditableKeys,
        }}
        scroll={{
          x: 1500,
        }}
        editableFormRef={editFormRef}
        headerTitle={editable ? headerTitle : ''}
        toolBarRender={() =>
          editable ? (
            <div>
              {/* <Switch onChange={handleToggleSwitch} value={showOnlyVisible}>
                展示必填信息
              </Switch> */}
              <TcsButton type="primary" onClick={handleAddExtra}>
                添加额外操作
              </TcsButton>
            </div>
          ) : (
            <></>
          )
        }
      />
      {visible && <AddExtraOperatorModal dataSource={dataSource} onCancel={handleCancel} onConfirm={handleConfirm} />}
    </>
  );
};
export default React.forwardRef(ProcessTable);
