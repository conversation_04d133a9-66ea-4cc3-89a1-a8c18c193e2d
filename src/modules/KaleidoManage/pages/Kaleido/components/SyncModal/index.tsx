/*
 * @Author: superfeng
 * @Date: 2021-12-03 17:25:17
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-10-14 16:03:15
 * @Description: 基本信息同步到其他版本
 */

import { TcsModal } from '@tencent/tcs-component';
import React, { useMemo, useState } from 'react';
import { TapdListData, IBaseInfo } from '../../type';
import { Alert, Checkbox, message } from '@tencent/tea-component';
import { SyncBase } from '@/common/api/kaleidoManage';

import { CTRL_STATUS_TYPE, NEW_CUSTOMER_EXPORT } from '../../../../constant';

export interface IProps {
  baseInfo: IBaseInfo;
  tapdList: TapdListData[];
  currentTapd: TapdListData;
  onCancel: () => void;
  onConfirm: () => void;
}

const SyncModal = ({ baseInfo, tapdList, currentTapd, onCancel, onConfirm }) => {
  const [checkedList, setCheckedList] = useState<string[]>([]);
  const list = useMemo(() => tapdList.filter((item) => item.key !== currentTapd.key), [tapdList, currentTapd]);
  const [loading, setLoading] = useState(false);
  function handleConfirm() {
    if (checkedList.length === 0) {
      return message.warning({ content: '请选择要同步的版本' });
    }

    setLoading(true);
    const syncList = tapdList.filter((item) => checkedList.includes(item.key));
    SyncBase({
      workspace_id: currentTapd.workspace_id,
      bug_id: currentTapd.bug_id,
      is_qflow: baseInfo?.is_qflow || false,
      sync_list: syncList.map((item) => ({
        workspace_id: item.workspace_id,
        bug_id: item.bug_id,
      })),
    }).then((data) => {
      if (data.code === 0) {
        message.success({ content: '同步成功' });
        onConfirm();
      } else {
        message.error({ content: data.message });
        onCancel();
      }
    });
  }

  return (
    <TcsModal
      title="基本信息同步"
      visible
      width={800}
      onCancel={onCancel}
      onOk={handleConfirm}
      confirmLoading={loading}
    >
      {baseInfo?.is_qflow && <Alert type="warning">同步后将触发紧急出包</Alert>}
      <Checkbox.Group value={checkedList} onChange={(value) => setCheckedList(value)}>
        {list.map((item) => {
          let tooltipTitle = '';
          if (!currentTapd.is_qflow && item.is_qflow) {
            tooltipTitle = '源单不是qflow,目标单是qflow不能同步';
          } else if (item.package_type === NEW_CUSTOMER_EXPORT) {
            tooltipTitle = '仅后续新增客户出包不需要同步';
          } else if (item.ctrl_status === CTRL_STATUS_TYPE.SUBMIT) {
            tooltipTitle = '已提交变更单无需同步';
          }
          return tooltipTitle !== '' ? (
            <Checkbox name={item.key} tooltip={tooltipTitle} disabled>
              {item.key}
            </Checkbox>
          ) : (
            <Checkbox name={item.key}>{item.key}</Checkbox>
          );
        })}
      </Checkbox.Group>
    </TcsModal>
  );
};

export default SyncModal;
