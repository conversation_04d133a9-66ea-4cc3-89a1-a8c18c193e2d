/*
 * @Author: lucyfang
 * @Date: 2024-09-25 11:47:53
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-10-14 16:38:26
 * @Description: 请输入注释信息
 */
import React, { useRef, useEffect, useState } from 'react';
import { TcsModal, FormInstance, TcsSpace, TcsButton } from '@tencent/tcs-component';
import { Select } from '@tencent/tea-component';
import ChangeTempNameModal from '../ChangeTempNameModal';

interface IProps {
  tempList?: any[];
  currentTemp?: any;
  onChange: (tempData: any, isReset: boolean) => void;
  onSaveTempName: (tempName: string) => void;
  saveLoading: boolean;
}

const SelectTemp: React.FC<IProps> = ({ tempList = [], currentTemp, onChange, onSaveTempName, saveLoading }) => {
  const formRef = useRef<FormInstance>();
  const [tempNameModalVisible, setTempNameModalVisible] = useState(false);
  useEffect(() => {
    if (currentTemp) {
      formRef.current?.setFieldValue('temp', currentTemp?.name);
    }
  }, [currentTemp]);

  function handleChange(name: string) {
    if (!currentTemp || name !== currentTemp.name) {
      TcsModal.confirm({
        title: '确认要切换模板，切换后将重置已编辑的内容',
        onOk() {
          const item = tempList.find((temp: any) => temp.name === name);
          onChange?.(item, false);
        },
      });
    }
  }

  function handleResetTemp() {
    TcsModal.confirm({
      title: '确认要初始化模板，切换后将重置已编辑的内容',
      onOk() {
        onChange?.(currentTemp, true);
      },
    });
  }
  function handleChangeTemp() {
    setTempNameModalVisible(true);
  }
  function handleCancel() {
    setTempNameModalVisible(false);
  }

  return (
    <>
      <TcsSpace>
        <span>加载模版：</span>
        <Select
          size="l"
          matchButtonWidth
          defaultSearchValue={currentTemp?.name}
          value={currentTemp?.name}
          options={tempList?.map((item) => {
            return { label: item?.name, value: item?.name };
          })}
          appearance="button"
          onChange={handleChange}
        />
        <TcsButton type="primary" onClick={handleResetTemp}>
          初始化模板
        </TcsButton>
        <TcsButton onClick={handleChangeTemp} tooltip="同一个组件的不同模板名称需唯一，若相同则覆盖保存">
          保存模版名称
        </TcsButton>
      </TcsSpace>
      <div style={{ color: 'red', fontSize: 12, marginBottom: 10, marginTop: 5 }}>
        提醒：模板提供通用变更步骤，请开发人员按实际情况评估覆盖变更全部操作
      </div>
      {tempNameModalVisible && (
        <ChangeTempNameModal
          onCancel={handleCancel}
          tempList={tempList}
          onSaveTempName={onSaveTempName}
          saveLoading={saveLoading}
        />
      )}
    </>
  );
};
export default SelectTemp;
