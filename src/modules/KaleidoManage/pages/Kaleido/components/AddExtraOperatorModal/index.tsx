/*
 * @Author: lucyfang
 * @Date: 2024-09-26 15:28:38
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-10-14 15:07:27
 * @Description: 请输入注释信息
 */
import React, { useRef } from 'react';
import { TcsModal, TcsForm, FormInstance, TcsFormText, TcsFormTextArea, TcsFormSelect } from '@tencent/tcs-component';
export interface IProps {
  onCancel: () => void;
  dataSource: any[];
  onConfirm: (data: any) => void;
}

const AddExtraOperatorModal: React.FC<IProps> = ({ onCancel, dataSource = [], onConfirm }) => {
  const formRef = useRef<FormInstance>();
  const handleConfirm = () => {
    formRef.current?.validateFields().then((form) => {
      formRef.current?.resetFields();
      onConfirm(form);
    });
  };
  const handleCancel = () => {
    onCancel();
    formRef.current?.resetFields();
  };

  return (
    <TcsModal title="添加额外操作" visible onCancel={handleCancel} width={600} onOk={handleConfirm}>
      <TcsForm formRef={formRef}>
        <TcsFormText
          label="操作名称"
          name="operation"
          rules={[
            {
              required: true,
              message: '请输入操作名称',
            },
          ]}
          fieldProps={{ size: 'full', placeholder: '请输入操作名称' }}
        />
        <TcsFormText
          label="目标主机名"
          name="target_host"
          rules={[
            {
              required: true,
              message: '请输入目标主机名',
            },
          ]}
          initialValue="部署机"
          fieldProps={{ size: 'full', placeholder: '请输入目标主机名' }}
        />
        <TcsFormText
          label="目标用户名"
          name="host_user"
          rules={[
            {
              required: true,
              message: '请输入目标用户名',
            },
          ]}
          initialValue="root"
          fieldProps={{ size: 'full', placeholder: '请输入目标用户名' }}
        />
        <TcsFormTextArea
          label="详细步骤描述"
          name="oper_detail"
          rules={[
            {
              required: true,
              message: '请输入详细步骤描述',
            },
          ]}
          fieldProps={{ size: 'full', placeholder: '请输入详细步骤描述' }}
        />
        <TcsFormText label="操作说明" name="oper_desc" fieldProps={{ size: 'full', placeholder: '请输入操作说明' }} />
        <TcsFormSelect
          label="插入位置所属操作"
          name="pos_operation"
          rules={[
            {
              required: true,
              message: '请选择插入位置所属操作',
            },
          ]}
          fieldProps={{
            size: 'full',
            placeholder: '请选择插入位置所属操作',
            options: dataSource?.map((item) => {
              return {
                label: `${item.stage}--${item.operation}`,
                value: `${item.stage}--${item.operation}`,
              };
            }),
          }}
        />
        <TcsFormSelect
          label="插入位置"
          name="pos"
          rules={[
            {
              required: true,
              message: '请选择插入位置',
            },
          ]}
          fieldProps={{
            size: 'full',
            placeholder: '请选择插入位置',
            options: [
              { label: '插入操作之前', value: 'before' },
              { label: '插入操作之后', value: 'later' },
            ],
          }}
        />
      </TcsForm>
    </TcsModal>
  );
};

export default AddExtraOperatorModal;
