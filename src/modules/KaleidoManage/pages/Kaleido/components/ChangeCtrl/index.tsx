/*
 * @Author: superfeng
 * @Date: 2021-12-03 09:42:04
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-10-14 17:02:47
 * @Description: 请输入注释信息
 */
import React, { useState, useEffect, useMemo } from 'react';
import { TcsSpin } from '@tencent/tcs-component';
import { message } from '@tencent/tea-component';
import { TapdListData, IBaseInfo, ICompCtrl, ISubmitInfo } from '../../type';
import BaseInfo from '../BaseInfo';
import { CTRL_STATUS_TYPE } from '../../../../constant';
import { getTapdInfoByTools, getTapdInfo } from '@/common/api/kaleidoManage';
import ChangeProcess from '../ChangeProcess';

export interface IInfo {
  base_info: IBaseInfo;
  comp_ctrl: ICompCtrl[];
  ctrl_id: string;
  submit_info: ISubmitInfo;
}

export interface IProps {
  workspaceId: string;
  bugId: string;
  currentTapd: TapdListData;
  tapdList: TapdListData[];
  addSyncKeys: () => void;
}

const ChangeCtrl: React.FC<IProps> = ({ workspaceId, bugId, currentTapd, tapdList, addSyncKeys }) => {
  const [loading, setLoading] = useState(false);

  const [update, setUpdate] = useState({});

  // tapd的信息
  const [tapdInfo, setTapdInfo] = useState<any>({});

  // kaleido变更控制表信息
  const [kaleidoInfo, setKaleidoInfo] = useState<IInfo>();
  // 变更流程子表编辑状态
  const [editablePanels, setEditablePanels] = useState({});

  const handleEditablePanelsChange = (newEditablePanels) => {
    setEditablePanels(newEditablePanels);
  };

  // 处理info数据
  const getInfoData = (response) => {
    if (response.code === 0) {
      const dt = response.data;
      if (!dt) {
        return;
      }
      return {
        ...dt,
        base_info: {
          ...dt.base_info,
          is_qflow: dt.is_qflow || false,
        },
        comp_ctrl: (dt.comp_ctrl || []).map((item: any, index: any) => {
          item.seq = index + 1;
          return item;
        }) as ICompCtrl[],
        submit_info: {
          status: dt.status,
          create_time: dt.create_time,
          modify_time: dt.modify_time,
          modify_user: dt.modify_user,
          ctrl_owner: dt.ctrl_owner,
        },
      };
    }
    message.error(response.message);
  };
  const fetchData = async () => {
    if (workspaceId && bugId && tapdInfo?.solution_ver) {
      setLoading(true);
      try {
        const response = await getTapdInfoByTools({
          tapd_id: bugId,
          workspace_id: workspaceId,
          tool: 'kaleido',
        });
        const kaleidoInfoData = getInfoData(response);
        if (kaleidoInfoData) {
          setKaleidoInfo(kaleidoInfoData);
        }
      } catch (error) {
        message.error({ content: '获取数据失败' });
      } finally {
        setLoading(false);
      }
    }
  };
  // 获取kaleido变更控制表信息
  useEffect(() => {
    fetchData();
  }, [workspaceId, bugId, tapdInfo, update]);

  // 获取 tapd 基本信息
  useEffect(() => {
    const fetchTapdInfo = async () => {
      if (workspaceId && bugId) {
        try {
          const response = await getTapdInfo({
            workspace_id: workspaceId,
            tapd_id: bugId,
          });
          if (response.status === 'success') {
            setTapdInfo({
              ...response.data,
              workspace_id: workspaceId,
              tapd_id: bugId,
            });
          }
        } catch (error) {}
      }
    };
    fetchTapdInfo();
  }, [workspaceId, bugId]);

  const isDominator = useMemo(() => {
    if (kaleidoInfo) {
      const currentUser = (window as any).jiguang_username;
      const { ctrl_owner = '' } = kaleidoInfo.submit_info;
      const ownerList = ctrl_owner.split(',');
      return ownerList.some((item) => item === currentUser || item.indexOf(`${currentUser}(`) !== -1);
    }
    return false;
  }, [kaleidoInfo]);

  const isReadonly = useMemo(() => {
    if (kaleidoInfo) {
      const currentUser = (window as any).jiguang_username;
      // 干系人 和 创建人
      const { rela_users = '' } = kaleidoInfo.base_info;
      const { ctrl_owner = '' } = kaleidoInfo.submit_info;
      const ownerList = ctrl_owner.split(',');
      const userList = rela_users ? [...ownerList, ...rela_users.split(',')] : [...ownerList];
      // 只有创建人和干系人可以修改记录，其他人只能查看和下载
      return !userList.some((item) => item === currentUser || item.indexOf(`${currentUser}(`) !== -1);
    }

    return false;
  }, [kaleidoInfo]);

  const resetCompCtrlList = (newCompCtrlData: ICompCtrl[]) => {
    setKaleidoInfo((prevKaleidoInfo) => ({
      ...prevKaleidoInfo!,
      comp_ctrl: newCompCtrlData,
    }));
  };

  return (
    <TcsSpin spinning={loading}>
      {/* 仅后续新增客户出包 */}
      {/* {tapdInfo?.pack_type === NEW_CUSTOMER_EXPORT ? (
        <NewCustomerExportInfo data={currentTapd} />
      ) : ( */}
      {/* 只有主导人可以编辑，其他人只读 */}
      <>
        <BaseInfo
          tapdList={tapdList || []}
          isDominator={isDominator}
          submitInfo={kaleidoInfo?.submit_info}
          baseInfo={kaleidoInfo?.base_info}
          subStatus={kaleidoInfo?.submit_info?.status || CTRL_STATUS_TYPE.TO_BE_ADVISED}
          ctrlId={kaleidoInfo?.ctrl_id || ''}
          isReadonly={!isDominator}
          tapdParams={tapdInfo}
          workspaceId={workspaceId}
          bugId={bugId}
          fetchData={fetchData}
          data={kaleidoInfo?.comp_ctrl || []}
          currentTapd={currentTapd}
          addSyncKeys={addSyncKeys}
          refreshData={() => setUpdate({})}
          editablePanels={editablePanels}
        />
        <ChangeProcess
          tapdList={tapdList || []}
          status={kaleidoInfo ? kaleidoInfo.submit_info.status : CTRL_STATUS_TYPE.TO_BE_ADVISED}
          baseInfoStatus={kaleidoInfo ? kaleidoInfo.base_info.status : CTRL_STATUS_TYPE.TO_BE_ADVISED}
          isReadonly={isReadonly}
          isDominator={isDominator}
          data={kaleidoInfo?.comp_ctrl || []}
          ctrlId={kaleidoInfo?.ctrl_id}
          bugId={bugId}
          workspaceId={workspaceId}
          tapdParams={tapdInfo}
          resetCompCtrlData={resetCompCtrlList}
          onEditablePanelsChange={handleEditablePanelsChange}
          addSyncKeys={() => {
            setUpdate({});
            addSyncKeys?.();
          }}
          currentTapd={currentTapd}
        />
      </>
      {/* )} */}
    </TcsSpin>
  );
};

export default ChangeCtrl;
