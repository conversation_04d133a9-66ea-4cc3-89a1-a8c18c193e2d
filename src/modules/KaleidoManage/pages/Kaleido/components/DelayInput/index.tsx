/*
 * @Author: lucyfang
 * @Date: 2024-09-23 15:56:47
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-10-14 15:35:21
 * @Description: 请输入注释信息
 */
import React, { useEffect, useState } from 'react';
import { InputNumber } from '@tencent/tea-component';

interface IProps {
  defaultValue?: number;
  onChange?: (value: any) => void;
  isEdit: boolean;
}

const DelayInput: React.FC<IProps> = ({ defaultValue, onChange, isEdit }) => {
  const [value, setValue] = useState<number>();
  function handleChange(value: number | undefined) {
    onChange?.(value);
    setValue(value);
  }
  useEffect(() => {
    if (defaultValue) {
      setValue(defaultValue);
    }
  }, [defaultValue]);
  return (
    <>
      <div style={{ display: 'flex', alignItems: 'center', height: 16 }}>
        有，
        <InputNumber
          disabled={isEdit}
          style={{ padding: 5 }}
          min={0}
          hideButton
          value={value}
          onChange={handleChange}
          precision={1}
        />
        分钟中断
      </div>
    </>
  );
};
export default DelayInput;
