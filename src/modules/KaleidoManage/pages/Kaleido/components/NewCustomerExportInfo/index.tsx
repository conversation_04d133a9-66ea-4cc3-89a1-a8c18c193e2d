/*
 * @Author: lucyfang
 * @Date: 2024-08-29 17:03:12
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-08-30 14:52:11
 * @Description: 仅后续新增客户出包
 */

import React, { useMemo } from 'react';
import { TapdListData, IBaseInfo, ICompCtrl, ISubmitInfo } from '../../type';

export interface IProps {
  data: TapdListData;
}

const NewCustomerExportInfo: React.FC<IProps> = ({ data }) => {
  return <></>;
};

export default NewCustomerExportInfo;
