/*
 * @Author: lucyfang
 * @Date: 2024-08-29 11:44:19
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-02-26 15:54:26
 * @Description: 基本信息组件
 */
import React, { useEffect, useState, useMemo } from 'react';
import { TcsSchemaForm, TcsColumns, TcsCard, TcsButton, TcsSpace } from '@tencent/tcs-component';
import { TapdListData, IBaseInfo } from '../../type';
import { CTRL_STATUS, CTRL_STATUS_TYPE } from '../../../../constant';
import moment from 'moment';
import copy from 'copy-to-clipboard';
import { message } from '@tencent/tea-component';
import EditFormModal from '../EditFormModal';
import { submitCtrl } from '@/common/api/kaleidoManage';
import SyncModal from '../SyncModal';
import { downloadFileByUrl } from '../../../../utils';

function getCtrlStatusText(key: number) {
  const find = CTRL_STATUS.find((item) => item.value === key);
  return find ? find.label : '';
}

interface IProps {
  // 提交信息
  submitInfo?: any;
  // 基本信息
  baseInfo?: IBaseInfo;
  isReadonly: boolean;
  // 提交状态
  subStatus: number;
  tapdParams?: any;
  ctrlId: string;
  workspaceId: string;
  bugId: string;
  fetchData: () => void;
  // 更新数据
  refreshData?: () => void;
  data?: any;
  isDominator: boolean;
  tapdList: TapdListData[];
  currentTapd: TapdListData;
  addSyncKeys?: () => void;
  editablePanels?: any;
}

const BaseInfo: React.FC<IProps> = ({
  baseInfo,
  isReadonly,
  subStatus,
  tapdParams,
  ctrlId,
  workspaceId,
  bugId,
  fetchData,
  tapdList,
  currentTapd,
  addSyncKeys,
  refreshData,
  editablePanels,
  submitInfo,
}) => {
  const [formValues, setFormValues] = useState({});
  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [syncVisible, setSyncVisible] = useState(false);
  // 初始化基本信息表单值
  useEffect(() => {
    if (baseInfo) {
      const initialValues = {
        status: getCtrlStatusText(baseInfo.status),
        rela_users: baseInfo.rela_users,
        modify_user: baseInfo.modify_user,
        modify_time: baseInfo.modify_time ? moment(baseInfo.modify_time).format('YYYY-MM-DD HH:mm:ss') : '',
      };
      setFormValues(initialValues);
    }
  }, [baseInfo]);

  const columns: TcsColumns[] = [
    {
      dataIndex: 'status',
      title: '基本信息完善状态',
    },
    {
      dataIndex: 'rela_users',
      title: '变更干系人',
    },
    {
      dataIndex: 'modify_user',
      title: '最后修改人',
    },
    {
      dataIndex: 'modify_time',
      title: '最后修改时间',
      valueType: 'dateTime',
    },
  ];
  const status = useMemo(() => {
    if (submitInfo) {
      return submitInfo.status;
    }
    return CTRL_STATUS_TYPE.TO_BE_ADVISED;
  }, [submitInfo]);

  const enableEdit = useMemo(
    () =>
      ((status === CTRL_STATUS_TYPE.ADVISED || status === CTRL_STATUS_TYPE.TO_BE_SUBMIT) &&
        subStatus !== CTRL_STATUS_TYPE.SUBMIT) ||
      tapdParams?.status === 'QA_audited',
    [status, subStatus, tapdParams?.status],
  );

  // 打开TAPD单
  function handleOpenTapd() {
    if (workspaceId && bugId) {
      window.open(`https://tapd.woa.com/tapd_fe/${workspaceId}/bug/detail/${bugId}`);
    }
  }
  // 复制TAPD链接
  function handleCopyTapd() {
    if (workspaceId && bugId) {
      copy(`https://tapd.woa.com/tapd_fe/${workspaceId}/bug/detail/${bugId}`);
      message.success({ content: '复制成功' });
    }
  }
  // 完善基本信息/编辑
  function handleEditBaseInfo() {
    setVisible(true);
  }
  function handleCancel() {
    setVisible(false);
    setIsEdit(false);
  }
  // 查看详情
  function handleViewBaseInfo() {
    setVisible(true);
    setIsEdit(true);
  }
  function handleSuccess() {
    setVisible(false);
    setIsEdit(false);
    fetchData();
  }
  function handleSubmit() {
    const hasTrue = Object.values(editablePanels).some((value) => value);
    if (hasTrue) {
      return message.error({ content: '当前有未保存的编辑，请保存再提交' });
    }
    submitCtrl({
      ctrl_id: ctrlId,
      status: 21,
      base_info: baseInfo,
    }).then((res) => {
      if (res.code !== 0) {
        return message.error({ content: `提交失败，失败原因为${res.message}` });
      }
      refreshData?.();
    });
  }
  function handleWithdraw() {
    submitCtrl({
      ctrl_id: ctrlId,
      status: 20,
      base_info: baseInfo,
    }).then((res) => {
      if (res.code !== 0) {
        return message.error({ content: `撤回失败，失败原因为${res.message}` });
      }
      message.success({ content: '撤回成功' });
      refreshData?.();
    });
  }
  // 同步到其他版本
  function handleSyncOtherVer() {
    setSyncVisible(true);
  }
  function handleSyncCancel() {
    setSyncVisible(false);
  }
  function handleSyncConfirm() {
    addSyncKeys?.();
    setSyncVisible(false);
  }
  function handleDownload() {
    const URL = window.location.origin;
    const time = Date.now();
    downloadFileByUrl(`${URL}/deliveryx/chgctrl/download/${ctrlId}?${time}=${time}`);
  }
  return (
    <TcsCard
      title="基本信息"
      extra={
        <TcsSpace>
          {status === CTRL_STATUS_TYPE.TO_BE_ADVISED && (
            <TcsButton
              type="link"
              onClick={handleEditBaseInfo}
              disabled={isReadonly}
              tooltip={isReadonly ? '只有主导人可以编辑' : ''}
            >
              完善基本信息
            </TcsButton>
          )}
          {enableEdit && (
            <TcsButton
              type="link"
              onClick={handleEditBaseInfo}
              disabled={isReadonly}
              tooltip={isReadonly ? '只有主导人可以编辑' : ''}
            >
              编辑
            </TcsButton>
          )}
          {status !== CTRL_STATUS_TYPE.TO_BE_ADVISED && (
            <TcsButton
              type="link"
              onClick={handleViewBaseInfo}
              disabled={isReadonly}
              tooltip={isReadonly ? '只有主导人可以查看详情' : ''}
            >
              查看详情
            </TcsButton>
          )}
          {tapdList && tapdList.length > 1 && (
            <TcsButton
              type="link"
              onClick={handleSyncOtherVer}
              disabled={isReadonly}
              tooltip={isReadonly ? '只有主导人可以同步到其他版本' : ''}
            >
              同步到其他版本
            </TcsButton>
          )}
          {status !== CTRL_STATUS_TYPE.SUBMIT && (
            <TcsButton
              type="link"
              onClick={handleSubmit}
              disabled={isReadonly}
              tooltip={isReadonly ? '只有主导人可以提交变更控制表' : ''}
            >
              提交变更控制表
            </TcsButton>
          )}
          {status === CTRL_STATUS_TYPE.SUBMIT && (
            <TcsButton
              type="link"
              onClick={handleWithdraw}
              disabled={isReadonly}
              tooltip={isReadonly ? '只有主导人可以撤销提交' : ''}
            >
              撤销提交
            </TcsButton>
          )}
          <TcsButton type="link" onClick={handleOpenTapd}>
            打开TAPD单
          </TcsButton>
          <TcsButton type="link" onClick={handleCopyTapd}>
            复制TAPD链接
          </TcsButton>
          <TcsButton
            type="link"
            onClick={handleDownload}
            disabled={status !== CTRL_STATUS_TYPE.SUBMIT}
            tooltip={status !== CTRL_STATUS_TYPE.SUBMIT ? '请先提交变更控制表' : ''}
          >
            全量打包下载
          </TcsButton>
        </TcsSpace>
      }
      collapsible
    >
      <TcsSchemaForm
        columns={columns}
        grid={true}
        readonly
        initialValues={formValues}
        key={JSON.stringify(formValues)} // 强制赋值重新渲染表单
      />
      {visible && (
        <EditFormModal
          onCancel={handleCancel}
          subCtrlId={baseInfo?.sub_ctrl_id}
          ctrlId={ctrlId}
          tapdParams={tapdParams}
          workspaceId={workspaceId}
          bugId={bugId}
          onSuccess={handleSuccess}
          isEdit={isEdit}
        />
      )}
      {syncVisible && (
        <SyncModal
          currentTapd={currentTapd}
          tapdList={tapdList}
          baseInfo={baseInfo}
          onCancel={handleSyncCancel}
          onConfirm={handleSyncConfirm}
        />
      )}
    </TcsCard>
  );
};

export default BaseInfo;
