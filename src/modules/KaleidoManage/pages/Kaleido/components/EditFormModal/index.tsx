/*
 * @Author: lucyfang
 * @Date: 2024-09-13 14:43:03
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-10-17 16:14:30
 * @Description: 请输入注释信息
 */
import React, { useRef, useState, useEffect } from 'react';
import {
  TcsModal,
  TcsForm,
  TcsFormText,
  TcsFormSelect,
  TcsFormTextArea,
  TcsSpin,
  TcsButtonGroup,
} from '@tencent/tcs-component';
import Assess from '../Assess';
import { getCtrlBaseInfo, queryMainProject, saveCtrlBaseInfo } from '@/common/api/kaleidoManage';
import { message, Checkbox, Text } from '@tencent/tea-component';
import ChangeObjective from '../ChangeObjective';
import Scope from '../Scope';
import { debounce } from 'lodash';

import { getChangePriority } from '../../../../utils';

interface IProps {
  onSuccess: () => void;
  onCancel: () => void;
  subCtrlId: number;
  ctrlId: string;
  tapdParams: any;
  bugId: string;
  workspaceId: string;
  isEdit: boolean;
}

const EditFormModal: React.FC<IProps> = ({ onSuccess, onCancel, subCtrlId, ctrlId, bugId, workspaceId, isEdit }) => {
  const formRef = useRef<any>();
  const [form] = TcsForm.useForm();
  const [originData, setOriginData] = useState<any>([]);
  const [formData, setFormData] = useState({});
  const [changeType, setChangeType] = useState('');
  const [assess, setAssess] = useState({});
  const [productList, setProductList] = useState<string[]>([]);
  const [objectiveData, setObjectiveData] = useState('');
  const [ctrlVer, setCtrlVer] = useState('');
  const scopeRef = useRef<any>();
  const [scopeData, setScopeData] = useState({
    criticalComponent: '',
    network: '',
    network_delay: 0,
    service: '',
    service_delay: 0,
    request: '',
    change_impact: '',
    change_affect: '',
  });
  const [stakeholderList, setStakeholderList] = useState([]);
  const [loading, setLoaidng] = useState(false);
  const [copyText, setCopyText] = useState('');

  // 因为后端返回的数据是一个数组，需要使用中文名来做映射， 所以维护了一个映射表
  const NAME_MAPPING = {
    变更控制表名称: 'ctrl_name',
    现网TCE版本: 'solution_ver',
    变更类型: 'change_type',
    变更优先级: 'changePriority',
    '涉及的产品/组件名称和版本': 'componentVersion',
    操作内容: 'oper_content',
    变更背景及目的: 'objective',
    本次操作影响范围: 'scope',
    变更干系人: 'rela_peops',
    主产品: 'main_product',
    变更工具: 'tool',
    变更工具版本: 'tool_version',
    跨变更单依赖标识: 'change_depend',
    扩展字段: 'assess',
    运维类标识: 'ops_flag',
    kaleido版本: 'kaleido_version',
  };
  // 在下拉框获取值为数组，保存时需要转换为字符串的字段
  const ARRAY_FIELDS = ['rela_peops', 'componentVersion'];

  // 构建表单数据
  const buildFormData = (data) => {
    const formData = {};
    data.forEach((item) => {
      const key = NAME_MAPPING[item.operation];
      if (key) {
        const value = item.oper_detail;
        if (key === 'scope' || key === 'assess') {
          try {
            // 尝试解析 JSON 格式的数据
            formData[key] = JSON.parse(value);
          } catch (e) {
            // 如果解析失败，保留原始字符串
            formData[key] = value;
          }
        } else if (key === 'change_depend' || key === 'ops_flag') {
          formData[key] = value === '1';
        } else if (ARRAY_FIELDS.includes(key)) {
          // 将逗号分隔的字符串转换为数组，此操作对 rela_peops, componentVersion 字段生效
          formData[key] = value ? value.split(',') : [];
        } else {
          // 对于其他所有字段，直接使用原始数据
          formData[key] = value;
        }
      }
    });
    // 确保所有字段都有一个值，即使数据中没有提供
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    Object.entries(NAME_MAPPING).forEach(([_, key]) => {
      if (!Object.prototype.hasOwnProperty.call(formData, key)) {
        formData[key] = '';
      }
    });
    return formData;
  };

  useEffect(() => {
    const fetchFormData = getCtrlBaseInfo({ sub_ctrl_id: subCtrlId });
    const fetchProductList = ctrlId ? queryMainProject({ ctrlId }) : Promise.resolve({ code: 0, data: [] });
    setLoaidng(true);
    Promise.all([fetchFormData, fetchProductList])
      .then(([formDataResponse, productListResponse]) => {
        setLoaidng(false);
        if (formDataResponse?.data?.list && productListResponse.code === 0) {
          setOriginData(formDataResponse.data.list);
          setCtrlVer(formDataResponse.data?.ctrl_ver);
          const builtFormData = buildFormData(formDataResponse.data.list);
          const type = builtFormData?.change_type || 'bugfix';
          setChangeType(type);
          setFormData(builtFormData);
          const productData = productListResponse.data?.filter((item) => item);
          setProductList(productData);
          const initialValues = {
            ...builtFormData,
            main_product: productData.length === 1 ? productData[0] : builtFormData?.main_product,
          };
          setAssess(initialValues?.assess);
          setObjectiveData(initialValues?.objective);
          setScopeData(initialValues?.scope);
          formRef.current?.setFieldsValue(initialValues);
        }
      })
      .finally(() => {
        setLoaidng(false);
      });
  }, [subCtrlId, ctrlId]);

  useEffect(() => {
    // 模拟加载全体成员列表
    const loadStakeholders = async () => {
      const allMembers = (window as any).allMemberList || []; // 假设这是从全局或API加载的成员列表
      const initialStakeholders = allMembers.slice(0, 5); // 默认加载前5个成员
      setStakeholderList(initialStakeholders.map((member) => ({ id: member.combineName, name: member.combineName })));
    };
    loadStakeholders();
  }, []);

  useEffect(() => {
    const text = formData?.componentVersion?.join(',') || '';
    if (text) {
      setCopyText(text);
    }
  }, [formData]);

  // 变更人展示处理
  const handleSearchStakeholder = (searchText) => {
    const allMembers = (window as any).allMemberList || [];
    const filteredMembers = allMembers.filter((member) => member.combineName.includes(searchText)).slice(0, 5);
    setStakeholderList(filteredMembers.map((member) => ({ id: member.combineName, name: member.combineName })));
  };

  // 防抖动处理
  const debouncedSearchStakeholder = debounce(handleSearchStakeholder, 500);

  // 定义标签映射
  const assessLabels = {
    bugfix: '缺陷评估影响',
    story: '需求',
    security_bug: '安全漏洞评估',
  };
  const handleValuesChange = () => {};
  const handleAssessChange = (newAssess) => {
    setAssess(newAssess);
  };
  const toolTip = (
    <>
      <div>
        <p>产研侧注重于严重程度分类，遵从最小出包原则，其他由其他角色负责</p>
        <p>
          1. 严重程度分类逻辑
          <div>A. 致命:业务数据丢失、核心业务中断+影响两个产品及以上+影响时长超0.5秒</div>
          <div>B. 严重:监控数据丢失、核心业务中断+影响两个产品及以上或影响时长超0.5秒</div>
          <div>C. 一般:其他</div>
        </p>
        <p>
          2. 业务重要性分类逻辑
          <div> A. 核心业务: 比如CVM创建或运行、YUNAPI访问链路等产品基础核心业务</div>
          <div>B. 非核心业务:显示类、操作体验类、需求类等非基础核心业务</div>
        </p>
        <p>
          3. 出包范围逻辑
          <div> A. 有依赖、致命需全客户推送</div>
          <div>B. 指定客户出包遵从最小出包逻辑，1个客户要只出1个客户</div>
        </p>
        <p>
          4. 严重程度与变更单优先级对应关系是自动的，如下
          <div>{' A. 致命、有依赖 -> 非常紧急'}</div>
          <div> {`B. 严重 -> 紧急`} </div>
          <div>{`C. 一般 -> 标准`}</div>
          <div>{'D. 1线发起紧急审批出包 -> SVIP紧急'}</div>
        </p>
      </div>
    </>
  );
  const handleObjectiveChange = (data) => {
    setObjectiveData(data);
  };
  const handleScopeChange = (field, value) => {
    setScopeData((prev) => ({ ...prev, [field]: value }));
  };
  // 获取表单数据并格式化数据
  const getFormData = () => {
    const promise = new Promise((resolve, reject) => {
      formRef.current
        ?.validateFields()
        ?.then((fieldsValue) => {
          const isScopeValid = scopeRef.current?.validateFields();
          if (!isScopeValid) {
            return reject('Scope validation failed');
          }
          const processedFieldsValue = { ...fieldsValue };

          // 处理变更控制表名称
          processedFieldsValue.ctrl_name = processedFieldsValue.ctrl_name.trim();

          const data = [...originData]; // 从 state 中获取原始数据并复制
          const result: any[] = [];

          const findAssess = data?.find((item) => item?.operation === '扩展字段');
          if (!findAssess) {
            data.push({
              seq: data.length,
              oper_detail: '',
              operation: '扩展字段',
              required: 1,
              oper_id: Date.now(),
              sub_ctrl_id: data[0]?.sub_ctrl_id,
              ctrl_ver: data[0]?.ctrl_ver,
            });
          }
          if (!data.find((item) => item.operation === '跨变更单依赖标识')) {
            data.push({
              seq: data.length,
              oper_detail: '0', // 0 不依赖， 1 依赖
              operation: '跨变更单依赖标识',
              required: 1,
              oper_id: Date.now(),
              sub_ctrl_id: data[0].sub_ctrl_id,
              ctrl_ver: data[0].ctrl_ver,
            });
          }
          if (!data.find((item) => item.operation === '运维类标识')) {
            data.push({
              seq: data.length,
              oper_detail: '0', // 0 不依赖， 1 依赖
              operation: '运维类标识',
              required: 1,
              oper_id: Date.now(),
              sub_ctrl_id: data[0].sub_ctrl_id,
              ctrl_ver: data[0].ctrl_ver,
            });
          }

          data.forEach((item: any, index: number) => {
            const field = NAME_MAPPING[item.operation];
            if (field) {
              let value = processedFieldsValue[field];

              if (ARRAY_FIELDS.includes(field) && Array.isArray(value)) {
                value = (value || []).join(',');
              }
              if (item.operation === '扩展字段') {
                value = JSON.stringify(value);
              } else if (item.operation === '跨变更单依赖标识' || item.operation === '运维类标识') {
                value = value ? '1' : '0';
              } else if (item.operation === '变更优先级') {
                const { assess, change_type } = processedFieldsValue;
                value = getChangePriority(assess, change_type);
              }

              result.push({
                ...item,
                // 添加序号
                seq: index + 1,
                oper_detail: field === 'scope' ? JSON.stringify(scopeData) : value,
              });
            } else {
              result.push({
                ...item,
              });
            }
          });

          resolve({
            data: result,
            ctrl_name: processedFieldsValue.ctrl_name,
            rela_peops: (processedFieldsValue.rela_peops || []).join(','),
          });
        })
        .catch(reject);
    });
    return promise;
  };
  const handleSave = () => {
    getFormData().then(({ data }) => {
      saveCtrlBaseInfo({
        sub_ctrl_id: subCtrlId,
        modify_user: (window as any).jiguang_username,
        ctrl_ver: ctrlVer,
        list: data,
        bugId: bugId,
        workspaceId: workspaceId,
      }).then(({ code, message: msg }: { code: number; message: string }) => {
        if (code === 0) {
          onSuccess();
        } else {
          message.error({ content: msg });
        }
      });
    });
  };
  const Footer = (
    <>
      <TcsButtonGroup
        type="button"
        items={[
          {
            text: '确认并返回',
            confirm: true,
            confirmProps: {
              title:
                '请确认变更背景及变更目的描述清晰，且没有包含具体客户的信息（如名称、局点等），否则可能会导致变更失败或驳回',
              onConfirm: () => handleSave(),
            },
            type: 'primary',
            hidden: isEdit,
          },
          {
            text: '返回',
            onClick: () => onCancel(),
          },
        ]}
      />
    </>
  );

  return (
    <TcsModal title="基本信息" visible fullScreen onCancel={onCancel} footer={Footer}>
      <TcsSpin spinning={loading}>
        <TcsForm formRef={formRef} form={form} onValuesChange={handleValuesChange}>
          <TcsFormText
            label="变更背景"
            name="ctrl_name"
            extra={
              <div>
                <p>参考模板：因XXX问题，引起业务不能正常访问（修复多个问题、应概要说明哪几个问题）</p>
                <p>例:运营端用户管理页面，关联用户或用户组时，搜索用户报错</p>
              </div>
            }
            fieldProps={{
              size: 'full',
            }}
            rules={[
              {
                required: true,
                message: '请输入变更背景(一定要写清楚出现了什么问题！！！)',
              },
              {
                validator: (_: any, value: string) => {
                  if (value.indexOf('/') !== -1) {
                    return Promise.reject('变更背景不能有"/"');
                  }
                  if (value.length < 2 || value.length > 50) {
                    return Promise.reject('变更背景长度在2~50之间');
                  }
                  if (/\s/.test(value)) {
                    return Promise.reject('变更背景不能有空格，制表符等');
                  }
                  return Promise.resolve();
                },
              },
            ]}
            disabled={isEdit}
          />

          <TcsFormSelect
            name="change_type"
            label="变更类型"
            fieldProps={{
              onChange: (value) => {
                setChangeType(value);
              },
              options: [
                { label: '软件bugfix修复', value: 'bugfix' },
                { label: '客户需求', value: 'story' },
                { label: '安全漏洞修复', value: 'security_bug' },
              ],
              placeholder: '请选择变更类型',
              size: 'full',
            }}
            rules={[
              {
                required: true,
                message: '变更类型必填',
              },
            ]}
            disabled={isEdit}
          />

          <TcsForm.Item
            label={`${assessLabels[changeType]}`}
            name="assess"
            tooltip={assessLabels[changeType] === '缺陷评估影响' ? toolTip : ''}
          >
            <Assess isEdit={isEdit} changeType={changeType} assess={assess} onChange={handleAssessChange} />
          </TcsForm.Item>

          <TcsForm.Item
            disabled={isEdit}
            label="跨变更单依赖标识"
            name="change_depend"
            tooltip="强依赖：该tapd内组件是否必须一起升级，否则会出现api调用不兼容"
          >
            <Checkbox value={formData?.change_depend}>该单是否会被强依赖</Checkbox>
          </TcsForm.Item>

          <TcsForm.Item
            disabled={isEdit}
            label="运维类标识"
            name="ops_flag"
            tooltip={
              <>
                <p>运维类样例： </p>
                <p>部署、监控、日志、自动恢复等功能优化属于运维类</p>
              </>
            }
          >
            <Checkbox value={formData?.ops_flag}>该单是否为运维类</Checkbox>
          </TcsForm.Item>

          <TcsFormSelect
            disabled={isEdit}
            name="main_product"
            label="主产品"
            fieldProps={{
              options: productList?.map((item) => {
                return { label: item, value: item };
              }),
              placeholder: '请选择主产品',
              size: 'full',
            }}
            rules={[
              {
                required: true,
                message: '请选择主产品',
              },
            ]}
            tooltip="变更控制表涉及多组件的变更时，安灯系统需要录入此次变更的主产品以及每个产品所属的组件。"
          />

          <TcsFormText
            label="现网TCE版本"
            name="solution_ver"
            fieldProps={{
              size: 'full',
            }}
            disabled
          />
          <TcsFormSelect
            name="componentVersion"
            label="涉及的产品/组件名称和版本"
            placeholder="涉及的产品/组件名称和版本"
            disabled
            fieldProps={{
              size: 'full',
              mode: 'tags',
            }}
            rules={[
              {
                required: true,
                message: '没有检测到合流组件，请先进行组件合流',
              },
            ]}
            suffix={<Text copyable={{ text: copyText }} />}
          />

          <TcsForm.Item
            label="变更目的"
            name="objective"
            rules={[
              {
                validator(_, value) {
                  if (value) {
                    const split = value.split('\n');
                    if (split.length === 3) {
                      if (split[0].startsWith('问题：') && !split[0].split('问题：')[1]) {
                        return '请输入问题';
                      }
                      if (split[1].startsWith('原因：') && !split[1].split('原因：')[1]) {
                        return '请输入原因';
                      }
                      if (split[2].startsWith('修复方案：') && !split[2].split('修复方案：')[1]) {
                        return '请输入修复方案';
                      }
                    }
                  }
                },
              },
            ]}
          >
            <ChangeObjective isEdit={isEdit} data={objectiveData} onChange={handleObjectiveChange} />
          </TcsForm.Item>

          <TcsFormTextArea
            disabled={isEdit}
            label="操作内容"
            name="oper_content"
            placeholder="概述变更组件及内容"
            fieldProps={{
              size: 'full',
            }}
          />
          <TcsForm.Item label="本次操作影响范围" name="scope">
            <Scope
              isEdit={isEdit}
              ref={scopeRef}
              data={scopeData}
              onChange={handleScopeChange}
              productData={productList}
            />
          </TcsForm.Item>

          <TcsFormSelect
            disabled={isEdit}
            name="rela_peops"
            label="变更干系人"
            fieldProps={{
              mode: 'tags',
              placeholder: '请选择涉及组件修复的处理人',
              size: 'full',
              onSearch: debouncedSearchStakeholder,
              options: stakeholderList.map((item) => ({
                label: item?.name,
                value: item?.id,
              })),
              showSearch: true,
              filterOption: false, // 禁用本地过滤，使用onSearch处理
            }}
          />
          <TcsFormText
            label="变更工具"
            name="tool"
            disabled
            initialValue="kaleido"
            fieldProps={{
              size: 'full',
            }}
          />
          <TcsFormText
            label="变更工具版本"
            name="tool_version"
            disabled
            initialValue="4.1.0"
            fieldProps={{
              size: 'full',
            }}
          />
        </TcsForm>
      </TcsSpin>
    </TcsModal>
  );
};
export default EditFormModal;
