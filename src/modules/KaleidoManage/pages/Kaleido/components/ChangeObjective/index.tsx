/*
 * @Author: lucyfang
 * @Date: 2024-09-19 16:24:48
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-10-14 15:29:43
 * @Description: 请输入注释信息
 */
import React, { useEffect, useState } from 'react';
import { Input, Form } from '@tencent/tea-component';
import styles from './index.module.less';

interface IProps {
  data: string;
  onChange: (data: string) => void;
  isEdit: boolean;
}
const ChangeObjective: React.FC<IProps> = ({ data, onChange, isEdit }) => {
  const [formData, setFormData] = useState({
    problem: '',
    cause: '',
    solution: '',
  });

  useEffect(() => {
    // 解析传入的字符串数据
    const parsedData: Record<string, string> = data.split('\n').reduce((acc, line) => {
      const [key, value] = line.split('：');
      if (key && value) {
        acc[key.trim()] = value.trim();
      }
      return acc;
    }, {});

    setFormData({
      problem: parsedData['问题'] || '',
      cause: parsedData['原因'] || '',
      solution: parsedData['修复方案'] || '',
    });
  }, [data]);

  const handleChange = (field: string, value: string) => {
    const updatedFormData = { ...formData, [field]: value };
    setFormData(updatedFormData);

    // 将更新后的数据序列化为字符串格式
    const serializedData = `问题：${updatedFormData.problem}\n原因：${updatedFormData.cause}\n修复方案：${updatedFormData.solution}`;
    onChange(serializedData);
  };

  return (
    <>
      <Form className={styles.form_color}>
        <Form.Item label="问题" message="问题：xxx（客户现场的问题现象） 【示例：tke webconsole pod 内存泄露】">
          <Input
            disabled={isEdit}
            value={formData.problem}
            size="full"
            placeholder="客户现场的问题现象"
            onChange={(val) => handleChange('problem', val)}
          />
        </Form.Item>
        <Form.Item
          label="原因"
          message="原因：xxx（引起问题的原因）【示例：进程会打开日志文件，随着日志信息增加，导致占用了大量内存】"
        >
          <Input
            disabled={isEdit}
            size="full"
            placeholder="引起问题的原因"
            value={formData.cause}
            onChange={(val) => handleChange('cause', val)}
          />
        </Form.Item>
        <Form.Item
          label="修复方案"
          message="修复方案：xxx（修改该问题的解决方案）【移除了打开日志文件的逻辑，释放内存】"
        >
          <Input
            disabled={isEdit}
            size="full"
            placeholder="修改该问题的解决方案"
            value={formData.solution}
            onChange={(val) => handleChange('solution', val)}
          />
        </Form.Item>
      </Form>
    </>
  );
};
export default ChangeObjective;
