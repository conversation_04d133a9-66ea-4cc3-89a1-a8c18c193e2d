/*
 * @Author: superfeng
 * @Date: 2021-12-03 17:25:17
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-10-14 17:08:48
 * @Description: 基本信息同步到其他版本
 */

import { TcsModal } from '@tencent/tcs-component';
import React, { useMemo, useState } from 'react';
import { TapdListData, ICompCtrl } from '../../type';
import { Checkbox, message, Status } from '@tencent/tea-component';
import { SyncSubCtrl } from '@/common/api/kaleidoManage';

import { CTRL_STATUS_TYPE, NEW_CUSTOMER_EXPORT } from '../../../../constant';
export interface SubCtrlList {
  comp_id: string;
}
interface SyncList {
  workspace_id: string;
  bug_id: string;
  sub_ctrl_list: SubCtrlList[];
}

export interface IProps {
  onCancel: () => void;
  onConfirm: () => void;
  tapdList: TapdListData[];
  currentTapd: TapdListData;
  ctrlList: ICompCtrl[];
}

const CtrlSyncModal = ({ onCancel, onConfirm, currentTapd, tapdList, ctrlList }) => {
  const [loading, setLoading] = useState(false);
  const [checkedComps, setCheckedComps] = useState<Record<string, string[]>>({});
  const currentComps = useMemo(() => {
    return Object.keys(currentTapd.comp_list);
  }, [currentTapd]);
  const allVerComps = useMemo(() => {
    return tapdList
      .map((item) => {
        const comps = Object.keys(item.comp_list).filter((key) => currentComps.includes(key));
        return {
          key: item.key,
          comps,
          base_status: item.base_status,
          package_type: item.package_type,
          ctrl_status: item.ctrl_status,
        };
      })
      .filter((item) => !!item.comps.length && item.key !== currentTapd.key);
  }, [currentComps, tapdList]);

  function handleChange(values, key) {
    setCheckedComps({
      ...checkedComps,
      [key]: values,
    });
  }

  function handleConfirm() {
    const compSubCtrlIds: Record<string, number> = ctrlList.reduce((res, item) => {
      res[item.comp_id.toLowerCase()] = item.sub_ctrl_id;
      return res;
    }, {});
    const syncList: SyncList[] = [];
    tapdList.forEach((item) => {
      const comps = checkedComps[item.key];
      if (comps?.length) {
        syncList.push({
          workspace_id: item.workspace_id,
          bug_id: item.bug_id,
          sub_ctrl_list: comps.map((comp) => {
            const subCtrlId = compSubCtrlIds[comp.toLowerCase()];
            return {
              sub_ctrl_id: subCtrlId,
              comp_id: comp,
            };
          }),
        });
      }
    });
    if (syncList.length === 0) {
      return message.warning({ content: '请选择要同步的版本' });
    }
    if (tapdList.length > 0) {
      setLoading(true);
      SyncSubCtrl({
        workspace_id: currentTapd.workspace_id,
        bug_id: currentTapd.bug_id,
        sync_list: syncList,
      })
        .then((data) => {
          if (data.code === 0) {
            onConfirm(Object.keys(checkedComps).filter((key) => checkedComps[key]?.length));
            message.success({ content: '同步成功' });
          } else {
            message.error({ content: data.message });
          }
        })
        .catch(() => {})
        .finally(() => {
          setLoading(false);
        });
    } else {
      message.warning({ content: '请选择要同步的变更控制表子项' });
    }
  }

  return (
    <TcsModal
      visible
      title="变更控制表同步"
      width={800}
      onCancel={onCancel}
      onOk={handleConfirm}
      confirmLoading={loading}
    >
      {allVerComps.length === 0 ? (
        <Status icon="blank" size="l" description="暂无可同步的变更控制表" />
      ) : (
        <>
          {allVerComps.map((item) => {
            const disabled =
              item.base_status === CTRL_STATUS_TYPE.TO_BE_ADVISED ||
              item.package_type === NEW_CUSTOMER_EXPORT ||
              item.ctrl_status === CTRL_STATUS_TYPE.SUBMIT;
            let tooltipTitle = '';

            if (item.package_type === NEW_CUSTOMER_EXPORT) {
              tooltipTitle = '出包类型为仅后续新增客户出包，无需同步变更控制表';
            } else if (item.ctrl_status === CTRL_STATUS_TYPE.SUBMIT) {
              tooltipTitle = '已提交的变更控制表无需同步';
            } else {
              tooltipTitle = `请先同步或完善${item.key}的基本信息后再同步变更控制表子项`;
            }
            return (
              <div key={item.key}>
                <span>{`${item.key}：`}</span>
                <Checkbox.Group
                  style={{ marginLeft: 5 }}
                  value={checkedComps[item.key]}
                  onChange={(checkedValue) => handleChange(checkedValue, item.key)}
                  disabled={disabled}
                >
                  {item.comps.map((comp) => (
                    <Checkbox name={comp} tooltip={tooltipTitle} key={comp}>
                      {comp}
                    </Checkbox>
                  ))}
                </Checkbox.Group>
              </div>
            );
          })}
        </>
      )}
    </TcsModal>
  );
};

export default CtrlSyncModal;
