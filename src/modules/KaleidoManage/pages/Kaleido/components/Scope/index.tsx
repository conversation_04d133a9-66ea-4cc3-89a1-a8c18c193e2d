/*
 * @Author: lucyfang
 * @Date: 2024-09-19 16:24:48
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-10-17 15:53:36
 * @Description: 请输入注释信息
 */
import React, { useImperativeHandle, forwardRef, useState, useEffect } from 'react';
import { Form, Radio, TextArea } from '@tencent/tea-component';
import styles from './index.module.less';
import { CRITICAL_COMPONENT_OPTIONS, REQUEST_AFFECT, CHANGE_AFFECT_PRODUCT } from '../../../../constant';
import DelayInput from '../DelayInput';

interface IProps {
  data: {
    criticalComponent: string;
    network: string;
    network_delay: number;
    service: string;
    service_delay: number;
    request: string;
    change_impact: string;
    change_affect: string;
  };
  onChange: (field: string, value: any) => void;
  productData: string[];
  isEdit: boolean;
}
const Scope = forwardRef(({ productData, onChange, data, isEdit }: IProps, ref) => {
  const [validationErrors, setValidationErrors] = useState({});

  useImperativeHandle(ref, () => ({
    validateFields: () => validate(),
  }));

  const validate = () => {
    let errors = {};
    if (!data?.criticalComponent) {
      errors = { ...errors, criticalComponent: '请选择是否为关键组件' };
    }
    if (data.network === '有') {
      if (!data.network_delay || data.network_delay <= 0) {
        errors = { ...errors, network: '中断时间必须大于0' };
      }
    }
    if (data.service === '有') {
      if (!data.service_delay || data.service_delay <= 0) {
        errors = { ...errors, service: '中断时间必须大于0' };
      }
    }
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  useEffect(() => {
    validate();
  }, [data]);

  const handleNetworkChange = (value) => {
    onChange('network', value);
    if (value !== '有') {
      onChange('network_delay', undefined);
    }
  };

  const handleServiceChange = (value) => {
    onChange('service', value);
    if (value !== '有') {
      onChange('service_delay', undefined);
    }
  };
  return (
    <>
      <Form className={styles.form_color}>
        <Form.Item
          label="1.是否为关键组件"
          tips="是否对业务数据有影响"
          message={validationErrors?.criticalComponent}
          status={validationErrors?.criticalComponent ? 'error' : 'success'}
          showStatusIcon={false}
        >
          <Radio.Group
            disabled={isEdit}
            onChange={(val) => onChange('criticalComponent', val)}
            value={data?.criticalComponent}
          >
            {CRITICAL_COMPONENT_OPTIONS.map((item: any) => (
              <Radio name={item.id} key={item.id}>
                {item.name}
              </Radio>
            ))}
          </Radio.Group>
        </Form.Item>
        <Form.Item
          label="2.网络是否中断"
          message={validationErrors?.network}
          status={validationErrors?.network ? 'error' : 'success'}
          showStatusIcon={false}
        >
          <Radio.Group disabled={isEdit} onChange={handleNetworkChange} value={data?.network || '无'}>
            <Radio name="有">
              <DelayInput
                isEdit={isEdit}
                defaultValue={data.network_delay}
                onChange={(val) => onChange('network_delay', val)}
              />
            </Radio>
            <Radio name="有，多次抖动(高延时或丢包)">有，多次抖动(高延时或丢包)</Radio>
            <Radio name="无">无</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item
          label="3.服务是否中断"
          message={validationErrors?.service}
          status={validationErrors?.service ? 'error' : 'success'}
          showStatusIcon={false}
        >
          <Radio.Group disabled={isEdit} onChange={handleServiceChange} value={data?.service || '无'}>
            <Radio name="有">
              <DelayInput
                isEdit={isEdit}
                defaultValue={data.service_delay}
                onChange={(val) => onChange('service_delay', val)}
              />
            </Radio>
            <Radio name="有，闪断">有，闪断</Radio>
            <Radio name="无">无</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item label="4.业务请求影响">
          <Radio.Group disabled={isEdit} onChange={(val) => onChange('request', val)} value={data?.request || '无影响'}>
            {REQUEST_AFFECT.map((item: any) => (
              <Radio name={item.id} key={item.id}>
                {item.name}
              </Radio>
            ))}
          </Radio.Group>
        </Form.Item>
        <Form.Item label="5.变更影响范围">
          <Radio.Group
            disabled={isEdit || productData.length > 1}
            onChange={(val) => onChange('change_affect', val)}
            value={data?.change_affect}
            defaultValue={productData.length > 1 ? '多产品' : data.change_affect}
          >
            {CHANGE_AFFECT_PRODUCT.map((item: any) => (
              <Radio name={item.id} key={item.id}>
                {item.name}
              </Radio>
            ))}
          </Radio.Group>
        </Form.Item>
        <Form.Item label="6.变更影响">
          <TextArea
            disabled={isEdit}
            size="full"
            onChange={(val) => onChange('change_impact', val)}
            value={data?.change_impact || ''}
          />
        </Form.Item>
      </Form>
    </>
  );
});
export default Scope;
