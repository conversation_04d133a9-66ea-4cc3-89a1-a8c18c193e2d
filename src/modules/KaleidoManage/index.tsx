import { DefectManagementRoutePath } from '@/common/routePath';
import React from 'react';
import { Route } from 'react-router-dom';
import Kaleido from './pages/Kaleido';

const Index = () => {
  const routesConfig = [
    {
      path: DefectManagementRoutePath.KALEIDO_MANAGE_PAGE,
      component: Kaleido,
      exact: true,
    },
  ];

  return (
    <>
      {routesConfig.map((item, index) => (
        <Route key={index} component={item.component} path={item.path} exact={item.exact} />
      ))}
    </>
  );
};
export default Index;
