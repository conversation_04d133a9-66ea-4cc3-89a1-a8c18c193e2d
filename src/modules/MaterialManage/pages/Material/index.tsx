import React from 'react';
import { Route } from 'react-router-dom';
import Material from './Material';
import { MaterialManageRoutePath } from '@/common/routePath';

const Index = () => {
  const routesConfig = [
    {
      component: Material,
      path: MaterialManageRoutePath.MATERIAL_INDEX_PAGE,
      exact: true,
    },
  ];

  return (
    <>
      {routesConfig.map((item, index) => (
        <Route key={index} component={item.component} path={item.path} exact={item.exact} />
      ))}
    </>
  );
};

export default Index;
