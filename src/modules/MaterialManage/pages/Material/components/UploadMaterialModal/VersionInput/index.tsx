import { Col, InputNumber, Row } from '@tencent/tea-component';
import React, { useMemo } from 'react';

export interface IProps {
  onChange?: (value: string) => void;
  value?: string;
}

const VersionInput: React.FC<IProps> = ({ onChange, value }) => {
  const values = useMemo(() => {
    if (value) {
      const split = value.split('.');

      return [
        parseInt(split[0] || '1', 10),
        parseInt(split[1] || '0', 10),
        parseInt(split[2] || '0', 10),
        parseInt(split[3] || '0', 10),
      ];
    }
    return [1, 0, 0, 0];
  }, [value]);

  function handleChange(val, index) {
    const split = value!.split('.');
    split[index] = val;
    onChange?.(split.join('.'));
  }

  return (
    <div style={{ width: 380 }}>
      <Row>
        <Col span={6}>
          <InputNumber hideButton min={0} size="l" value={values[0]} onChange={(value) => handleChange(value, 0)} />
        </Col>
        <Col span={6}>
          <InputNumber hideButton min={0} size="l" value={values[1]} onChange={(value) => handleChange(value, 1)} />
        </Col>
        <Col span={6}>
          <InputNumber hideButton min={0} size="l" value={values[2]} onChange={(value) => handleChange(value, 2)} />
        </Col>
        <Col span={6}>
          <InputNumber hideButton min={0} size="l" value={values[3]} onChange={(value) => handleChange(value, 3)} />
        </Col>
      </Row>
    </div>
  );
};

export default VersionInput;
