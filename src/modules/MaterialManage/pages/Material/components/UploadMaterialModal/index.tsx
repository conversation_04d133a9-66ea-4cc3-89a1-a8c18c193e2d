import { listMaterialLabels } from '@/common/api/materialManage';
import { ProForm, ProModal } from '@/common/components';
import useLookup from '@/common/hookups/useLookup';
import { Bubble, Icon, Input, Select, SelectMultiple, TagSelect, message, Modal, Switch } from '@tencent/tea-component';
import React, { useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import VersionInput from './VersionInput';
import { requestAuroraApi } from '@/common/api/api';
import UploadField from './UploadField';
import { TcsButton } from '@tencent/tcs-component';

export interface IProps {
  onConfirm: () => void;
}

export interface IUploadMaterialModalRef {
  show: (type: 'product' | 'solution', treeData: any, materialType: 'install' | 'fix') => void;
}

const UploadMaterialModal: React.ForwardRefRenderFunction<IUploadMaterialModalRef, IProps> = ({ onConfirm }, ref) => {
  const [visible, setVisible] = useState(false);
  const [type, setType] = useState<'product' | 'solution'>();
  const [treeData, setTreeData] = useState<any>();
  const { lookups } = useLookup(['PackageArch']);

  const [labelList, setLabelList] = useState<any[]>([]);
  const [uploadType, setUploadType] = useState(true);
  const [loading, setLoading] = useState(false);
  const formRef = useRef<any>(null);
  const [materialType, setMaterialType] = useState<string>();

  useEffect(() => {
    listMaterialLabels().then((res) => {
      if (res.Error) {
        message.error({
          content: res.Error.Message,
        });
      } else {
        setLabelList(res.ListMaterialLabels || []);
      }
    });
  }, []);
  useImperativeHandle(
    ref,
    () => ({
      show(type, treeData, materialType) {
        setVisible(true);
        setTreeData(treeData);
        setType(type);
        setMaterialType(materialType);
      },
    }),
    [],
  );

  function handleCancel() {
    setVisible(false);
  }

  function handleConfirm() {
    return formRef.current.validateFields().then((values) => {
      const { ProductVersionUUIDList, SolutionVersionUUIDList, uploadType, ...otherValues } = values;

      if (!values.Version) {
        return message.warning({
          content: '请输入版本号',
        });
      }
      setLoading(true);
      return requestAuroraApi<any>(
        // 接口的Action
        'CreatePackage',
        // 接口的参数
        {
          MaterialData: {
            ...otherValues,
            // ...$context.inputParams.selectedProductInfo,
            Type: materialType,
            Url: values.Url?.url,
            ProductUUID: treeData.parentData?.ProductUUID,
            SolutionUUID: treeData.parentData?.SolutionUUID,
            ProductVersionUUID: treeData?.ProductVersionUUID,
            SolutionVersionUUID: treeData?.SolutionVersionUUID,
            Source: 'upload',
            // DryRun 默认值为 false, true 时表示预上传，返回上传命令。
            DryRun: !uploadType,
          },
          ProductVersionUUIDList,
          SolutionVersionUUIDList,
        },
      )
        .then((res) => {
          // Aurora报错信息会放到Error里面
          if (res.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            if (!uploadType) {
              // 预上传，返回上传命令
              Modal.alert({
                message: '后台物料包上传步骤',
                size: 'l',
                onClose() {
                  handleCancel();
                },
                description: (
                  <div>
                    <p>在devcloud机器上执行如下命令集:</p>
                    <p>1.下载物料包上传工具（cli)，下载地址：{res.InstallCommand}</p>
                    <p>
                      2.执行物料包上传命令, 将 {'<file-path>'} 换成上传文件的本地路径：{res.UploadCommand}{' '}
                    </p>
                    <p>3.通过极光标准物料管理页面验证物料包已经上传</p>
                  </div>
                ),
                buttons: [
                  <TcsButton
                    type="primary"
                    key="1"
                    onClick={() => {
                      onConfirm?.();
                      handleCancel();
                    }}
                  >
                    知道了
                  </TcsButton>,
                ],
              });
            } else {
              message.success({
                content: '上传物料成功',
              });
              onConfirm?.();
              handleCancel();
            }
          }
        })
        .finally(() => {
          setLoading(false);
        });
    });
  }

  const initialValues = useMemo(() => {
    const values: Record<string, any> = { uploadType: true, IsDownloadPackage: false, Version: '1.0.0.0' };
    if (treeData && type) {
      if (type === 'solution') {
        values.SolutionVersionUUIDList = [treeData.SolutionVersionUUID];
      } else {
        values.ProductVersionUUIDList = [treeData.ProductVersionUUID];
      }
    }
    return values;
  }, [type, treeData]);

  function handleUploadLoading(isLoading) {
    setLoading(isLoading);
  }

  return (
    <ProModal
      title="上传物料"
      size="xl"
      visible={visible}
      onCancel={handleCancel}
      onOk={handleConfirm}
      confirmLoading={loading}
    >
      <ProForm grid initialValues={initialValues} layout="fixed" fixedLabelWidth={100} formRef={formRef}>
        <ProForm.Item
          colProps={{ span: 18 }}
          label="包名称"
          dataIndex="MaterialName"
          rules={{
            required: true,
          }}
        >
          <Input size="full" />
        </ProForm.Item>

        <ProForm.Item
          colProps={{ span: 6 }}
          label={
            <>
              极光物料
              <Bubble
                arrowPointAtCenter
                placement="top-end"
                content="上传通过极光运维中心流程生成的物料包时，需要勾选。"
              >
                <Icon type="info" />
              </Bubble>
            </>
          }
          dataIndex="IsDownloadPackage"
        >
          <Switch />
        </ProForm.Item>
        <ProForm.Item colProps={{ span: 18 }} label="版本" dataIndex="Version">
          <VersionInput />
        </ProForm.Item>
        <ProForm.Item
          label={
            <>
              页面内上传
              <Bubble
                arrowPointAtCenter
                placement="top-end"
                content="如果文件小于5G，可在此页面进行上传，否则需要使用命令进行上传"
              >
                <Icon type="info" />
              </Bubble>
            </>
          }
          dataIndex="uploadType"
          colProps={{
            span: 6,
          }}
        >
          <Switch value={uploadType} onChange={(checked) => setUploadType(checked)} />
        </ProForm.Item>
        {uploadType && (
          <ProForm.Item
            label="上传文件"
            dataIndex="Url"
            colProps={{
              span: 12,
            }}
            rules={{
              required: {
                value: true,
                message: '请选择待上传的文件',
              },
            }}
          >
            <UploadField onLoading={handleUploadLoading} />
          </ProForm.Item>
        )}
        {type === 'product' && (
          <ProForm.Item
            label="关联产品版本"
            dataIndex="ProductVersionUUIDList"
            colProps={{ span: 24 }}
            rules={{
              required: {
                value: true,
                message: '请选择关联产品版本',
              },
            }}
          >
            <SelectMultiple
              matchButtonWidth
              appearance="button"
              size="full"
              options={
                treeData?.parentData?.ProductVersionList?.map((item) => ({
                  text: item.ProductVersionName,
                  value: item.ProductVersionUUID,
                })) || []
              }
            />
          </ProForm.Item>
        )}
        {type === 'solution' && (
          <ProForm.Item
            label="关联解决方案版本"
            dataIndex="SolutionVersionUUIDList"
            colProps={{ span: 24 }}
            rules={{
              required: {
                value: true,
                message: '请选择关联解决方案版本',
              },
            }}
          >
            <SelectMultiple
              matchButtonWidth
              appearance="button"
              size="full"
              options={
                treeData?.parentData?.SolutionVersionList?.map((item) => ({
                  text: item.Code,
                  value: item.SolutionVersionUUID,
                })) || []
              }
            />
          </ProForm.Item>
        )}
        <ProForm.Item
          label="适用架构"
          dataIndex="Arch"
          colProps={{ span: 24 }}
          rules={{
            required: {
              value: true,
              message: '请选择适用架构',
            },
          }}
        >
          <Select
            matchButtonWidth
            appearance="button"
            size="full"
            options={lookups?.PackageArch?.map((item) => ({
              text: item.Name,
              value: item.Code,
            }))}
          />
        </ProForm.Item>
        <ProForm.Item
          label="标签"
          dataIndex="labelNameList"
          colProps={{ span: 24 }}
          rules={{
            required: {
              value: true,
              message: '请选择标签',
            },
          }}
        >
          <TagSelect
            style={{ width: '100%' }}
            options={
              labelList?.map((item) => ({
                text: item.Name,
                value: item.Name,
              })) || []
            }
          />
        </ProForm.Item>
        <ProForm.Item label="发布说明" dataIndex="ReleaseNotes" colProps={{ span: 24 }}>
          <Input.TextArea style={{ width: '100%' }} />
        </ProForm.Item>
      </ProForm>
    </ProModal>
  );
};

export default React.forwardRef(UploadMaterialModal);
