import { jgNextCapiRequest, requestNormalApi } from '@/common/api/api';
import { TcsButton } from '@tencent/tcs-component';
import { Text, Upload, message } from '@tencent/tea-component';
import React, { useState } from 'react';

export interface IProps {
  value?: any;
  onChange?: (value: any) => void;
  onLoading?: (isLoading: boolean) => void;
}

const UploadField: React.FC<IProps> = ({ value, onChange, onLoading }) => {
  const [loading, setLoading] = useState(false);

  function handleBeforeUpload(file) {
    const filename = file.name;
    const size = file.size / 1024 / 1024 / 1024;
    const xhr = new XMLHttpRequest();
    if (size > 5) {
      message.error({
        content: '上传文件不能大于5G',
      });

      return Promise.reject(new Error('上传文件不能大于5G'));
    }
    const prefixUrl = `${window.location.host}/cgw/`;
    let promise: Promise<any> | undefined = undefined;
    if ((window as any).JIGUANG_is_tea_control_env) {
      promise = jgNextCapiRequest(
        { ServiceType: 'BackendService', Action: 'PackageAPI' },
        {
          Action: 'CreateCSPUploadURL',
          FileName: filename,
          FileVersion: '00',
          PkgName: '0',
        },
      );
    } else {
      promise = requestNormalApi({
        url: 'packages-api/',
        method: 'POST',
        prefixUrl,
        // 请求参数
        data: {
          Action: 'CreateCSPUploadURL',
          FileName: filename,
          FileVersion: '00',
          PkgName: '0',
        },
      });
    }
    onChange?.({
      name: filename,
      percent: 0,
      status: 'warning',
    });
    setLoading(true);
    onLoading?.(true);
    promise!
      .then((result) => {
        if (result.Response.Url) {
          const url = new URL(result.Response.Url);
          xhr.open('PUT', `${window.location.protocol}//${url.hostname}${url.pathname}${url.search}`, true);
          xhr.onreadystatechange = () => {
            if (xhr.readyState === 4) {
              const res = {
                uid: file.uid,
                url: `/${result.Response.Key}`,
                name: filename,
                status: 'success',
                search: url.search,
              };
              onChange?.(res);
              setLoading(false);
              onLoading?.(false);
            }
          };

          xhr.onerror = function () {
            message.error({
              content: `${file.name}上传失败`,
            });
            onChange?.(undefined);
            setLoading(false);
            onLoading?.(false);
          };
          xhr.send(file);
        }
      })
      .catch(() => {
        message.error({
          content: `${file.name}上传失败`,
        });
        onChange?.(undefined);
        setLoading(false);
        onLoading?.(false);
      });

    return Promise.reject();
  }

  return (
    <>
      <Upload beforeUpload={handleBeforeUpload}>
        <TcsButton loading={loading}>点击上传</TcsButton>
      </Upload>
      {!!value && <hr />}
      {!!value && (
        <Text theme={value.status} style={{ fontSize: 12 }}>
          {value.name}
        </Text>
      )}
    </>
  );
};

export default UploadField;
