import {
  listMaterialLabels,
  listMaterialProductVersionRelDetails,
  listMaterialSolutionVersionRelDetails,
  updateMaterialPackageInfo,
} from '@/common/api/materialManage';
import { ProForm, ProModal } from '@/common/components';
import { Form, SelectMultiple, TagSelect, Input, message } from '@tencent/tea-component';

import React, { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';

export interface IProps {
  onConfirm: () => void;
}

export interface IEditorMaterialModalRef {
  show: (params: { type: 'product' | 'solution'; treeData: any; materialType: 'install' | 'fix'; record: any }) => void;
}

const EditMaterialModal: React.ForwardRefRenderFunction<IEditorMaterialModalRef, IProps> = ({ onConfirm }, ref) => {
  const [visible, setVisible] = useState(false);
  const [type, setType] = useState<'product' | 'solution'>();
  const [treeData, setTreeData] = useState<any>();
  const [, setMaterialType] = useState<string>();
  const [loading, setLoading] = useState(false);
  const [labelList, setLabelList] = useState<any[]>([]);
  const [record, setRecord] = useState<any>();
  const formRef = useRef<any>(null);

  const [selectedProductVersionUUIDList, setSelectedProductVersionUUIDList] = useState<string[]>([]);
  const [selectedSolutionVersionUUIDList, setSelectedSolutionVersionUUIDList] = useState<string[]>([]);

  useImperativeHandle(
    ref,
    () => ({
      show({ type, treeData, materialType, record }) {
        setVisible(true);
        setTreeData(treeData);
        setType(type);
        setMaterialType(materialType);
        setRecord(record);
      },
    }),
    [],
  );

  useEffect(() => {
    listMaterialLabels().then((res) => {
      if (res.Error) {
        message.error({
          content: res.Error.Message,
        });
      } else {
        setLabelList(res.ListMaterialLabels || []);
      }
    });
  }, []);

  useEffect(() => {
    if (visible && record) {
      const method = type === 'product' ? listMaterialProductVersionRelDetails : listMaterialSolutionVersionRelDetails;
      method({
        MaterialInfoUUID: record.MaterialInfoUUID,
      }).then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          if (type === 'product') {
            setSelectedProductVersionUUIDList(
              (res.ListMaterialProductVersionRels || []).map((item) => item.ProductVersionUUID),
            );
          } else {
            setSelectedSolutionVersionUUIDList(
              (res.ListMaterialSolutionVersionRels || []).map((item) => item.SolutionVersionUUID),
            );
          }
        }
      });
    }
  }, [visible, type, record]);

  function handleCancel() {
    setVisible(false);
    formRef.current.resetFields();
  }

  function handleConfirm() {
    formRef.current.validateFields().then((values) => {
      const params = {
        ...values,
        MaterialUUID: record.MaterialInfoUUID,
      };
      setLoading(true);
      updateMaterialPackageInfo(params)
        .then((res) => {
          if (res.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            message.success({
              content: '更新成功',
            });
            onConfirm();
            handleCancel();
          }
        })
        .finally(() => {
          setLoading(false);
        });
    });
  }

  const initialValues = useMemo(() => {
    if (record) {
      return {
        ...record,
        ProductVersionUUIDList: selectedProductVersionUUIDList,
        SolutionVersionUUIDList: selectedSolutionVersionUUIDList,
      };
    }

    return {};
  }, [record, selectedProductVersionUUIDList, selectedSolutionVersionUUIDList]);

  return (
    <ProModal
      title="编辑物料"
      visible={visible}
      size="l"
      onCancel={handleCancel}
      onOk={handleConfirm}
      confirmLoading={loading}
    >
      <ProForm formRef={formRef} layout="fixed" fixedLabelWidth={100} initialValues={initialValues}>
        <ProForm.Item label="物料名称" dataIndex="MaterialName">
          <Form.Text>{record?.MaterialName}</Form.Text>
        </ProForm.Item>
        {type === 'product' && (
          <ProForm.Item
            label="关联产品版本"
            dataIndex="ProductVersionUUIDList"
            rules={{
              required: {
                value: true,
                message: '请选择关联产品版本',
              },
            }}
          >
            <SelectMultiple
              matchButtonWidth
              appearance="button"
              size="full"
              options={
                treeData?.parentData?.ProductVersionList?.map((item) => ({
                  text: item.ProductVersionName,
                  value: item.ProductVersionUUID,
                })) || []
              }
            />
          </ProForm.Item>
        )}
        {type === 'solution' && (
          <ProForm.Item
            label="关联解决方案版本"
            dataIndex="SolutionVersionUUIDList"
            rules={{
              required: {
                value: true,
                message: '请选择关联解决方案版本',
              },
            }}
          >
            <SelectMultiple
              matchButtonWidth
              appearance="button"
              size="full"
              options={
                treeData?.parentData?.SolutionVersionList?.map((item) => ({
                  text: item.Code,
                  value: item.SolutionVersionUUID,
                })) || []
              }
            />
          </ProForm.Item>
        )}
        <ProForm.Item
          label="标签"
          dataIndex="labelNameList"
          rules={{
            required: {
              value: true,
              message: '请选择标签',
            },
          }}
        >
          <TagSelect
            style={{ width: '100%' }}
            options={
              labelList?.map((item) => ({
                text: item.Name,
                value: item.Name,
              })) || []
            }
          />
        </ProForm.Item>
        <ProForm.Item label="发布说明" dataIndex="ReleaseNotes">
          <Input.TextArea style={{ width: '100%' }} />
        </ProForm.Item>
      </ProForm>
    </ProModal>
  );
};

export default forwardRef(EditMaterialModal);
