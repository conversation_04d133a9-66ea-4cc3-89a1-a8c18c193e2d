import { getProductList, getSiteProductList } from '@/common/api/materialManage';
import Loading from '@/common/components/Loading';
import { SearchBox, Status, Tree, message } from '@tencent/tea-component';
import React, { useEffect, useMemo, useState } from 'react';

export interface IProps {
  onSelect: (data: any) => void;
  isActive: boolean;
  // 产品节点能否选中
  productSelectable?: boolean;
  siteUUID?: string;
}
const types = ['product', 'productVersion'];
function buildTree(data, typeIndex, parent?) {
  return (data || []).map((item) => {
    const type = types[typeIndex];
    const nodeData: any = {
      data: item,
      dataType: type,
    };
    if (type === 'product') {
      nodeData.content = item.ProductName;
      nodeData.id = item.ProductUUID;
      nodeData.children = buildTree(item.ProductVersionList || [], typeIndex + 1, item);
    } else if (type === 'productVersion') {
      nodeData.parentData = parent;
      nodeData.content = item.ProductVersionName;
      nodeData.id = item.ProductVersionUUID;
    }
    return nodeData;
  });
}

const ProductTree: React.FC<IProps> = ({ onSelect, isActive, productSelectable = true, siteUUID }) => {
  const [data, setData] = useState<any[]>([]);
  const [activeData, setActiveData] = useState<any>();
  const [filterParams, setFilterParams] = useState<{ Name?: string }>({});
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    const method = siteUUID ? getSiteProductList : getProductList;
    setLoading(true);
    method({
      ...filterParams,
      SiteUUID: siteUUID,
    })
      .then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          setData(buildTree(res.ProductList, 0));
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }, [filterParams, siteUUID]);

  function handleSearch(value) {
    if (value) {
      setFilterParams({ Name: value });
    } else {
      setFilterParams({});
    }
  }

  useEffect(() => {
    if (isActive) {
      if (activeData) {
        onSelect(activeData);
      } else {
        if (data.length) {
          // if (productSelectable) {
          //   setActiveData(data[0].data);
          //   onSelect(data[0].data);
          // } else {
          const child = data[0]?.children?.[0].data;
          setActiveData({
            ...child,
            parentData: data[0]?.data,
          });
          onSelect({
            ...child,
            parentData: data[0]?.data,
          });
        }
        // }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isActive, data, activeData]);

  function handleActive(activeIds: string[], context) {
    let data: any;
    if (context.data.parentData) {
      data = {
        ...context.data.data,
        parentData: context.data.parentData,
      };
    } else {
      // 如果产品节点不能选中，则不做赋值操作
      if (!productSelectable) {
        return;
      }
      data = context.data.data;
    }
    setActiveData(data);
    onSelect(data);
  }

  const activeIds = useMemo(() => {
    if (activeData) {
      return activeData.parentData ? [activeData.ProductVersionUUID] : [activeData.ProductUUID];
    }
    return [];
  }, [activeData]);

  return (
    <Loading loading={loading}>
      <SearchBox placeholder="请输入产品名称进行搜索" onSearch={handleSearch} />
      {data?.length ? (
        <Tree data={data} defaultExpandAll activable activeIds={activeIds} fullActivable onActive={handleActive} />
      ) : (
        <Status description="暂无数据" />
      )}
    </Loading>
  );
};

export default ProductTree;
