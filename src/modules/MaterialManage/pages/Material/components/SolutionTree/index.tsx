import { getSiteSolutionList, getSolutionList } from '@/common/api/materialManage';
import Loading from '@/common/components/Loading';
import { SearchBox, Status, Tree, message } from '@tencent/tea-component';
import React, { useEffect, useMemo, useState } from 'react';

export interface IProps {
  onSelect: (data: any) => void;
  isActive: boolean;
  // 解决方案能否选中
  solutionSelectable?: boolean;
  siteUUID?: string;
}

const types = ['solution', 'solutionVersion'];
function buildTree(data, typeIndex, parent?) {
  return (data || []).map((item) => {
    const type = types[typeIndex];
    const nodeData: any = {
      data: item,
      dataType: type,
    };
    if (type === 'solution') {
      nodeData.content = item.SolutionName;
      nodeData.id = item.SolutionUUID;
      nodeData.children = buildTree(item.SolutionVersionList || [], typeIndex + 1, item);
    } else if (type === 'solutionVersion') {
      nodeData.parentData = parent;
      nodeData.content = item.Code;
      nodeData.id = item.SolutionVersionUUID;
    }
    return nodeData;
  });
}

const SolutionTree: React.FC<IProps> = ({ onSelect, isActive, solutionSelectable = true, siteUUID }) => {
  const [data, setData] = useState<any[]>([]);
  const [activeData, setActiveData] = useState<any>();
  const [filterParams, setFilterParams] = useState<{ Name?: string }>({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const method = siteUUID ? getSiteSolutionList : getSolutionList;
    setLoading(true);
    method({
      ...filterParams,
      SiteUUID: siteUUID,
    })
      .then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          setData(buildTree(res.SolutionList, 0));
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }, [filterParams, siteUUID]);

  function handleSearch(value) {
    if (value) {
      setFilterParams({ Name: value });
    } else {
      setFilterParams({});
    }
  }

  function handleActive(activeIds: string[], context) {
    let data: any;
    if (context.data.parentData) {
      data = {
        ...context.data.data,
        parentData: context.data.parentData,
      };
    } else {
      // 如果解决方案节点不能选中，则不做赋值操作
      if (!solutionSelectable) {
        return;
      }
      data = context.data.data;
    }
    setActiveData(data);
    // onSelect(data);
  }

  const activeIds = useMemo(() => {
    if (activeData) {
      return activeData.parentData ? [activeData.SolutionVersionUUID] : [activeData.SolutionUUID];
    }
    return [];
  }, [activeData]);

  useEffect(() => {
    if (isActive) {
      if (activeData) {
        onSelect(activeData);
      } else {
        if (data.length) {
          // if (solutionSelectable) {
          //   setActiveData(data[0].data);
          //   onSelect(data[0].data);
          // } else {
          const child = data[0]?.children?.[0].data;
          setActiveData({
            ...child,
            parentData: data[0]?.data,
          });
          onSelect({
            ...child,
            parentData: data[0]?.data,
          });
          // }
        }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isActive, data, activeData]);

  return (
    <Loading loading={loading}>
      <SearchBox placeholder="请输入解决方案版本进行搜索" onSearch={handleSearch} />
      {data?.length ? (
        <>
          <Tree data={data} defaultExpandAll activable fullActivable activeIds={activeIds} onActive={handleActive} />
        </>
      ) : (
        <Status description="暂无数据" />
      )}
    </Loading>
  );
};

export default SolutionTree;
