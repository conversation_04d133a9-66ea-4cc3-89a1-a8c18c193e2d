import React from 'react';
import { But<PERSON>, Popover, Tag } from '@tencent/tea-component';

export const getColumns = ({
  onDownload,
  onCopyOriginDownloadUrl,
  onReleasePackage,
  onPublish,
  onCancelPublish,
  onAbandoned,
  onEdit,
  onlyPushMaterial,
}: {
  onDownload: (record: any) => void;
  onCopyOriginDownloadUrl: (record: any) => void;
  onReleasePackage: (record: any) => void;
  onPublish: (record: any) => void;
  onCancelPublish: (record: any) => void;
  onAbandoned: (record: any) => void;
  onEdit: (record: any) => void;
  onlyPushMaterial: boolean;
}) => [
  {
    title: '物料名称',
    dataIndex: 'MaterialName',
    width: '20%',
    fixed: 'left',
    search: false,
    renderText(text, record) {
      return record.MaterialName;
    },
  },
  {
    title: '物料名称',
    dataIndex: 'Name',
    hideInTable: true,
  },
  {
    title: '状态',
    dataIndex: 'Status',
    width: '8%',
    valueType: 'dictSelect',
    fieldProps: {
      dictType: 'MaterialPublishStatus',
    },
  },
  {
    title: '架构',
    dataIndex: 'Arch',
    width: '12%',
    valueType: 'dictSelect',
    fieldProps: {
      dictType: 'PackageArch',
    },
  },
  {
    title: '版本',
    dataIndex: 'Version',
    width: '10%',
  },
  {
    title: '发布说明',
    dataIndex: 'ReleaseNotes',
    width: '15%',
    search: false,
  },
  {
    title: '发布时间',
    dataIndex: 'CreateAt',
    width: '15%',
    valueType: 'dateTime',
    search: false,
  },
  {
    title: '来源',
    dataIndex: 'Source',
    search: false,
    width: '7%',
    renderText(text, record) {
      if (record?.Source) {
        return {
          upload: '上传',
          workflow: '流水线',
        }[record.Source];
      }
      return '-';
    },
  },
  {
    title: '标签',
    dataIndex: 'labelNameList',
    width: '16%',
    search: false,
    render(text, record) {
      if (!record?.labelNameList || record.labelNameList.length === 0) {
        return '-';
      }

      function getTagEls(labelNameList) {
        return labelNameList.map((item) => (
          <Tag theme="primary" key={item} style={{ marginRight: 5 }}>
            {item}
          </Tag>
        ));
      }

      if (record.labelNameList.length <= 5) {
        return getTagEls(record.labelNameList);
      }
      return <Popover overlay={getTagEls(record.labelNameList)}>{getTagEls(record.labelNameList.slice(0, 5))}</Popover>;
    },
  },
  {
    title: '标签',
    dataIndex: 'LabelName',
    hideInTable: true,
  },
  {
    title: '操作人',
    dataIndex: 'Operator',
    width: '10%',
    search: false,
  },
  {
    title: '操作',
    dataIndex: 'action',
    valueType: 'option',
    width: '20%',
    fixed: 'right',
    render(text, record) {
      const disabled = record.Status === 'abandoned';
      const isCps = !!(window as any).JIGUANG_is_tea_control_env;
      return (
        <>
          {!onlyPushMaterial && !disabled && (
            <Button type="link" onClick={() => onDownload(record)}>
              下载
            </Button>
          )}
          {!isCps && !disabled && (
            <Button type="link" onClick={() => onReleasePackage(record)}>
              推送
            </Button>
          )}
          {!onlyPushMaterial && !isCps && !disabled && (
            <Button type="link" onClick={() => onEdit(record)}>
              编辑物料
            </Button>
          )}
          {!onlyPushMaterial && !isCps && !disabled && (
            <Button type="link" onClick={() => onAbandoned(record)}>
              废弃
            </Button>
          )}
          {!onlyPushMaterial && !isCps && !['published', 'abandoned'].includes(record.Status) && (
            <Button type="link" onClick={() => onPublish(record)}>
              发布
            </Button>
          )}
          {!onlyPushMaterial &&
            ((!isCps && !['unpublished', 'abandoned'].includes(record.Status)) ||
              (record.Status === 'published' && !record.MaterialCanUnPublished)) && (
              <Button type="link" onClick={() => onCancelPublish(record)}>
                取消发布
              </Button>
            )}
          {!onlyPushMaterial && !isCps && !disabled && (
            <Button type="link" onClick={() => onCopyOriginDownloadUrl(record)}>
              复制原始物料下载链接
            </Button>
          )}
        </>
      );
    },
  },
];
