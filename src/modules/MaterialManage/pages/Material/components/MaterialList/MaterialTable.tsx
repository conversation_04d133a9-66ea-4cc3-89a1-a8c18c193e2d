import React, { useMemo, useRef, useState } from 'react';
import { getColumns } from './config';
import { message } from '@tencent/tea-component';
import {
  abandonedPackage,
  downloadMaterialPackage,
  downloadOriginalPackage,
  getMaterialList,
  publishMaterial,
  unpublishedMaterial,
} from '@/common/api/materialManage';
import UploadMaterialModal, { IUploadMaterialModalRef } from '../UploadMaterialModal';
import EditMaterialModal, { IEditorMaterialModalRef } from '../EditMaterialModal';
import ReleasePackageModal, { IReleasePackageModalRef } from '../ReleasePackageModal';
import { copyText } from '@/common/utils';
import { ActionType, TcsButton, TcsTable } from '@tencent/tcs-component';

export interface IProps {
  materialType: 'install' | 'fix';
  data: any;
  type: 'solution' | 'product';
  siteUUID?: string;
}

const MaterialTable: React.FC<IProps> = ({ type, materialType, data, siteUUID }) => {
  // 是否只支持推送物料
  const onlyPushMaterial = useMemo(() => !!siteUUID, [siteUUID]);
  const columns = getColumns({
    onDownload: handleDownload,
    onCopyOriginDownloadUrl: handleCopyOriginDownloadUrl,
    onReleasePackage: handleReleasePackage,
    onPublish: handlePublish,
    onCancelPublish: handleCancelPublish,
    onAbandoned: handleAbandoned,
    onEdit: handleEdit,
    onlyPushMaterial,
  });
  const actionRef = useRef<ActionType>();
  const uploadModalRef = useRef<IUploadMaterialModalRef>(null);
  const editRef = useRef<IEditorMaterialModalRef>(null);
  const releaseRef = useRef<IReleasePackageModalRef>(null);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);

  const [loading, setLoading] = useState(false);

  function handleUploadMaterialConfirm() {
    actionRef.current?.reload();
  }

  function handleUploadMaterial() {
    uploadModalRef.current?.show(type, data, materialType);
  }

  function handleDownload(record) {
    setLoading(true);
    downloadMaterialPackage({
      MaterialInfoUUID: record.MaterialInfoUUID,
      ProductVersionUUID: data.ProductVersionUUID,
      SolutionVersionUUID: data.SolutionVersionUUID,
    })
      .then((res) => {
        // Aurora报错信息会放到Error里面
        if (res.Error) {
          // 弹出错误提示
          message.error({
            content: res.Error.Message,
          });
        } else {
          const { DownloadURL } = res;
          if (DownloadURL) {
            window.open(`//registry.jiguang.woa.com${DownloadURL}`, '_blank');
          }
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }

  function handleEdit(record) {
    editRef.current?.show({ type, treeData: data, materialType, record });
  }

  function handleCopyOriginDownloadUrl(record) {
    setLoading(true);
    downloadOriginalPackage({
      MaterialInfoUUID: record.MaterialInfoUUID,
    })
      .then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          const u = new URL(res.DownloadUrl);
          u.protocol = window.location.protocol;
          const url = u.toString();
          copyText(url, '原始物料下载链接复制成功');
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }
  function handleReleasePackage(record) {
    releaseRef?.current?.show([record], data);
  }

  function handlePatchRelease() {
    releaseRef?.current?.show(selectedRows, data);
  }

  function handleCancelPublish(record) {
    setLoading(true);
    unpublishedMaterial({
      MaterialInfoUUID: record.MaterialInfoUUID,
    })
      .then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          message.success({
            content: '取消发布成功',
          });
          // 刷新表格数据
          actionRef.current?.reload();
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }
  function handlePublish(record) {
    setLoading(true);
    publishMaterial({
      MaterialInfoUUID: record.MaterialInfoUUID,
    })
      .then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          message.success({
            content: '发布成功',
          });
          // 刷新表格数据
          actionRef.current?.reload();
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }
  function handleAbandoned(record) {
    setLoading(true);
    abandonedPackage({
      MaterialInfoUUID: record.MaterialInfoUUID,
    })
      .then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          message.success({
            content: '废弃成功',
          });
          // 刷新表格数据
          actionRef.current?.reload();
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }

  function handleEditSuccess() {
    actionRef.current?.reload();
  }
  const params = useMemo(() => {
    const params: any = {
      Type: materialType,
    };
    if (type === 'solution') {
      params.SolutionVersionUUID = data.SolutionVersionUUID;
    } else {
      params.ProductVersionUUID = data.ProductVersionUUID;
    }
    return params;
  }, [materialType, type, data.SolutionVersionUUID, data.ProductVersionUUID]);
  return (
    <>
      <TcsTable
        search={{}}
        cardBordered
        options={false}
        params={params}
        key={JSON.stringify(params)}
        headerTitle={
          <>
            {!onlyPushMaterial && (
              <TcsButton type="primary" onClick={handleUploadMaterial}>
                上传物料
              </TcsButton>
            )}
            {
              <TcsButton onClick={handlePatchRelease} disabled={!selectedRows?.length}>
                批量推送
              </TcsButton>
            }
          </>
        }
        columns={columns}
        actionRef={actionRef}
        loading={loading}
        rowKey={(record) => record.MaterialInfoUUID}
        pagination={{}}
        request={({ current, pageSize, ...filterParams }: any) => {
          const params: any = {
            Limit: pageSize,
            Offset: (current - 1) * pageSize,
            ...filterParams,
          };
          setLoading(true);
          return getMaterialList(params)
            .then((res) => {
              if (res.Error) {
                message.error({
                  content: res.Error.Message,
                });
                return {
                  data: [],
                };
              }
              return {
                success: true,
                data: res.MaterialList || [],
                total: (res.MaterialList || []).length,
              };
            })
            .finally(() => {
              setLoading(false);
            });
        }}
        rowSelection={{
          targetColumnKey: 'MaterialName',
          selectedRowKeys: selectedRows.map((item) => item.MaterialInfoUUID),
          onChange(selectedRowKeys, selectedRows) {
            setSelectedRows(selectedRows);
          },
        }}
        scroll={{
          x: 1600,
        }}
      />
      <UploadMaterialModal ref={uploadModalRef} onConfirm={handleUploadMaterialConfirm} />
      <EditMaterialModal ref={editRef} onConfirm={handleEditSuccess} />
      <ReleasePackageModal ref={releaseRef} onConfirm={handleEditSuccess} siteUUID={siteUUID} />
    </>
  );
};

export default MaterialTable;
