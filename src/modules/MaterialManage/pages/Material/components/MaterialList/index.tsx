import { TabPanel, Tabs } from '@tencent/tea-component';
import React, { useEffect } from 'react';
import MaterialTable from './MaterialTable';

const tabs = [
  {
    id: 'install',
    label: '安装包',
  },
  {
    id: 'fix',
    label: '补充包',
  },
];

export interface IProps {
  type: 'product' | 'solution';
  data: any;
  siteUUID?: string;
}

const MaterialList: React.FC<IProps> = ({ type, data, siteUUID }) => {
  useEffect(() => {}, [type, data]);

  return (
    <Tabs tabs={tabs}>
      <TabPanel id="install">
        <MaterialTable materialType="install" type={type} data={data} siteUUID={siteUUID} />
      </TabPanel>
      <TabPanel id="fix">
        <MaterialTable materialType="fix" type={type} data={data} siteUUID={siteUUID} />
      </TabPanel>
    </Tabs>
  );
};

export default MaterialList;
