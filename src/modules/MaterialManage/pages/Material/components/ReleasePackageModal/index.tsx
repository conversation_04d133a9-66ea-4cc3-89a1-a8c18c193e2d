import { getPushableSite, releasePackage } from '@/common/api/materialManage';
import { ProForm, ProModal } from '@/common/components';
import { Card, SelectMultiple, Table, message } from '@tencent/tea-component';
import React, { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';

const columns = [
  {
    header: '物料名称',
    key: 'MaterialName',
  },
  {
    header: '架构',
    key: 'Arch',
  },
  {
    header: '版本',
    key: 'Version',
  },
];

export interface IProps {
  onConfirm: () => void;
}

export interface IReleasePackageModalRef {
  show: (materialList: any[], treeData: any) => void;
  // 如果传入siteUUID,则只给此siteUUID推送
  siteUUID?: string;
}

const ReleasePackageModal: React.ForwardRefRenderFunction<IReleasePackageModalRef, IProps> = (
  { onConfirm, siteUUID },
  ref,
) => {
  const [visible, setVisible] = useState(false);
  const [materialList, setMaterialList] = useState<any[]>([]);
  const [treeData, setTreeData] = useState<any>();
  const formRef = useRef<any>(null);
  const [loading, setLoading] = useState(false);

  const [siteList, setSiteList] = useState<any[]>([]);

  useImperativeHandle(
    ref,
    () => ({
      show(materialList, treeData) {
        setVisible(true);
        setMaterialList(materialList);
        setTreeData(treeData);
      },
    }),
    [],
  );

  const hasUnPublished = useMemo(() => {
    if (!materialList) {
      return false;
    }
    return materialList.findIndex((item) => item.Status === 'unpublished') !== -1;
  }, [materialList]);

  useEffect(() => {
    if (visible && materialList) {
      const params: any = {
        MaterialInfoUUIDList: materialList.map((item) => item.MaterialInfoUUID),
      };
      // 如果有未发布的物料，只能发布到未发布的局点
      if (hasUnPublished) {
        params.Status = 'unpublished';
      }
      setLoading(true);
      getPushableSite(params)
        .then((res) => {
          if (res.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            setSiteList(res.SiteInfoList || []);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [visible, materialList, hasUnPublished]);

  function handleCancel() {
    setVisible(false);
    formRef.current.resetFields();
  }

  function handleConfirm() {
    formRef.current.validateFields().then((values) => {
      if (!values.SiteIDList?.length) {
        return message.warning({
          content: siteUUID ? '所选物料无法推送至此局点' : '清选择要推送的局点',
        });
      }
      setLoading(true);
      releasePackage({
        SiteUUIDList: values.SiteIDList,
        MaterialInfoUUIDList: materialList.map((item) => item.MaterialInfoUUID),
        ProductVersionUUID: treeData?.ProductVersionUUID,
        SolutionVersionUUID: treeData?.SolutionVersionUUID,
      })
        .then((res) => {
          if (res.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            message.success({
              content: '推送物料成功,已推送物料可在局点管理->物料管理中查看',
            });
            onConfirm();
            handleCancel();
          }
        })
        .finally(() => {
          setLoading(false);
        });
    });
  }

  const siteInSiteList = useMemo(() => {
    if (!siteUUID || !siteList?.length) {
      return false;
    }

    return siteList.findIndex((item) => item.SiteUUID === siteUUID) !== -1;
  }, [siteList, siteUUID]);

  useEffect(() => {
    if (siteUUID && visible && siteList?.length) {
      if (!siteInSiteList) {
        message.warning({
          content: '无法将所选物料推送到此局点',
        });
      } else {
        setTimeout(() => {
          formRef?.current?.setFieldsValue({
            SiteIDList: [siteUUID],
          });
        });
      }
    }
  }, [siteInSiteList, siteUUID, visible, siteList]);

  return (
    <ProModal
      title="推送物料"
      visible={visible}
      size="xl"
      onCancel={handleCancel}
      onOk={handleConfirm}
      confirmLoading={loading}
    >
      <Card bordered>
        <Card.Body title="已选物料">
          <Table columns={columns} records={materialList || []} />
        </Card.Body>
      </Card>

      <Card>
        <Card.Body
          title={
            <>
              {hasUnPublished ? (
                <span>
                  目标局点
                  <span style={{ color: 'red', fontWeight: 600, fontSize: 16 }}>
                    (注意:存在未发布的物料，只能推送到【内部局点】)
                  </span>
                </span>
              ) : (
                '目标局点'
              )}
            </>
          }
        >
          <ProForm formRef={formRef}>
            <ProForm.Item label="请选择目标局点" dataIndex="SiteIDList">
              <SelectMultiple
                matchButtonWidth
                appearance="button"
                disabled={!!siteUUID}
                searchable
                size="full"
                options={
                  siteList?.map((item) => ({
                    text: item.SiteName,
                    value: item.SiteUUID,
                  })) || []
                }
              />
            </ProForm.Item>
          </ProForm>
        </Card.Body>
      </Card>
    </ProModal>
  );
};

export default forwardRef(ReleasePackageModal);
