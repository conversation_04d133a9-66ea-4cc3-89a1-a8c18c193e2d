import { ProForm } from '@/common/components';
import useLookup from '@/common/hookups/useLookup';
import { Card, Form } from '@tencent/tea-component';
import React, { useMemo } from 'react';

interface IProps {
  data: any;
}

const ProductInfo: React.FC<IProps> = ({ data }) => {
  const { getLookupByCode } = useLookup([]);

  const CategoryName = useMemo(() => {
    if (data?.Category) {
      const lookup = getLookupByCode('ProductCategory', data.Category);
      return lookup?.Name || '-';
    }
    return '-';
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data?.Category]);

  const SourceName = useMemo(() => {
    if (data?.Source) {
      const lookup = getLookupByCode('ProductSource', data.Source);
      return lookup?.Name || '-';
    }
    return '-';
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data?.Source]);

  return (
    <Card style={{ width: '100%' }} bordered>
      <Card.Body title="产品信息">
        <ProForm readonly grid>
          <ProForm.Item label="产品名称" dataIndex="ProductName" colProps={{ span: 12 }}>
            <Form.Text>{data.ProductName}</Form.Text>
          </ProForm.Item>
          <ProForm.Item label="产品Code" dataIndex="Code" colProps={{ span: 12 }}>
            <Form.Text>{data.Code}</Form.Text>
          </ProForm.Item>
          <ProForm.Item label="产品分类" dataIndex="Category" colProps={{ span: 12 }}>
            <Form.Text>{CategoryName}</Form.Text>
          </ProForm.Item>
          <ProForm.Item label="产品来源" dataIndex="Source" colProps={{ span: 12 }}>
            <Form.Text>{SourceName}</Form.Text>
          </ProForm.Item>
          <ProForm.Item label="产品Owner" dataIndex="Owner" colProps={{ span: 12 }}>
            <Form.Text>{data.Owner}</Form.Text>
          </ProForm.Item>
          <ProForm.Item label="产品说明" dataIndex="Description" colProps={{ span: 12 }}>
            <Form.Text>{data.Description}</Form.Text>
          </ProForm.Item>
        </ProForm>
      </Card.Body>
    </Card>
  );
};

export default ProductInfo;
