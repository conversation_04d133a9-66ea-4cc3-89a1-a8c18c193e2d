import { Card, Layout, TabPanel, Tabs } from '@tencent/tea-component';
import React, { useState } from 'react';
import ProductTree from './ProductTree';
import SolutionTree from './SolutionTree';
import MaterialList from './MaterialList';
import SolutionInfo from './SolutionInfo';
import ProductInfo from './ProductInfo';

const tabs = [
  {
    id: 'product',
    label: '产品版本',
  },
  {
    id: 'solution',
    label: '解决方案版本',
  },
];

export interface IProps {
  // 如果传入指定局点，说明时从局点列表过来的，需要对页面做一些处理
  siteUUID?: string;
}

const MainPage: React.FC<IProps> = ({ siteUUID }) => {
  const [selectedTreeData, setSelectedTreeData] = useState<any>();
  const [activeTab, setActiveTab] = useState('product');

  function handleSelectTreeNode(data: any, type: 'solution' | 'product') {
    setSelectedTreeData({
      type,
      data,
    });
  }

  return (
    <Layout style={{ height: '100%' }}>
      <Layout.Body>
        <Layout.Sider style={{ width: 300 }}>
          <Card bordered style={{ maxHeight: '100%', overflow: 'auto' }}>
            <Card.Body>
              <Tabs tabs={tabs} activeId={activeTab} onActive={(tab) => setActiveTab(tab.id)}>
                <TabPanel id="product">
                  <ProductTree
                    isActive={activeTab === 'product'}
                    onSelect={(data) => handleSelectTreeNode(data, 'product')}
                  />
                </TabPanel>
                <TabPanel id="solution">
                  <SolutionTree
                    isActive={activeTab === 'solution'}
                    onSelect={(data) => handleSelectTreeNode(data, 'solution')}
                  />
                </TabPanel>
              </Tabs>
            </Card.Body>
          </Card>
        </Layout.Sider>
        <Layout.Content style={{ backgroundColor: '#ffffff', paddingLeft: 10 }}>
          {/** 如果有parentData,说明要显示表格 */}
          {selectedTreeData?.data?.parentData && (
            <Card bordered style={{ width: '100%' }}>
              <Card.Body>
                <MaterialList type={selectedTreeData?.type} data={selectedTreeData.data} siteUUID={siteUUID} />
              </Card.Body>
            </Card>
          )}
          {/** 如果type是solution，显示解决方案版本详情 */}
          {selectedTreeData?.type === 'solution' && !selectedTreeData?.data?.parentData && (
            <SolutionInfo data={selectedTreeData?.data} />
          )}
          {/** 如果type是product，显示产品版本详情 */}
          {selectedTreeData?.type === 'product' && !selectedTreeData?.data?.parentData && (
            <ProductInfo data={selectedTreeData?.data} />
          )}
        </Layout.Content>
      </Layout.Body>
    </Layout>
  );
};

export default MainPage;
