import { ProForm } from '@/common/components';
import { Card, Form } from '@tencent/tea-component';
import React from 'react';

interface IProps {
  data: any;
}

const SolutionInfo: React.FC<IProps> = ({ data }) => (
  <Card style={{ width: '100%' }} bordered>
    <Card.Body title="解决方案信息">
      <ProForm readonly grid initialValues={data}>
        <ProForm.Item label="解决方案名称" dataIndex="SolutionName" colProps={{ span: 12 }}>
          <Form.Text>{data.SolutionName}</Form.Text>
        </ProForm.Item>
        <ProForm.Item label="解决方案Code" dataIndex="SolutionCode" colProps={{ span: 12 }}>
          <Form.Text>{data.SolutionCode}</Form.Text>
        </ProForm.Item>

        <ProForm.Item label="解决方案Owner" dataIndex="Owner" colProps={{ span: 12 }}>
          <Form.Text>{data.Owner}</Form.Text>
        </ProForm.Item>
        <ProForm.Item label="解决方案说明" dataIndex="Description" colProps={{ span: 12 }}>
          <Form.Text>{data.Description}</Form.Text>
        </ProForm.Item>
      </ProForm>
    </Card.Body>
  </Card>
);

export default SolutionInfo;
