import { SelectOption } from '@tencent/tcs-component/esm/components/fields/Select';

export interface ProductVersionArtifactTagPackage {
  ApplicationPackageID: number;
  SubsystemID?: string;
  ApplicationBranchUUID?: string;
  PackageVersionName: string;
  ApplicationName: string;
  ApplicationArch: string;
  ProductCode?: string;
  ApplicationInstanceID: string;
}

export interface IProductVersionArtifactTag {
  Arch: string;
  ID: number;
  Label: string;
  TagDescription: string;
  TestStatus: string;
}

export namespace IListSolutionVersionArtifactBranches {
  export interface Item {
    BranchName: string;
    BranchCode: string;
    BranchType: string;
    CreatedAt: string;
    Creator: string;
    DeletedAt: string;
    Description: string;
    ID: number;
    Label: string;
    Modifier: string;
    SolutionVersionID: string;
    UUID: string;
    UpdatedAt: string;
    DataBranch: string;
    Owner: string;
    Operator: string;
    EnablePermission: number;
    IsMixedArch: boolean;
  }
}

export interface ProductVersionArtifactTag {
  TagNum?: number;
  ArtifactTagID: number;
  ProductVersionID: string;
  ProductVersionName?: string;
  ProductCode?: string;
  FromSpace?: string;
  ArtifactBranchUUID?: string;
  TestStatus?: string;
  ProductArch: string;
}

export namespace IListSolutionVersionArtifactTags {
  export interface Item {
    ArtifactBranchUUID?: string;
    Arch: string;
    CreatedAt: string;
    Creator: string;
    DataTag: string;
    DeletedAt: string;
    ID: number;
    Modifier: string;
    ProductVersionArtifactTagList: ProductVersionArtifactTag[];
    SolutionMarketArtifactTag?: {
      ArtifactTagID: number;
      ID: number;
    };
    SolutionVersionID: string;
    TagNum: number;
    TagStatus: string;
    Label: string;
    TagDescription: string;
    PublishStatus: string;
    UUID: string;
    UpdatedAt: string;
  }
}

export namespace IListProductVersionArtifactBranches {
  export interface Item {
    BranchName: string;
    BranchCode: string;
    BranchType: string;
    CreatedAt: string;
    Creator: string;
    DeletedAt: string;
    Description: string;
    ID: number;
    Label: string;
    Modifier: string;
    ProductVersionID: string;
    UUID: string;
    UpdatedAt: string;
    DataBranch: string;
    Owner: string;
    Operator: string;
    EnablePermission: number;
    IsMixedArch: boolean;
  }
}

export interface SolutionArtifactBranchWithTags extends IListSolutionVersionArtifactBranches.Item {
  children: IListSolutionVersionArtifactTags.Item[];
}

export interface IOptionProductArtifactBranch extends SelectOption {
  item?: IListProductVersionArtifactBranches.Item;
  children: IOptionProductArtifactTag[];
}

export interface IOptionProductArtifactTag extends SelectOption {
  item?: IListProductVersionArtifactTags.Item;
}

export namespace IListProductVersionArtifactTags {
  export interface Item {
    ArtifactBranchUUID?: string;
    ApplicationPackageList?: ProductVersionArtifactTagPackage[];
    Arch: string;
    CreatedAt?: string;
    Creator?: string;
    DataTag: string;
    DeletedAt?: string;
    Label: string;
    Tag: string;
    TagDescription: string;
    TestStatus: string;
    ID: number;
    Modifier?: string;
    ProductVersionID: string;
    TagNum: number;
    ProductVersionArtifactTag: IProductVersionArtifactTag;
    ProductMarketArtifactTag?: {
      ArtifactTagID: number;
      ID: number;
    };
    TagStatus: string;
    UUID: string;
    UpdatedAt?: string;
  }
}

export interface IOptionSolutionArtifactTag extends SelectOption {
  item?: IListSolutionVersionArtifactTags.Item;
  value: any;
}

export interface IOptionSolutionArtifactBranch extends SelectOption {
  item?: IListSolutionVersionArtifactBranches.Item;
  children: IOptionSolutionArtifactTag[];
  value: any;
}

export interface IProductDictionary {
  Code: string;
  CreatedAt: string;
  Creator: string;
  DeletedAt: string;
  Extra: any;
  ID: number;
  Modifier: string;
  Name: string;
  ParentID: string;
  Sort: number;
  TenantID: string;
  Type: string;
  UUID: string;
  UpdatedAt: string;
  ProductDictionary?: IProductDictionary[];
}

export interface IAppVersionList {
  ApplicationWorkspaceID: number;
  Arch: string;
  ApplicationName: string;
  ApplicationPackageID: number;
}
