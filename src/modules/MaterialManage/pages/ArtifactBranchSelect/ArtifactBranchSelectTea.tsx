/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-12-11 10:13:07
 * @LastEditors: w<PERSON><PERSON>
 * @LastEditTime: 2024-12-12 11:10:13
 * @Description:
 */
import React, { useMemo } from 'react';
import { getArtifactBranchTypeTeaColor } from '@/common/utils/tools';
import styles from './index.module.scss';
import { IOptionSolutionArtifactBranch, IOptionProductArtifactBranch } from '../type';
import { Select, Tag, Text } from '@tencent/tea-component';
import { SelectProps } from '@tencent/tea-component/src/select/SelectProps';
import { SelectOptionWithGroup } from '@tencent/tea-component/src/select/SelectOption';

interface IProductDictionary {
  Code: string;
  CreatedAt: string;
  Creator: string;
  DeletedAt: string;
  Extra: any;
  ID: number;
  Modifier: string;
  Name: string;
  ParentID: string;
  Sort: number;
  TenantID: string;
  Type: string;
  UUID: string;
  UpdatedAt: string;
  ProductDictionary?: IProductDictionary[];
}
// @ts-ignore
export interface ArtifactBranchSelectTeaProps extends SelectProps {
  label?: string;
  name?: string;
  placeholder?: string;
  tenantId?: string;
  showSearch?: boolean;
  options?: IOptionSolutionArtifactBranch[] | IOptionProductArtifactBranch[] | SelectOptionWithGroup[];
  branchTypeDict?: IProductDictionary[];
  labelCol?: any;
  wrapperCol?: any;
  onChange?: (value: string) => void;

  [k: string]: any;
}

const ArtifactBranchSelectTea: React.FC<ArtifactBranchSelectTeaProps> = ({
  placeholder = '',
  tenantId,
  showSearch = false,
  options = [],
  branchTypeDict = [],
  labelCol,
  wrapperCol,
  onChange,
  ...rest
}) => {
  // const [branchTypeDict, setBranchTypeDict] = useState<IProductDictionary[]>([]);

  // 如果获取了字典，可以按字典的sort给分支分类排序。否则按默认顺序。
  const sortOptions = useMemo(() => {
    if (!branchTypeDict || branchTypeDict.length < 1)
      return options.sort((a, b) => a?.item?.BranchType - b?.item?.BranchType);
    return (options || [])
      .map((optionItem) => ({
        ...optionItem,
        order: branchTypeDict?.find((dictItem) => dictItem?.Code === optionItem?.item?.BranchType)?.Sort || 99,
      }))
      .sort((a, b) => a.order - b.order);
  }, [branchTypeDict, options]);

  return (
    <Select
      {...rest}
      placeholder={placeholder}
      onChange={onChange}
      searchable={showSearch}
      appearance={'button'}
      matchButtonWidth
      options={sortOptions.map((branchOption) => ({
        text: (
          <div className={styles.selectOption}>
            <Text parent="div" style={{ width: 100 }} overflow>
              {branchOption.label}
            </Text>
            <Tag
              style={{ margin: 0, width: '50px', display: 'flex', justifyContent: 'center' }}
              theme={getArtifactBranchTypeTeaColor(branchOption.item?.BranchType || '')}
            >
              {branchTypeDict?.find((typeDict) => typeDict.Code === branchOption.item?.BranchType)?.Name ||
                branchOption.item?.BranchType ||
                ''}
            </Tag>
          </div>
        ),
        value: branchOption.value,
        tooltip: branchOption.label,
      }))}
      listWidth={400}
    />
  );
};

export default ArtifactBranchSelectTea;
