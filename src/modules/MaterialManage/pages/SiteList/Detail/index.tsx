import React, { useEffect, useState } from 'react';
import SiteInfo from './components/SiteInfo';
import { getOperationSiteDetail } from '@/common/api/materialManage';
import { getUrlParams } from '@/common/utils';
import { message } from '@tencent/tea-component';
import Record from './components/Record';
import { TcsLayout, TcsSpin } from '@tencent/tcs-component';

const SiteDetail = () => {
  const params = getUrlParams();
  const [siteInfo, setSiteInfo] = useState<any>();
  const [loading, setLoading] = useState(false);
  const { history } = TcsLayout.useHistory();
  useEffect(() => {
    setLoading(true);
    if (!params.site_uuid) {
      message.warning({
        content: '缺少SiteUUID',
      });
      return;
    }
    getOperationSiteDetail({
      SiteUUID: params.site_uuid,
    })
      .then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          setSiteInfo(res.Site);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }, [params.site_uuid]);

  return (
    <TcsLayout title="局点详情" customizeCard history={history}>
      <TcsSpin spinning={loading} full>
        <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
          <SiteInfo siteInfo={siteInfo} />
          {params.site_uuid && <Record />}
        </div>
      </TcsSpin>
    </TcsLayout>
  );
};

export default SiteDetail;
