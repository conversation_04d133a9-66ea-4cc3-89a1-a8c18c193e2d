import { Button } from '@tencent/tea-component';
import dayjs from 'dayjs';
import React from 'react';

export const getColumns = () => [
  {
    header: 'ID',
    key: 'ID',
    width: '5%',
    fixed: 'left',
  },
  {
    header: '流程名称',
    key: 'WorkflowName',
    render(record) {
      return (
        <a
          onClick={() => {
            window.open(
              `/page/flow-design/flow-publish/exec?workflow_id=${record?.WorkflowID}&instance_id=${record?.WorkflowInstanceID}`,
              '_blank',
            );
          }}
        >
          {record.WorkflowName}
        </a>
      );
    },
  },
  {
    header: '物料包数量',
    key: 'ExtensionAttributes.PackageNum',
    width: '7%',
  },
  {
    header: '物料包详情',
    key: 'ExtensionAttributes.PackageDetail',
    width: '25%',
  },
  {
    header: '创建时间',
    key: 'CreatedAt',
    render(record) {
      return record.CreatedAt ? dayjs(record.CreatedAt).format('YYYY-MM-DD HH:mm:ss') : '-';
    },
  },
  {
    header: '扩展信息',
    key: 'ExtensionAttributeInfo',
    width: '35%',
    render(record) {
      return JSON.stringify(record.ExtensionAttributes, null, 2);
    },
  },
  {
    header: '操作',
    key: 'Operation',
    width: '5%',
    fixed: 'right',
    render(record) {
      return record.ExtensionAttributes?.ArtifactURL ? (
        <Button
          type="link"
          onClick={() => {
            window.open(`//registry.jiguang.woa.com/${record.ExtensionAttributes.ArtifactURL}`, '_blank');
          }}
        >
          下载
        </Button>
      ) : (
        ''
      );
    },
  },
];
