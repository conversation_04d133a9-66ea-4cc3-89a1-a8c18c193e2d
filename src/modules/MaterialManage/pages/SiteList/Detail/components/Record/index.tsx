import { useClassNames } from '@tencent/tea-component';
import React, { useState } from 'react';
import OperationRecord from './OperationRecord';
import { getUrlParams } from '@/common/utils';
import PublishApplication from './PushApplication';
import Material from './Material';
import Certificate from './Certificate';
import AssociatedDefect from './AssociatedDefect';
import TagManage from './TagManger';
import { TcsCard, TcsTabs } from '@tencent/tcs-component';

const Record = () => {
  const params = getUrlParams();
  const [activeId, setActiveId] = useState(params.active_tab || 'OperationRecord');
  const { Margin } = useClassNames();

  return (
    <div style={{ flex: 1, overflow: 'auto' }} className={Margin.Top['5n']}>
      <TcsCard fullHeight>
        {/* <Card.Body> */}
        <TcsTabs activeKey={activeId} fullHeight onChange={(activeKey) => setActiveId(activeKey)}>
          <TcsTabs.TabPane tab="运维记录" key="OperationRecord">
            <OperationRecord siteUUID={params.site_uuid} />
          </TcsTabs.TabPane>
          <TcsTabs.TabPane tab="已推送应用" key="PushApp">
            <PublishApplication siteUUID={params.site_uuid} />
          </TcsTabs.TabPane>
          <TcsTabs.TabPane tab="物料管理" key="Material">
            <Material siteUUID={params.site_uuid} />
          </TcsTabs.TabPane>
          <TcsTabs.TabPane tab="证书管理" key="Certificate">
            <Certificate siteUUID={params.site_uuid} />
          </TcsTabs.TabPane>
          <TcsTabs.TabPane tab="未修复缺陷" key="AssociatedDefect">
            <AssociatedDefect issueType="bug" siteUUID={params.site_uuid} />
          </TcsTabs.TabPane>
          <TcsTabs.TabPane tab="未修复需求" key="AssociatedStory">
            <AssociatedDefect issueType="story" siteUUID={params.site_uuid} />
          </TcsTabs.TabPane>
          <TcsTabs.TabPane tab="已部署应用" key="TagManage">
            <TagManage siteUUID={params.site_uuid} />
          </TcsTabs.TabPane>
        </TcsTabs>
        {/* </Card.Body> */}
      </TcsCard>
    </div>
  );
};

export default Record;
