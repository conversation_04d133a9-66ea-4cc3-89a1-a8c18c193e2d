import { Button, Card, Modal } from '@tencent/tea-component';
import React, { useImperativeHandle, useState } from 'react';

const OfflineDeployModal: React.ForwardRefRenderFunction<any, any> = (props, ref) => {
  const [visible, setVisible] = useState(false);

  useImperativeHandle(
    ref,
    () => ({
      show() {
        setVisible(true);
      },
    }),
    [],
  );

  return (
    <Modal visible={visible} caption="离线部署引导">
      <Modal.Body>
        <Card>
          <Card.Body title="离线部署请参考以下文档操作：">
            <p style={{ fontWeight: 'bold', fontSize: 18 }}>第一步：物料下载</p>
            <p>1. 选择要部署的物料包，下载包到本地。</p>

            <p style={{ fontWeight: 'bold', fontSize: 18 }}>第二步：部署TCS core</p>
            <p>{`//TCS Core的部署过程，待完善。`}</p>

            <p style={{ fontWeight: 'bold', fontSize: 18 }}>第三步：部署产品</p>

            <p>通过产品市场，将所需的产品部署起来。具体步骤:</p>
            <p>1. 将第一步获取的物料包上传到物料机/data/tce_dc/software/ 下；</p>
            <p>{`2. 进入产品中心->产品管理，点击“一键上传”并进行相关配置、发起上传；`}</p>
            <p>{`3. 进入运维中心->产品运维，点击发起产品部署。`}</p>
          </Card.Body>
        </Card>
      </Modal.Body>
      <Modal.Footer>
        <Button type="primary" onClick={() => setVisible(false)}>
          知道了
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default React.forwardRef(OfflineDeployModal);
