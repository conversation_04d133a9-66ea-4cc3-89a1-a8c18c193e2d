import React, { useEffect, useState, useRef } from 'react';
import { TcsModal, TcsForm, TcsLayout } from '@tencent/tcs-component';
import { Checkbox, Select } from '@tencent/tea-component';
import { MaterialManageRoutePath } from '@/common/routePath';
import { IWorkspaceInfoResponse } from './config';
import { GenerateSiteApi } from '@/common/api/materialManage';
interface IProps {
  showSelectArchModal: boolean;
  setVisible: (value: boolean) => void;
  siteUUID: string;
  defaultArch: string;
  applicationArtifactInfo: IWorkspaceInfoResponse;
}
interface IForm {
  SiteUUID: string;
  ArtifactVersionBackfillUUID: string;
  UsePackageHistory?: boolean;
  Arch: string[];
  KeepManualModification: string[];
  rematchApplicationVersion?: string[];
}

const ArchEnumOptions = [
  { label: 'rhel.amd64(x86_64)', value: 'rhel.amd64' },
  { label: 'rhel.arm64(aarch64)', value: 'rhel.arm64' },
];

const SelectArchModal: React.FC<IProps> = ({
  showSelectArchModal,
  siteUUID,
  defaultArch,
  setVisible,
  applicationArtifactInfo,
}) => {
  const {
    WorkspaceActive,
    IsLatestEnableCommitChanged,
    ArchList,
    LastBackfillUUID,
    SiteBackfillList,
    SolutionVersionUUID,
  } = applicationArtifactInfo;
  const { history } = TcsLayout.useHistory();
  const formRef = useRef<any>(null);
  const [form] = TcsForm.useForm();
  const [chooseRematchVersion, setChooseRematchVersion] = useState<string[]>([]);
  const [isChangeDefaultBackData, setIsChangeDefaultBackData] = useState<boolean>(false);
  const defaultCheckedValues = ArchList?.length > 0 ? ArchList : [defaultArch];
  const [loading, setLoading] = useState<boolean>(false);
  const isChooseRematchVersion = chooseRematchVersion.includes('rematchApplicationVersion');
  const shouldRedirectToTag =
    WorkspaceActive && !IsLatestEnableCommitChanged && !isChangeDefaultBackData && !isChooseRematchVersion;

  const jumpToEditSiteTagPage = (archInfo: string, ArtifactVersionBackfillUUID: string) => {
    history.push(
      `${MaterialManageRoutePath.EDIT_SITE_TAG}?siteUUID=${siteUUID}&archInfo=${archInfo}&backFillUUID=${ArtifactVersionBackfillUUID}&solutionVersionUUID=${SolutionVersionUUID}`,
    );
  };

  const handleFinish = async () => {
    try {
      const values = (await formRef.current?.validateFields({})) as IForm;
      const archInfo = values.Arch.join(',');
      const { ArtifactVersionBackfillUUID } = values;
      if (shouldRedirectToTag) {
        jumpToEditSiteTagPage(archInfo, ArtifactVersionBackfillUUID);
      } else {
        const params: any = { ...values, SiteUUID: siteUUID };
        delete params.rematchApplicationVersion;
        if ('KeepManualModification' in values) {
          params.KeepManualModification = values.KeepManualModification.includes('KeepManualModification');
        }
        setLoading(true);
        await GenerateSiteApi.GenerateSiteAppArtifactInfo(params);
        jumpToEditSiteTagPage(archInfo, ArtifactVersionBackfillUUID);
      }
      setVisible(false);
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleChangeBackData = (value: string) => {
    if (value !== LastBackfillUUID) {
      setIsChangeDefaultBackData(true);
      form.setFieldsValue({ rematchApplicationVersion: [] });
    } else {
      setIsChangeDefaultBackData(false);
    }
  };

  const onChooseRematchVersion = (value) => {
    setChooseRematchVersion(value);
  };

  useEffect(() => {
    if (showSelectArchModal) {
      form.setFieldsValue({
        Arch: defaultCheckedValues,
        ArtifactVersionBackfillUUID: LastBackfillUUID,
      });
    } else {
      setIsChangeDefaultBackData(false);
    }
  }, [showSelectArchModal]);

  return (
    <TcsModal
      visible={showSelectArchModal}
      onOk={handleFinish}
      onCancel={() => setVisible(false)}
      destroyOnClose
      confirmLoading={loading}
    >
      <TcsForm formRef={formRef} form={form}>
        <TcsForm.Item
          label="请选择需要为哪些架构维护应用版本"
          name="Arch"
          rules={[
            {
              required: true,
              message: '请选择对应架构',
            },
          ]}
        >
          <Checkbox.Group>
            {ArchEnumOptions.map((item) => (
              <Checkbox
                key={item.value}
                name={item.value}
                disabled={!!ArchList?.length}
                tooltip={!!ArchList?.length ? '默认选中历史规划中应用架构' : ''}
              >
                {item.label}
              </Checkbox>
            ))}
          </Checkbox.Group>
        </TcsForm.Item>
        <TcsForm.Item
          label="请选择要参考的回录信息"
          name="ArtifactVersionBackfillUUID"
          rules={[{ required: true, message: '请选择对应产品' }]}
        >
          <Select
            appearance="button"
            size="m"
            placeholder="选择信息"
            defaultValue={LastBackfillUUID}
            options={SiteBackfillList?.map?.((item) => ({
              text: item.TagDescription,
              value: item.UUID,
            }))}
            listWidth={200}
            onChange={handleChangeBackData}
          />
        </TcsForm.Item>
        {WorkspaceActive && !isChangeDefaultBackData && !IsLatestEnableCommitChanged && (
          <TcsForm.Item label="重新匹配应用版本" name="rematchApplicationVersion" initialValue={[]}>
            <Checkbox.Group onChange={onChooseRematchVersion}>
              <Checkbox name="rematchApplicationVersion">{''}</Checkbox>
            </Checkbox.Group>
          </TcsForm.Item>
        )}
        {WorkspaceActive && !shouldRedirectToTag && (
          <TcsForm.Item
            label="是否保留手动指定版本信息"
            name="KeepManualModification"
            initialValue={['KeepManualModification']}
          >
            <Checkbox.Group>
              <Checkbox name={'KeepManualModification'}>{''}</Checkbox>
            </Checkbox.Group>
          </TcsForm.Item>
        )}
      </TcsForm>
    </TcsModal>
  );
};
export default SelectArchModal;
