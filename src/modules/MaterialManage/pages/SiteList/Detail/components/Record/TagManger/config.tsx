import React from 'react';
import { Button } from '@tencent/tea-component';
import { ProColumns } from '@tencent/tcs-component';
export interface IRecord {
  ID: string;
}
export interface GetTagColumns {
  goDetail: (record: IRecord) => void;
  [key: string]: any;
}
export interface IWorkspaceInfoResponse {
  WorkspaceActive: boolean;
  IsEnableCommitExist: boolean;
  IsLatestEnableCommitChanged: boolean;
  ArchList: string[];
  LastBackfillUUID: string;
  SolutionVersionUUID: string;
  SiteBackfillList: {
    TagDescription: string;
    UUID: string;
    Time: string;
  }[];
}
export const getColumns = ({ goDetail }: GetTagColumns): ProColumns[] => [
  {
    width: 70,
    title: 'tag号',
    dataIndex: 'TagNum',
    copyable: true,
    hideInSearch: true,
  },
  {
    width: 70,
    title: '描述',
    dataIndex: 'TagDescription',
    hideInSearch: true,
  },
  {
    width: 70,
    title: '时间',
    dataIndex: 'CreatedAt',
    hideInSearch: true,
    valueType: 'dateTime',
  },
  {
    title: '操作',
    width: 120,
    key: 'option',
    valueType: 'option',
    fixed: 'right',
    render: (value, record) => (
      <>
        <Button type={'link'} onClick={() => goDetail(record)}>
          查看
        </Button>
      </>
    ),
  },
];
