import { getOTACertificate, signOTACertificate } from '@/common/api/materialManage';
import { ProForm } from '@/common/components';
import Loading from '@/common/components/Loading';
import { Button, Card, Form, Status, Text, message } from '@tencent/tea-component';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';

export interface IProps {
  siteUUID: string;
}

const Certificate: React.FC<IProps> = ({ siteUUID }) => {
  const [data, setData] = useState<any>();
  const [exist, setExist] = useState(false);
  const [loading, setLoading] = useState(false);
  const [update, setUpdate] = useState({});
  useEffect(() => {
    if (siteUUID) {
      setLoading(true);
      getOTACertificate({
        SiteUUID: siteUUID,
      })
        .then((res) => {
          if (res.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            setData(res.Certificate);
            setExist(res.Exist);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [siteUUID, update]);

  function handleSign() {
    setLoading(true);
    signOTACertificate({
      SiteUUID: siteUUID,
    }).then((res) => {
      if (res.Error) {
        message.error({
          content: res.Error.Message,
        });
      } else {
        message.success({
          content: '签发成功',
        });
        setUpdate({});
      }
    });
  }

  return (
    <Card>
      <Card.Body
        title="证书"
        operation={
          <>
            {!exist && (
              <Button type="link" onClick={handleSign}>
                签发证书
              </Button>
            )}
          </>
        }
      >
        <Loading loading={loading}>
          {exist && (
            <ProForm grid>
              <ProForm.Item label="证书版本" dataIndex="Version" colProps={{ span: 12 }}>
                <Form.Text>{data.Version}</Form.Text>
              </ProForm.Item>
              <ProForm.Item label="签发日期" dataIndex="IssueDate" colProps={{ span: 12 }}>
                <Form.Text>{dayjs(data.IssueDate).format('YYYY-MM-DD HH:mm:ss')}</Form.Text>
              </ProForm.Item>
              <ProForm.Item label="证书内容" dataIndex="Data" colProps={{ span: 24 }}>
                <Form.Text>
                  <Text copyable>{data.Data}</Text>
                </Form.Text>
              </ProForm.Item>
            </ProForm>
          )}
          {!exist && <Status title="暂无证书" />}
        </Loading>
      </Card.Body>
    </Card>
  );
};

export default Certificate;
