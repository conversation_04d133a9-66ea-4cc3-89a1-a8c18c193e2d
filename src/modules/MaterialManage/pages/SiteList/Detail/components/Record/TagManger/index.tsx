import React, { useEffect, useState, useRef } from 'react';
import { getColumns, IWorkspaceInfoResponse } from './config';
import { Button, message } from '@tencent/tea-component';
import { MaterialManageRoutePath } from '@/common/routePath';
import { GenerateSiteApi } from '@/common/api/materialManage';
import SelectArchModal from './selectArchModal';
import { APP_ARCH_MAPPING } from '@/modules/MaterialManage/pages/SiteList/constant';
import { ActionType, TcsCard, TcsLayout, TcsTable, TcsTabs } from '@tencent/tcs-component';
interface IProps {
  siteUUID: string;
}
interface IRecord {
  ID: string;
}
const TagManage: React.FC<IProps> = ({ siteUUID }) => {
  const actionRef = useRef<ActionType>();
  const [defaultArch, setDefaultArch] = useState<string>('rhel.amd64');
  const [showSelectArchModal, setShowSelectArchModal] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [applicationArtifactInfo, setApplicationArtifactInfo] = useState<IWorkspaceInfoResponse>({} as any);
  const { history } = TcsLayout.useHistory();

  const onCreate = () => {
    const { IsEnableCommitExist } = applicationArtifactInfo;
    if (!IsEnableCommitExist) {
      message.warning({
        content: '请先做规划并生效到局点',
      });
      return;
    }
    setShowSelectArchModal(true);
  };

  const goDetail = (record: IRecord) => {
    history.push(`${MaterialManageRoutePath.EDIT_SITE_TAG}?siteUUID=${siteUUID}&readonly=${true}&tagId=${record?.ID}`);
  };

  const columns = getColumns({ goDetail });

  const fetchData = async (params) => {
    const res = await GenerateSiteApi.ListSiteAppArtifactHistoryTags({
      ...params,
      SiteUUID: siteUUID,
      Arch: defaultArch,
    });
    return {
      data: res.ListSiteAppArtifactHistoryTags || [],
      success: !!res,
      total: res?.Total,
    };
  };

  useEffect(() => {
    (async () => {
      try {
        setLoading(true);
        const applicationArtifactInfo = await GenerateSiteApi.GetSiteGeneralInfoForApplicationArtifact({
          SiteUUID: siteUUID,
        });
        setApplicationArtifactInfo(applicationArtifactInfo);
      } catch (error) {
        console.error('获取信息出错：', error);
      } finally {
        setLoading(false);
      }
    })();
  }, [siteUUID]);

  const renderTable = () => (
    <TcsTable
      columns={columns}
      actionRef={actionRef}
      search={{ filterType: 'light' }}
      request={fetchData}
      pagination={{ defaultPageSize: 20 }}
      headerTitle={
        <Button type="primary" onClick={onCreate} loading={loading}>
          新建
        </Button>
      }
    />
  );
  return (
    <>
      <TcsCard>
        <TcsTabs
          onChange={(arch) => setDefaultArch(arch)}
          activeKey={defaultArch}
          items={Object.entries(APP_ARCH_MAPPING).map(([key, label]) => ({
            key,
            label,
            children: renderTable(),
          }))}
        />
      </TcsCard>
      <SelectArchModal
        showSelectArchModal={showSelectArchModal}
        siteUUID={siteUUID}
        defaultArch={defaultArch}
        applicationArtifactInfo={applicationArtifactInfo}
        setVisible={(visible: boolean) => {
          setShowSelectArchModal(visible);
        }}
      />
    </>
  );
};
export default TagManage;
