import { ILookup } from '@/common/context';
import { getAppVersion } from '@/common/utils';
import { TcsTag } from '@tencent/tcs-component';
import { Button } from '@tencent/tea-component';
import dayjs from 'dayjs';
import React from 'react';
export const getColumns = ({
  onDetail,
  getLookupByCode,
  onViewAppInfo,
  lookups,
  issueType,
}: {
  onDetail: (record) => void;
  getLookupByCode: (type, code) => any;
  onViewAppInfo: (record: any) => void;
  lookups: Record<string, ILookup[]>;
  issueType: 'bug' | 'story';
}) =>
  [
    {
      title: issueType === 'story' ? '需求单ID' : '缺陷ID',
      dataIndex: 'IssueID',
      fixed: 'left',
      copyable: true,
      width: '7%',
    },
    {
      title: '标题',
      dataIndex: 'Title',
      width: '15%',
      render(text: string, record: any) {
        return <a onClick={() => onDetail(record)}>{record.Title}</a>;
      },
    },
    {
      title: '解决方案名称',
      dataIndex: 'SolutionName',
      width: '10%',
      search: false,
    },
    {
      title: '解决方案版本',
      dataIndex: 'SolutionVersion',
      width: '10%',
      search: false,
    },
    {
      title: '架构',
      dataIndex: 'Arch',
      search: false,
      width: '5%',
    },
    {
      title: '关联应用个数',
      dataIndex: 'ApplicationNum',
      search: false,
      width: '7%',
      render(text: any, record: any) {
        return record.IssueAppList?.length || 0;
      },
    },
    {
      title: '待更新应用个数',
      dataIndex: 'UpdateApplicationNum',
      search: false,
      width: '7%',
      render(text: any, record: any) {
        const list = (record.IssueAppList || []).filter(
          (item) =>
            !item.ReleasePackage ||
            !item.PushedPackage ||
            getAppVersion({
              ...item.ReleasePackage,
              Arch: record.Arch,
            }) !==
              getAppVersion({
                ...item.PushedPackage,
                Arch: record.Arch,
              }),
        );
        return list.length;
      },
    },
    issueType === 'bug' && {
      title: '出包类型',
      dataIndex: 'DeliverType',
      search: true,
      valueType: 'select',
      fieldProps: {
        options:
          lookups.DeliverType?.map((item) => ({
            value: item.Code,
            label: item.Name,
          })) || [],
      },
      width: '5%',
      render(text: any, record: any) {
        const { DeliverType } = record;
        const lookup = getLookupByCode('DeliverType', DeliverType);
        if (lookup) {
          return <TcsTag color={lookup.Extra?.Color}>{lookup.Name}</TcsTag>;
        }
        return DeliverType || '-';
      },
    },
    issueType === 'bug' && {
      dataIndex: 'SeverityLevel',
      title: '严重程度',
      search: false,
      width: '5%',
      render(text: any, record: any) {
        const { SeverityLevel } = record;
        const lookup = getLookupByCode('Severity', SeverityLevel);
        if (lookup) {
          return <TcsTag color={lookup.Extra?.Color}>{lookup.Name}</TcsTag>;
        }
        return SeverityLevel || '-';
      },
    },
    {
      dataIndex: 'Priority',
      title: '优先级',
      width: '5%',
      search: false,
      render(text: string, record: any) {
        const { Priority } = record;
        const lookup = getLookupByCode('Priority', Priority);
        if (lookup) {
          return <TcsTag color={lookup.Extra?.Color}>{lookup.Name}</TcsTag>;
        }
        return Priority || '-';
      },
    },
    {
      dataIndex: 'Status',
      title: '修复状态',
      search: false,
      width: '5%',
      render(text: string, record: any) {
        const { Status } = record;
        const lookup = getLookupByCode('IssueSolutionArchStatus', Status);
        if (lookup) {
          return <TcsTag color={lookup.Extra?.Color}>{lookup.Name}</TcsTag>;
        }
        return Status || '-';
      },
    },
    // {
    //   dataIndex: 'Owner',
    //   title: '处理人',
    //   search: false,
    //   width: '10%',
    // },
    // {
    //   dataIndex: 'Creator',
    //   title: '创建人',
    //   width: '5%',
    //   search: false,
    // },
    {
      dataIndex: 'TimeFrame',
      title: '时间范围',
      valueType: 'dateRange',
      hideInTable: true,
      initialValue: [
        dayjs()
          .set('month', new Date().getMonth() - 1)
          .format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
    },
    {
      dataIndex: 'IsSameSolutionVersion',
      title: '仅显示当前解决方案关联缺陷',
      width: '5%',
      hideInTable: true,
      initialValue: true,
      valueType: 'switch',
    },
    {
      dataIndex: 'option',
      title: '操作',
      width: '8%',
      fixed: 'right',
      valueType: 'option',
      render(text, record) {
        return (
          <>
            <Button type="link" onClick={() => onViewAppInfo(record)}>
              查看应用详情
            </Button>
            {record?.TAPDUrl && (
              <Button
                type="link"
                onClick={() => {
                  window.open(record.TAPDUrl, '_blank');
                }}
              >
                跳转至Tapd
              </Button>
            )}
          </>
        );
      },
    },
  ].filter((item) => item) as any[];

export const getAppDiffColumns = () => [
  {
    title: '应用名',
    dataIndex: 'ApplicationName',
    width: '26%',
  },
  {
    title: '缺陷单应用版本',
    dataIndex: 'ReleasePackageVersion',
    width: '32%',
    render(text: string, record: any) {
      if (record.ReleasePackageVersion !== record.DeployedPackageVersion) {
        return <span style={{ color: 'red' }}>{text}</span>;
      }
      return text;
    },
  },
  {
    title: '局点已出包版本',
    dataIndex: 'DeployedPackageVersion',
    width: '32%',
    render(text: string, record: any) {
      if (record.ReleasePackageVersion !== record.DeployedPackageVersion) {
        return <span style={{ color: 'red' }}>{text}</span>;
      }
      return text;
    },
  },
];
