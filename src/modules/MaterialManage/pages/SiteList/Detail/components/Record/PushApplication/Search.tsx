import { ProForm, QueryFilter } from '@/common/components';
import { Card, Input } from '@tencent/tea-component';
import React from 'react';

export interface IProps {
  onSearch: (value: any) => void;
}

const Search: React.FC<IProps> = ({ onSearch }) => (
  <Card bordered>
    <Card.Body>
      <QueryFilter onSearch={onSearch}>
        <ProForm.Item label="应用名称" dataIndex="ApplicationName">
          <Input />
        </ProForm.Item>
      </QueryFilter>
    </Card.Body>
  </Card>
);
export default Search;
