import React from 'react';
import { But<PERSON>, Popover, Tag } from '@tencent/tea-component';
import dayjs from 'dayjs';

export const getColumns = ({
  onDownloadMaterial,
  onUpdateDeployStatus,
  onOfflineDeploy,
  onDownloadOriginMaterial,
  onCopyOriginMaterialUrl,
  getLookupByCode,
}: {
  onDownloadMaterial: (record) => void;
  onUpdateDeployStatus: (record) => void;
  onOfflineDeploy: (record) => void;
  onDownloadOriginMaterial: (record) => void;
  onCopyOriginMaterialUrl: (record) => void;
  getLookupByCode: (type, code) => any;
}) => [
  {
    header: '物料名称',
    key: 'MaterialName',
    width: '15%',
  },
  {
    header: '架构',
    key: 'Arch',
  },
  {
    header: '版本',
    key: 'Version',
  },
  {
    header: '发布说明',
    key: 'ReleaseNotes',
    width: '10%',
  },
  {
    header: '推送时间',
    key: 'CreateAt',
    width: '10%',
    render(record) {
      return record.CreateAt ? dayjs(record.CreateAt).format('YYYY-MM-DD HH:mm:ss') : '-';
    },
  },
  {
    header: '推送人',
    key: 'Operator',
  },
  {
    header: '部署状态',
    key: 'Status',
    render(record) {
      if (record.Status) {
        const lookup = getLookupByCode('MaterialDeployStatus', record.Status);
        return lookup?.Name || '-';
      }
      return '-';
    },
  },
  {
    header: '部署时间',
    key: 'UpdateAt',
    width: '10%',
    render(record) {
      // 有UpdateAt字段且部署状态为已部署
      return record.UpdateAt && record.Status === 'deployed'
        ? dayjs(record.UpdateAt).format('YYYY-MM-DD HH:mm:ss')
        : '-';
    },
  },
  {
    header: '标签',
    key: 'labelNameList',
    width: '14%',
    render(record) {
      if (!record?.labelNameList || record.labelNameList.length === 0) {
        return '-';
      }

      function getTagEls(labelNameList) {
        return labelNameList.map((item) => (
          <Tag theme="primary" key={item} style={{ marginRight: 5 }}>
            {item}
          </Tag>
        ));
      }

      if (record.labelNameList.length <= 5) {
        return getTagEls(record.labelNameList);
      }
      return <Popover overlay={getTagEls(record.labelNameList)}>{getTagEls(record.labelNameList.slice(0, 5))}</Popover>;
    },
  },
  {
    header: '操作人',
    key: 'DeployOperator',
  },
  {
    header: '操作',
    key: 'Operation',
    width: '15%',
    fixed: 'right',
    render(record) {
      return (
        <>
          <Button
            type="link"
            onClick={() => {
              onDownloadMaterial(record);
            }}
          >
            下载
          </Button>
          <Button
            type="link"
            onClick={() => {
              onUpdateDeployStatus(record);
            }}
          >
            修改部署状态
          </Button>
          <Button
            type="link"
            onClick={() => {
              onOfflineDeploy(record);
            }}
          >
            离线部署
          </Button>

          <Button
            type="link"
            onClick={() => {
              onDownloadOriginMaterial(record);
            }}
          >
            下载原始物料
          </Button>
          <Button
            type="link"
            onClick={() => {
              onCopyOriginMaterialUrl(record);
            }}
          >
            复制原始物料下载链接
          </Button>
        </>
      );
    },
  },
];
