import { ProForm, QueryFilter } from '@/common/components';
import useLookup from '@/common/hookups/useLookup';
import { Card, Input, Select } from '@tencent/tea-component';
import React from 'react';

export interface IProps {
  onSearch: (values: any) => void;
}

const Search: React.FC<IProps> = ({ onSearch }) => {
  const { lookups } = useLookup(['PackageArch', 'MaterialPublishStatus']);

  function handleSearch(values) {
    onSearch(values);
  }

  return (
    <Card bordered>
      <Card.Body>
        <QueryFilter onSearch={handleSearch}>
          <ProForm.Item label="物料名称" dataIndex="Name">
            <Input placeholder="请输入物料名称" />
          </ProForm.Item>

          <ProForm.Item label="架构" dataIndex="Arch">
            <Select
              appearance="button"
              placeholder="请选择架构"
              size="m"
              matchButtonWidth
              clearable
              options={
                lookups?.PackageArch?.map((item) => ({
                  value: item.Code,
                  text: item.Name,
                })) || []
              }
            />
          </ProForm.Item>
          <ProForm.Item label="版本" dataIndex="Version">
            <Input placeholder="请输入版本" />
          </ProForm.Item>
        </QueryFilter>
      </Card.Body>
    </Card>
  );
};

export default Search;
