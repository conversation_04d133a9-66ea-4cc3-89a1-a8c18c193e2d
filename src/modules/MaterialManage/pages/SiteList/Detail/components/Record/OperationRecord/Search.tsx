import { ProForm, QueryFilter } from '@/common/components';
// import useLookup from '@/common/hookups/useLookup';
import { Card, Input } from '@tencent/tea-component';
import React from 'react';

export interface IProps {
  onSearch: (values: any) => void;
}

const Search: React.FC<IProps> = ({ onSearch }) => {
  function handleSearch(values) {
    onSearch(values);
  }

  return (
    <Card bordered>
      <Card.Body>
        <QueryFilter onSearch={handleSearch}>
          <ProForm.Item label="ID" dataIndex="ID">
            <Input placeholder="请输入ID" />
          </ProForm.Item>

          <ProForm.Item label="流程名称" dataIndex="WorkflowName">
            <Input placeholder="请输入版本" />
          </ProForm.Item>
        </QueryFilter>
      </Card.Body>
    </Card>
  );
};

export default Search;
