import { Card, Table, message } from '@tencent/tea-component';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { getColumns } from './config';
import { downloadMaterialPackage, downloadOriginalPackage, getSiteMaterialList } from '@/common/api/materialManage';
import Search from './Search';
import OfflineDeployModal from '../OfflineDeployModal';
import UpdateDeployStatusModal from '../UpdateDeployStatusModal';
import useLookup from '@/common/hookups/useLookup';
import { unstable_batchedUpdates } from 'react-dom';
import { copyText } from '@/common/utils';

export interface IProps {
  siteUUID: string;
  materialType: string;
  data?: any;
}

const List: React.ForwardRefRenderFunction<any, IProps> = ({ siteUUID, materialType, data }, ref) => {
  const offlineDeployModal = useRef<any>(null);
  const updateDeployStatusModal = useRef<any>(null);
  const { getLookupByCode } = useLookup([]);
  const columns = getColumns({
    onCopyOriginMaterialUrl: handleCopyOriginMaterialUrl,
    onDownloadMaterial: handleDownloadMaterial,
    onDownloadOriginMaterial: handleDownloadOriginMaterial,
    onOfflineDeploy: handleOfflineDeploy,
    onUpdateDeployStatus: handleUpdateDeployStatus,
    getLookupByCode,
  });
  const [records, setRecords] = useState<any[]>([]);
  const [filterParams, setFilterParams] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const [update, setUpdate] = useState({});
  const [pageInfo, setPageInfo] = useState({
    pageIndex: 1,
    pageSize: 10,
    recordCount: 0,
  });

  useEffect(() => {
    if (data) {
      setLoading(true);
      getSiteMaterialList({
        SiteUUID: siteUUID,
        Limit: pageInfo.pageSize,
        Offset: (pageInfo.pageIndex - 1) * pageInfo.pageSize,
        Type: materialType,
        ProductVersionUUID: data.ProductVersionUUID,
        SolutionVersionUUID: data.SolutionVersionUUID,
        ...filterParams,
      })
        .then((res) => {
          if (res.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            setRecords(res.MaterialList || []);
            setPageInfo((info) => ({
              ...info,
              recordCount: res.Total || 0,
            }));
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [filterParams, pageInfo.pageIndex, pageInfo.pageSize, siteUUID, data, materialType, update]);

  function handleCopyOriginMaterialUrl(record) {
    setLoading(true);
    downloadOriginalPackage({
      MaterialInfoUUID: record.MaterialInfoUUID,
      // ProductVersionUUID: data.ProductVersionUUID,
      // SolutionVersionUUID: data.SolutionVersionUUID,
    })
      .then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          // const { DownloadUrl } = res;
          const u = new URL(res.DownloadUrl);
          u.protocol = window.location.protocol;
          const url = u.toString();
          copyText(url, '原始物料复制成功');
          // copyText(`${window.location.protocol}//registry.jiguang.woa.com${DownloadUrl}`, '原始物料下载链接复制成功');
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }
  function handleDownloadMaterial(record) {
    setLoading(true);
    downloadMaterialPackage({
      MaterialInfoUUID: record.MaterialInfoUUID,
      ProductVersionUUID: data.ProductVersionUUID,
      SolutionVersionUUID: data.SolutionVersionUUID,
    })
      .then((res) => {
        // Aurora报错信息会放到Error里面
        if (res.Error) {
          // 弹出错误提示
          message.error({
            content: res.Error.Message,
          });
        } else {
          const { DownloadURL } = res;
          if (DownloadURL) {
            window.open(`//registry.jiguang.woa.com${DownloadURL}`, '_blank');
          }
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }
  function handleDownloadOriginMaterial(record) {
    setLoading(true);
    downloadOriginalPackage({
      MaterialInfoUUID: record.MaterialInfoUUID,
      // ProductVersionUUID: data.ProductVersionUUID,
      // SolutionVersionUUID: data.SolutionVersionUUID,
    })
      .then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          const u = new URL(res.DownloadUrl);
          u.protocol = window.location.protocol;
          const url = u.toString();
          window.open(url, '_blank');
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }
  function handleOfflineDeploy() {
    offlineDeployModal.current.show();
  }
  function handleUpdateDeployStatus(record) {
    updateDeployStatusModal.current.show(record);
  }

  useImperativeHandle(
    ref,
    () => ({
      refresh() {
        setUpdate({});
      },
    }),
    [],
  );

  return (
    <>
      <Search
        onSearch={(values) => {
          unstable_batchedUpdates(() => {
            setPageInfo((info) => ({
              ...info,
              pageIndex: 1,
            }));
            setFilterParams(values);
          });
        }}
      />
      <Card bordered>
        <Card.Body>
          <Table
            columns={columns}
            records={records}
            addons={[
              Table.addons.pageable({
                ...pageInfo,
                onPagingChange: (query) =>
                  setPageInfo((info) => ({
                    ...info,
                    ...query,
                  })),
              }),
              Table.addons.autotip({
                isLoading: loading,
              }),
              Table.addons.scrollable({
                minWidth: 2000,
              }),
            ]}
          />
          <OfflineDeployModal ref={offlineDeployModal} />
          <UpdateDeployStatusModal
            ref={updateDeployStatusModal}
            siteUUID={siteUUID}
            treeData={data}
            onConfirm={() => {
              setUpdate({});
            }}
          />
        </Card.Body>
      </Card>
    </>
  );
};

export default forwardRef(List);
