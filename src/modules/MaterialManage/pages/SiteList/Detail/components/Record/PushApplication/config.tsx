import { getAppVersion } from '@/common/utils';
import { TcsColumns } from '@tencent/tcs-component';

export const getColumns = () =>
  [
    {
      title: '应用名称',
      dataIndex: 'ApplicationName',
    },
    {
      title: '制品版本',
      dataIndex: 'ApplicationVersion',
      renderText(text, record) {
        return getAppVersion(record);
      },
      search: false,
    },

    {
      title: '推送时间',
      dataIndex: 'PushedTime',
      valueType: 'dateTime',
      search: false,
      // render(record) {
      //   return record.PushedTime ? dayjs(record.PushedTime).format('YYYY-MM-DD HH:mm:ss') : '-';
      // },
    },
    {
      title: '推送人',
      dataIndex: 'Creator',
      search: false,
    },
  ] as TcsColumns[];
