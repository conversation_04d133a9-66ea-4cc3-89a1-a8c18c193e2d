import { Card, Table, message } from '@tencent/tea-component';
import React, { useEffect, useState } from 'react';
import { getColumns } from './config';
import { listOperationHistories } from '@/common/api/materialManage';
import Search from './Search';
import { unstable_batchedUpdates } from 'react-dom';

export interface IProps {
  siteUUID: string;
}

const OperationRecord: React.FC<IProps> = ({ siteUUID }) => {
  const columns = getColumns();
  const [records, setRecords] = useState<any[]>([]);
  const [filterParams, setFilterParams] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const [pageInfo, setPageInfo] = useState({
    pageIndex: 1,
    pageSize: 10,
    recordCount: 0,
  });

  useEffect(() => {
    setLoading(true);
    listOperationHistories({
      SiteUUID: siteUUID,
      current: pageInfo.pageIndex,
      pageSize: pageInfo.pageSize,
      ...filterParams,
    })
      .then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          setRecords(res.ListOperationHistories || []);
          setPageInfo((info) => ({
            ...info,
            recordCount: res.Total || 0,
          }));
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }, [filterParams, pageInfo.pageIndex, pageInfo.pageSize, siteUUID]);

  return (
    <>
      <Search
        onSearch={(values) => {
          unstable_batchedUpdates(() => {
            setPageInfo((info) => ({
              ...info,
              pageIndex: 1,
            }));
            setFilterParams(values);
          });
        }}
      />
      <Card bordered>
        <Card.Body>
          <Table
            columns={columns}
            records={records}
            addons={[
              Table.addons.pageable({
                ...pageInfo,
                onPagingChange: (query) =>
                  setPageInfo((info) => ({
                    ...info,
                    ...query,
                  })),
              }),
              Table.addons.autotip({
                isLoading: loading,
              }),
              Table.addons.scrollable({
                minWidth: 1400,
              }),
            ]}
          />
        </Card.Body>
      </Card>
    </>
  );
};

export default OperationRecord;
