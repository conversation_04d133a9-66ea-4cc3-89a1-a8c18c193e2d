import { TcsLayout, TcsTable } from '@tencent/tcs-component';
import React, { useMemo, useRef } from 'react';
import { getColumns } from './config';
import useLookup from '@/common/hookups/useLookup';
import { listSiteUnresolvedIssues } from '@/common/api/materialManage';
import { toListParamsCApi } from '@/common/api/api';
import { message } from '@tencent/tea-component';
import { withRouteBasename } from '@/common/routePath';
import ViewAppInfo from './ViewAppInfo';
import dayjs from 'dayjs';

export interface IProps {
  siteUUID: string;
  issueType: 'bug' | 'story';
}

const AssociatedDefect: React.FC<IProps> = ({ siteUUID, issueType }) => {
  const { getLookupByCode, lookups } = useLookup(['DeliverType']);

  const viewAppInfoRef = useRef<any>();
  const { history } = TcsLayout.useHistory();
  const columns = useMemo(
    () =>
      getColumns({
        getLookupByCode,
        onDetail: handleViewDefectDetail,
        onViewAppInfo: handleViewInfo,
        lookups,
        issueType,
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [getLookupByCode, issueType],
  );
  function handleViewDefectDetail(record: any) {
    // if (issueType === 'bug') {
    history.push(
      `${withRouteBasename(`/iteration_manage/${issueType === 'bug' ? 'defect' : 'story'}/detail`)}?issue_id=${
        record.IssueID
      }`,
    );
    // } else {
    //   history.push(`${withRouteBasename(`/iteration_manage/story/detail`)}?issue_id=${record.IssueID}`);
    // }
  }

  function handleViewInfo(record: any) {
    viewAppInfoRef.current?.show(record);
  }

  function handleRequest(params: any) {
    const { pageSize, current, TimeFrame, IsSameSolutionVersion, ...rest } = params;

    return listSiteUnresolvedIssues({
      PageSize: pageSize,
      PageNo: current,
      StartTime: TimeFrame?.[0] ? `${dayjs(TimeFrame?.[0]).format('YYYY-MM-DD')} 00:00:00` : undefined,
      EndTime: TimeFrame?.[1] ? `${dayjs(TimeFrame?.[1]).format('YYYY-MM-DD')} 23:59:59` : undefined,
      SiteUUID: siteUUID,
      IssueType: issueType,
      IsSameSolutionVersion,
      Filters:
        toListParamsCApi(
          {
            ...rest,
          },
          {
            useEqFields: ['SiteUUID', 'DeliverType', 'IsSameSolutionVersion'],
            retainEmptyString: false,
          },
        )._Filter || [],
    }).then((res) => {
      if (res.Error) {
        message.error({
          content: res.Error.Message,
        });
        return {
          success: false,
          message: res.Error.Message,
        };
      }
      return {
        success: true,
        data: res.Issues,
        total: res.Total,
      };
    });
  }

  return (
    <>
      <TcsTable
        columns={columns}
        search={{
          labelWidth: 90,
        }}
        cardBordered
        scrollInTable
        request={handleRequest}
        scroll={{
          x: 1800,
        }}
        pagination={{}}
      />
      <ViewAppInfo ref={viewAppInfoRef} />
    </>
  );
};

export default AssociatedDefect;
