import { message } from '@tencent/tea-component';
import React, { useEffect, useState } from 'react';
import { getColumns } from './config';
import { getOperationPushedPackageDetail } from '@/common/api/materialManage';

import { TcsTable } from '@tencent/tcs-component';

import { TcscExcelExportButton, TcscExcelExportWrap } from '@tencent/tcsc-base';
export interface IProps {
  siteUUID: string;
}

const PublishApplication: React.FC<IProps> = ({ siteUUID }) => {
  const columns = getColumns();
  const [records, setRecords] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  // const [filterParams, setFilterParams] = useState<any>({});
  // const [pageInfo, setPageInfo] = useState({
  //   pageIndex: 1,
  //   pageSize: 10,
  // });

  useEffect(() => {
    setLoading(true);
    getOperationPushedPackageDetail({
      SiteUUID: siteUUID,
      TagType: 'application-pushed',
    })
      .then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          const { CreatedAt, TagDetail, Creator } = res.GetOperationPushedPackage?.OperationPublicTag || {};
          const records =
            (TagDetail?.ApplicationList || []).map((item) => ({
              ...item,
              CreatedAt,
              Creator,
            })) || [];
          setRecords(records);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }, [siteUUID]);

  // function handleSearch(values: any) {
  //   setFilterParams(values);
  //   setPageInfo((info) => ({
  //     ...info,
  //     pageIndex: 1,
  //   }));
  // }

  // const tableData = useMemo(() => {
  //   if (filterParams?.ApplicationName) {
  //     return records.filter((item) => item.ApplicationName.includes(filterParams.ApplicationName));
  //   }

  //   return records;
  // }, [filterParams, records]);

  function handleRequest(params) {
    let data = records;
    if (params?.ApplicationName) {
      data = records.filter((item) => item.ApplicationName.includes(params.ApplicationName));
    }
    return Promise.resolve({
      data,
      total: data.length,
    });
  }

  return (
    <>
      {/* <Search onSearch={handleSearch} /> */}
      <TcscExcelExportWrap>
        <TcsTable
          columns={columns}
          params={{
            records,
          }}
          search={{}}
          request={handleRequest}
          loading={loading}
          options={false}
          cardBordered
          pagination={{}}
          toolBarRender={() => <TcscExcelExportButton fieldName="已推送应用" />}
        />
      </TcscExcelExportWrap>
    </>
  );
};

export default PublishApplication;
