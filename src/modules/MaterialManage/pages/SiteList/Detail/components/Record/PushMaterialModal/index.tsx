import { ProModal } from '@/common/components';
import MainPage from '@/modules/MaterialManage/pages/Material/components/MainPage';
import React, { useImperativeHandle, useState } from 'react';

export interface IProps {
  onConfirm: () => void;
}

const PushMaterialModal: React.ForwardRefRenderFunction<any, IProps> = ({ onConfirm }, ref) => {
  const [visible, setVisible] = useState(false);
  const [siteUUID, setSiteUUID] = useState<string>();

  useImperativeHandle(
    ref,
    () => ({
      show(siteUUID: string) {
        setVisible(true);
        setSiteUUID(siteUUID);
      },
    }),
    [],
  );

  function handleCancel() {
    setVisible(false);
    onConfirm();
  }

  return (
    <ProModal visible={visible} title="推送物料到此局点" size={1400} onCancel={handleCancel} footer={false}>
      <div style={{ height: 600 }}>
        <MainPage siteUUID={siteUUID} />
      </div>
    </ProModal>
  );
};

export default React.forwardRef(PushMaterialModal);
