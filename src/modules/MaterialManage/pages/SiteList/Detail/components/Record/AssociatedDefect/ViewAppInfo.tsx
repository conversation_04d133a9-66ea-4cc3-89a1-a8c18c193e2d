import { TcsModal, TcsTable } from '@tencent/tcs-component';
import { Button } from '@tencent/tea-component';
import React, { useImperativeHandle, useMemo, useState } from 'react';
import { getAppDiffColumns } from './config';
import { getAppVersion } from '@/common/utils';

const ViewAppInfo: React.ForwardRefRenderFunction<any, any> = ({}, ref) => {
  const [visible, setVisible] = useState(false);
  const [record, setRecord] = useState<any>();

  useImperativeHandle(
    ref,
    () => ({
      show(record) {
        setRecord(record);
        setVisible(true);
      },
    }),
    [],
  );

  function handleCancel() {
    setVisible(false);
  }

  const records = useMemo(() => {
    if (!record?.IssueAppList) {
      return [];
    }
    return record.IssueAppList.map((item) => ({
      ApplicationName: item.ApplicationName,
      ReleasePackageVersion: item.ReleasePackage
        ? getAppVersion({
            ...item.ReleasePackage,
            Arch: record.Arch,
          })
        : '-',
      DeployedPackageVersion: item.PushedPackage
        ? getAppVersion({
            ...item.PushedPackage,
            Arch: record.Arch,
          })
        : '-',
    }));
  }, [record]);

  return (
    <TcsModal
      title="局点关联应用版本对比"
      visible={visible}
      width={1000}
      onCancel={handleCancel}
      footer={<Button onClick={handleCancel}>取消</Button>}
    >
      <TcsTable columns={getAppDiffColumns()} dataSource={records} options={false} />
    </TcsModal>
  );
};

export default React.forwardRef(ViewAppInfo);
