import { updateSiteMaterialStatus } from '@/common/api/materialManage';
import { ProForm, ProModal } from '@/common/components';
import useLookup from '@/common/hookups/useLookup';
import { Form, Select, message } from '@tencent/tea-component';
import React, { useImperativeHandle, useRef, useState } from 'react';

export interface IProps {
  siteUUID: string;
  treeData: any;
  onConfirm: () => void;
}

const OfflineDeployModal: React.ForwardRefRenderFunction<any, IProps> = ({ siteUUID, treeData, onConfirm }, ref) => {
  const [visible, setVisible] = useState(false);
  const { lookups, getLookupByCode } = useLookup(['MaterialDeployStatus']);
  const [record, setRecord] = useState<any>();
  const [loading, setLoading] = useState(false);
  const formRef = useRef<any>(null);

  useImperativeHandle(
    ref,
    () => ({
      show(record: any) {
        setVisible(true);
        setRecord(record);
      },
    }),
    [],
  );

  function handleOk() {
    formRef.current.validateFields().then((values) => {
      setLoading(true);
      updateSiteMaterialStatus({
        Status: values.Status,
        SiteUUID: siteUUID,
        ProductVersionUUID: treeData?.ProductVersionUUID,
        SolutionVersionUUID: treeData?.SolutionVersionUUID,
        MaterialInfoUUID: record.MaterialInfoUUID,
      })
        .then((res) => {
          if (res.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            onConfirm();
            setVisible(false);
            formRef.current.resetFields();
          }
        })
        .finally(() => {
          setLoading(false);
        });
    });
  }

  function handleCancel() {
    setVisible(false);
    formRef.current.resetFields();
  }

  return (
    <ProModal visible={visible} title="更新部署状态" onOk={handleOk} onCancel={handleCancel} confirmLoading={loading}>
      <ProForm formRef={formRef}>
        <ProForm.Item label="包名称" dataIndex="MaterialName">
          <Form.Text>{record?.MaterialName}</Form.Text>
        </ProForm.Item>
        <ProForm.Item label="当前状态" dataIndex="currentStatus">
          <Form.Text>{record?.Status ? getLookupByCode('MaterialDeployStatus', record.Status)?.Name : '-'}</Form.Text>
        </ProForm.Item>
        <ProForm.Item
          label="目标状态"
          dataIndex="Status"
          rules={{
            required: {
              value: true,
              message: '请选择目标状态',
            },
          }}
        >
          <Select
            appearance="button"
            placeholder="请选择目标状态"
            size="m"
            matchButtonWidth
            clearable
            options={
              lookups?.MaterialDeployStatus?.map((item) => ({
                value: item.Code,
                text: item.Name,
              })) || []
            }
          />
        </ProForm.Item>
      </ProForm>
    </ProModal>
  );
};

export default React.forwardRef(OfflineDeployModal);
