import ProductTree from '@/modules/MaterialManage/pages/Material/components/ProductTree';
import SolutionTree from '@/modules/MaterialManage/pages/Material/components/SolutionTree';
import { Button, Card, Layout, TabPanel, Tabs } from '@tencent/tea-component';
import React, { useEffect, useRef, useState } from 'react';
import List from './List';
import PushMaterialModal from '../PushMaterialModal';

const tabs = [
  {
    id: 'product',
    label: '产品版本',
  },
  {
    id: 'solution',
    label: '解决方案版本',
  },
];

const materialTabs = [
  {
    id: 'install',
    label: '安装包',
  },
  {
    id: 'fix',
    label: '补充包',
  },
];

export interface IProps {
  siteUUID: string;
}

const Material: React.FC<IProps> = ({ siteUUID }) => {
  const [selectedTreeData, setSelectedTreeData] = useState<any>();
  const [activeTab, setActiveTab] = useState('product');
  const pushMaterialModal = useRef<any>();

  const installTableRef = useRef<any>();
  const fixTableRef = useRef<any>();

  function handleSelectTreeNode(data: any, type: 'solution' | 'product') {
    setSelectedTreeData({
      type,
      data,
    });
  }

  useEffect(() => {}, []);

  function handleAddMaterial() {
    pushMaterialModal.current.show(siteUUID);
  }

  function handlePushSuccess() {
    installTableRef.current?.refresh();
    fixTableRef.current?.refresh();
  }

  return (
    <Layout style={{ maxWidth: '100%' }}>
      <Layout.Body>
        <Layout.Sider style={{ width: 300 }}>
          <Card bordered>
            <Card.Body>
              <Tabs tabs={tabs} activeId={activeTab} onActive={(tab) => setActiveTab(tab.id)}>
                <TabPanel id="product">
                  <ProductTree
                    isActive={activeTab === 'product'}
                    onSelect={(data) => handleSelectTreeNode(data, 'product')}
                    productSelectable={false}
                    siteUUID={siteUUID}
                  />
                </TabPanel>
                <TabPanel id="solution">
                  <SolutionTree
                    isActive={activeTab === 'solution'}
                    onSelect={(data) => handleSelectTreeNode(data, 'solution')}
                    solutionSelectable={false}
                    siteUUID={siteUUID}
                  />
                </TabPanel>
              </Tabs>
            </Card.Body>
          </Card>
        </Layout.Sider>
        <Layout.Content style={{ backgroundColor: '#ffffff', paddingLeft: 10 }}>
          <Card style={{ width: '100%' }} bordered>
            <Card.Body>
              <Tabs
                tabs={materialTabs}
                addon={
                  <Button type="link" onClick={handleAddMaterial}>
                    添加物料
                  </Button>
                }
              >
                <TabPanel id="install">
                  <List materialType="install" siteUUID={siteUUID} data={selectedTreeData?.data} />
                </TabPanel>
                <TabPanel id="fix">
                  <List materialType="fix" siteUUID={siteUUID} data={selectedTreeData?.data} />
                </TabPanel>
              </Tabs>
            </Card.Body>
          </Card>
          <PushMaterialModal ref={pushMaterialModal} onConfirm={handlePushSuccess} />
        </Layout.Content>
      </Layout.Body>
    </Layout>
  );
};

export default Material;
