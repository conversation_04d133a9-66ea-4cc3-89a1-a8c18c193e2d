import { message } from '@tencent/tea-component';
import React, { useState, useEffect, useMemo, useRef } from 'react';
import { SiteStatus } from '../../../constant';
import { TcsSchemaForm, TcsCard, TcsButtonGroup } from '@tencent/tcs-component';
import useLookup from '@/common/hookups/useLookup';
import { GeneratedApi } from '@/common/api/materialManage';

export interface IProps {
  siteInfo: any;
}

const SiteInfo: React.FC<IProps> = ({ siteInfo }) => {
  const { lookups } = useLookup(['OperationSiteVariable']);
  const formRef = useRef<any>();
  const [columns, setColumns] = useState([]);
  // 存储当前表单值的状态
  const [formValues, setFormValues] = useState({});
  // 存储原始表单值的状态
  const [originalValues, setOriginalValues] = useState({});
  // 切换编辑模式
  const [isEdit, setIsEdit] = useState(false);
  // 检查是否所有lookup数据加载完成;
  const allLookupsLoaded = useMemo(() => lookups.OperationSiteVariable, [lookups]);

  useEffect(() => {
    if (!siteInfo) return;
    // 默认不可编辑的列
    const defaultColumns = [
      {
        title: '客户',
        dataIndex: 'ClientName',
        fieldProps: {
          readonly: true,
        },
      },
      {
        title: '局点',
        dataIndex: 'SiteName',
        fieldProps: {
          readonly: true,
        },
      },
      {
        title: '状态',
        dataIndex: 'Status',
        fieldProps: {
          readonly: true,
        },
      },
    ];
    // 获取表单初始值
    const getInitialValue = (column, siteInfo) => {
      if (column.dataIndex === 'Status') {
        return SiteStatus[siteInfo[column.dataIndex]] || '-';
      }
      return siteInfo[column.dataIndex] || siteInfo.SiteVariableMap[column.dataIndex] || column.initialValue || '';
    };
    const operationSiteVariables = lookups.OperationSiteVariable || [];
    // 增加从字典中获取自定义属性
    const defaultChangeColumns = defaultColumns.map((item) => ({
      formItemProps: {
        colProps: {
          xs: 12,
          sm: 12,
          lg: 8,
          xxl: 6,
        },
      },
      ...item,
    }));

    const additionalColumns =
      operationSiteVariables?.length > 0
        ? operationSiteVariables.map((item) => ({
            formItemProps: {
              colProps: {
                xs: 12,
                sm: 12,
                lg: 8,
                xxl: 6,
              },
            },
            ...item?.Extra?.info,
            title: item.Name,
            dataIndex: item.Code,
            // siteInfo有值从siteInfo取，没值从默认值取
            initialValue: siteInfo?.SiteVariableMap[item.Code] || item?.Extra?.info?.initialValue || '',
          }))
        : [];

    // 合并默认列和额外列
    const allColumns = [...defaultChangeColumns, ...additionalColumns];
    setColumns(allColumns);
    // 根据列设置初始表单值
    const initialValues = allColumns.reduce((acc, column) => {
      acc[column.dataIndex] = getInitialValue(column, siteInfo);
      return acc;
    }, {});
    setFormValues(initialValues);
    setOriginalValues(initialValues);
  }, [siteInfo, allLookupsLoaded]);

  // 处理表单值变化
  const handleFormChange = (changedValues) => {
    setFormValues((prevValues) => ({
      ...prevValues,
      ...changedValues,
    }));
  };
  // 处理请求参数，转换开关的布尔值为字符串
  const preparePayload = (formValues, columns) => {
    const diff = Object.keys(formValues).reduce((acc, key) => {
      if (formValues[key] !== originalValues[key]) {
        const column = columns.find((col) => col.dataIndex === key);
        if (column?.valueType === 'switch') {
          acc[key] = formValues[key] ? 'true' : 'false';
        } else {
          acc[key] = formValues[key];
        }
      }
      return acc;
    }, {});

    return {
      SiteUUID: siteInfo.SiteUUID,
      SiteVariableList: Object.keys(diff).map((key) => ({
        Key: key,
        Value: diff[key],
      })),
    };
  };
  // 保存
  const handleSave = () => {
    formRef.current?.validateFields().then(() => {
      const payload = preparePayload(formValues, columns);
      GeneratedApi.SetSiteVariableInfo(payload)
        .then(() => {
          setOriginalValues(formValues);
          message.success({ content: '编辑成功' });
        })
        .finally(() => {
          setIsEdit(false);
        });
    });
  };
  // 取消
  const handleCancel = () => {
    setFormValues(originalValues);
    setIsEdit(false);
  };
  return (
    <TcsCard
      collapsible
      title="基本信息"
      style={{ width: '100%' }}
      extra={
        <TcsButtonGroup
          items={[
            {
              text: '编辑',
              onClick: () => {
                setIsEdit(true);
              },
              hidden: isEdit,
            },
            {
              text: '确定',
              hidden: !isEdit,
              confirm: true,
              confirmProps: {
                title: '确认编辑？',
                onConfirm: () => handleSave(),
              },
            },
            {
              text: '取消',
              onClick: () => handleCancel(),
              hidden: !isEdit,
            },
          ]}
        />
      }
    >
      <TcsSchemaForm
        formRef={formRef}
        columns={columns}
        grid={true}
        readonly={!isEdit}
        onValuesChange={handleFormChange}
        initialValues={formValues}
        key={isEdit ? 'edit-mode' : 'view-mode'} // 强制重新渲染
      />
    </TcsCard>
  );
};

export default SiteInfo;
