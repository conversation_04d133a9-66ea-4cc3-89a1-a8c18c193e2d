/*
 * @Author: super<PERSON>
 * @Date: 2023-05-22 14:39:58
 * @LastEditors: superfeng
 * @LastEditTime: 2023-05-22 19:12:48
 * @Description: 请输入注释信息
 */
import React from 'react';
import { TcsTable } from '@tencent/tcs-component';

export interface IProps {
  record: any;
}

const getColumns = ({}) => [
  {
    title: '局点信息库局点',
    dataIndex: 'EnglishName',
  },
  {
    title: '局点状态',
    dataIndex: 'Status',
  },
  {
    title: '安灯局点ID',
    dataIndex: 'AnDonID',
  },
  {
    title: '可用区ID',
    dataIndex: 'ZoneID',
  },
  {
    title: '可用区名称',
    dataIndex: 'AndonSiteName',
  },
];

const AndonSiteTable: React.FC<IProps> = ({ record }) => {
  const columns = getColumns({});
  return <TcsTable options={false} columns={columns} dataSource={record.AndonSiteInfos || []} />;
};

export default AndonSiteTable;
