import React from 'react';
import { Route } from 'react-router-dom';
import { MaterialManageRoutePath } from '@/common/routePath';
import SiteList from '../SiteList/List';
import SiteDetail from '../SiteList/Detail';
import EditSiteTag from '../SiteList/EditSiteTag';

const Index = () => {
  const routesConfig = [
    {
      component: SiteList,
      path: MaterialManageRoutePath.SITE_LIST_PAGE,
      exact: true,
    },
    {
      component: SiteDetail,
      path: MaterialManageRoutePath.SITE_DETAIL_PAGE,
      exact: true,
    },
    {
      component: EditSiteTag,
      path: MaterialManageRoutePath.EDIT_SITE_TAG,
      exact: true,
    },
  ];

  return (
    <>
      {routesConfig.map((item, index) => (
        <Route key={index} component={item.component} path={item.path} exact={item.exact} />
      ))}
    </>
  );
};

export default Index;
