import { MaterialManageRoutePath } from '@/common/routePath';
import { ProColumns } from '@tencent/tcs-component';
import { Button } from '@tencent/tea-component';
import React from 'react';

export function getSiteColumns({}): ProColumns[] {
  return [
    {
      width: '15%',
      dataIndex: 'SiteName',
      disable: true,
      title: '局点名称',
      linkable: true,
      linkProps: {
        linkUrl: (text, record) => `${MaterialManageRoutePath.SITE_DETAIL_PAGE}?site_uuid=${record.SiteUUID}`,
      },
    },
    {
      width: '15%',
      dataIndex: 'ClientName',
      title: '客户名称',
    },
    {
      width: '10%',
      dataIndex: 'AndonID',
      title: '安灯局点ID',
      render: (text, record) => {
        const andonIDList = record?.AndonSiteInfos?.reduce((acc, item) => {
          if (item?.AnDonID) {
            acc.add(item.AnDonID);
          }
          return acc;
        }, new Set());
        return andonIDList?.size ? [...andonIDList].join(',') : '-';
      },
    },
    {
      width: '10%',
      dataIndex: 'LTCID',
      title: '局点LTCID',
      render: (text, record) => {
        // 确保 record.LTCID 是一个数组并且长度不为零
        if (Array.isArray(record.LTCID) && record.LTCID.length) {
          // 使用 Set 去重，然后转换为数组并用逗号连接
          return Array.from(new Set(record.LTCID)).join(',');
        }
        return '-';
      },
    },
    {
      width: '8%',
      title: '局点类型',
      dataIndex: ['SiteVariable', 'SiteType'],
      search: false,
    },
    {
      width: '8%',
      title: '局点推包类型',
      dataIndex: ['SiteVariable', 'PushPackageType'],
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'PushPackageType',
        showType: 'tag',
      },
      search: false,
    },
    {
      width: '8%',
      title: '缺陷修复方式',
      dataIndex: ['SiteVariable', 'BugfixType'],
      search: false,
    },
    {
      width: '8%',
      title: '部署工具',
      dataIndex: ['SiteVariable', 'OperationTool'],
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'OperationTool',
        showType: 'tag',
      },
      search: false,
    },
    {
      width: '8%',
      title: '局点状态',
      dataIndex: 'Status',
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'ProjectsSiteStatus',
        showType: 'tag',
        mode: 'multiple',
        showSelectAll: true,
      },
    },
    {
      width: '8%',
      title: '安灯局点状态',
      dataIndex: 'AndonSiteStatus',
      hideInForm: true,
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'AndonSiteStatus',
        showType: 'tag',
        mode: 'multiple',
        showSelectAll: true,
      },
    },
    {
      dataIndex: 'operation',
      title: '操作',
      width: '10%',
      fixed: 'right',
      hideInSearch: true,
      valueType: 'option',
      render: (text, record) => (
        <Button
          type="link"
          onClick={() => {
            window.open(
              `/page/project_center_next/customer_list/edit/site/manage/?hideTab=true&siteName=${record?.SiteName}&siteId=${record?.SiteUUID}&clientId=${record?.ClientUUID}&ClientName=${record?.ClientName}&ClientID=${record?.ClientUUID}`,
              '_blank',
            );
          }}
        >
          跳转至项目局点
        </Button>
      ),
    },
  ];
}
