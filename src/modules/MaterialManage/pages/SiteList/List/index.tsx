/*
 * @Author: lucyfang
 * @Date: 2024-08-05 10:21:06
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-03-19 14:58:50
 * @Description: 请输入注释信息
 */
import React, { useRef } from 'react';
import { getSiteColumns } from './config';
import { TcsLayout, TcsTable, TcsActionType } from '@tencent/tcs-component';
import AndonSiteTable from '../Detail/components/AndonSiteTable';
import { siteListApi } from '@/common/api/siteList';

const SiteList = () => {
  const { history } = TcsLayout.useHistory();
  const actionRef = useRef<TcsActionType>();

  function handleRequest(queryParams: any, options: any) {
    const { current: pageIndex, pageSize, AndonID, LTCID, Status, AndonSiteStatus, ClientName, SiteName } = queryParams;
    return siteListApi.ListOperationSiteList(
      {
        ClientName,
        SiteName,
        PageNo: Number(pageIndex),
        PageSize: Number(pageSize),
        AndonID: Number(AndonID) || undefined,
        LTCID: Number(LTCID) || undefined,
        SiteStatus: Status,
        AndonSiteStatus: AndonSiteStatus,
      },
      options,
    );
  }

  return (
    <TcsLayout history={history} title="局点列表" customizeCard fullHeight>
      <TcsTable
        rowKey="SiteUUID"
        columns={getSiteColumns({})}
        search={{ labelWidth: 80 }}
        actionRef={actionRef}
        request={handleRequest}
        expandable={{
          rowExpandable: (record) => !!record.AndonSiteInfos?.length,
          expandedRowRender(record) {
            return <AndonSiteTable record={record} />;
          },
        }}
        columnsState={{
          persistenceKey: 'defect_manage/__flow-design/site_list',
        }}
        pagination={{}}
        scrollInTable
        scroll={{
          x: 1800,
        }}
      />
    </TcsLayout>
  );
};

export default SiteList;
