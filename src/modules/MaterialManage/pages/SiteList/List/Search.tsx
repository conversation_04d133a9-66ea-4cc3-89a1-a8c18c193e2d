import { ProForm, QueryFilter } from '@/common/components';
import { Card, Input } from '@tencent/tea-component';
import React from 'react';
export interface IProps {
  onSearch: (values: any) => void;
}

const Search: React.FC<IProps> = ({ onSearch }) => (
  <Card className="tcs-layout__table_search">
    <Card.Body>
      <QueryFilter onSearch={onSearch}>
        <ProForm.Item label="局点" dataIndex="SiteName">
          <Input />
        </ProForm.Item>
        <ProForm.Item label="客户" dataIndex="ClientName">
          <Input />
        </ProForm.Item>
      </QueryFilter>
    </Card.Body>
  </Card>
);

export default Search;
