import React, { useState, useRef } from 'react';
import { Select, Tag, Tooltip, PopConfirm, Text, message } from '@tencent/tea-component';
import { TcsButton, TcsSpace } from '@tencent/tcs-component';
import { IOptionSolutionArtifactBranch, IProductDictionary } from '../../type';
import { combineSources } from './util';
import styles from './index.module.scss';
import ArtifactBranchSelectTea from '@/modules/MaterialManage/pages/ArtifactBranchSelect/ArtifactBranchSelectTea';
interface ISetAllArtifactTagComponent {
  onAllSetOrigin: () => void;
  onAllSetInherit: (inheritBranch: string, inheritTag: string) => void;
  onChangeInheritTag: () => void;
  onChangeInheritBranch: (value: string) => void;
  inheritTag: string;
  fetchInheritTagLoading: boolean;
  dicts: { [key: string]: IProductDictionary[] };
  inheritBranch: string;
  branchOptions: IOptionSolutionArtifactBranch[];
  selectedRowKeys: string[];
}

const SetAllArtifactTagComponent = ({
  onAllSetOrigin,
  onAllSetInherit,
  onChangeInheritTag,
  onChangeInheritBranch,
  inheritTag,
  fetchInheritTagLoading,
  dicts,
  inheritBranch,
  branchOptions,
  selectedRowKeys,
}: ISetAllArtifactTagComponent) => {
  const [bubbleVisible, setBubbleVisible] = useState(false);
  const handleFinish = async () => {
    await onAllSetInherit(inheritBranch, inheritTag);
    setBubbleVisible(false);
  };
  const handleShowBubble = () => {
    if (!selectedRowKeys?.length) {
      message.warning({ content: '请先选中要修改的应用！' });
      return;
    }
    setBubbleVisible(true);
  };
  return (
    <TcsSpace size={'s'}>
      <Tooltip placement="top" title="所有组件都设置为上一个制品配套的版本">
        <TcsButton type="text" onClick={onAllSetOrigin}>
          <Tag theme="primary">原版本</Tag>
        </TcsButton>
      </Tooltip>
      <Tooltip placement="top" title="所有组件都设置为选中制品tag配套的版本（继承只对共同的组件生效）">
        <PopConfirm
          title="请选择继承的制品"
          visible={bubbleVisible}
          onVisibleChange={() => {
            if (!!bubbleVisible) {
              setBubbleVisible(false);
            }
          }}
          placement="top"
          className={styles.inheritWrap}
          message={
            <TcsSpace>
              <ArtifactBranchSelectTea
                placeholder="请选择分支"
                branchTypeDict={dicts?.ArtifactBranchType || []}
                value={inheritBranch}
                options={branchOptions}
                onChange={onChangeInheritBranch}
                showSearch={true}
                style={{ width: 180 }}
              />
              <Select
                appearance="button"
                options={
                  inheritBranch
                    ? branchOptions
                        ?.find((item) => item.value === inheritBranch)
                        ?.children?.map(({ label, value }) => ({ value: String(value), text: label })) || []
                    : []
                }
                placeholder="请选择制品tag"
                value={inheritTag}
                onChange={onChangeInheritTag}
                style={{ width: 180 }}
                listWidth={300}
              />
              <TcsButton
                onClick={handleFinish}
                type="primary"
                loading={fetchInheritTagLoading}
                disabled={!inheritBranch || !inheritTag}
              >
                继承
              </TcsButton>
            </TcsSpace>
          }
        >
          <TcsButton type="text" onClick={handleShowBubble}>
            <Tag theme="primary">继承版本</Tag>
          </TcsButton>
        </PopConfirm>
      </Tooltip>
    </TcsSpace>
  );
};
export const getVersionColumns: any = ({
  readonly,
  onAllSetOrigin,
  onAllSetInherit,
  onChangeInheritTag,
  inheritTag,
  inheritBranch,
  fetchInheritTagLoading,
  branchOptions,
  dicts,
  onChangeInheritBranch,
  applicationNameMap,
  onApplicationVersionChange,
  selectedRowKeys,
}) => [
  {
    title: '产品code',
    dataIndex: 'ProductCode',
    ellipsis: false,
    fixed: 'left',
  },
  {
    title: '应用code',
    dataIndex: 'ApplicationName',
    ellipsis: false,
    fixed: 'left',
  },
  {
    title: '应用代码分支',
    dataIndex: 'ApplicationBranchCode',
    ellipsis: false,
  },
  ...(readonly
    ? []
    : [
        {
          title: '原版本',
          dataIndex: 'ApplicationVersion',
          ellipsis: false,
          search: false,
          width: 280,
          render: (value, record) => {
            const { ApplicationVersion, ApplicationInstanceID } = record;
            const isSame = record?.ApplicationPackageID === applicationNameMap?.[ApplicationInstanceID]?.value;
            return (
              <Text
                className={isSame ? '' : styles.redFont}
                copyable={ApplicationVersion ? { text: ApplicationVersion } : undefined}
              >
                {ApplicationVersion || '-'}
              </Text>
            );
          },
        },
      ]),

  {
    title: (
      <div>
        <div>制品版本</div>
        {!readonly && (
          <SetAllArtifactTagComponent
            onAllSetOrigin={onAllSetOrigin}
            onChangeInheritTag={onChangeInheritTag}
            inheritTag={inheritTag}
            onAllSetInherit={onAllSetInherit}
            fetchInheritTagLoading={fetchInheritTagLoading}
            branchOptions={branchOptions}
            dicts={dicts}
            onChangeInheritBranch={onChangeInheritBranch}
            inheritBranch={inheritBranch}
            selectedRowKeys={selectedRowKeys}
          />
        )}
      </div>
    ),
    dataIndex: 'ApplicationPackageID',
    ellipsis: false,
    search: false,
    width: 300,
    render: (value, record) => {
      const { PackageVersionName, ApplicationInstanceID } = record;
      const curAppVersionInfo = applicationNameMap[ApplicationInstanceID];
      return readonly ? (
        <Text copyable={PackageVersionName ? { text: PackageVersionName } : undefined}>
          {PackageVersionName || '-'}
        </Text>
      ) : (
        <Select
          searchable
          appearance="button"
          matchButtonWidth
          style={{ width: 280 }}
          value={curAppVersionInfo?.value}
          options={curAppVersionInfo?.opts}
          clearable
          onChange={(value: string) => {
            onApplicationVersionChange(record, value);
          }}
        />
      );
    },
  },
  {
    title: '数据来源',
    dataIndex: 'combinSource',
    ellipsis: false,
    render: (value, record) => <Text>{combineSources(record.Source, record.BackfillSource)}</Text>,
  },
  {
    title: '所属地域',
    dataIndex: 'RegionToleType',
    ellipsis: false,
  },
  {
    title: '所属可用区',
    dataIndex: 'ZoneRoleType',
    ellipsis: false,
  },
  {
    title: '应用实例名',
    dataIndex: 'ApplicationInstanceID',
    ellipsis: false,
  },
];

// 自定义hook，解决同步调用setState时合并调用的问题，导致数据丢失，可通过getState获取最新数据
export const useGetState = (initVal) => {
  const [state, setState] = useState(initVal);
  const ref = useRef(initVal);
  const setStateCopy = (newVal) => {
    ref.current = newVal;
    setState(newVal);
  };
  const getState = () => ref.current;

  return [state, setStateCopy, getState];
};
