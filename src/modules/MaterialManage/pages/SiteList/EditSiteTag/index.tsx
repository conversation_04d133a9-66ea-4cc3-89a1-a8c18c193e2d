import React, { useEffect, useState, useRef } from 'react';
import { TcsLayout, TcsTable, TcsTabs, ActionType, TcsButton, TcsForm } from '@tencent/tcs-component';
import { getVersionColumns, useGetState } from './config';
import {
  getInheritAppsVersionInfoMap,
  getApplicationNameInfoMap,
  getArtifactBranchTagFormatDataBySolutionVersionID,
  getSolutionArtifactBranchTagOptionsByFormatData,
  getOriginAppsVersionInfoMap,
  AppsVersionInfoArchMap,
  getAppVersionListAndCheckRet,
  fetchDefaultData,
  searchFilterDataSource,
  compatibilityParamsToSearch,
  getFilterWorkSpaceList,
} from './util';
import { IOptionSolutionArtifactBranch, ProductVersionArtifactTagPackage } from '../../type';
import styles from './index.module.scss';
import { APP_ARCH_KES, APP_ARCH_MAPPING } from '@/modules/MaterialManage/pages/SiteList/constant';
import { GenerateSiteApi } from '@/common/api/materialManage';
import { Select, message, Modal, Input, Button, Switch } from '@tencent/tea-component';
import { useTcsContext } from '@tencent/tcsc-base';
interface IUrlParams {
  siteUUID?: string;
  readonly?: string;
  archInfo?: string;
  backFillUUID?: string;
  solutionVersionUUID?: string;
  tagId?: string;
}

const SiteEditTag: React.FC = () => {
  const [urlState] = TcsLayout.useUrlState<IUrlParams>({});
  const [defaultArch, setDefaultArch] = useState<string>(urlState.archInfo?.split(',')?.[0] || APP_ARCH_KES.X86);
  const [SiteUUID] = useState<string>(urlState.siteUUID);
  const [defalutBackFillMap, setDefalutBackFillMap] = useState<{ [key: string]: string }>({
    [APP_ARCH_KES.X86]: urlState.backFillUUID,
    [APP_ARCH_KES.ARM]: urlState.backFillUUID,
  });
  const [isReInistailRequestData, setInistailRequestData] = useState<{ [key: string]: boolean }>({
    [APP_ARCH_KES.X86]: false,
    [APP_ARCH_KES.ARM]: false,
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [buttonLoading, setButtonLoading] = useState<boolean>(false);
  const [applicationNameMap, setApplicationNameMap, getApplicationNameMap] = useGetState({});
  const [fetchInheritTagLoading, setFetchInheritTagLoading] = useState(false);
  const { history } = TcsLayout.useHistory();
  const [inheritTag, setInheritTag] = useState<string>();
  const [inheritBranch, setInheritBranch] = useState<string>();
  const [isGenerateTag, setIsGenerateTag] = useState<boolean>(true);
  const [confirmModal, setConfirmModal] = useState<boolean>(false);
  const [isFilterEmptyVersion, setIsFilterEmptyVersion] = useState<boolean>(false);
  const [branchOptions, setBranchOptions] = useState<IOptionSolutionArtifactBranch[]>([]);
  const [selectedRowKeysMap, setSelectedRowKeysMap, getSelectedRowKeysMap] = useGetState({});
  const tagApplicationListRef = useRef();
  const formRef = useRef<any>();
  const [backFills, setBackFills] = useState([]);
  const actionRef = useRef<ActionType>();
  const { dictMap } = useTcsContext();

  const onAllSetOrigin = () => {
    if (!selectedRowKeysMap[defaultArch]?.length) {
      message.warning({ content: '请先选中要修改的应用！' });
      return;
    }
    const newApplicationNameMap = getOriginAppsVersionInfoMap(applicationNameMap, selectedRowKeysMap[defaultArch]);
    setApplicationNameMap({ ...(newApplicationNameMap || {}) });
  };

  const onAllSetInherit = async (inheritBranch: string, inheritTag: string) => {
    if (!inheritBranch || !inheritTag) {
      message.warning({ content: '请先选择要继承的解决方案制品' });
      return;
    }
    try {
      setFetchInheritTagLoading(true);
      const newAppsVersion = await getInheritAppsVersionInfoMap(
        { ...getApplicationNameMap() },
        inheritTag,
        selectedRowKeysMap[defaultArch],
      );
      setApplicationNameMap(newAppsVersion);
    } catch (e) {
    } finally {
      setFetchInheritTagLoading(false);
    }
  };

  const onChangeInheritTag = (value: string) => {
    setInheritTag(value);
  };

  const onChangeInheritBranch = (value: string) => {
    setInheritBranch(value);
    setInheritTag(undefined);
  };

  const onApplicationVersionChange = (record: ProductVersionArtifactTagPackage, value: string) => {
    const key = record?.ApplicationInstanceID;
    setApplicationNameMap({
      ...getApplicationNameMap(),
      [key]: {
        ...getApplicationNameMap()?.[key],
        value,
      },
    });
  };

  /**
   * 监听用户更新在工作区选择组件并且更改制品版本，这种case需要屏蔽生成制品tag，用户保存后可生成
   */
  const updateGenerateBtnStatus = () => {
    const selectedIds = getSelectedRowKeysMap()?.[defaultArch] || [];
    const appEntries = Object.entries(getApplicationNameMap());
    const isAnyInconsistent = selectedIds.some((id) => {
      const appInfo = appEntries.find(([, app]: any) => String(app.ID) === id)?.[1] as AppsVersionInfoArchMap;
      if (!appInfo || appInfo.value !== appInfo.ApplicationPackageID) {
        return true;
      }
      return false;
    });
    setIsGenerateTag(!isAnyInconsistent);
  };

  const fetchBranchTagOptions = async (solutionVersionID: string, arch: string[]) => {
    if (!solutionVersionID) return;
    try {
      const formatData = await getArtifactBranchTagFormatDataBySolutionVersionID({
        solutionVersionID,
        arch,
      });
      const { branchOptions } = getSolutionArtifactBranchTagOptionsByFormatData(formatData);
      setBranchOptions(branchOptions);
      return branchOptions;
    } catch {
      setBranchOptions([]);
      return [];
    }
  };

  const onRow = (record: any) => {
    const key = record?.ApplicationInstanceID;
    return getApplicationNameMap()?.[key]?.opts?.length > 0 || urlState.readonly
      ? {}
      : { className: 'warning-table-row' };
  };

  const fetchData = async (curParams) => {
    setLoading(true);
    setSelectedRowKeysMap({ ...getSelectedRowKeysMap(), [defaultArch]: [] });
    const params = compatibilityParamsToSearch(curParams);
    try {
      if (urlState.tagId) {
        const searchKeys = [
          'ProductCode',
          'ApplicationName',
          'RegionToleType',
          'ZoneRoleType',
          'ApplicationInstanceID',
          'BackfillSource',
          'Source',
        ];
        const appVerisonList = tagApplicationListRef.current || (await fetchDefaultData(urlState.tagId, params));
        tagApplicationListRef.current = appVerisonList;
        return {
          data: searchFilterDataSource(appVerisonList, params, searchKeys),
          success: true,
        };
      }
      setButtonLoading(true);
      const workSpaces = await GenerateSiteApi.ListOperationSiteAppArtifactWorkspaces({
        ...params,
        SiteUUID,
        Arch: defaultArch,
      });
      const workSpaceList = workSpaces?.ListOperationSiteAppArtifactWorkspaces || [];
      const filterWorkSpaceList = isFilterEmptyVersion ? getFilterWorkSpaceList(workSpaceList) : workSpaceList;
      const ApplicationUUIDs = filterWorkSpaceList.map((item) => item.ApplicationUUID);
      const packageRes: any = await GenerateSiteApi.ListApplicationPackages({
        Arch: defaultArch,
        ApplicationUUID: ApplicationUUIDs,
      });
      const curApplicationNameMap = getApplicationNameInfoMap(filterWorkSpaceList, packageRes?.ListApplicationPackages);
      setApplicationNameMap(curApplicationNameMap);
      return {
        data: filterWorkSpaceList,
        success: true,
      };
    } catch (error) {
      return {
        data: [],
        success: false,
      };
    } finally {
      setInistailRequestData({
        ...isReInistailRequestData,
        [defaultArch]: true,
      });
      updateGenerateBtnStatus();
      setLoading(false);
      setButtonLoading(false);
    }
  };

  useEffect(() => {
    if (isReInistailRequestData[defaultArch]) {
      actionRef.current?.reload();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultArch]);

  useEffect(() => {
    updateGenerateBtnStatus();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [applicationNameMap, selectedRowKeysMap]);

  useEffect(() => {
    actionRef.current?.reload();
  }, [isFilterEmptyVersion]);

  useEffect(() => {
    const fetchCommonData = async () => {
      try {
        await fetchBranchTagOptions(urlState.solutionVersionUUID, [defaultArch]);
        const backFillRes = await GenerateSiteApi.ListProjectSiteArtifactVersionBackfills({
          SiteUUID,
        });
        const ListProjectSiteArtifactVersionBackfills =
          backFillRes?.ListProjectSiteArtifactVersionBackfills?.map((item) => ({
            ...item,
            value: item.UUID,
            text: item.TagDescription,
          })) || [];
        setBackFills(ListProjectSiteArtifactVersionBackfills);
      } catch (error) {
        console.error(error);
      }
    };

    !urlState.readonly && fetchCommonData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleChangeBackFillMap = async (value) => {
    setDefalutBackFillMap({
      ...defalutBackFillMap,
      [defaultArch]: value,
    });
    setLoading(true);
    setButtonLoading(true);
    setSelectedRowKeysMap({ ...getSelectedRowKeysMap(), [defaultArch]: [] });
    await GenerateSiteApi.GenerateSiteAppArtifactInfo({
      SiteUUID,
      Arch: [defaultArch],
      ArtifactVersionBackfillUUID: value,
    });
    actionRef.current?.reload();
  };

  const handleSelectedRowKeysChange = (selectedRowKeys: string[] | number[]) => {
    setSelectedRowKeysMap({
      ...getSelectedRowKeysMap(),
      [defaultArch]: selectedRowKeys,
    });
  };

  const onSave = async () => {
    const selectedRowKeys = getSelectedRowKeysMap()?.[defaultArch] || {};
    const appNameMap = getApplicationNameMap();
    if (!selectedRowKeys.length) {
      return message.warning({ content: '请先选中要修改的组件' });
    }
    const { appVersionList, unCheckedVersionList } = getAppVersionListAndCheckRet(
      selectedRowKeys,
      appNameMap,
      defaultArch,
    );
    const appNames = unCheckedVersionList?.map((item) => item.ApplicationName)?.join(', ');
    const mergedList = [
      ...appVersionList.map((item) => ({ ...item, IsVersionEmpty: false })),
      ...unCheckedVersionList.map((item) => ({ ...item, IsVersionEmpty: true, ApplicationPackageID: 0 })),
    ];
    if (!!unCheckedVersionList.length) {
      await Modal.alert({
        type: 'warning',
        message: '确认',
        description: `应用 ${appNames} 未选择版本，要继续保存工作区数据吗`,
        buttons: [
          <TcsButton type="primary" key={'confirm'} onClick={() => modifyAppArtifactVersion(mergedList)}>
            确认
          </TcsButton>,
          <TcsButton key={'cancel'}>取消</TcsButton>,
        ],
      });
      return;
    }
    modifyAppArtifactVersion(mergedList);
  };

  const modifyAppArtifactVersion = (appVersionList) => {
    setButtonLoading(true);
    GenerateSiteApi.ModifyAppArtifactVersion({
      AppArtifactModifyList: appVersionList,
      SiteUUID,
    })
      .then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          setIsGenerateTag(true);
          message.success({
            content: '保存成功',
          });
        }
      })
      .finally(() => setButtonLoading(false));
  };

  const onConfirmGenerateTagModal = (status: boolean) => {
    setConfirmModal(status);
  };

  const onGenerateTag = async () => {
    try {
      const values = await formRef.current?.validateFields();
      const { Comment } = values;
      setButtonLoading(true);
      onConfirmGenerateTagModal(false);
      const { Error } = await GenerateSiteApi.PackSiteAppArtifactTag({ SiteUUID, Arch: [defaultArch], Comment });
      if (Error) {
        message.error(Error.message);
        return;
      }
      message.success({ content: '生成制品成功' });
      history.goPrev();
    } finally {
      setButtonLoading(false);
    }
  };

  const onChangeFilterEmptyVersion = () => {
    setIsFilterEmptyVersion(!isFilterEmptyVersion);
  };

  const renderTable = (Arch: string) => {
    const selectedRowKeys = selectedRowKeysMap[Arch] || [];
    return (
      <TcsTable
        cardBordered={true}
        actionRef={actionRef}
        request={fetchData}
        manualRequest={true}
        columns={getVersionColumns({
          onAllSetOrigin,
          onAllSetInherit,
          onChangeInheritTag,
          onChangeInheritBranch,
          onApplicationVersionChange,
          selectedRowKeys,
          applicationNameMap,
          dicts: dictMap.originValue,
          inheritTag,
          inheritBranch,
          fetchInheritTagLoading,
          readonly: !!urlState.readonly,
          branchOptions,
        })}
        rowKey="ID"
        className={styles.customTable}
        search={{ filterType: 'light' }}
        pagination={{ defaultPageSize: 20 }}
        options={{ reload: false }}
        onRow={onRow}
        rowSelection={
          !urlState.readonly
            ? {
                type: 'checkbox',
                selectedRowKeys,
                onChange: handleSelectedRowKeysChange,
              }
            : undefined
        }
        headerTitle={
          !urlState.readonly && (
            <>
              {' '}
              <span style={{ fontSize: '13px' }}>选择回录数据：</span>
              <Select
                appearance="button"
                size="m"
                listWidth={200}
                placeholder="选择信息"
                options={backFills}
                value={defalutBackFillMap[defaultArch]}
                onChange={handleChangeBackFillMap}
              />
              <Switch value={isFilterEmptyVersion} onClick={onChangeFilterEmptyVersion}>
                筛选未指定版本组件
              </Switch>
            </>
          )
        }
      />
    );
  };

  return (
    <TcsLayout
      title={urlState?.readonly ? '制品tag详情' : '新建制品tag'}
      history={history}
      customizeCard
      bottomButtons={
        urlState?.readonly
          ? undefined
          : [
              {
                text: '保存',
                type: 'primary',
                onClick: onSave,
                disabled: buttonLoading,
                loading: buttonLoading,
              },
              {
                text: '返回',
                onClick: () => history.goPrev(),
                disabled: buttonLoading,
              },
              {
                text: '生成制品Tag',
                type: 'primary',
                onClick: () => onConfirmGenerateTagModal(true),
                disabled: !isGenerateTag || buttonLoading,
                loading: buttonLoading,
              },
            ]
      }
      loading={loading}
    >
      {!!urlState.readonly ? (
        renderTable(defaultArch)
      ) : (
        <TcsTabs
          onChange={(arch) => setDefaultArch(arch)}
          activeKey={defaultArch}
          items={Object.entries(APP_ARCH_MAPPING).map(([key, label]) => ({
            key,
            label,
            children: renderTable(key),
          }))}
        />
      )}
      <Modal visible={confirmModal} disableCloseIcon>
        <Modal.Message icon="warning" message="确认" />
        <Modal.Body style={{ marginTop: '10px' }}>
          <TcsForm formRef={formRef}>
            <TcsForm.Item
              label="局点制品tag备注："
              rules={[{ required: true, message: '请输入制品备注信息' }]}
              name="Comment"
            >
              <Input size="full" />
            </TcsForm.Item>
          </TcsForm>
        </Modal.Body>
        <Modal.Footer>
          <Button type="primary" onClick={onGenerateTag}>
            确定
          </Button>
          <Button type="weak" onClick={() => onConfirmGenerateTagModal(false)}>
            取消
          </Button>
        </Modal.Footer>
      </Modal>
    </TcsLayout>
  );
};
export default SiteEditTag;
