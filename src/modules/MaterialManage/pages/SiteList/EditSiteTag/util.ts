import { GenerateSiteApi } from '@/common/api/materialManage';
import { message } from '@tencent/tea-component';
import {
  SolutionArtifactBranchWithTags,
  IListSolutionVersionArtifactTags,
  IOptionSolutionArtifactBranch,
  IOptionSolutionArtifactTag,
  IListSolutionVersionArtifactBranches,
  ProductVersionArtifactTagPackage,
  IAppVersionList,
} from '../../type';
import { cloneDeep, isEqual, uniqWith, get, set } from 'lodash-es';
type FirstSourceEnum = 'deliverrecord' | 'backfill' | 'productmarket' | 'manual';
type SecondSourceEnum = 'pod' | 'app-label' | 'workspace' | 'k8s-describe-global';

interface GetPackageVersionNameParams {
  applicationVersion?: string;
  buildTime?: string;
  commitId?: string;
}
interface getArtifactBranchTagFormatDataBySolutionVersionIDProps {
  solutionVersionID: string;
  arch?: string[];
}
interface FirstSourceMatch {
  Source: FirstSourceEnum;
}
interface SecondSourceMatch {
  BackfillSource: SecondSourceEnum;
}

export interface AppsVersionInfoArchMap {
  [ApplicationName: string]: {
    value: number;
    opts: [];
    ApplicationPackageID: number;
    ID: number;
    ApplicationName: string;
    originTagInfo: {
      ApplicationPackageID: string;
      value: number;
    };
  };
}

export const getArtifactBranchListBySolutionVersionID = async (
  solutionVersionID: string,
): Promise<IListSolutionVersionArtifactBranches.Item[]> => {
  if (!solutionVersionID) return [];
  const res = await GenerateSiteApi.ListSolutionVersionArtifactBranches({
    SolutionVersionID: solutionVersionID,
  });
  return res.ListSolutionVersionArtifactBranches || [];
};

export const getInheritAppsVersionInfoMap = async (
  curApplicationMap: ProductVersionArtifactTagPackage,
  inheritTag: string,
  selectedRowKeys: string[],
) => {
  const inheritTagData = await GenerateSiteApi.GetSolutionVersionArtifactTag({ ID: inheritTag });
  const inheritAppList = inheritTagData.GetSolutionVersionArtifactTag?.SolutionPackageList || [];
  const updatedApplicationMap: ProductVersionArtifactTagPackage = { ...curApplicationMap };
  const matchedKeys = new Set();
  const errorList: string[] = [];

  const selectedAppInfos = Object.entries(curApplicationMap)
    .filter(([, appInfo]) => selectedRowKeys.includes(String(appInfo.ID)))
    .map(([mapKey, appInfo]) => ({ mapKey, appInfo }));
  inheritAppList.forEach((appVersion) => {
    const { ApplicationName, ProductCode, ApplicationPackageID } = appVersion;
    selectedAppInfos.forEach(({ mapKey, appInfo }) => {
      const { ProductCode: currentProductCode, ApplicationName: currentAppName } = appInfo;
      const key = `${currentProductCode}-${currentAppName}`;
      if (key === `${ProductCode}-${ApplicationName}`) {
        matchedKeys.add(mapKey);
        const isValidOption = appInfo.opts.some((opt) => opt.value === ApplicationPackageID);
        if (isValidOption) {
          updatedApplicationMap[mapKey].value = ApplicationPackageID;
        } else {
          errorList.push(appInfo.originTagInfo.ApplicationName);
        }
      }
    });
  });

  selectedAppInfos
    .filter(({ mapKey }) => !matchedKeys.has(mapKey))
    .forEach(({ appInfo }) => {
      errorList.push(appInfo.originTagInfo.ApplicationName);
    });

  if (!inheritAppList?.length) {
    message.warning({ content: '未获取到该解决方案制品下的应用信息' });
  } else if (errorList.length > 0) {
    message.warning({ content: `继承的解决方案制品中，以下应用制品版本不存在或不匹配：${errorList?.join(';')}` });
  }
  return updatedApplicationMap;
};

/**
 * 根据解决方案版本UUID获取制品tag列表
 * @param solutionVersionID 解决方案版本UUID
 * @param arch （可选）架构。如没有该参数则不筛选
 */
export const getArtifactTagListBySolutionVersionID = async (
  solutionVersionID: string,
  arch?: string[],
): Promise<IListSolutionVersionArtifactTags.Item[]> => {
  const res = await GenerateSiteApi.ListSolutionVersionArtifactTagsSimple({
    SolutionVersionID: solutionVersionID,
    Arch: arch,
  });
  return res.ListSolutionVersionArtifactTags || [];
};

/**
 * 获取当前解决方案制品的分支和制品tag选择下拉项
 * @param branchTagFormatData 格式化的制品分支和tag数据
 */
export const getSolutionArtifactBranchTagOptionsByFormatData = (
  branchTagFormatData: SolutionArtifactBranchWithTags[],
): {
  branchOptions: IOptionSolutionArtifactBranch[];
  tagOptions: IOptionSolutionArtifactTag[];
} => {
  // 制品分支选项、制品tag选项，用于级联等场景
  const branchOptions = branchTagFormatData.map((branchItem) => ({
    label: branchItem.BranchName,
    value: branchItem.UUID,
    item: branchItem,
    children: (branchItem?.children || []).map((tagItem) => ({
      label: tagItem.TagDescription ? `${tagItem.TagNum} - ${tagItem.TagDescription}` : `${tagItem.TagNum}`,
      value: tagItem.ID,
      item: tagItem,
    })),
  }));
  // 产品下的全量制品Tag
  const tagOptions = branchTagFormatData
    .reduce((total: IListSolutionVersionArtifactTags.Item[], curBranch) => total.concat(curBranch.children), [])
    .map((tagItem) => ({
      label: tagItem.TagDescription
        ? `${tagItem.TagNum} (${tagItem.Arch}) - ${tagItem.TagDescription}`
        : `${tagItem.TagNum} (${tagItem.Arch})`,
      tooltip: tagItem.TagDescription,
      value: tagItem.ID,
      item: tagItem,
    }));
  return {
    branchOptions,
    tagOptions,
  };
};

export const getPackageVersionName = ({ applicationVersion, buildTime, commitId }: GetPackageVersionNameParams) =>
  `${applicationVersion || ''}-${buildTime || ''}-${commitId?.substring(0, 7) || ''}`;

export const getApplicationNameInfoMap = (workList, appPackageList) =>
  workList.reduce((acc, curr) => {
    const { ApplicationName, ApplicationInstanceID, ApplicationUUID } = curr;
    if (ApplicationUUID) {
      const opts =
        appPackageList
          ?.filter((pkg) => pkg.ApplicationUUID === ApplicationUUID)
          ?.map((item) => ({
            text: getPackageVersionName({
              applicationVersion: item?.ApplicationVersion,
              buildTime: item?.BuildTime,
              commitId: item?.CommitID,
            }),
            value: item.ID,
          })) || [];
      acc[ApplicationInstanceID] = {
        // 使用 key 作为键
        ...curr,
        originTagInfo: {
          ApplicationName,
          ApplicationPackageID: curr.ApplicationPackageID,
          ApplicationVersion: curr.ApplicationVersion,
        },
        value: opts.some((opt) => opt.value === curr.ApplicationPackageID) ? curr.ApplicationPackageID ?? null : null,
        opts,
      };
    }
    return acc;
  }, {});

export const fetchBranchTagOptions = async (solutionVersionID, arch) => {
  await getArtifactBranchTagFormatDataBySolutionVersionID({
    solutionVersionID,
    arch,
  });
};

export const getArtifactBranchTagFormatDataBySolutionVersionID = async ({
  solutionVersionID,
  arch,
}: getArtifactBranchTagFormatDataBySolutionVersionIDProps) => {
  const [solutionArtifactBranchList, solutionArtifactTagList] = await Promise.all([
    getArtifactBranchListBySolutionVersionID(solutionVersionID),
    getArtifactTagListBySolutionVersionID(solutionVersionID, arch),
  ]);
  const resData = (solutionArtifactBranchList || []).map((branch) => ({
    ...branch,
    children: solutionArtifactTagList.filter((tag) => branch.UUID === tag.ArtifactBranchUUID),
  }));
  return resData;
};

const firstSourceMap: Record<FirstSourceEnum, string> = {
  deliverrecord: '出包记录',
  backfill: '纳管采集工具',
  productmarket: '产品市场',
  manual: '手动指定',
};

const secondSourceMap: Record<SecondSourceEnum, string> = {
  pod: 'pod',
  'app-label': 'app标签',
  workspace: 'workspace目录',
  'k8s-describe-global': 'describe global',
};

export const combineSources = (firstSource: FirstSourceEnum, secondSource: SecondSourceEnum): string => {
  let result = '';
  if (firstSource in firstSourceMap && secondSource in secondSourceMap) {
    result = `${firstSourceMap[firstSource]} - ${secondSourceMap[secondSource]}`;
  } else if (firstSource in firstSourceMap) {
    result = firstSourceMap[firstSource];
  } else if (secondSource in secondSourceMap) {
    result = secondSourceMap[secondSource];
  } else {
    result = '-';
  }
  return result;
};

export const getOriginAppsVersionInfoMap = (appNameMap: AppsVersionInfoArchMap, selectedRowKeys: string[]) =>
  Object.entries(appNameMap)?.reduce((acc, [key, appInfo]) => {
    if (selectedRowKeys.includes(String(appInfo.ID))) {
      const { originTagInfo } = appInfo;
      const newAppInfo = {
        ...appInfo,
        value: originTagInfo.ApplicationPackageID,
      };
      acc[key] = newAppInfo;
    } else {
      acc[key] = appInfo;
    }
    return acc;
  }, {});

export const getAppVersionListAndCheckRet = (
  selectedRowKeys: string[],
  appNameMap: AppsVersionInfoArchMap,
  Arch: string,
) => {
  const appVersionList: IAppVersionList[] = [];
  const unCheckedVersionList: IAppVersionList[] = [];
  selectedRowKeys.forEach((key) => {
    const app = Object.values(appNameMap).find((app) => String(app.ID) === key);
    if (app) {
      const item = {
        ApplicationWorkspaceID: app.ID,
        ApplicationName: app.ApplicationName,
        ApplicationPackageID: app.value,
        Arch,
      };
      app.value ? appVersionList.push(item) : unCheckedVersionList.push(item);
    }
  });
  return { appVersionList, unCheckedVersionList };
};

export const fetchDefaultData = async (tagId: string, extraParams: object) => {
  const res = await GenerateSiteApi.GetSiteAppArtifactHistoryTag({ ID: tagId, ...extraParams });
  const sitePackageList = res?.GetSiteAppArtifactHistoryTag?.SitePackageList || [];
  return sitePackageList;
};

export function flattenObject(obj, prefix = '') {
  return Object.entries(obj).reduce((result, [key, value]) => {
    const newKey = prefix ? `${prefix}.${key}` : key;
    if (typeof value === 'object') {
      const subPaths = flattenObject(value, newKey);
      return { ...result, ...subPaths };
    }
    result[newKey] = value;
    return result;
  }, {});
}

const getSearchObject = (params: object, searchKeys: string[]) => {
  const searchObj = searchKeys.reduce((ret, key) => {
    set(ret, key, get(params, key));
    return ret;
  }, {});
  return searchObj;
};

export const searchFilterDataSource = (dataSource: any[], searchValues: object, searchKeys: string[]) => {
  const searchObj = getSearchObject(searchValues, searchKeys);
  const temp: any[] = cloneDeep(dataSource);
  const searchKeyValue = flattenObject(searchObj);
  Object.keys(searchKeyValue).forEach((key) => {
    const searchValue = get(searchKeyValue, key)?.toLowerCase();
    if (searchValue) {
      dataSource.forEach((item, index) => {
        const itemValue = get(item, key)?.toLowerCase();
        if (!itemValue || (itemValue && itemValue.indexOf(searchValue) === -1)) {
          temp[index] = undefined;
        }
      });
    }
  });
  return uniqWith(
    temp.filter((t) => t),
    isEqual,
  );
};

export const findMatchingKeys = (input: string): (FirstSourceMatch | SecondSourceMatch)[] => {
  const result: (FirstSourceMatch | SecondSourceMatch)[] = [];
  if (input.trim() === '') {
    return result;
  }
  const seenValues = new Set<string>();
  for (const [key, value] of Object.entries(firstSourceMap)) {
    if ((input.includes(value) || value.includes(input)) && !seenValues.has(value)) {
      result.push({ Source: key as FirstSourceEnum });
      seenValues.add(value);
    }
  }
  for (const [key, value] of Object.entries(secondSourceMap)) {
    if ((input.includes(value) || value.includes(input)) && !seenValues.has(value)) {
      result.push({ BackfillSource: key as SecondSourceEnum });
      seenValues.add(value);
    }
  }
  return result;
};

export const compatibilityParamsToSearch = (params) => {
  const curParams = { ...(params || {}) };
  if (curParams.combinSource) {
    const sourceMapArr = findMatchingKeys(curParams.combinSource);
    sourceMapArr.forEach((item) => {
      const key = Object.keys(item)[0];
      const value = item[key];
      curParams[key] = value;
    });
  }
  delete curParams.combinSource;
  return curParams;
};

export const getFilterWorkSpaceList = (workSpaceList) => {
  const filteredList = workSpaceList.filter((workSpace) => !workSpace.ApplicationVersion);
  return filteredList;
};
