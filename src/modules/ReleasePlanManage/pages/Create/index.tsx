import { UpdateReleasePlan } from '@/common/api/releasePlan';
import { CreateIteration } from '@/common/api/requirementIteration';
import { SolutionVersionSelect } from '@/common/components';
import {
  TcsForm,
  TcsFormDatePicker,
  TcsFormStaffSelect,
  TcsFormText,
  TcsFormTextArea,
  TcsModal,
} from '@tencent/tcs-component';
import { message } from '@tencent/tea-component';
import moment from 'moment';
import React, { useImperativeHandle, useRef, useState } from 'react';
declare const window: any;

export interface IProps {
  handleSuccess: () => void;
}

const CreateIterationModal: React.ForwardRefRenderFunction<any, IProps> = ({ handleSuccess }, ref) => {
  const [visible, setVisible] = useState<boolean>(false);
  const [record, setRecord] = useState<any>();
  const formRef = useRef<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [form] = TcsForm.useForm();

  useImperativeHandle(
    ref,
    () => ({
      show(value) {
        setVisible(true);
        if (value) {
          let owners = value.Owners ? value.Owners.split(';') : [];
          if (owners.length && value.Owners?.includes(',')) {
            owners = owners.map((item) => item.split(',')).flat();
          }

          form.setFieldsValue({
            ReleaseName: value?.ReleaseName,
            Description: value?.Description,
            Type: value.Type,
            SolutionVersionUUID: [value?.SolutionVersion?.SolutionID, value.SolutionVersionUUID],
            StartDate: value?.StartDate ? moment(value?.StartDate) : undefined,
            FeatureFreezeDate: value.FeatureFreezeDate ? moment(value.FeatureFreezeDate) : undefined,
            ReviewDate: value.ReviewDate ? moment(value.ReviewDate) : undefined,
            EndDate: value.EndDate ? moment(value.EndDate) : undefined,
            ReleaseReviewDate: value.ReleaseReviewDate ? moment(value.ReleaseReviewDate) : undefined,
            CodeFreezeDate: value.CodeFreezeDate ? moment(value.CodeFreezeDate) : undefined,
            Owners: owners,
            Status: value?.Status,
            WechatGroup: value?.WechatGroup,
          });
          setRecord(value);
        } else {
          form.setFieldsValue({
            Owners: window?.jiguang_username?.split(';'),
          });
        }
      },
    }),

    [],
  );

  const formatData = (value) => value.format('YYYY-MM-DD');
  const handleConfirm = () => {
    formRef.current.validateFields().then((value) => {
      setLoading(true);
      console.log('value?.SolutionVersion?.SolutionID', value?.SolutionVersionUUID, [
        value?.SolutionVersion?.SolutionID,
        value.SolutionVersionUUID,
      ]);
      const params = {
        ...value,
        FeatureFreezeDate: formatData(value.FeatureFreezeDate),
        ReviewDate: formatData(value.ReviewDate),
        EndDate: formatData(value.EndDate),
        ReleaseReviewDate: formatData(value.ReleaseReviewDate),
        StartDate: formatData(value.StartDate),
        CodeFreezeDate: formatData(value.CodeFreezeDate),
        Owners: value.Owners?.join(';'),
        SolutionVersionUUID: value.SolutionVersionUUID?.[1],
        SolutionUUID: value.SolutionVersionUUID?.[0],
        TapdURL: '',
        WechatGroup: value.WechatGroup,
      };
      if (record) {
        UpdateReleasePlan({
          ...params,
          UUID: record.UUID,
          Status: record?.Status,
        })
          .then((res) => {
            if (res.Error) {
              setLoading(false);
              return message.error({ content: res.Error.Message });
            }
            setVisible(false);
            handleSuccess();
          })
          .finally(() => {
            setLoading(false);
          });
      } else {
        CreateIteration({
          ...params,
        })
          .then((res) => {
            if (res.Error) {
              setLoading(false);

              return message.error({ content: res.Error.Message });
            }
            setVisible(false);
            handleSuccess();
          })
          .finally(() => {
            setLoading(false);
          });
      }
    });
  };
  const handleCancel = () => {
    setVisible(false);
    form.resetFields();
  };

  return (
    <TcsModal
      visible={visible}
      title={record ? '编辑发布计划' : '创建发布计划'}
      onCancel={handleCancel}
      onOk={handleConfirm}
      confirmLoading={loading}
    >
      <TcsForm form={form} formRef={formRef}>
        <TcsFormText
          label="发布计划名称"
          name="ReleaseName"
          rules={[
            {
              required: true,
              message: '请输入迭代名称',
            },
          ]}
          fieldProps={{
            placeholder: '请输入迭代名称',
            size: 'full',
          }}
        />
        {/* <TcsForm.Item
          label="迭代类型"
          name="Type"
          rules={[
            {
              required: true,
              message: '请选择迭代类型',
            },
          ]}
        >
          <Select
            appearance="button"
            placeholder="请选择迭代类型"
            options={
              lookups.IterationType?.map((item) => ({
                value: item.Code,
                text: item.Name,
              })) || []
            }
            size="full"
            matchButtonWidth={true}
          />
        </TcsForm.Item> */}
        <TcsForm.Item
          label="解决方案版本"
          name="SolutionVersionUUID"
          rules={[
            {
              required: true,
              message: '请选择解决方案版本',
            },
          ]}
        >
          <SolutionVersionSelect />
        </TcsForm.Item>
        <TcsFormDatePicker
          label="迭代开始"
          name="StartDate"
          rules={[
            {
              required: true,
              message: '请选择迭代开始时间',
            },
          ]}
        />
        <TcsFormDatePicker
          label="需求评审"
          name="ReviewDate"
          rules={[
            {
              required: true,
              message: '请选择需求评审时间',
            },
          ]}
        />
        <TcsFormDatePicker
          label="需求冻结"
          name="FeatureFreezeDate"
          rules={[
            {
              required: true,
              message: '请选择需求冻结时间',
            },
          ]}
        />
        <TcsFormDatePicker
          label="封板"
          name="CodeFreezeDate"
          rules={[
            {
              required: true,
              message: '请选择封板时间',
            },
          ]}
        />
        <TcsFormDatePicker
          label="发布评审"
          name="ReleaseReviewDate"
          rules={[
            {
              required: true,
              message: '请选择发布评审时间',
            },
          ]}
        />
        <TcsFormDatePicker
          label="迭代发布"
          name="EndDate"
          rules={[
            {
              required: true,
              message: '请选择迭代发布时间',
            },
          ]}
        />

        <TcsFormStaffSelect
          label="迭代管理员"
          name="Owners"
          placeholder="请选择迭代管理员"
          fieldProps={{ multiple: true }}
        />
        <TcsFormTextArea
          label="迭代描述"
          name="Description"
          fieldProps={{ placeholder: '请输入迭代描述', size: 'full' }}
        />
        <TcsFormText
          label="通知群ID"
          name="WechatGroup"
          fieldProps={{ placeholder: '请输入通知群ID', size: 'full' }}
          tooltip="添加 crid 机器人到群聊，@crid 查询会话id 获取群ID"
        />
      </TcsForm>
    </TcsModal>
  );
};

export default React.forwardRef(CreateIterationModal);
