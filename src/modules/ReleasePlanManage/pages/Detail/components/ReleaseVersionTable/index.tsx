import { ListVersionReleaseApproval } from '@/common/api/requirementIteration';
import { withRouteBasename } from '@/common/routePath';
import { TcsButton, TcsLayout, TcsTable } from '@tencent/tcs-component';
import { message } from '@tencent/tea-component';
import React from 'react';
export interface IProps {
  releasePlanUUID?: string;
  iterationUUID?: string;
}
const ReleaseVersionTable = ({ releasePlanUUID, iterationUUID }: IProps) => {
  const { history } = TcsLayout.useHistory();
  function handleEnterReleaseVersion(record) {
    history.push(
      `${withRouteBasename(
        `/${!iterationUUID ? 'release_plan_manage' : 'requirement_iteration_manage'}/detail/release?iteration_id=${
          record?.ReleasePlanUUID
        }&approval_id=${record?.UUID}`,
      )}`,
    );
  }
  const columns = [
    {
      title: '发布说明',
      dataIndex: 'Description',
    },
    {
      title: '创建时间',
      dataIndex: 'CreatedAt',
      valueType: 'dateTime',
    },
    {
      title: '审批状态',
      dataIndex: 'Status',
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'ReleaseVersionStatus',
        showType: 'tag',
      },
    },
    {
      title: '操作',
      dataIndex: 'options',
      render: (text, record) => {
        console.log('record: ');
        return (
          <TcsButton type="link" onClick={() => handleEnterReleaseVersion(record)}>
            进入审批
          </TcsButton>
        );
      },
    },
  ];

  const fetchData = async () => {
    try {
      const { ListVersionReleaseApprovalSheets, Error } = await ListVersionReleaseApproval({
        ReleasePlanUUID: releasePlanUUID,
        IterationUUID: iterationUUID,
      });
      if (Error) {
        return message.error({ content: Error.Message });
      }
      return {
        data: ListVersionReleaseApprovalSheets,
        success: true,
        total: 0,
      };

      // setApprovalData(ListVersionReleaseApprovalSheets);
    } catch (error) {
      console.error(error);
    }
    return {
      data: [],
      success: true,
      total: 0,
    };
  };

  return <TcsTable columns={columns} request={fetchData} />;
};

export default ReleaseVersionTable;
