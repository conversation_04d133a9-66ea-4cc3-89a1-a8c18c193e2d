/*
 * @Author: superfeng
 * @Date: 2023-03-16 20:04:55
 * @LastEditors: superfeng
 * @LastEditTime: 2023-07-03 17:20:34
 * @Description: 请输入注释信息
 */
import { ProForm, QueryFilter } from '@/common/components';
import { IterationPathConfig } from '@/modules/IterationManage/config';
import { Card, Input } from '@tencent/tea-component';

import React from 'react';

export interface IProps {
  onSearch: (values: any) => void;
  initialValues?: any;
  pagePath: string;
}

const Search: React.FC<IProps> = ({ onSearch, initialValues, pagePath }) => {
  const handleSearch = (values) => {
    onSearch(values);
  };

  return (
    <Card style={{ marginBottom: 5 }}>
      <Card.Body>
        <QueryFilter onSearch={handleSearch} initialValues={initialValues}>
          {pagePath === IterationPathConfig.Defect ? (
            <ProForm.Item label="缺陷单ID" dataIndex="IssueID">
              <Input placeholder="请输入缺陷单ID" />
            </ProForm.Item>
          ) : (
            <ProForm.Item label="需求单ID" dataIndex="IssueID">
              <Input placeholder="请输入需求单ID" />
            </ProForm.Item>
          )}

          <ProForm.Item label="标题" dataIndex="Title">
            <Input placeholder="请输入标题" />
          </ProForm.Item>
        </QueryFilter>
      </Card.Body>
    </Card>
  );
};

export default Search;
