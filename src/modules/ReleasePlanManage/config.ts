export enum IterationStatusEnum {
  Open = 'open',
  FeatureReviewing = 'feature_reviewing',
  Processing = 'processing',
  FeatureFrozen = 'feature_frozen',
  CodeFrozen = 'code_frozen',
  ReleaseReviewing = 'release_reviewing',
  Closed = 'closed',
}

export enum IterationLockStatus {
  Unlock = 'unlock',
  LockStory = 'lock_story',
  LockAll = 'lock_all',
}
export interface IProps {
  title: string;
  label: string;
  theme: any;
}
export const IterationStatusMap: IProps[] = [
  {
    title: IterationStatusEnum.FeatureReviewing,
    label: 'StartDate',
    theme: 'default',
  },
  {
    title: IterationStatusEnum.Processing,
    label: '',
    theme: 'default',
  },
  {
    title: IterationStatusEnum.FeatureFrozen,
    label: 'FeatureFreezeDate',
    theme: 'default',
  },
  {
    title: IterationStatusEnum.CodeFrozen,
    label: 'CodeFreezeDate',
    theme: 'default',
  },
  {
    title: IterationStatusEnum.ReleaseReviewing,
    label: 'ReleaseReviewDate',
    theme: 'default',
  },
  {
    title: IterationStatusEnum.Closed,
    label: 'EndDate',
    theme: 'default',
  },
];
