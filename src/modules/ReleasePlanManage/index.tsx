import { DefectManagementRoutePath } from '@/common/routePath';
import React from 'react';
import { Route } from 'react-router-dom';
import ReleaseVersion from '../RequirementIterationManage/pages/ReleaseVersionDetail';
import ProductLevelApproval from '../RequirementIterationManage/pages/ReleaseVersionDetail/components/ReleaseVersionFlow/components/DeveloperApproval/components/ProductLevelApproval';
import Detail from './pages/Detail';
import List from './pages/List';

const Index = () => {
  const routesConfig = [
    {
      path: DefectManagementRoutePath.RELEASE_PLAN_MANAGE_LIST_PAGE,
      component: List,
      exact: true,
    },
    {
      path: DefectManagementRoutePath.RELEASE_PLAN_MANAGE_DETAIL_PAGE,
      component: Detail,
      exact: true,
    },
    {
      path: DefectManagementRoutePath.RELEASE_PLAN_MANAGE_RELEASE_VERSION_PAGE,
      component: ReleaseVersion,
      exact: true,
    },
    {
      path: DefectManagementRoutePath.RELEASE_PLAN_MANAGE_RELEASE_VERSION_PRODUCT_PAGE,
      component: ProductLevelApproval,
      exact: true,
    },
  ];

  return (
    <>
      {routesConfig.map((item, index) => (
        <Route key={index} component={item.component} path={item.path} exact={item.exact} />
      ))}
    </>
  );
};
export default Index;
