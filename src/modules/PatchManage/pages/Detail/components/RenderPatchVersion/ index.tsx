/*
 * @Author: lucyfang
 * @Date: 2024-12-25 16:44:24
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-04-01 15:55:17
 * @Description: 请输入注释信息
 */
import { listPatchApplicationPackages, modifyPatchApplications } from '@/common/api/patchManage';
import React, { useEffect, useState } from 'react';
import { Select, StatusTip, message } from '@tencent/tea-component';

interface RenderPatchVersionProps {
  record: any;
  patchId: string;
  onloading: (boolean) => void;
  isPublished: boolean;
  updateAuthorized: boolean;
  isAfter: boolean;
}

const RenderPatchVersion = (props: RenderPatchVersionProps) => {
  const { record, patchId, onloading, isPublished, isAfter, updateAuthorized } = props;
  const patchVersion = record?.Details?.Latest;
  const [options, setOptions] = useState<any>([]);
  const [status, setStatus] = useState<any>('loading');
  const [value, setValue] = useState(patchVersion);
  const handleOpen = () => {
    listPatchApplicationPackages({
      PatchID: patchId,
      ApplicationName: record?.ApplicationName,
    })
      .then((res) => {
        if (res?.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          setOptions(() =>
            res?.ApplicationPackageList?.map((item) => ({
              value: item?.ApplicationPackageID,
              text: item?.PackageVersionName,
              extra: item?.ApplicationBranchUUID,
            })),
          );
        }
      })
      .finally(() => {
        setStatus(null);
      });
  };

  const handleModifyPatchApplications = ({ option }) => {
    onloading(true);
    const Applications = [
      {
        ApplicationName: record?.ApplicationName,
        ApplicationPackageID: option?.value ?? '',
        PackageVersionName: option?.text ?? '',
        ApplicationBranchUUID: option?.extra ?? '',
      },
    ];
    modifyPatchApplications({
      PatchID: patchId,
      Applications,
      Operation: 'select',
    })
      .then((res) => {
        if (res?.Error) {
          setValue(patchVersion);
          message.error({
            content: res.Error.Message,
          });
        }
      })
      .finally(() => {
        onloading(false);
      });
  };
  useEffect(() => {
    setValue(record?.Details?.Latest);
  }, [record?.Details?.Latest]);
  if (isPublished || (isAfter && !updateAuthorized)) {
    return value;
  } else {
    return (
      <Select
        appearance="button"
        size="full"
        matchButtonWidth
        clearable
        searchable
        placeholder={value}
        options={options}
        onChange={(_value, context) => {
          const { option } = context;
          setValue(option?.text);
          handleModifyPatchApplications({ option });
        }}
        tips={status && <StatusTip status={status as any} />}
        onOpen={handleOpen}
      />
    );
  }
};
export default RenderPatchVersion;
