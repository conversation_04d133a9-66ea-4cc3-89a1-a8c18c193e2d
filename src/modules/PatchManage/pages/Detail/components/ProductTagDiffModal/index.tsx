import React, { useEffect, useRef, useState } from 'react';

import { Button, Justify, Modal, Select, Table, message, Switch } from '@tencent/tea-component';
import { ProForm } from '@/common/components';
import {
  listSolutionVersionArtifactBranches,
  listSolutionVersionArtifactTagsSimple,
  modifyPatchApplications,
  PatchManageApi,
} from '@/common/api/patchManage';
import { toListParamsCApi } from '@/common/api/api';

interface ProductTagDiffModalProps {
  solutionVersionUUID: string;
  onClose: () => void;
  onSuccess: () => void;
  arch: string;
  baselineTagID: string;
  patchId: string;
  patchDetail?: any;
}

const { pageable, autotip, scrollable, selectable, filterable } = Table.addons;

const ALL_VALUE = '__ALL__';

const columns = [
  { key: 'ProductVersionName', header: '配置产品名称', width: 80 },
  { key: 'ProductCode', header: '配置产品Code', width: 100 },
  { key: 'ApplicationName', header: '应用名称', width: 180 },
  {
    key: 'InitialTag',
    header: '初始制品版本',
    width: 200,
    render: (record) => {
      if (record?.shouldHighlight) {
        return (
          <span title={record?.InitialTag} style={{ color: 'red' }}>
            {record?.InitialTag}
          </span>
        );
      }
      return record?.InitialTag;
    },
  },
  {
    key: 'TargetTag',
    header: '目标制品版本',
    width: 200,
    render: (record) => {
      if (record?.shouldHighlight) {
        return (
          <span title={record?.TargetTag} style={{ color: 'red' }}>
            {record?.TargetTag}
          </span>
        );
      }
      return record?.TargetTag;
    },
  },
  {
    key: 'PatchTag',
    header: 'Patch制品版本',
    width: 200,
  },
];

const extractBuildTime = (tag: string) => {
  if (!tag) return '';
  const regex = /(\d{8}-\d{6})/;
  const [, time = ''] = tag.match(regex) ?? [];
  return time;
};

const ProductTagDiffModal: React.FC<ProductTagDiffModalProps> = (props) => {
  const { onClose, solutionVersionUUID, arch, baselineTagID, patchId, onSuccess, patchDetail } = props;
  const [artifactBranches, setArtifactBranches] = useState([]);
  const [artifactTags, setArtifactTags] = useState([]);
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<any>([]);
  const [selectedIds, setSelectedIds] = useState([]);
  const [selectedRecords, setSelectedRecords] = useState([]);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const formRef = useRef<any>();

  const [appList, setAppList] = useState<string[]>([]);
  const [filterApp, setFilterApp] = useState<string[]>([]);
  const [productCodeList, setProductCodeList] = useState<string[]>([]);
  const [filterProductCode, setFilterProductCode] = useState<string[]>([]);
  const [pageInfo, setPageInfo] = useState({
    pageIndex: 1,
    pageSize: 10,
    recordCount: 0,
  });
  const [isVersionDiff, setIsVersionDiff] = useState(false);

  // 获取初始制品TAG
  const getArtifactTagsSimple = (ArtifactBranchUUID) => {
    formRef.current?.setFieldValue('TargetTag', undefined);
    setArtifactTags([]);
    listSolutionVersionArtifactTagsSimple({
      SolutionVersionArtifactTag: {
        _Filter: toListParamsCApi(
          {
            SolutionVersionID: solutionVersionUUID,
            ArtifactBranchUUID,
            Arch: arch,
            PublishStatus: ['可发布', '内部测试'],
          },
          {
            useEqFields: ['ArtifactBranchUUID', 'SolutionVersionID'],
          },
        )._Filter,
        _Order: [{ Key: 'TagNum', Sort: 'DESC' }],
      },
    }).then((res) => {
      if (res.Error) {
        return message.error({
          content: res.Error.Message,
        });
      }
      setArtifactTags(res.ListSolutionVersionArtifactTags || []);
    });
  };

  // 获取列表
  const handleTargetTagChange = (targetTagUUID) => {
    setFilterApp([]);
    setFilterProductCode([]);
    setPageInfo((info) => ({
      ...info,
      pageIndex: 1,
    }));
    setLoading(true);

    // 重置选择状态
    setSelectedIds([]);
    setSelectedRecords([]);

    // 调用table数据
    PatchManageApi.GetPatchArtifactTagDiffInfo({
      PatchID: patchId,
      SourceTagUUID: baselineTagID,
      TargetTagUUID: targetTagUUID,
    })
      .then((res) => {
        if (res.message) {
          return message.error({
            content: res.message,
          });
        }
        const diffData = res.PatchArtifactTagApplication || [];

        // 创建一个Map来去重
        const uniqueMap = new Map();
        diffData.forEach((item) => {
          const key = item.TargetPackageInfo
            ? `${item.TargetPackageInfo?.ApplicationName}-${item.TargetPackageInfo?.ApplicationPackageID}-${item.TargetPackageInfo?.ApplicationBranchUUID}-${item.TargetPackageInfo?.PackageVersionName}`
            : item.ApplicationName;

          if (!uniqueMap.has(key)) {
            uniqueMap.set(key, item);
          }
        });
        const uniqueData = Array.from(uniqueMap.values());
        const finalData = uniqueData.map((item) => {
          const TargetTagBuildTime = extractBuildTime(item.TargetPackageInfo?.PackageVersionName) ?? '';
          const PatchTagBuildTime = extractBuildTime(item.PatchPackageInfo?.PatchVersionName) ?? '';
          const SourceTagBuildTime = extractBuildTime(item.SourcePackageInfo?.PackageVersionName) ?? '';

          const shouldHighlight = TargetTagBuildTime !== SourceTagBuildTime;
          const shouldDisabled =
            !item.TargetPackageInfo?.PackageVersionName ||
            TargetTagBuildTime < SourceTagBuildTime ||
            TargetTagBuildTime < PatchTagBuildTime;

          return {
            ApplicationName: item.ApplicationName,
            InitialTag: item.SourcePackageInfo?.PackageVersionName || '-',
            TargetTag: item.TargetPackageInfo?.PackageVersionName || '-',
            PatchTag: item.PatchPackageInfo?.PatchVersionName || '-',
            InitialTagBuildTime: extractBuildTime(item.SourcePackageInfo?.PackageVersionName) ?? '',
            TargetTagBuildTime,
            PatchTagBuildTime,
            ProductCode: item.ProductCode,
            ProductVersionName: item.ProductVersionName,
            TargetPackageInfo: item.TargetPackageInfo,
            shouldHighlight,
            shouldDisabled,
          };
        });

        const filteredData = finalData.sort((a, b) => {
          if (a.shouldDisabled && !b.shouldDisabled) {
            return 1;
          }
          if (!a.shouldDisabled && b.shouldDisabled) {
            return -1;
          }
          if (a.PatchTag !== '-' && b.PatchTag === '-') {
            return -1;
          }
          if (a.PatchTag === a.TargetTag && b.PatchTag !== b.TargetTag) {
            return -1;
          }
          if (a.PatchTag !== a.TargetTag && b.PatchTag === b.TargetTag) {
            return 1;
          }
          if (a.PatchTag === '-' && b.PatchTag !== '-') {
            return 1;
          }
          return 0;
        });
        setAppList(() => Array.from(new Set(filteredData?.map((item) => item.ApplicationName))));
        setProductCodeList(() => Array.from(new Set(filteredData?.map((item) => item.ProductCode))));
        setLoading(false);
        setDataSource(filteredData as any);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleConfirm = () => {
    setConfirmLoading(true);
    const formattedApplications = selectedRecords.map((item: any) => ({
      ApplicationName: item?.TargetPackageInfo?.ApplicationName,
      ApplicationPackageID: item?.TargetPackageInfo?.ApplicationPackageID,
      PackageVersionName: item?.TargetTag,
      ApplicationBranchUUID: item?.TargetPackageInfo?.ApplicationBranchUUID,
    }));

    modifyPatchApplications({
      PatchID: patchId,
      Applications: formattedApplications,
      Operation: 'select',
    })
      .then((res) => {
        if (res?.Error) {
          return message.error({
            content: res.Error.Message,
          });
        }
        onSuccess();
      })
      .finally(() => {
        onClose();
        setConfirmLoading(false);
      });
  };

  useEffect(() => {
    listSolutionVersionArtifactBranches({
      SolutionVersionArtifactBranch: {
        _Filter: toListParamsCApi(
          { SolutionVersionID: solutionVersionUUID, BranchType: 'release' },
          {
            useEqFields: ['SolutionVersionID', 'BranchType'],
          },
        )._Filter,
        _Order: [{ Key: 'ID', Sort: 'ASC' }],
      },
    }).then((res) => {
      if (res.Error) {
        return message.error({
          content: res.Error.Message,
        });
      }
      const branches = res.ListSolutionVersionArtifactBranches || [];
      setArtifactBranches(branches);

      // 设置默认值
      const defaultBranch = branches.find((branch) => branch.UUID === patchDetail?.ArtifactBranch?.UUID);
      if (defaultBranch) {
        formRef.current?.setFieldValue('TargetBranch', defaultBranch.UUID);
        getArtifactTagsSimple(defaultBranch.UUID);
      }
    });

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [baselineTagID, solutionVersionUUID]);

  let filteredRecords = dataSource.slice();

  // 根据应用名称筛选
  if (filterApp.length > 0) {
    filteredRecords = filteredRecords.filter((item) => filterApp.includes(item?.ApplicationName));
  }

  // 根据产品代码筛选
  if (filterProductCode.length > 0) {
    filteredRecords = filteredRecords.filter((item) => filterProductCode.includes(item?.ProductCode));
  }

  // 根据版本差异筛选
  if (isVersionDiff) {
    filteredRecords = filteredRecords.filter((item) => item?.TargetTagBuildTime !== item?.InitialTagBuildTime);
  }

  return (
    <Modal
      visible
      caption={
        <span style={{ display: 'flex' }}>
          制品Tag差异对比
          <span style={{ flex: 1, marginTop: -4, marginLeft: 10 }}>
            <Modal.Message
              description={`初始制品Tag：${patchDetail?.BaselineTag?.TagNum}；Patch制品Tag：${
                patchDetail?.PatchTag?.TagNum || '-'
              }`}
            />
          </span>
        </span>
      }
      size={1200}
      onClose={onClose}
    >
      <Modal.Body>
        <ProForm formRef={formRef} grid>
          <ProForm.Item colProps={{ span: 7 }} label="目标制品分支" dataIndex="TargetBranch">
            <Select
              appearance="button"
              placeholder="请选择目标制品分支"
              size="m"
              matchButtonWidth
              clearable
              options={artifactBranches?.map((item: any) => ({
                text: item?.BranchName,
                value: item?.UUID,
              }))}
              onChange={getArtifactTagsSimple}
            />
          </ProForm.Item>

          <ProForm.Item label="目标制品Tag" colProps={{ span: 7 }} dataIndex="TargetTag">
            <Select
              appearance="button"
              placeholder="请选择目标制品Tag"
              size="m"
              matchButtonWidth
              clearable
              options={artifactTags?.map((item: any) => ({
                text: `${item?.TagNum}-${item?.TagDescription}`,
                value: item?.UUID,
              }))}
              onChange={(value) => {
                if (value) {
                  handleTargetTagChange(value);
                }
              }}
              searchable
              defaultValue={patchDetail?.ArtifactBranch?.UUID}
            />
          </ProForm.Item>
        </ProForm>
        <Table.ActionPanel style={{ marginTop: 21 }}>
          <Justify
            left={
              <Button type="primary" onClick={handleConfirm} loading={confirmLoading}>
                批量添加目标制品Tag
              </Button>
            }
            right={
              <Switch
                defaultChecked={isVersionDiff}
                onChange={(checked) => {
                  // 重置选择状态
                  setIsVersionDiff(checked);
                  // 重置分页
                  setPageInfo({
                    pageIndex: 1,
                    pageSize: 10,
                    recordCount: 0,
                  });
                }}
              >
                仅显示差异版本
              </Switch>
            }
          />
        </Table.ActionPanel>
        <Table
          columns={columns}
          records={filteredRecords}
          rowDisabled={(record) => record?.shouldDisabled}
          recordKey={(record) => {
            if (record.TargetPackageInfo) {
              return `${record.TargetPackageInfo.ApplicationName}-${record.TargetPackageInfo.ApplicationPackageID}-${record.TargetPackageInfo.ApplicationBranchUUID}-${record.TargetPackageInfo.PackageVersionName}`;
            }
            return record.ApplicationName;
          }}
          addons={[
            pageable({
              ...pageInfo,
              recordCount: filteredRecords?.length ?? 0,
              onPagingChange: (query) =>
                setPageInfo((info) => ({
                  ...info,
                  ...query,
                })),
            }),
            autotip({
              isLoading: loading,
            }),
            scrollable({ maxHeight: 380, minWidth: 1300 }),
            selectable({
              value: selectedIds,
              onChange: (keys, context) => {
                const { selectedRecords } = context;
                setSelectedIds(keys as any);
                setSelectedRecords(selectedRecords as any);
              },
              rowSelect: true,
              width: 36,
              all: true,
            }),
            filterable({
              type: 'multiple',
              column: 'ApplicationName',
              value: filterApp,
              all: {
                value: ALL_VALUE,
                text: '全部',
              },
              searchable: true,
              onChange: (value) => setFilterApp(value),
              options: appList?.map((text) => ({
                value: text,
                text,
              })),
            }),
            filterable({
              type: 'multiple',
              column: 'ProductCode',
              value: filterProductCode,
              all: {
                value: ALL_VALUE,
                text: '全部',
              },
              searchable: true,
              onChange: (value) => setFilterProductCode(value),
              options: productCodeList?.map((text) => ({
                value: text,
                text,
              })),
            }),
            Table.addons.rowtooltip({
              tooltip: (record) => (record?.shouldDisabled ? '该目标制品版本不支持添加' : null),
            }),
          ]}
        />
      </Modal.Body>
    </Modal>
  );
};
export default ProductTagDiffModal;
