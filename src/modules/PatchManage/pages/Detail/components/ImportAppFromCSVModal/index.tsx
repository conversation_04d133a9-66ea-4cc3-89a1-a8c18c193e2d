import { T<PERSON><PERSON><PERSON><PERSON>, T<PERSON><PERSON><PERSON>, T<PERSON><PERSON>orm, TcsModal, TcsSpace, TcsTabs } from '@tencent/tcs-component';
import { message } from '@tencent/tea-component';
import React, { useImperativeHandle, useState } from 'react';
import UploadCsv from './components/UploadCsv';
import ApplicationTable from './components/ApplicationTable';
import { AddApplicationToPatchResponse, PatchManageApi } from '@/common/api/patchManage';

export interface IProps {
  patchId: string;
  onConfirm?: () => void;
}

const ImportAppFromCSVModal: React.ForwardRefRenderFunction<any, IProps> = ({ patchId, onConfirm }, ref) => {
  const [visible, setVisible] = useState(false);
  const [appList, setAppList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [allApplications, setAllApplications] = useState<AddApplicationToPatchResponse['Applications']>([]);
  const [errorApplications, setErrorApplications] = useState<AddApplicationToPatchResponse['Applications']>([]);
  useImperativeHandle(
    ref,
    () => ({
      show: () => {
        setVisible(true);
      },
    }),
    [],
  );

  async function handleConfirm(IgnoreErrorApplication = false) {
    if (appList?.length) {
      try {
        setLoading(true);
        const res = await PatchManageApi.BulkAddPatchApplications({
          PatchID: patchId,
          ApplicationList: appList.map((item) => ({
            ...item,
            PackageVersion: item.PackageVersionName,
          })),
          IgnoreErrorApplication,
        });
        if (!IgnoreErrorApplication) {
          const applications = res.Applications;
          setAllApplications(applications);
          const errorApplications = applications.filter((item) => !item.CanImport);
          if (errorApplications.length) {
            setErrorApplications(errorApplications);
            message.warning({
              content: '导入存在异常应用，请检查应用制品版本是否存在',
            });
            return;
          }
        }

        message.success({
          content: '导入成功',
        });
        handleResetUpload();
        setVisible(false);
        onConfirm?.();
      } catch (error) {
      } finally {
        setLoading(false);
      }
    } else {
      message.warning({
        content: '请先上传CSV文件之后再导入',
      });
    }
  }

  function handleCancel() {
    handleResetUpload();
    setVisible(false);
  }

  function handleResetUpload() {
    setErrorApplications([]);
    setAllApplications([]);
    setAppList([]);
  }

  function generateAndDownloadCSV() {
    const data = [['connect-ft-product-center-management', '0.0.1-20240719-174057-a0bbcb3', 'rhel.amd64']];

    const filename = 'demo.csv';
    // 将数据转换为CSV格式的字符串
    const csvContent = data.map((row) => row.join(',')).join('\n');

    // 创建一个Blob对象
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

    // 创建一个指向Blob对象的URL
    const url = URL.createObjectURL(blob);

    // 创建一个a标签并设置下载属性
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.style.display = 'none';

    // 将a标签添加到DOM中并触发点击事件
    document.body.appendChild(link);
    link.click();

    // 从DOM中移除a标签并释放URL对象
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  return (
    <TcsModal
      destroyOnClose
      title="基于导入CSV添加应用制品版本"
      visible={visible}
      width={1000}
      onCancel={handleCancel}
      footer={
        <TcsSpace>
          {errorApplications.length ? (
            <>
              <TcsButton
                type="primary"
                onClick={() => {
                  handleConfirm(true);
                }}
                loading={loading}
              >
                仅导入正常应用
              </TcsButton>
              <TcsButton
                onClick={() => {
                  handleResetUpload();
                }}
              >
                重新上传
              </TcsButton>
              <TcsButton onClick={handleCancel}>取消</TcsButton>
            </>
          ) : (
            <>
              <TcsButton
                type="primary"
                onClick={() => {
                  handleConfirm();
                }}
                loading={loading}
              >
                开始导入
              </TcsButton>
              <TcsButton onClick={handleCancel}>取消</TcsButton>
            </>
          )}
        </TcsSpace>
      }
    >
      {errorApplications?.length ? (
        <>
          <TcsTabs>
            <TcsTabs.TabPane tab="导入失败清单" key="1">
              <ApplicationTable data={errorApplications} showError={true} />
            </TcsTabs.TabPane>
            <TcsTabs.TabPane tab="全量应用清单" key="2">
              <ApplicationTable data={allApplications} showError={false} />
            </TcsTabs.TabPane>
          </TcsTabs>
        </>
      ) : (
        <>
          <TcsCard title="文件上传">
            <TcsForm>
              <TcsForm.Item
                label="选择CSV文件"
                extra={
                  <>
                    请注意：需上传csv文件，csv分为三列，第一列是应用，第二列是制品版本，第三列是架构，
                    <span>
                      <a
                        href="#"
                        onClick={() => {
                          generateAndDownloadCSV();
                        }}
                      >
                        点击下载示例csv
                      </a>
                    </span>
                  </>
                }
              >
                <UploadCsv onChange={(data) => setAppList(data)} />
              </TcsForm.Item>
            </TcsForm>
          </TcsCard>
          <TcsCard title="导入应用数据预览">
            <ApplicationTable data={appList} showError={false} />
          </TcsCard>
        </>
      )}
    </TcsModal>
  );
};

export default React.forwardRef(ImportAppFromCSVModal);
