import { TcsButton } from '@tencent/tcs-component';
import { Form, Text, Upload } from '@tencent/tea-component';
import React, { useState } from 'react';

export interface IProps {
  onChange: (data: any[]) => void;
}

const UploadCsv: React.FC<IProps> = ({ onChange }) => {
  const [file, setFile] = useState<File>();

  async function handleBeforeUpload(file: File) {
    const csv = await new Promise<string>((resolve, reject) => {
      try {
        const reader = new FileReader();
        reader.addEventListener('loadend', () => {
          if (reader.result) {
            resolve(reader.result.toString());
          } else {
            reject(new Error('Failed to read file'));
          }
        });
        reader.readAsText(file);
      } catch (e) {
        reject(e);
      }
    });
    setFile(file);
    onChange(
      csv.split('\n').map((item) => {
        const [ApplicationName, PackageVersionName, Arch] = item.split(',');
        return {
          ApplicationName,
          PackageVersionName,
          Arch,
        };
      }),
    );
    return false;
  }

  return (
    <>
      <Upload accept="application/csv" multiple={false} beforeUpload={handleBeforeUpload}>
        <TcsButton>点击上传</TcsButton>
      </Upload>
      {file && <hr />}
      {file && (
        <Form.Text>
          <Text theme="success">{file.name}</Text>
        </Form.Text>
      )}
    </>
  );
};

export default UploadCsv;
