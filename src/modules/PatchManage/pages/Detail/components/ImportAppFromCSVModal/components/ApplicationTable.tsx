import { AddApplicationToPatchResponse } from '@/common/api/patchManage';
import { TcsTable } from '@tencent/tcs-component';
import React, { useEffect, useMemo } from 'react';

const getColumns = (showError?: boolean) => {
  const columns = [
    {
      title: '应用名',
      dataIndex: 'ApplicationName',
      width: showError ? '25%' : '33%',
    },
    {
      title: '制品Tag',
      dataIndex: 'PackageVersionName',
      width: showError ? '25%' : '33%',
    },
    {
      title: '架构',
      dataIndex: 'Arch',
      width: showError ? '15%' : '33%',
    },
  ];
  if (showError) {
    columns.push({
      title: '异常原因',
      dataIndex: 'Reason',
      width: '35%',
    });
  }
  return columns;
};

export interface IProps {
  data: AddApplicationToPatchResponse['Applications'];
  showError?: boolean;
}

const ApplicationTable: React.FC<IProps> = ({ data, showError }) => {
  useEffect(() => {}, []);
  const columns = useMemo(() => getColumns(showError), [showError]);
  return (
    <TcsTable
      cardBordered
      options={false}
      ghost
      columns={columns}
      dataSource={data}
      pagination={{
        pageSize: 10,
      }}
    />
  );
};

export default ApplicationTable;
