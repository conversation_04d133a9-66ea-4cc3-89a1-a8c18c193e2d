import { TcsPop<PERSON>onfirm, TcsSpin, TcsTable } from '@tencent/tcs-component';
import React, { useCallback, useState } from 'react';
import { selectPatchIssues } from '@/common/api/patchManage';
import { Button, message } from '@tencent/tea-component';
import { useIssueColumns } from './config';
import moment from 'moment';

export interface IProps {
  records: any[];
  patchId: string;
  onUpdate: () => void;
  isPublished: boolean;
  handelAddIssues: () => void;
  updateAuthorized?: boolean;
  isAfter: boolean;
}

const SelectedTable: React.FC<IProps> = ({
  records,
  patchId,
  onUpdate,
  isPublished,
  handelAddIssues,
  updateAuthorized,
  isAfter,
}) => {
  const columns = useIssueColumns({});
  const [loading, setLoading] = useState(false);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  function handleRequest(params, options) {
    const data = records;
    const { pageSize, current, IssueID, IssueSolution, Title, Type, Priority, CreatedAt } = params;
    const { IssueAppRel, ProductInfo, Status } = IssueSolution;
    let createdAtRange = CreatedAt;
    if (CreatedAt && CreatedAt.length === 2) {
      createdAtRange = [
        moment(CreatedAt?.[0])?.format('YYYY-MM-DD 00:00:00'),
        moment(CreatedAt?.[1])?.format('YYYY-MM-DD 23:59:59'),
      ];
    }
    const filteredData = data?.filter((item) => {
      const issueData = item?.Issue || {};
      const storyData = item?.Story || {};
      const issueSolutionData = item?.IssueSolution || {};
      const issueAppRelData = issueSolutionData?.IssueAppRel || [];
      // 缺陷单 ID 或 需求 ID
      const matchesID =
        !IssueID ||
        issueData.IssueID?.toLowerCase().includes(IssueID.toLowerCase()) ||
        storyData.IssueID?.toLowerCase().includes(IssueID.toLowerCase());

      // 缺陷单/需求 标题
      const matchesTitle =
        !Title ||
        issueData.Title?.toLowerCase().includes(Title.toLowerCase()) ||
        storyData.Title?.toLowerCase().includes(Title.toLowerCase());

      // 类型（缺陷或需求）
      const matchesType = !Type || Type.length === 0 || Type.includes(item?.Story ? 'story' : 'bug');

      // 优先级
      const matchesPriority =
        !Priority ||
        issueData.Priority?.toLowerCase().includes(Priority.toLowerCase()) ||
        storyData.Priority?.toLowerCase().includes(Priority.toLowerCase());

      // 涉及产品
      const matchesProduct =
        !ProductInfo ||
        issueAppRelData.some((appRel) =>
          appRel.ProductVersionInfo?.ProductInfo?.Code.toLowerCase().includes(ProductInfo.toLowerCase()),
        );

      // 涉及应用
      const matchesApplication =
        !IssueAppRel ||
        issueAppRelData.some((appRel) => appRel.ApplicationName.toLowerCase().includes(IssueAppRel.toLowerCase()));

      // 状态
      const matchesStatus = !Status || issueSolutionData.Status?.toLowerCase().includes(Status.toLowerCase());

      // 创建时间
      const createdAtDate = new Date(item.CreatedAt);
      const matchesCreatedAt =
        !createdAtRange ||
        ((!createdAtRange[0] || createdAtDate >= new Date(createdAtRange[0])) &&
          (!createdAtRange[1] || createdAtDate <= new Date(createdAtRange[1])));

      return (
        matchesID &&
        matchesTitle &&
        matchesType &&
        matchesPriority &&
        matchesProduct &&
        matchesApplication &&
        matchesStatus &&
        matchesCreatedAt
      );
    });

    // 处理排序
    let sortedData = filteredData;
    const sorter = options?.sorter || [];
    if (sorter.length) {
      const { field, order } = sorter[0];
      sortedData = filteredData.sort((a, b) => {
        let aValue = a[field];
        let bValue = b[field];

        if (field === 'CreatedAt') {
          aValue = new Date(aValue).getTime();
          bValue = new Date(bValue).getTime();
        }
        if (order === 'ascend') {
          return aValue > bValue ? 1 : -1;
        }
        if (order === 'descend') {
          return aValue < bValue ? 1 : -1;
        }
        return 0;
      });
    } else {
      // 默认按 CreatedAt 倒序排列
      sortedData = filteredData.sort((a, b) => {
        const dateA = new Date(a.CreatedAt).getTime();
        const dateB = new Date(b.CreatedAt).getTime();
        return dateB - dateA; // 倒序排列，最晚时间排在前面
      });
    }

    const startIndex = (current - 1) * pageSize;
    const pagedData = sortedData.slice(startIndex, startIndex + pageSize);

    // 返回数据和分页信息
    return Promise.resolve({
      data: pagedData,
      success: true,
      total: filteredData.length,
    });
  }

  const handleCancelIssues = useCallback(
    () => {
      setLoading(true);
      selectPatchIssues({
        Operation: 'cancel',
        PatchID: patchId,
        IssueSolutionUUIDs: selectedIds,
      })
        .then((res) => {
          if (res?.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            onUpdate();
          }
        })
        .finally(() => {
          setSelectedIds([]);
          setLoading(false);
        });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [patchId, selectedIds],
  );

  return (
    <TcsSpin spinning={loading}>
      <TcsTable
        params={{
          records,
        }}
        rowKey="IssueSolutionRelUUID"
        columns={columns}
        cardBordered
        pagination={{ defaultPageSize: 10 }}
        rowSelection={
          isPublished
            ? false
            : {
                selectedRowKeys: selectedIds,
                onChange: (selectedRowKeys) => {
                  setSelectedIds(selectedRowKeys as string[]);
                },
              }
        }
        request={handleRequest}
        headerTitle={
          <>
            <TcsPopConfirm
              disabled={selectedIds.length === 0 || isPublished}
              title="警告"
              message="确认要移除？"
              onConfirm={() => handleCancelIssues()}
            >
              <Button
                disabled={selectedIds.length === 0 || isPublished || (isAfter && !updateAuthorized)}
                tooltip={
                  isPublished
                    ? '已发布的Patch单不能移除'
                    : isAfter && !updateAuthorized
                    ? '只有负责人才能添加缺陷/需求'
                    : '请勾选缺陷后进行移除'
                }
                type="primary"
              >
                批量移除
              </Button>
            </TcsPopConfirm>
            <Button
              type="primary"
              onClick={handelAddIssues}
              disabled={(isAfter && !updateAuthorized) || isPublished}
              tooltip={
                isPublished
                  ? '已发布Patch单不能继续添加缺陷'
                  : isAfter && !updateAuthorized
                  ? '只有负责人才能添加缺陷/需求'
                  : ''
              }
            >
              添加缺陷/需求
            </Button>
          </>
        }
        toolBarRender={() => <></>}
        scroll={{ x: 1800 }}
        scrollInTable
        search={{ labelWidth: 80 }}
        options={false}
      />
    </TcsSpin>
  );
};

export default SelectedTable;
