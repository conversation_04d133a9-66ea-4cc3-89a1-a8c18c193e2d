import { ProColumns } from '@tencent/tcs-component';
import { DropdownBox, List, Popover } from '@tencent/tea-component';
import React from 'react';
export function useIssueColumns({}): ProColumns[] {
  return [
    { dataIndex: 'IssueID', title: '缺陷单ID', width: '10%', sorter: true },
    {
      dataIndex: 'Title',
      title: '标题',
      width: '20%',
      linkable: true,
      linkProps: {
        target: 'blank',
        linkUrl: (text, record) =>
          `/page/defect_manage/__develop_center_next/iteration_manage/${
            record.Story ? 'story' : 'defect'
          }/detail?issue_id=${record?.IssueID}`,
      },
      renderText: (text, record) => (record?.Story ? record?.Story?.Title : record?.Issue?.Title),
      search: false,
    },
    {
      dataIndex: 'Title',
      title: '缺陷/需求标题',
      hideInTable: true,
    },
    {
      dataIndex: 'Type',
      title: '类型',
      width: '5%',
      renderText: (text, record) => (record?.Story ? 'story' : 'bug'),
      fieldProps: {
        mode: 'multiple',
        showSelectAll: true,
      },
      valueEnum: {
        story: {
          text: '需求',
          color: 'primary',
        },
        bug: {
          text: '缺陷',
          color: 'red',
        },
      },
    },
    {
      dataIndex: 'Priority',
      title: '优先级',
      width: '5%',
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'Priority',
        showType: 'tag',
      },
      renderText: (text, record) => (record?.Story ? record?.Story.Priority : record?.Issue.Priority),
    },
    {
      dataIndex: ['IssueSolution', 'ProductInfo'],
      title: '涉及产品',
      width: '10%',
      valueType: 'text',
      render: (text, record) => {
        const issueAppRel = record?.IssueSolution?.IssueAppRel;
        const productList = issueAppRel?.map((item) => item?.ProductVersionInfo?.ProductInfo?.Code) ?? [];
        return Array.from(new Set(productList)).join(',');
      },
    },
    {
      dataIndex: ['IssueSolution', 'IssueAppRel'],
      title: '涉及应用',
      width: '10%',
      valueType: 'text',
      render: (text, record) => {
        const issueAppRel = record?.IssueSolution?.IssueAppRel ?? [];
        return (
          <Popover
            placement="top"
            overlay={
              <DropdownBox>
                <List type="option">
                  {issueAppRel.map((item) => {
                    const version = `${item?.ReleaseApplicationPackage?.ApplicationVersion ?? ''}-${
                      item?.ReleaseApplicationPackage?.BuildTime ?? ''
                    }-${item?.ReleaseApplicationPackage?.CommitID?.substring(0, 8) ?? ''}`;
                    return (
                      <List.Item key={item?.ApplicationName}>
                        {item?.ApplicationName}-{version}
                      </List.Item>
                    );
                  })}
                </List>
              </DropdownBox>
            }
          >
            {issueAppRel.slice(0, 5).map((item) => (
              <div key={item?.ApplicationName}>{item?.ApplicationName};</div>
            ))}
            {issueAppRel?.length > 5 ? (
              <Popover
                trigger={'click'}
                placement="top"
                overlay={
                  <DropdownBox>
                    <List type="option">
                      {issueAppRel.map((item) => (
                        <List.Item key={item?.ApplicationName}>{item?.ApplicationName}</List.Item>
                      ))}
                    </List>
                  </DropdownBox>
                }
              >
                点击查看更多
              </Popover>
            ) : (
              ''
            )}
          </Popover>
        );
      },
    },
    {
      dataIndex: ['IssueSolution', 'Arch'],
      title: '架构',
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'PackageArch',
      },
      width: '10%',
      search: false,
    },
    {
      dataIndex: ['IssueSolution', 'Status'],
      title: '状态',
      width: '5%',
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'IssueStatus',
        showType: 'tag',
      },
    },
    {
      dataIndex: 'CreatedAt',
      title: '创建时间',
      width: '10%',
      valueType: 'dateTime',
      search: false,
      sorter: true,
    },
    {
      dataIndex: 'CreatedAt',
      title: '创建时间',
      width: '10%',
      valueType: 'dateRange',
      hideInTable: true,
    },
    { dataIndex: 'Warning', search: false, title: '备注', width: '10%' },
    {
      dataIndex: 'operation',
      valueType: 'option',
      title: '操作',
      fixed: 'right',
      width: '10%',
      render: (text, record) => {
        if (record?.IssueSolution?.TapdUrl) {
          return (
            <a href={record?.IssueSolution?.TapdUrl} target="_blank" rel="noreferrer">
              跳转至Tapd单
            </a>
          );
        }
        return '-';
      },
    },
  ];
}
