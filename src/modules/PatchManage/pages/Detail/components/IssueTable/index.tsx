/*
 * @Author: lucyfang
 * @Date: 2024-08-08 10:57:20
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-11-12 15:33:33
 * @Description: 请输入注释信息
 */
import React, { useCallback, useState } from 'react';
import IssueModal from '../IssueModal';
import { TcsCard } from '@tencent/tcs-component';
import SelectedTable from './SelectedTable';

const IssueTable = ({ getDetail, patchId, isPublished, records, MainProductCode, updateAuthorized, isAfter }) => {
  const [issueModalVisible, setIssueModalVisible] = useState(false);
  const handelAddIssues = useCallback(() => {
    setIssueModalVisible(!issueModalVisible);
  }, [issueModalVisible]);

  return (
    <TcsCard>
      <SelectedTable
        patchId={patchId}
        onUpdate={getDetail}
        records={records || []}
        isPublished={isPublished}
        handelAddIssues={handelAddIssues}
        updateAuthorized={updateAuthorized}
        isAfter={isAfter}
      />
      {issueModalVisible && (
        <IssueModal
          patchId={patchId}
          onClose={handelAddIssues}
          onSuccess={getDetail}
          selectedDefects={records}
          MainProductCode={MainProductCode}
        />
      )}
    </TcsCard>
  );
};
export default IssueTable;
