/*
 * @Author: lucyfang
 * @Date: 2025-03-20 16:12:24
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-03-25 17:33:17
 * @Description: 请输入注释信息
 */
import React, { useState, useEffect } from 'react';
import { TcsModal, TcsTable, TcsColumns } from '@tencent/tcs-component';
import { message } from '@tencent/tea-component';
import { createPatchVerification } from '@/common/api/patchManage';
interface IProps {
  onClose: () => void;
  patchDetail: any;
  onConfirm: () => void;
}
const getAppColumns = ({ patchDetail }) => {
  const uniqueProductCodes = [...new Set(patchDetail?.PatchedApplications?.map((item) => item.ProductInfo.Code))];
  const codeEnum = uniqueProductCodes.reduce((acc, code) => {
    acc[code] = {
      text: code,
    };
    return acc;
  }, {});
  return [
    {
      dataIndex: 'ApplicationName',
      title: '应用名称',
      width: '20%',
    },
    {
      dataIndex: ['ProductInfo', 'Code'],
      title: '产品Code',
      width: '15%',
      fieldProps: {
        mode: 'multiple',
        showSelectAll: true,
      },
      valueEnum: codeEnum,
    },
    {
      dataIndex: ['ProductVersion', 'Name'],
      title: '产品版本',
      width: '20%',
      search: false,
    },
    {
      dataIndex: ['PatchPackage', 'Arch'],
      title: '架构',
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'PackageArch',
      },
      width: '15%',
      search: false,
    },
    {
      width: '30%',
      dataIndex: ['Details', 'Latest'],
      title: 'Patch版本',
      search: false,
    },
  ] as TcsColumns[];
};

const ApplicationManageModal: React.FC<IProps> = ({ onClose, patchDetail, onConfirm }) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (patchDetail) {
      const allSelectedKeys = patchDetail?.PatchedApplications?.map((item) => item?.PatchPackage?.UUID);
      setSelectedRowKeys(allSelectedKeys);
    }
  }, [patchDetail]);
  const handleConfirm = () => {
    if (selectedRowKeys?.length === 0) {
      return message.error({ content: '请选择应用' });
    }
    setLoading(true);
    createPatchVerification({ PatchID: patchDetail?.PatchID || '', PackageUUIDList: selectedRowKeys })
      .then((res) => {
        if (res?.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          window.open(
            `/page/flow-design/flow-publish/exec?instance_id=${res?.WorkflowInstanceID}&workflow_id=${res?.WorkflowID}`,
            '_blank',
          );
        }
      })
      .finally(() => {
        setLoading(false);
        onConfirm();
      });
  };
  const handleRequest = (queryParams: any) => {
    const { current: pageIndex, pageSize, ApplicationName, ProductInfo: { Code } = {} } = queryParams;

    // 过滤数据源
    const filteredData = (patchDetail?.PatchedApplications || []).filter((app) => {
      const appName = app.ApplicationName || '';
      const appCodes = app.ProductInfo?.Code || [];

      const matchesAppName = appName.toLowerCase().includes(ApplicationName?.toLowerCase() || '');

      const matchesProductCode = Array.isArray(Code)
        ? Code.length === 0 || Code.some((code: string) => appCodes.includes(code))
        : appCodes.includes(Code);

      return matchesAppName && matchesProductCode;
    });

    return {
      data: filteredData || [],
      success: true,
      total: filteredData?.length,
      pageSize,
      current: pageIndex,
    };
  };
  return (
    <TcsModal visible title="应用列表" width={1200} onCancel={onClose} onOk={handleConfirm} confirmLoading={loading}>
      <TcsTable
        request={handleRequest}
        columns={getAppColumns({ patchDetail })}
        rowKey={(record) => record?.PatchPackage?.UUID}
        rowSelection={{
          type: 'checkbox',
          selectedRowKeys,
          onChange: (keys: string[]) => {
            setSelectedRowKeys(keys);
          },
        }}
        pagination={{ defaultPageSize: 10, current: 1 }}
        search={{}}
      />
    </TcsModal>
  );
};

export default ApplicationManageModal;
