import { toListParamsCApi } from '@/common/api/api';
import { listDataSolutionVersions, syncPatchToJG, updatePatchInfos, PatchManageApi } from '@/common/api/patchManage';
import { PACKAGE_STATUS, PUBLIC_PATCH_TYPE } from '@/common/config';
import { Bubble, Button, Form, message } from '@tencent/tea-component';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import styles from './index.module.scss';
import useLookup from '@/common/hookups/useLookup';
import { getDeliveryInfosByFromType } from '@/common/api/commonApi';
import { DefectManagementRoutePath } from '@/common/routePath';
import {
  TcsCard,
  TcsFormStaffSelect,
  TcsForm,
  TcsFormText,
  TcsFormSelect,
  TcsFormTextArea,
} from '@tencent/tcs-component';
import AttachmentManageModal from '../AttachmentManageModal';
import { AuthApi } from '@tencent/tcsc-base';
import PublishPatchModal from '../PublishPatchModal';
import CancelPublishModal from '../CancelPublishModal';
import ApplicationManageModal from '../ApplicationManageModal';
interface IBaselineInfoProps {
  patchDetail: any;
  patchId: string;
  onLoading: (boolean) => void;
  getDetail: () => void;
  onPublish: () => void;
  updateAuthorized: boolean;
  userList?: any;
  getUserList: () => void;
  isAfter: boolean;
}

const PatchBasicInfo: React.FC<IBaselineInfoProps> = (props) => {
  const {
    patchDetail,
    patchId,
    onLoading,
    getDetail,
    // onViewDeliveryList,
    onPublish,
    updateAuthorized,
    userList,
    getUserList,
    isAfter,
  } = props;

  const [isEdit, setIsEdit] = useState(false);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const [patchDetailVersionList, setPatchDetailVersionList] = useState<any>([]);

  const isPublished = patchDetail?.Status === 'published';
  const { getLookupByCode } = useLookup();
  const [visible, setVisible] = useState(false);
  const [formValue, setFormValue] = useState({});
  const formRef = useRef<any>();
  const [publishPatchVisible, setPublishPatchVisible] = useState(false);
  const [cancelPublishVisible, setCancelPublishVisible] = useState(false);
  const [visibleApplication, setVisibleApplication] = useState(false);

  const handleModifyDescription = () => {
    formRef?.current?.validateFields().then((values) => {
      onLoading(true);
      const originUsers = userList;
      const newUsers = values?.Owner;
      const usersToAdd = newUsers.filter((user) => !originUsers.includes(user));
      const usersToRemove = originUsers.filter((user) => !newUsers.includes(user));
      const updateBatch: any[] = [];

      if (usersToAdd.length > 0) {
        updateBatch.push({
          UserSubAccountUin: usersToAdd,
          RoleName: 'PatchOwner',
          AddScopes: [
            {
              Kind: 'Patch',
              Values: [patchId],
            },
          ],
        });
      }

      if (usersToRemove.length > 0) {
        updateBatch.push({
          UserSubAccountUin: usersToRemove,
          RoleName: 'PatchOwner',
          RemoveScopes: [
            {
              Kind: 'Patch',
              Values: [patchId],
            },
          ],
        });
      }

      if (updateBatch.length > 0) {
        AuthApi.BatchUpdateUserRoleScope({
          Items: updateBatch,
        }).then(() => {
          updatePatchInfos({
            PatchID: patchId,
            SolutionVersionID: Number(values?.DataVersionText),
            Description: values?.Description,
            PatchName: values?.PatchName,
          })
            .then((res) => {
              if (!res) {
                return;
              }
              if (res?.Error) {
                message.error({
                  content: res.Error.Message,
                });
              }
              getDetail();
              getUserList();
            })
            .finally(() => {
              onLoading(false);
              setIsEdit(false);
            });
        });
      } else {
        updatePatchInfos({
          PatchID: patchId,
          SolutionVersionID: Number(values?.DataVersionText),
          Description: values?.Description,
          PatchName: values?.PatchName,
        })
          .then((res) => {
            if (!res) {
              return;
            }
            if (res?.Error) {
              message.error({
                content: res.Error.Message,
              });
            }
            getDetail();
            getUserList();
          })
          .finally(() => {
            onLoading(false);
            setIsEdit(false);
          });
      }
    });
  };

  const handleCreatePatchVerification = () => {
    setVisibleApplication(true);
  };

  const handleSyncPatch = () => {
    onLoading(true);
    syncPatchToJG({ PatchID: patchId })
      .then((res) => {
        if (res?.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          const { Stage, CycleID } = res as any;
          switch (Stage) {
            case 0:
              return message.warning({
                content: '当前制品分支不支持同步，请联系TCSC小助手配置同步白名单',
                duration: 5000,
              });
            case 1:
              return message.warning({
                content: '后台任务处理中，5分钟后重试如果仍然是此结果，请联系开发同学处理',
                duration: 5000,
              });
            case 2:
              return message.success({
                content: `对应的周期出包记录ID是 ${CycleID}, 请前往极光系统直接出包`,
                duration: 5000,
              });
            default:
              return null;
          }
        }
      })
      .finally(() => {
        onLoading(false);
      });
  };
  // 发布到patch公共市场
  const handlePublishPatch = () => {
    if (patchDetail?.SolutionVersionUUID) {
      PatchManageApi.ValidateMarketSupportSolution({
        SolutionVersionUUID: patchDetail.SolutionVersionUUID,
      }).then(() => {
        setPublishPatchVisible(true);
      });
    }
  };
  // 撤回发布
  const handleCancelPublishPatch = () => {
    setCancelPublishVisible(true);
  };

  useEffect(() => {
    if (patchDetail?.SolutionVersion?.UUID && patchDetail?.ArtifactBranch?.DataBranch) {
      listDataSolutionVersions({
        SolutionVersion: {
          _Filter: toListParamsCApi(
            { UUID: patchDetail?.SolutionVersion?.UUID, Branch: patchDetail?.SolutionVersion?.Branch },
            {
              useEqFields: ['UUID', 'Branch'],
            },
          )._Filter?.concat([{ Key: 'Tag', Op: '!=', Value: '' }]),
          _Order: [
            {
              Key: 'TagNum',
              Sort: 'DESC',
            },
          ],
          _Select: ['id', 'tag_num', 'audit_description'],
        },
      })
        .then((res) => {
          if (res?.Error) {
            return message.error({
              content: res.Error.Message,
            });
          }
          setPatchDetailVersionList(res?.ListSolutionVersions || []);
        })
        .finally(() => {});
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [patchDetail?.ArtifactBranch?.DataBranch, patchDetail?.SolutionVersion?.UUID]);

  useEffect(() => {
    if (patchDetail) {
      // 保留原始值
      const value = {
        PatchID: patchDetail?.PatchID,
        PatchName: patchDetail?.Name,
        PatchQueue: patchDetail?.PatchQueue?.Name,
        Solution: patchDetail?.Solution?.Name,
        SolutionVersion: patchDetail?.SolutionVersion?.Code,
        Arch: patchDetail?.Arch ? getLookupByCode('PackageArch', patchDetail.Arch)?.Name : '-',
        ArtifactBranch: (
          <a
            href={`/page/product_center/workspace/solution/tag_list?pageType=2&defaultArch=${patchDetail?.Arch}&solutionVersionCode=${patchDetail?.SolutionVersion?.Code}&solutionVersionUUID=${patchDetail?.SolutionVersion?.UUID}`}
            target="_blank"
            rel="noreferrer"
          >
            {patchDetail?.ArtifactBranch?.BranchName}
          </a>
        ),
        BaselineTag: (
          <a
            href={`/page/product_center/workspace/solution/edit_tag?readonly=true&pageType=2&artifactBranchUUID=${patchDetail?.ArtifactBranchUUID}defaultArch=${patchDetail?.Arch}&solutionVersionCode=${patchDetail?.SolutionVersion?.Code}&solutionVersionUUID=${patchDetail?.SolutionVersion?.UUID}&tagId=${patchDetail?.BaselineTag?.ID}`}
            target="_blank"
            rel="noreferrer"
          >
            {patchDetail?.BaselineTag?.TagNum}
          </a>
        ),
        PatchTag: (
          <a
            href={`/page/product_center/workspace/solution/edit_tag?readonly=true&pageType=2&artifactBranchUUID=${patchDetail?.ArtifactBranchUUID}defaultArch=${patchDetail?.Arch}&solutionVersionCode=${patchDetail?.SolutionVersion?.Code}&solutionVersionUUID=${patchDetail?.SolutionVersion?.UUID}&tagId=${patchDetail?.PatchTag?.ID}`}
            target="_blank"
            rel="noreferrer"
          >
            {patchDetail?.PatchTag?.TagNum}
          </a>
        ),
        DataVersionText: String(patchDetail?.SolutionVersion?.ID),
        Branch: patchDetail?.SolutionVersion?.Branch,
        Description: patchDetail?.Description,
        Owner: userList || [],
      };
      setFormValue(value);
      formRef?.current?.setFieldsValue({
        ...value,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [patchDetail, patchDetail?.Arch, userList]);
  const editText = useMemo(() => {
    if (isPublished) {
      return '已发布Patch单不能继续修改基本信息';
    }
    return isAfter && !updateAuthorized ? '只有负责人有编辑权限' : '';
  }, [isPublished, isAfter, updateAuthorized]);

  const editCtrlText = useMemo(() => {
    if (isPublished) {
      return '已发布Patch单不能继续完善变更单';
    }
    return isAfter && !updateAuthorized ? '只有负责人才能完善变更单' : '';
  }, [isPublished, isAfter, updateAuthorized]);

  const publishBtnTooltip = useMemo(() => {
    if (isAfter && !updateAuthorized) {
      return '只有负责人才能发布';
    }
    if (isPublished) {
      return '当前为已发布状态不能发布';
    }
    return '';
  }, [isAfter, isPublished, updateAuthorized]);

  const handleOpenDelevery = () => {
    onLoading(true);
    getDeliveryInfosByFromType({
      FromType: 'patch',
      FromKey: patchDetail.PatchID,
    })
      .then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          const deliveryList = res.DeliveryDetailInfo || [];
          if (deliveryList.length === 0) {
            message.warning({
              content: '当前缺陷单没有关联交付单',
            });
          } else {
            // 直接跳转到交付单详情页
            window.open(`${DefectManagementRoutePath.DELIVERY_DETAIL_PAGE}?uuid=${deliveryList[0].UUID}`, '_blank');
          }
        }
      })
      .finally(() => {
        onLoading(false);
      });
  };
  return (
    <>
      <TcsCard
        className={styles.basic}
        title="基本信息"
        collapsible
        collapseState={{
          persistenceKey: '/defect_manage/patch_manage/detail/basic-card',
        }}
        extra={
          <>
            {isEdit ? (
              <>
                <Button type="link" onClick={handleModifyDescription}>
                  确定
                </Button>
                <Button
                  type="link"
                  onClick={() => {
                    setIsEdit(false);

                    // 恢复原始值并重置表单验证状态
                    formRef.current?.resetFields();
                    // 恢复原始值
                    formRef.current?.setFieldsValue(formValue);
                  }}
                >
                  取消
                </Button>
              </>
            ) : (
              <>
                <Button
                  type="link"
                  disabled={(isAfter && !updateAuthorized) || isPublished}
                  onClick={() => {
                    inputRef?.current?.focus();
                    setIsEdit(true);
                  }}
                  tooltip={editText}
                >
                  编辑
                </Button>
                {!patchDetail?.OperationSheetID && patchDetail?.PatchedApplications?.length > 0 && (
                  <Button
                    type="link"
                    disabled={(isAfter && !updateAuthorized) || isPublished}
                    tooltip={editCtrlText}
                    onClick={() => {
                      window.open(
                        `/page/product-market-jiguang/ops-sheet-manager/create-parent-sheet?patchId=${patchId}&sheetScene=patch`,
                        '_blank',
                      );
                    }}
                  >
                    完善变更单
                  </Button>
                )}
                <>
                  <Button
                    disabled={
                      !(
                        patchDetail?.Status === PACKAGE_STATUS.Draft &&
                        patchDetail?.OperationSheetID &&
                        patchDetail?.PatchedApplications?.length > 0
                      )
                    }
                    tooltip={
                      // eslint-disable-next-line no-nested-ternary
                      !(patchDetail?.Status === PACKAGE_STATUS.Draft)
                        ? 'Patch单应为草稿状态'
                        : // eslint-disable-next-line no-nested-ternary
                        !patchDetail?.OperationSheetID && patchDetail?.PatchedApplications?.length > 0
                        ? '需完善变更单'
                        : !(patchDetail?.PatchedApplications?.length > 0)
                        ? '需添加应用及产品'
                        : ''
                    }
                    type="link"
                    onClick={handleCreatePatchVerification}
                  >
                    测试出包
                  </Button>
                  <Button
                    type="link"
                    onClick={onPublish}
                    disabled={(isAfter && !updateAuthorized) || isPublished}
                    tooltip={publishBtnTooltip}
                  >
                    发布
                  </Button>
                  {isPublished && patchDetail?.IsPublishMarket === PUBLIC_PATCH_TYPE.No_Publish && (
                    <Button
                      type="link"
                      onClick={handlePublishPatch}
                      disabled={isAfter && !updateAuthorized}
                      tooltip={isAfter && !updateAuthorized ? '只有负责人才能发布' : ''}
                    >
                      发布到Patch公共市场
                    </Button>
                  )}
                  {patchDetail?.IsPublishMarket === PUBLIC_PATCH_TYPE.Publish && (
                    <Button
                      type="link"
                      onClick={handleCancelPublishPatch}
                      disabled={isAfter && !updateAuthorized}
                      tooltip={isAfter && !updateAuthorized ? '只有负责人才能发布' : ''}
                    >
                      撤回发布
                    </Button>
                  )}
                </>
                <Button type="link" onClick={handleSyncPatch}>
                  同步至周期出包
                </Button>
                <Button
                  type="link"
                  onClick={() => {
                    setVisible(true);
                  }}
                >
                  查看附件
                </Button>
                {patchDetail?.OperationSheetID && (
                  <Button
                    type="link"
                    onClick={() => {
                      window.open(
                        `/page/product-market-jiguang/ops-sheet-manager/parent-sheet-detail?parentSheetId=${patchDetail?.OperationSheetID}&sheetScene=patch`,
                        '_blank',
                      );
                    }}
                  >
                    查看变更单
                  </Button>
                )}
                {patchDetail?.Status === PACKAGE_STATUS.Published && (
                  <Button type="link" onClick={handleOpenDelevery}>
                    查看关联交付单
                  </Button>
                )}
              </>
            )}
          </>
        }
      >
        <TcsForm formRef={formRef} grid={true}>
          <TcsFormText readonly label="PatchID" name="PatchID" />
          <TcsFormText readonly={!isEdit || isPublished} label="Patch名称" name="PatchName" />
          <TcsFormText readonly label="所属队列" name="PatchQueue" />
          <TcsFormText readonly label="解决方案" name="Solution" />
          <TcsFormText readonly label="解决方案版本" name="SolutionVersion" />
          <TcsFormText readonly label="架构" name="Arch" />
          <TcsFormText readonly label="制品分支" name="ArtifactBranch" />
          <TcsFormText readonly label="初始制品Tag" name="BaselineTag" />
          <TcsFormText readonly label="Patch制品Tag" name="PatchTag" />
          <TcsFormSelect
            readonly={!isEdit || isPublished}
            label="数据快照版本"
            name="DataVersionText"
            fieldProps={{
              options:
                patchDetailVersionList.map((item) => ({
                  value: `${item.ID}`,
                  label: `${item?.TagNum ?? ''}-${item?.AuditDescription ?? ''}`,
                })) || [],
            }}
            render={(text, context, dom) => {
              const item = patchDetailVersionList.find((item) => `${item.ID}` === `${text}`);
              return (
                <Bubble content={dom}>
                  <Form.Text style={{ marginTop: 6 }}>{item?.TagNum || text}</Form.Text>
                </Bubble>
              );
            }}
          />
          <TcsFormText readonly label="数据快照分支" name="Branch" />
          <TcsFormTextArea readonly={!isEdit || isPublished} label="描述" name="Description" />
          <TcsFormStaffSelect
            readonly={!isEdit || isPublished}
            label="Patch单负责人"
            name="Owner"
            valueField="enName"
            fieldProps={{
              multiple: true,
            }}
            rules={[
              {
                required: true,
                message: '请选择Patch单负责人',
              },
            ]}
          />
        </TcsForm>
        {visible && (
          <AttachmentManageModal
            onClose={() => {
              setVisible(false);
            }}
            onSuccess={() => {
              setVisible(false);
            }}
            patchDetail={patchDetail}
            updateAuthorized={updateAuthorized}
            isAfter={isAfter}
          />
        )}
      </TcsCard>
      {publishPatchVisible && (
        <PublishPatchModal
          patchID={patchId}
          onCancel={() => {
            setPublishPatchVisible(false);
          }}
          onSuccess={() => {
            setPublishPatchVisible(false);
            getDetail();
          }}
        />
      )}
      {cancelPublishVisible && (
        <CancelPublishModal
          patchID={patchId}
          onCancel={() => {
            setCancelPublishVisible(false);
          }}
          onSuccess={() => {
            setCancelPublishVisible(false);
            getDetail();
          }}
        />
      )}
      {visibleApplication && (
        <ApplicationManageModal
          onClose={() => setVisibleApplication(false)}
          patchDetail={patchDetail}
          onConfirm={() => {
            setVisibleApplication(false);
            getDetail();
          }}
        />
      )}
    </>
  );
};

export default PatchBasicInfo;
