import { Tcs<PERSON><PERSON>, Tcs<PERSON>ormText, TcsQueryFilter } from '@tencent/tcs-component';

import React from 'react';

export interface IProps {
  onSearch: (values: any) => void;
}

const Search: React.FC<IProps> = ({ onSearch }) => {
  const handleSearch = (values) => {
    onSearch(values);
  };
  // const { lookups } = useLookup(['Severity', 'IssueStatus']);

  return (
    <TcsCard style={{ marginBottom: 20 }} bordered={true}>
      <TcsQueryFilter onSearch={handleSearch}>
        <TcsFormText label="产品Code" name="Code" />
        <TcsFormText label="产品版本" name="ProductVersion" />
        <TcsFormText label="应用名称" name="ApplicationName" />
      </TcsQueryFilter>
    </TcsCard>
  );
};

export default Search;
