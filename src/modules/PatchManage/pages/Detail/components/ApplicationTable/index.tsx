import { Button, Dropdown, Justify, List, Modal, Table, Tooltip, message } from '@tencent/tea-component';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { getPatchColumns } from '../../config';
import {
  groupable,
  pageable,
  scrollable,
  selectable,
  sortable,
  autotip,
} from '@tencent/tea-component/lib/table/addons';
import AppProductModal from '../AppProductModal';
import { modifyPatchApplications } from '@/common/api/patchManage';
import ProductTagDiffModal from '../ProductTagDiffModal';
import Search from './Search';
import _ from 'lodash';
import { TcsCard } from '@tencent/tcs-component';
import ImportAppFromCSVModal from '../ImportAppFromCSVModal';
import useLookup from '@/common/hookups/useLookup';

interface ApplicationTableProps {
  isPublished: boolean;
  patchId: string;
  onloading: (boolean) => void;
  records: any[];
  getDetail: () => void;
  detail: any;
  updateAuthorized?: boolean;
  isAfter: boolean;
}

const ApplicationTable = ({
  isPublished,
  patchId,
  onloading,
  records,
  getDetail,
  detail,
  updateAuthorized,
  isAfter,
}: ApplicationTableProps) => {
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [appModalVisible, setAppModalVisible] = useState(false);
  const [compareModalVisible, setCompareModalVisible] = useState(false);
  const [isBatchCancel, setIsBatchCancel] = useState(false);
  const [sorts, setSorts] = useState<any>([]);
  const [tableRecords, setTableRecords] = useState<any>([]);
  const importAppRef = useRef<any>();
  const { getLookupByCode } = useLookup([]);

  tableRecords?.sort(sortable.comparer(sorts));

  const handleBeforeRemove = () => {
    const selectedRecords = records.filter((item) => selectedIds.includes(item?.ApplicationName));

    const selectedRecordsWithIssues = selectedRecords.reduce((acc, record) => {
      const keys = Object.keys(record?.Details?.Selected).filter((item) => item !== 'Manual');
      return acc.concat(keys);
    }, []);
    return selectedRecordsWithIssues;
  };

  const handleBatchRemove = () => {
    const selectedRecordsWithIssues = handleBeforeRemove();
    if (selectedRecordsWithIssues.length > 0) {
      setSelectedIds([]);
      return Modal.error({
        message: '移除失败',
        description: `存在关联的缺陷单：${selectedRecordsWithIssues.join(',')},请取消关联后移除`,
      });
    }
    onloading(true);
    modifyPatchApplications({
      PatchID: patchId,
      Applications: selectedIds.map((item) => ({
        ApplicationName: item,
      })),
      Operation: 'cancel',
    })
      .then((res) => {
        if (res?.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          getDetail();
        }
      })
      .finally(() => {
        onloading(false);
        setIsBatchCancel(false);
        setSelectedIds([]);
      });
  };

  const handleModifyVersion = useCallback(() => {
    setAppModalVisible(!appModalVisible);
  }, [appModalVisible]);

  const handleCloseCompare = useCallback(() => {
    setCompareModalVisible(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [compareModalVisible]);

  useEffect(() => {
    setTableRecords(records);
  }, [records]);

  const handleSearch = (values) => {
    const findTableRecords = records?.filter((item) => {
      const findCode = !values?.Code || item?.ProductInfo?.Code.toLowerCase()?.includes(values?.Code.toLowerCase());
      const findProductVersion =
        !values?.ProductVersion || item?.ProductVersion?.Name?.includes(values?.ProductVersion);
      const findAppName =
        !values?.ApplicationName ||
        item?.ApplicationName.toLowerCase()?.includes(values?.ApplicationName.toLowerCase());
      return findCode && findProductVersion && findAppName;
    });
    if (_.isUndefined(findTableRecords)) {
      setTableRecords(records);
    } else {
      setTableRecords(findTableRecords);
    }
  };

  return (
    <TcsCard title="应用及产品" collapsible>
      <Search onSearch={handleSearch} />
      <Table.ActionPanel>
        <Justify
          left={
            isBatchCancel ? (
              <>
                <Button type="primary" disabled={selectedIds.length < 1} onClick={handleBatchRemove}>
                  确定
                </Button>
                <Button
                  type="primary"
                  onClick={() => {
                    setIsBatchCancel(false);
                    setSelectedIds([]);
                  }}
                >
                  取消
                </Button>
              </>
            ) : (
              <>
                {/* <Button
                  type="primary"
                  onClick={handleModifyVersion}
                  tooltip={isPublished && '已发布Patch单不能继续添加应用制品版本'}
                  disabled={isPublished}
                >
                  添加应用制品版本
                </Button> */}
                <Tooltip
                  title={
                    isPublished
                      ? '已发布Patch单不能继续添加应用制品版本'
                      : isAfter && !updateAuthorized
                      ? '只有负责人可以添加应用制品版本'
                      : ''
                  }
                >
                  <Dropdown
                    button="添加应用制品版本"
                    appearance="button"
                    disabled={isPublished || (isAfter && !updateAuthorized)}
                  >
                    <List type="option">
                      <List.Item onClick={handleModifyVersion}>手动添加</List.Item>
                      <List.Item
                        onClick={() => {
                          importAppRef.current?.show();
                        }}
                      >
                        导入CSV文件
                      </List.Item>
                    </List>
                  </Dropdown>
                </Tooltip>
                <Button
                  type="primary"
                  tooltip={
                    isPublished
                      ? '已发布Patch单不能批量移除应用'
                      : isAfter && !updateAuthorized
                      ? '只有负责人可以移除应用'
                      : ''
                  }
                  onClick={() => setIsBatchCancel(true)}
                  disabled={isPublished || (isAfter && !updateAuthorized)}
                >
                  批量移除
                </Button>
                <Button
                  type="primary"
                  tooltip={
                    isPublished
                      ? '已发布Patch单不能进行制品版本对比'
                      : isAfter && !updateAuthorized
                      ? '只有负责人可以操作'
                      : ''
                  }
                  onClick={() => setCompareModalVisible(true)}
                  disabled={isPublished || (isAfter && !updateAuthorized)}
                >
                  制品版本对比
                </Button>
              </>
            )
          }
        />
      </Table.ActionPanel>
      <Table
        key="app"
        columns={getPatchColumns({ patchId, onloading, isPublished, getLookupByCode, isAfter, updateAuthorized })}
        bordered="all"
        records={tableRecords || []}
        recordKey="ApplicationName"
        addons={[
          scrollable({
            minWidth: 1200,
          }),

          ...[
            isBatchCancel
              ? selectable({
                  value: selectedIds,
                  onChange: (keys) => {
                    setSelectedIds(keys);
                  },
                  rowSelect: true,
                  width: 36,
                  all: true,
                })
              : {},
          ],

          groupable({
            headerGroups: [
              {
                key: 'PatchAppVersion',
                title: '应用制品版本',
                children: ['Latest', 'Baseline'],
              },
            ],
          }),
          sortable({
            columns: [
              {
                key: 'Code',
                prefer: 'desc',
              },
            ],
            value: sorts,
            onChange: (value) => setSorts(value),
          }),
          pageable(),
          autotip({
            emptyText: '暂无数据',
          }),
        ]}
      />
      {appModalVisible && <AppProductModal patchId={patchId} onClose={handleModifyVersion} onSuccess={getDetail} />}
      {compareModalVisible && (
        <ProductTagDiffModal
          solutionVersionUUID={detail?.SolutionVersionUUID}
          onClose={handleCloseCompare}
          onSuccess={getDetail}
          patchDetail={detail}
          arch={detail?.Arch}
          baselineTagID={detail?.BaselineTagUUID}
          patchId={patchId}
        />
      )}
      <ImportAppFromCSVModal
        patchId={patchId}
        ref={importAppRef}
        onConfirm={() => {
          getDetail();
        }}
      />
    </TcsCard>
  );
};
export default ApplicationTable;
