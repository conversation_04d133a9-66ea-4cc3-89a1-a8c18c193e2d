import React, { useState } from 'react';
import { Input, message } from '@tencent/tea-component';
import { updatePatchApplicationRel } from '@/common/api/patchManage';
import { toListParamsCApi } from '@/common/api/api';

const RenderDescription = ({ onLoading, record, isPublished }) => {
  const [inputValue, setInputValue] = useState(record?.Description);

  const handelUpdateVerification = ({ VerificationID, Description }) => {
    onLoading(true);
    updatePatchApplicationRel({
      PatchVerificationRel: {
        Description,
        _Filter: toListParamsCApi(
          { ID: VerificationID },
          {
            useEqFields: ['ID'],
          },
        )._Filter,
      },
    })
      .then((res) => {
        if (res?.Error) {
          message.error({
            content: res?.Error?.Message,
          });
        } else {
          setInputValue(Description);
        }
      })
      .finally(() => {
        onLoading(false);
      });
  };

  return (
    <Input
      onChange={(value) => setInputValue(value)}
      value={inputValue}
      readOnly={isPublished}
      onBlur={(e) => {
        handelUpdateVerification({
          Description: e.target.value,
          VerificationID: record?.ID,
        });
      }}
    />
  );
};

export default RenderDescription;
