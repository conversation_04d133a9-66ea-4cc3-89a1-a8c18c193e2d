import { getPatchIssueDependencies, selectPatchIssues } from '@/common/api/patchManage';
import { Bubble, Button, Card, Icon, Transfer, message } from '@tencent/tea-component';
import React, { useEffect, useState, useCallback } from 'react';
import Search from './Search';
// import { getIssueColumns } from '../../config';
// import useLookup from '@/common/hookups/useLookup';
// import { SORT_TYPE } from '@/common/config';
// import moment from 'moment';
import { TcsCard, TcsTabs, TcsModal, TcsTable, TcsButtonGroup } from '@tencent/tcs-component';
import SelectIssueTable from './SelectIssueTable';
import styles from './index.module.less';
import IssueTableModal from './IssueTableModal';

interface IssueModalProps {
  patchId: string;
  onClose: () => void;
  onSuccess: () => void;
  selectedDefects?: any[];
  MainProductCode: string;
}
const IssueModal: React.FC<IssueModalProps> = (props) => {
  const { patchId, onClose, onSuccess, selectedDefects = [], MainProductCode } = props;
  const [queryParams, setQueryParams] = useState({});
  const [confirmLoading, setConfirmLoading] = useState(false);
  // const [dataLoading, setDataLoading] = useState(true);
  const [transferLoading, setTransferLoading] = useState(false);

  const [targetRecords, setTargetRecords] = useState<any>(selectedDefects || []);
  const [dependentIssues, setDependentIssues] = useState<any>({});

  const [selectedBugs, setSelectedBugs] = useState<any[]>([]);
  const [selectedStorys, setSelectedStorys] = useState<any[]>([]);
  const [issueVisibility, setIssueVisibility] = useState<{ [key: string]: boolean }>({});
  const [isFullScreen, setIsFullScreen] = useState(false);

  const rightTableColumns = [
    { dataIndex: 'IssueID', title: '缺陷ID', width: '30%' },
    {
      dataIndex: ['IssueSolution', 'IssueType'],
      title: '类型',
      width: '30%',
      render(_, record: any) {
        return record?.IssueSolution?.IssueType === 'bug' ? '缺陷' : '需求';
      },
    },
    {
      dataIndex: 'Dependent',
      width: '30%',
      title: (
        <>
          依赖缺陷个数
          <Bubble content="对于所选缺陷关联的应用，存在一些大于基线的应用版本而小于所选的缺陷的应用版本的中间版本，关联了这些中间版本的缺陷称为依赖缺陷（基线应用版本 < 依赖缺陷应用版本 < 所选缺陷版本）">
            <Icon type="info" />
          </Bubble>
        </>
      ),
      render: (_, record) => {
        const dependencies = dependentIssues?.[record?.IssueSolutionRelUUID] ?? [];
        return (
          <>
            <a onClick={() => handleOpenIssueTabel(record)}>{dependencies.length ?? 0}</a>
            {issueVisibility[record.IssueSolutionRelUUID] && (
              <IssueTableModal
                onClose={() => handleClose(record.IssueSolutionRelUUID)}
                dependencies={dependencies}
                targetRecords={targetRecords}
                issueSolutionRelUUID={record?.IssueSolutionRelUUID}
                handleUpdataInfo={handleUpdataInfo}
              />
            )}
          </>
        );
      },
    },
    {
      dataIndex: 'operation',
      title: '操作',
      width: '20%',
      render: (_, record) => (
        <TcsButtonGroup
          items={[
            {
              text: '移除',
              onClick: () => handleRemove(record?.IssueSolutionRelUUID),
              hidden: selectedDefects?.find((item) => item.IssueSolutionRelUUID === record?.IssueSolutionRelUUID),
            },
          ]}
        />
      ),
    },
  ];

  const handleFullScreenChange = (isFullScreen) => {
    setIsFullScreen(isFullScreen);
  };

  const handleSearch = (values) => {
    setQueryParams(values);
  };

  useEffect(() => {
    if (selectedDefects?.length) {
      getDependencies({ ids: selectedDefects.map((item) => item.IssueSolutionRelUUID) });
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 每次更改都需要重新计算依赖
  const getDependencies = useCallback(
    ({ ids }) => {
      setTransferLoading(true);
      getPatchIssueDependencies({
        PatchID: patchId,
        IssueSolutionRelUUIDs: ids,
      })
        .then((res) => {
          if (res?.Error) {
            message.error({ content: res.Error.Message });
          } else {
            setDependentIssues(res?.Dependencies || {});
            setTargetRecords((records) => [...records, ...selectedBugs, ...selectedStorys]);
            setSelectedBugs([]);
            setSelectedStorys([]);
          }
        })
        .finally(() => {
          setTransferLoading(false);
        });
    },
    [patchId, selectedBugs, selectedStorys],
  );

  const handleTransfer = () => {
    const allSelectedIds = [
      ...selectedBugs.map((item) => item.IssueSolutionRelUUID),
      ...selectedStorys.map((item) => item.IssueSolutionRelUUID),
    ].concat(targetRecords.map((item) => item?.IssueSolutionRelUUID));
    getDependencies({ ids: allSelectedIds });
  };

  const handleRemove = (key) => {
    const allSelectedIds = targetRecords
      .filter((item) => item?.IssueSolutionRelUUID !== key)
      .map((item) => item?.IssueSolutionRelUUID);
    setTargetRecords((records) => records.filter((item) => item?.IssueSolutionRelUUID !== key));
    getDependencies({ ids: allSelectedIds });
  };

  const handleConfirm = () => {
    setConfirmLoading(true);
    selectPatchIssues({
      Operation: 'select',
      PatchID: patchId ?? '',
      IssueSolutionUUIDs: targetRecords.map((item) => item?.IssueSolutionRelUUID),
    })
      .then((res) => {
        if (res?.Error) {
          message.error({ content: res.Error.Message });
        } else {
          onSuccess();
        }
      })
      .finally(() => {
        setConfirmLoading(false);
        onClose();
      });
  };
  // 打开特定记录的弹窗
  const handleOpenIssueTabel = (record) => {
    setIssueVisibility((prev) => ({
      ...prev,
      [record.IssueSolutionRelUUID]: true,
    }));
  };

  // 关闭特定记录的弹窗
  const handleClose = (issueSolutionRelUUID) => {
    setIssueVisibility((prev) => ({
      ...prev,
      [issueSolutionRelUUID]: false,
    }));
  };
  const handleUpdataInfo = useCallback(
    (updataInfo) => {
      setTargetRecords((prevRecords) => {
        const recordExists = prevRecords.some(
          (record) => record.IssueSolutionRelUUID === updataInfo.IssueSolutionRelUUID,
        );
        let newTargetRecords = prevRecords;
        if (!recordExists) {
          newTargetRecords = [...prevRecords, updataInfo];
        }
        const allSelectedIds = newTargetRecords.map((item) => item.IssueSolutionRelUUID);
        getDependencies({ ids: allSelectedIds });
        return newTargetRecords;
      });
    },
    [getDependencies],
  );

  return (
    <TcsModal
      visible
      title="添加缺陷/需求"
      width="80%"
      onCancel={onClose}
      destroyOnClose
      footer={
        <Button type="primary" onClick={handleConfirm} loading={confirmLoading} disabled={targetRecords.length === 0}>
          确认添加
        </Button>
      }
      onFullScreenChange={handleFullScreenChange}
      showFullScreenIcon={true}
      wrapClassName={`${isFullScreen ? styles.full_screen_modal : ''}`}
    >
      <Search onSearch={handleSearch} MainProductCode={MainProductCode} />
      <Card className={`${isFullScreen ? styles.full_screen_card : ''}`}>
        <Card.Body>
          <Transfer
            operations={[
              {
                disabled: selectedBugs.length === 0 && selectedStorys.length === 0,
                onClick: handleTransfer,
                loading: transferLoading,
              },
              false,
            ]}
            leftCell={
              <Transfer.Cell style={{ width: '70%' }} scrollable={!isFullScreen}>
                <TcsTabs>
                  <TcsTabs.TabPane tab="选择缺陷" key="bug" id="bug">
                    <SelectIssueTable
                      patchId={patchId}
                      type="bug"
                      targetRecords={targetRecords}
                      params={queryParams}
                      selectedIds={selectedBugs.map((item) => item.IssueSolutionRelUUID)}
                      onChange={(rows: any[]) => {
                        setSelectedBugs(rows);
                      }}
                      isFullScreen={isFullScreen}
                    />
                  </TcsTabs.TabPane>
                  <TcsTabs.TabPane tab="选择需求" key="story" id="story">
                    <SelectIssueTable
                      patchId={patchId}
                      type="story"
                      targetRecords={targetRecords}
                      params={queryParams}
                      selectedIds={selectedStorys.map((item) => item.IssueSolutionRelUUID)}
                      onChange={(rows: any[]) => setSelectedStorys(rows)}
                      isFullScreen={isFullScreen}
                    />
                  </TcsTabs.TabPane>
                </TcsTabs>
              </Transfer.Cell>
            }
            rightCell={
              <Transfer.Cell style={{ width: '30%' }} scrollable={!isFullScreen}>
                <TcsCard title={`已选缺陷/需求 (${targetRecords.length})`}>
                  <TcsTable
                    rowKey="IssueSolutionRelUUID"
                    dataSource={targetRecords}
                    columns={rightTableColumns}
                    scrollInTable={isFullScreen}
                    cardBordered
                    options={false}
                    onRow={(record) => {
                      const isModalVisible = issueVisibility[record?.IssueSolutionRelUUID];
                      if (dependentIssues?.[record?.IssueSolutionRelUUID] && !isModalVisible) {
                        return {
                          tooltip: '该缺陷存在依赖缺陷项',
                        };
                      }
                      return {};
                    }}
                  />
                </TcsCard>
              </Transfer.Cell>
            }
          />
        </Card.Body>
      </Card>
    </TcsModal>
  );
};

export default IssueModal;
