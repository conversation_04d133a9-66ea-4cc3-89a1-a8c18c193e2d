import { ProForm, QueryFilter } from '@/common/components';
// import useLookup from '@/common/hookups/useLookup';
import { Card, Input } from '@tencent/tea-component';
import { RangePicker } from '@tencent/tea-component/lib/datepicker/RangePicker';
import React, { useEffect } from 'react';

export interface IProps {
  onSearch: (values: any) => void;
  initialValues?: any;
  MainProductCode?: string;
}
const Search: React.FC<IProps> = ({ onSearch, MainProductCode }) => {
  useEffect(() => {}, []);
  const handleSearch = (values) => {
    onSearch(values);
  };
  // 设置默认值
  const initialValues = MainProductCode ? { ProductCode: MainProductCode } : {};
  useEffect(() => {
    // 在组件初始化时调用 handleSearch 并传递默认值
    if (MainProductCode) {
      handleSearch(initialValues);
    }
  }, [MainProductCode]);

  // const { lookups } = useLookup(['Priority']);
  return (
    <Card>
      <Card.Body>
        <QueryFilter onSearch={handleSearch} defaultSpread initialValues={initialValues}>
          <ProForm.Item label="产品" dataIndex="ProductCode" key="ProductCode">
            <Input placeholder="请输入产品" />
          </ProForm.Item>
          <ProForm.Item label="应用名称" dataIndex="ApplicationName" key="ApplicationName">
            <Input placeholder="请输入应用名称" />
          </ProForm.Item>
          <ProForm.Item label="缺陷单/需求单ID" dataIndex="IssueID" key="IssueID">
            <Input placeholder="请输入缺陷单/需求单ID" />
          </ProForm.Item>
          <ProForm.Item label="缺陷单标题" dataIndex="Title" key="Title">
            <Input placeholder="请输入缺陷单标题" />
          </ProForm.Item>
          <ProForm.Item label="TAPD" dataIndex="TapdID" key="TapdID">
            <Input placeholder="请输入TAPD" />
          </ProForm.Item>
          {/* <ProForm.Item label="优先级" dataIndex="Priority" key="Priority">
            <SelectMultiple
              appearance="button"
              placeholder="请选择优先级"
              size="m"
              matchButtonWidth
              clearable
              options={
                lookups?.Priority?.map((item) => ({
                  value: item.Code,
                  text: item.Name,
                })) || []
              }
            />
          </ProForm.Item> */}
          <ProForm.Item label="创建时间" dataIndex="CreatedAt" key="CreatedAt">
            <RangePicker />
          </ProForm.Item>
          <ProForm.Item label="更新时间" dataIndex="UpdatedAt" key="UpdatedAt">
            <RangePicker />
          </ProForm.Item>
        </QueryFilter>
      </Card.Body>
    </Card>
  );
};

export default Search;
