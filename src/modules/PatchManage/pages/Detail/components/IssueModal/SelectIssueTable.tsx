import React from 'react';
import { TcsTable } from '@tencent/tcs-component';
import { PatchManageApi } from '@/common/api/patchManage';
import { useIssueColumns } from './config';
import moment from 'moment';

export interface IProps {
  targetRecords: any[];
  type: 'bug' | 'story';
  params?: any;
  patchId: string;
  selectedIds: string[];
  onChange: (rows: any[]) => void;
  isFullScreen?: boolean;
}

const SelectIssueTable: React.FC<IProps> = ({
  patchId,
  targetRecords,
  selectedIds,
  onChange,
  type,
  params,
  isFullScreen,
}) => {
  const columns = useIssueColumns({ type });

  function handleRequest(params, options) {
    const OrderBy: any[] = [];
    if (options.sorter?.length) {
      const field = options.sorter[0]?.field[1];
      const order = options.sorter[0].order === 'ascend' ? 'ASC' : 'DESC';

      if (field === 'UpdateAt') {
        OrderBy.push({
          Key: 'UpdatedAt',
          Sort: order,
        });
      } else if (field === 'CreatedAt') {
        OrderBy.push({
          Key: 'CreatedAt',
          Sort: order,
        });
      } else {
        OrderBy.push({
          Key: 'SeverityLevelSort',
          Sort: order,
        });
      }
    }
    const { CreatedAt, UpdatedAt, current, pageSize, ...other } = params;
    let updatedAtDuration = UpdatedAt;
    if (UpdatedAt && UpdatedAt.length === 2) {
      updatedAtDuration = [
        moment(UpdatedAt?.[0])?.format('YYYY-MM-DD 00:00:00'),
        moment(UpdatedAt?.[1])?.format('YYYY-MM-DD 23:59:59'),
      ];
    }
    return PatchManageApi.ListPatchIssues({
      Filters: {
        CreatedAt,
      },
      ...other,
      OrderBy,
      PatchID: patchId,
      IssueType: type,
      UpdatedAtDuration: updatedAtDuration,
      PageSize: pageSize,
      PageNo: current,
    });
  }

  return (
    <TcsTable
      columns={columns}
      rowKey="IssueSolutionRelUUID"
      options={false}
      cardBordered
      scroll={{ x: 1600 }}
      rowSelection={{
        selectedRowKeys: selectedIds,
        onChange: (keys, rows) => {
          onChange(rows as any[]);
        },
      }}
      request={handleRequest}
      params={params}
      rowDisabled={(record) => targetRecords.find((item) => item?.IssueID === record?.IssueID)}
      pagination={{
        pageSize: 10,
      }}
      tableAlertRender={false}
      scrollInTable={isFullScreen}
    />
  );
};

export default SelectIssueTable;
