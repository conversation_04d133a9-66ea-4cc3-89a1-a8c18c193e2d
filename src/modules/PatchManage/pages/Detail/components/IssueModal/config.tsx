import { TcsColumns } from '@tencent/tcs-component';
import { DropdownBox, List, Popover } from '@tencent/tea-component';
import React, { useMemo } from 'react';
export const useIssueColumns = ({ type }: { type: 'story' | 'bug' }) =>
  useMemo(
    () =>
      [
        { dataIndex: 'IssueID', title: '缺陷单ID', width: 100 },
        {
          dataIndex: ['Issue', 'Title'],
          title: type === 'story' ? '需求标题' : '缺陷标题',
          width: '20%',
          linkable: true,
          search: false,
          linkProps: {
            target: 'blank',
            linkUrl: (text, record) =>
              `/page/defect_manage/__develop_center_next/iteration_manage/${
                type === 'story' ? 'story' : 'defect'
              }/detail?issue_id=${record?.IssueID}`,
          },
        },
        type === 'bug'
          ? {
              dataIndex: ['Issue', 'SeverityLevel'],
              title: '严重程度',
              sorter: true,
              width: 100,
              valueType: 'dictSelect',
              fieldProps: {
                dictType: 'Severity',
                showType: 'tag',
              },
            }
          : undefined,
        {
          dataIndex: ['Issue', 'Priority'],
          title: '优先级',
          sorter: true,
          width: 100,
          valueType: 'dictSelect',
          fieldProps: {
            dictType: 'Priority',
            showType: 'tag',
          },
        },
        {
          dataIndex: ['IssueSolution', 'ProductInfo'],
          title: '涉及产品',
          width: 100,
          valueType: 'text',
          render: (text, record) => {
            const issueAppRel = record?.IssueSolution?.IssueAppRel;
            const productList = issueAppRel?.map((item) => item?.ProductVersionInfo?.ProductInfo?.Code) ?? [];
            return Array.from(new Set(productList)).join(',');
          },
        },
        {
          dataIndex: ['IssueSolution', 'IssueAppRel'],
          title: '涉及应用',
          width: '30%',
          valueType: 'text',
          render: (text, record) => {
            const issueAppRel = record?.IssueSolution?.IssueAppRel ?? [];
            return (
              <Popover
                placement="top"
                overlay={
                  <DropdownBox>
                    <List type="option">
                      {issueAppRel.map((item) => {
                        const version = `${item?.ReleaseApplicationPackage?.ApplicationVersion ?? ''}-${
                          item?.ReleaseApplicationPackage?.BuildTime ?? ''
                        }-${item?.ReleaseApplicationPackage?.CommitID?.substring(0, 8) ?? ''}`;
                        return (
                          <List.Item key={item?.ApplicationName}>
                            {item?.ApplicationName}-{version}
                          </List.Item>
                        );
                      })}
                    </List>
                  </DropdownBox>
                }
              >
                {issueAppRel.slice(0, 2).map((item) => (
                  <div key={item?.ApplicationName}>{item?.ApplicationName};</div>
                ))}
                {issueAppRel?.length > 2 ? (
                  <Popover
                    trigger={'click'}
                    placement="top"
                    overlay={
                      <DropdownBox>
                        <List type="option">
                          {issueAppRel.map((item) => (
                            <List.Item key={item?.ApplicationName}>{item?.ApplicationName}</List.Item>
                          ))}
                        </List>
                      </DropdownBox>
                    }
                  >
                    点击查看更多
                  </Popover>
                ) : (
                  ''
                )}
              </Popover>
            );
          },
        },
        {
          dataIndex: ['IssueSolution', 'Status'],
          title: '状态',
          width: 100,
          valueType: 'dictSelect',
          fieldProps: {
            dictType: 'IssueStatus',
            showType: 'tag',
          },
        },

        {
          dataIndex: ['Issue', 'CreatedAt'],
          title: '创建时间',
          width: '15%',
          search: false,
          valueType: 'dateTime',
          sorter: true,
        },
        {
          dataIndex: 'CreatedAt',
          title: '创建时间',
          width: '15%',
          hideInTable: true,
          valueType: 'dateTime',
        },
        {
          dataIndex: ['Issue', 'UpdateAt'],
          title: '最后更新时间',
          width: '20%',
          search: false,
          valueType: 'dateTime',
          sorter: true,
        },
        {
          dataIndex: 'UpdateAt',
          title: '最后更新时间',
          width: '20%',
          hideInTable: true,
          valueType: 'dateTime',
        },
        { dataIndex: 'Warning', search: false, title: '备注', width: '10%' },
        {
          dataIndex: 'operation',
          valueType: 'option',
          title: '操作',
          fixed: 'right' as any,
          width: 100,
          render: (text, record) => {
            if (record?.IssueSolution?.TapdUrl) {
              return (
                <a href={record?.IssueSolution?.TapdUrl} target="_blank" rel="noreferrer">
                  跳转至Tapd单
                </a>
              );
            }
            return '-';
          },
        },
      ].filter((item) => item) as TcsColumns[],
    [type],
  );
