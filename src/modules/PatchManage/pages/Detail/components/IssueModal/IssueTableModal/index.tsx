import React, { useCallback, useEffect, useRef } from 'react';
import { TcsModal, TcsTable, TcsActionType } from '@tencent/tcs-component';
import { getColumns } from './config.tsx';
import _ from 'lodash';
interface IProps {
  onClose: () => void;
  dependencies: [];
  targetRecords: [];
  issueSolutionRelUUID: string;
  handleUpdataInfo: (params: { record: any }) => void;
}
const IssueTableModal: React.FC<IProps> = (props) => {
  const { onClose, dependencies, targetRecords, issueSolutionRelUUID, handleUpdataInfo } = props;
  const actionRef = useRef<TcsActionType>();
  const targetRecord = targetRecords.find((record) => record?.IssueSolutionRelUUID === issueSolutionRelUUID);

  dependencies.forEach((dependency) => {
    if (!dependency.IssueSolution) {
      return;
    }
    // eslint-disable-next-line no-param-reassign
    dependency.IssueSolution.IssueAppRelInclude = _.intersectionWith(
      targetRecord?.IssueSolution?.IssueAppRel,
      dependency?.IssueSolution?.IssueAppRel,
      (targetApp, depApp) => targetApp?.ApplicationName === depApp?.ApplicationName,
    );
  });

  useEffect(() => {
    if (actionRef.current?.reload) {
      actionRef.current.reload();
    }
  }, [dependencies]);
  const handleRequest = useCallback(
    (params) => {
      const { pageSize, current } = params;
      // 计算当前页的数据范围
      const startIndex = (current - 1) * pageSize;
      const endIndex = startIndex + pageSize;

      // 获取当前页的数据
      const currentData = dependencies.slice(startIndex, endIndex);

      // 返回分页数据
      return Promise.resolve({
        data: currentData,
        success: true,
        total: dependencies.length, // 更新总条目数
      });
    },
    [dependencies],
  );

  const handleAssociation = (record) => {
    handleUpdataInfo(record);
  };

  return (
    <TcsModal width={1300} title="依赖缺陷列表" visible footer={<></>} onCancel={onClose}>
      <TcsTable
        scroll={{
          y: 310,
          x: 1600,
        }}
        options={false}
        columns={getColumns({ handleAssociation })}
        request={handleRequest}
        actionRef={actionRef}
        pagination={{ defaultPageSize: 10 }}
        scrollInTable
      />
    </TcsModal>
  );
};
export default IssueTableModal;
