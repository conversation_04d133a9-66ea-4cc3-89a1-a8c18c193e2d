import React from 'react';
import { DropdownBox, List, Popover } from '@tencent/tea-component';
import { ProColumns, TcsButtonGroup } from '@tencent/tcs-component';

const AppRelPopover = ({ items }) => {
  const content = (
    <DropdownBox>
      <List type="option">
        {items.map((item) => {
          const version = `${item?.ReleaseApplicationPackage?.ApplicationVersion ?? ''}-${
            item?.ReleaseApplicationPackage?.BuildTime ?? ''
          }-${item?.ReleaseApplicationPackage?.CommitID?.substring(0, 8) ?? ''}`;
          return (
            <List.Item key={item?.ApplicationName}>
              {item?.ApplicationName}-{version}
            </List.Item>
          );
        })}
      </List>
    </DropdownBox>
  );

  return (
    <>
      {items.slice(0, 5).map((item) => (
        <div key={item?.ApplicationName}>{item?.ApplicationName};</div>
      ))}
      {items.length > 5 && (
        <Popover trigger={'click'} placement="top" overlay={content}>
          点击查看更多
        </Popover>
      )}
    </>
  );
};

export function getColumns({ handleAssociation }): ProColumns[] {
  return [
    {
      width: '10%',
      dataIndex: 'IssueID',
      title: '缺陷单ID',
      fixed: 'left',
    },
    {
      width: '25%',
      dataIndex: ['Issue', 'Title'],
      title: '缺陷单标题',
      render: (text, record) => (
        <a
          href={`/page/defect_manage/__develop_center_next/iteration_manage/defect/detail?issue_id=${record?.IssueID}`}
          target="_blank"
          rel="noreferrer"
        >
          {record?.Issue?.Title ?? ''}
        </a>
      ),
    },

    {
      width: '20%',
      dataIndex: 'IssueAppRel',
      title: '涉及应用',
      render: (text, record) => {
        const issueAppRel = record?.IssueSolution?.IssueAppRel ?? [];
        return <AppRelPopover items={issueAppRel} />;
      },
    },
    {
      width: '20%',
      dataIndex: 'IssueAppRelInclude',
      title: '涉及关联应用',
      render: (text, record) => {
        const IssueAppRelInclude = record?.IssueSolution?.IssueAppRelInclude ?? [];
        return <AppRelPopover items={IssueAppRelInclude} />;
      },
    },
    {
      width: '10%',
      dataIndex: 'operation',
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      render: (text, record) => (
        <TcsButtonGroup
          items={[
            {
              text: '关联',
              confirm: true,
              confirmProps: {
                title: '确认要关联？',
                onConfirm: () => handleAssociation(record),
              },
              tooltip: '添加到已选缺陷/需求',
              disabled: !record?.IssueSolution,
            },
          ]}
        />
      ),
    },
  ];
}
