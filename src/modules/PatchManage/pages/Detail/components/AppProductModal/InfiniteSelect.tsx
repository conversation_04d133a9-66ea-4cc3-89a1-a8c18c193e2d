import React, { useState, useEffect, useCallback, CSSProperties } from 'react';
import { Select, LoadingTip, message } from '@tencent/tea-component';
import { ControllerRenderProps } from 'react-hook-form';
import { useDebounceFn } from 'ahooks';

const PAGE_SIZE = 50;
export interface ISelectProps extends ControllerRenderProps {
  renderOptions: any;
  onLoadMore: ({
    keyword,
    pageInfo,
    index,
  }: {
    keyword: string;
    pageInfo: any;
    index?: number;
  }) => Promise<any> | undefined;
  style?: CSSProperties;
  placeholder?: string;
  index?: number;
  onSelectChange?: any;
}
const InfiniteSelect = (props: ISelectProps) => {
  const { onChange, renderOptions, onLoadMore, index, onSelectChange, style, ...rest } = props;
  const { textGetter, valueGetter, itemsGetter, extraGetter } = renderOptions();
  const [first, setFirst] = useState(true);
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<any>([]);
  const [total, setTotal] = useState(0);

  const [page, setPage] = useState(1);

  const fetch = useCallback(
    ({ pageNo, searchWord }) => {
      setLoading(true);
      onLoadMore({
        keyword: searchWord ?? '',
        pageInfo: {
          pageSize: PAGE_SIZE,
          pageNo,
        },
        index,
      })
        ?.then((res: any) => {
          setTotal(res?.Total);

          if (res?.Error) {
            return message.error({
              content: res.Error.Message,
            });
          }
          if (pageNo === 1) {
            setOptions(() => {
              const items = itemsGetter(res);
              return items?.map((item) => ({
                value: valueGetter(item),
                text: textGetter(item),
                extra: extraGetter?.(item),
              }));
            });
          } else {
            setOptions((options) => {
              const items = itemsGetter(res);
              return [
                ...options,
                ...items.map((item) => ({
                  value: valueGetter(item),
                  text: textGetter(item),
                  extra: extraGetter?.(item),
                })),
              ];
            });
          }
        })
        .finally(() => {
          setLoading(false);
        });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [index],
  );

  useEffect(() => {
    if (!first) {
      fetch({ pageNo: page, searchWord: '' });
    }
  }, [fetch, first, page]);

  const { run: debouncedFetch } = useDebounceFn(
    (value) => {
      fetch({ pageNo: 1, searchWord: value });
    },
    { wait: 500 },
  );

  return (
    <Select
      {...rest}
      searchable
      matchButtonWidth
      appearance="button"
      style={style}
      autoClearSearchValue={false}
      onOpen={() => setFirst(false)}
      onClose={() => setFirst(true)}
      onSearch={(value) => {
        setOptions([]);
        setPage(1);
        debouncedFetch(value);
      }}
      onScrollBottom={() => {
        if (!loading && page < total / PAGE_SIZE) {
          setPage((page) => page + 1);
        }
      }}
      options={options || []}
      tips={`共 ${total ?? 0} 项`}
      clearable
      bottomTips={loading && <LoadingTip />}
      onChange={onSelectChange ?? onChange}
    />
  );
};

export default InfiniteSelect;
