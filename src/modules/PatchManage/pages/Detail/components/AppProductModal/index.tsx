import {
  listPatchApplicationInfos,
  listPatchApplicationPackages,
  modifyPatchApplications,
} from '@/common/api/patchManage';
import { Button, Modal, Icon, Form, message, Select, StatusTip } from '@tencent/tea-component';
import React, { useEffect, useState } from 'react';
import { Controller, useFieldArray, useForm } from 'react-hook-form';

interface AppModalProps {
  patchId: string;
  onClose: () => void;
  onSuccess: () => void;
}

const AppModal: React.FC<AppModalProps> = (props) => {
  const { onClose, onSuccess, patchId } = props;
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [status, setStatus] = useState<any>('loading');
  const [options, setOptions] = useState<any>([]);
  const [appList, setAppList] = useState<any>([]);
  const [appLoading, setAppLoading] = useState(false);

  const defaultApplications = [
    {
      ApplicationName: '',
      ApplicationPackageID: '',
      PackageVersionName: '',
      ApplicationBranchUUID: '',
      ApplicationUUID: '',
    },
  ];

  const { control, getValues, watch, formState, setValue, trigger } = useForm({
    defaultValues: {
      Applications: defaultApplications,
    },
    mode: 'onBlur',
  });

  const { fields, remove, insert } = useFieldArray({
    control,
    name: 'Applications',
  });

  const handleConfirm = () => {
    setConfirmLoading(true);
    const { Applications } = getValues();
    const formattedApplications = Applications.map((item) => {
      // @ts-ignore
      // eslint-disable-next-line no-param-reassign
      delete item.ApplicationUUID;
      return {
        ...item,
        ApplicationName: item?.ApplicationName.split('_')[0],
      };
    });

    modifyPatchApplications({
      PatchID: patchId,
      Applications: formattedApplications,
      Operation: 'select',
    })
      .then((res) => {
        if (res?.Error) {
          return message.error({
            content: res.Error.Message,
          });
        }
        onSuccess();
      })
      .finally(() => {
        onClose();
        setConfirmLoading(false);
      });
  };

  const handleOpen = (index) => {
    setStatus('loading');
    setOptions([]);
    const ApplicationName = getValues().Applications[index]?.ApplicationName.split('_')[0];
    const ApplicationUUID = getValues().Applications[index]?.ApplicationUUID;
    const ApplicationBranchUUID = getValues().Applications[index]?.ApplicationBranchUUID;
    listPatchApplicationPackages({
      PatchID: patchId,
      ApplicationName,
      ApplicationUUID,
      ApplicationBranchUUID,
    })
      .then((res) => {
        if (res?.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          setOptions(() =>
            res?.ApplicationPackageList?.map((item) => ({
              value: item?.ApplicationPackageID,
              text: item?.PackageVersionName,
            })),
          );
        }
      })
      .finally(() => {
        setStatus(null);
      });
  };

  useEffect(() => {
    setAppLoading(true);
    listPatchApplicationInfos({
      PatchID: patchId,
    })
      .then((res) => {
        if (res?.Error) {
          message.error({ content: res?.Error });
        } else {
          setAppList(() =>
            (res?.List || []).map((item, index) => ({
              value: `${item?.ApplicationName}_${index}`,
              text: item?.ApplicationName,
              extra: {
                ApplicationUUID: item?.ApplicationUUID,
                ApplicationBranchUUID: item?.ApplicationBranchUUID,
              },
            })),
          );
        }
      })
      .finally(() => {
        setAppLoading(false);
      });
  }, [patchId]);

  return (
    <Modal visible caption="添加应用制品版本" size="auto" onClose={onClose}>
      <Modal.Body>
        <ul>
          {fields.map((item, index) => {
            const watchedAppName = watch(`Applications.${index}.ApplicationName`);
            return (
              <li key={index} style={{ padding: 10, display: 'flex', alignItems: 'flex-start' }}>
                <Controller
                  rules={{
                    required: {
                      value: true,
                      message: '请选择应用',
                    },
                  }}
                  render={({ field }) => (
                    <Form.Item>
                      <Select
                        matchButtonWidth
                        clearable
                        searchable
                        tips={appLoading ? <StatusTip.LoadingTip /> : undefined}
                        options={appList}
                        appearance="button"
                        style={{ width: 430, paddingRight: 10 }}
                        placeholder="请选择应用"
                        value={field.value}
                        onChange={(value, { option }) => {
                          const { extra } = option as any;
                          setValue(`Applications.${index}.ApplicationName`, value);
                          setValue(`Applications.${index}.ApplicationUUID`, extra?.ApplicationUUID);
                          setValue(`Applications.${index}.ApplicationBranchUUID`, extra?.ApplicationBranchUUID);
                          setValue(`Applications.${index}.PackageVersionName`, '');
                        }}
                      />
                    </Form.Item>
                  )}
                  name={`Applications.${index}.ApplicationName`}
                  control={control}
                />
                <Controller
                  rules={{
                    required: watchedAppName
                      ? {
                          value: true,
                          message: '请选择制品版本',
                        }
                      : false,
                  }}
                  render={({ field, fieldState: { error }, formState }) => {
                    const hasError = error && formState.touchedFields.Applications?.[index].ApplicationPackageID;
                    return (
                      <Form.Item
                        message={hasError ? error?.message : undefined}
                        status={hasError ? 'error' : undefined}
                        showStatusIcon={false}
                      >
                        <Select
                          appearance="button"
                          matchButtonWidth
                          clearable
                          searchable
                          options={options}
                          style={{ width: 300 }}
                          value={field.value}
                          placeholder="请选择制品版本"
                          onChange={(_value, context) => {
                            const { option } = context as any;
                            setValue(field.name, option?.value ?? '', {
                              shouldTouch: true,
                            });
                            setValue(`Applications.${index}.PackageVersionName`, option?.text ?? '');
                            // 需要手动触发一次，因为这里使用手动赋值的方式
                            trigger(field.name);
                          }}
                          tips={status && <StatusTip status={status as any} />}
                          onOpen={() => handleOpen(index)}
                          onClose={() => trigger(field.name)}
                        />
                      </Form.Item>
                    );
                  }}
                  name={`Applications.${index}.ApplicationPackageID`}
                  control={control}
                />
                <div style={{ display: 'flex', alignItems: 'center', height: 30 }}>
                  <Icon
                    type="plus"
                    onClick={() =>
                      insert(index + 1, {
                        ApplicationName: '',
                        ApplicationPackageID: '',
                        PackageVersionName: '',
                        ApplicationBranchUUID: '',
                        ApplicationUUID: '',
                      })
                    }
                    style={{ margin: '0px 10px', cursor: 'pointer' }}
                  />
                  <Icon
                    type="minus"
                    onClick={() => {
                      if (fields.length === 1) return;
                      remove(index);
                    }}
                    tooltip={fields.length === 1 && '至少保留一个'}
                    style={{ cursor: 'pointer' }}
                  />
                </div>
              </li>
            );
          })}
        </ul>
      </Modal.Body>

      <Modal.Footer>
        <Button type="primary" onClick={handleConfirm} loading={confirmLoading} disabled={!formState.isValid}>
          确认调整
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default AppModal;
