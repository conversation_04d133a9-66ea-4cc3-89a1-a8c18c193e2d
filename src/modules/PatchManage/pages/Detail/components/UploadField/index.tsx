import { jgNextCapiRequest, requestNormalApi } from '@/common/api/api';
import { TcsButton } from '@tencent/tcs-component';
import { Text, Upload, message } from '@tencent/tea-component';
import React, { useState } from 'react';

export interface IProps {
  onLoading?: (isLoading: boolean) => void;
  onUploadSuccess?: (any) => void;
}

const UploadField: React.FC<IProps> = ({ onLoading, onUploadSuccess }) => {
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);

  function handleBeforeUpload(file) {
    const filename = file.name;
    const size = file.size / 1024 / 1024 / 1024;
    if (size > 5) {
      message.error({
        content: '上传文件不能大于5G',
      });

      return Promise.reject(new Error('上传文件不能大于5G'));
    }
    const prefixUrl = `${window.location.host}/cgw/`;
    let promise: Promise<any> | undefined = undefined;
    if ((window as any).JIGUANG_is_tea_control_env) {
      promise = jgNextCapiRequest(
        { ServiceType: 'BackendService', Action: 'PackageAPI' },
        {
          Action: 'CreateCSPUploadURL',
          FileName: filename,
          FileVersion: '00',
          PkgName: '0',
        },
      );
    } else {
      promise = requestNormalApi({
        url: 'packages-api/',
        method: 'POST',
        prefixUrl,
        data: {
          Action: 'CreateCSPUploadURL',
          FileName: filename,
          FileVersion: '00',
          PkgName: '0',
        },
      });
    }
    setLoading(true);
    onLoading?.(true);
    promise!
      .then((result) => {
        if (result.Response.Url) {
          const url = new URL(result.Response.Url);
          const xhr = new XMLHttpRequest();
          xhr.open('PUT', `${window.location.protocol}//${url.hostname}${url.pathname}${url.search}`, true);
          xhr.onload = () => {
            if (xhr.status === 200) {
              const res = {
                uid: file.id,
                url: `/${result.Response.Key}`,
                name: filename,
                status: 'success',
                search: url.search,
              };
              setFile(res);
              setLoading(false);
              onLoading?.(false);
              onUploadSuccess?.(res);
            } else {
              message.error({
                content: `${file.name}上传失败`,
              });
              setFile(null);
              setLoading(false);
              onLoading?.(false);
              onUploadSuccess?.({
                status: 'error',
              });
            }
          };
          xhr.onerror = function () {
            message.error({
              content: `${file.name}上传失败`,
            });
            setFile(null);
            setLoading(false);
            onLoading?.(false);
          };
          xhr.send(file);
        }
      })
      .catch(() => {
        message.error({
          content: `${file.name}上传失败`,
        });
        setFile(null);
        setLoading(false);
        onLoading?.(false);
      });

    return Promise.reject();
  }

  return (
    <>
      <Upload beforeUpload={handleBeforeUpload}>
        <TcsButton loading={loading}>点击上传</TcsButton>
      </Upload>
      {file && <hr />}
      {file && (
        <Text theme={file.status} style={{ fontSize: 12 }}>
          {file.name}
        </Text>
      )}
    </>
  );
};

export default UploadField;
