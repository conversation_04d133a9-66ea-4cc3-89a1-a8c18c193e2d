/*
 * @Author: lucyfang
 * @Date: 2024-12-10 10:51:18
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-12-24 17:09:13
 * @Description: 请输入注释信息
 */
import React, { useRef, useEffect, useState } from 'react';
import { PatchManageApi } from '@/common/api/patchManage';
import {
  TcsForm,
  TcsModal,
  TcsFormText,
  TcsTable,
  TcsFormRadio,
  TcsFormSelect,
  TcsButton,
  TcsSpin,
} from '@tencent/tcs-component';
import { message } from '@tencent/tea-component';

interface IProps {
  onCancel: () => void;
  onSuccess: () => void;
  patchID: string;
}
interface ProductLIst {
  ProductCode: string;
  ProductDataBranch: string;
  ProductDataTagNum: number;
  ProductVersionID: number;
  ProductVersionName: string;
  ProductVersionUUID: string;
}

const PublishPatchModal: React.FC<IProps> = ({ onCancel, onSuccess, patchID }) => {
  const formRef = useRef<any>();
  const [scope, setScope] = useState('all');
  const [loading, setLoading] = useState(false);
  const [tenantList, setTenantList] = useState<{ Name: string; UUID: string }[]>([]);
  const [data, setData] = useState<ProductLIst[]>([]);
  useEffect(() => {
    if (patchID) {
      setLoading(true);
      PatchManageApi.GetPatchMarketPublishPreData({ PatchID: patchID })
        .then((res) => {
          if (res) {
            formRef?.current?.setFieldsValue({
              PatchID: res?.PatchID || '-',
              PatchName: res?.PatchName || '-',
              scope,
            });
            if (res?.TenantList?.length) {
              setTenantList(res.TenantList);
            }
            if (res?.ProductList?.length) {
              setData(res.ProductList);
            }
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [patchID]);
  const getColumns = () => [
    {
      title: '产品',
      dataIndex: 'ProductCode',
      width: '30%',
    },
    {
      title: '产品版本',
      dataIndex: 'ProductVersionName',
      width: '30%',
    },
    {
      title: '数据快照',
      dataIndex: 'ProductDataTagNum',
      width: '30%',
    },
  ];
  const [publishLoad, setPublishLoad] = useState(false);
  const handlePublish = () => {
    formRef.current?.validateFields().then((res) => {
      const tenantUUIDList: string[] = [];
      if (res.scope === 'other') {
        tenantUUIDList.push(...res.space);
      } else {
        tenantUUIDList.push('0');
      }
      setPublishLoad(true);
      PatchManageApi.PublishPatchToMarket({
        PatchID: patchID,
        TenantUUIDList: tenantUUIDList,
      })
        .then((res) => {
          if (res) {
            message.success({ content: '发布成功' });
            onSuccess();
          }
        })
        .finally(() => {
          setPublishLoad(false);
        });
    });
  };
  return (
    <TcsModal
      title="Patch发布公共市场"
      visible
      onCancel={onCancel}
      width={800}
      onOk={onCancel}
      footer={
        <>
          <TcsButton type="primary" onClick={handlePublish} loading={publishLoad}>
            发布
          </TcsButton>
          <TcsButton onClick={onCancel}>取消</TcsButton>
        </>
      }
    >
      <TcsSpin spinning={loading}>
        <TcsForm formRef={formRef}>
          <TcsFormText label="PatchID" name="PatchID" readonly />
          <TcsFormText label="Patch名称" name="PatchName" readonly />
          <TcsForm.Item label="涉及产品">
            <TcsTable
              rowKey="ProductCode"
              columns={getColumns()}
              options={false}
              dataSource={data}
              pagination={{
                pageSize: 10,
              }}
            />
          </TcsForm.Item>
          <TcsFormRadio
            label="发布范围"
            name="scope"
            options={[
              { label: '所有空间', value: 'all' },
              { label: '指定空间', value: 'other' },
            ]}
            onChange={setScope}
          />
          {scope === 'other' && (
            <TcsFormSelect
              label="选择空间"
              name="space"
              rules={[
                {
                  required: true,
                  message: '请选择空间',
                },
              ]}
              fieldProps={{ size: 'full', mode: 'tags', placeholder: '请选择空间', optionsOnly: true }}
              options={tenantList?.map((item) => ({ label: item.Name, value: item.UUID }))}
            />
          )}
        </TcsForm>
      </TcsSpin>
    </TcsModal>
  );
};
export default PublishPatchModal;
