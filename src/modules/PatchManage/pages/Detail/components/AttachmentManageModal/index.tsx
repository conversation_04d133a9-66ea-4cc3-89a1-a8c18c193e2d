/*
 * @Author: lucyfang
 * @Date: 2024-10-31 11:46:38
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-11-13 14:31:37
 * @Description: 请输入注释信息
 */
import React, { useState, useRef } from 'react';
import { TcsModal, TcsTable, TcsCard, TcsForm, TcsButton, TcsButtonGroup, ActionType } from '@tencent/tcs-component';
import UploadField from '../UploadField';
import { message } from '@tencent/tea-component';
import { PatchManageApi } from '@/common/api/patchManage';
interface IProps {
  onClose: () => void;
  onSuccess: () => void;
  patchDetail: any;
  updateAuthorized: boolean;
  isAfter: boolean;
}

const AttachmentManageModal: React.FC<IProps> = ({ onClose, patchDetail, isAfter, updateAuthorized }) => {
  const [loading, setLoading] = useState(false);
  const [attachmentKey, setAttachmentKey] = useState<any>({});
  const actionRef = useRef<ActionType>();

  function handleUploadLoading(load) {
    setLoading(load);
  }
  function handleUploadSuccess(value) {
    value?.status === 'success' ? setAttachmentKey(value) : setAttachmentKey(null);
  }
  function handleAddAttachment() {
    if (attachmentKey?.status !== 'success') {
      message.error({ content: '请点击上传附件' });
      return;
    }
    setLoading(true);
    PatchManageApi.CreatePatchAttachment({
      PatchID: patchDetail?.PatchID,
      FileName: attachmentKey?.name,
      DownloadPath: attachmentKey?.url,
      Source: 'manual',
    })
      .then(() => {
        message.success({ content: '附件添加成功' });
        actionRef.current?.reload();
      })
      .finally(() => {
        setLoading(false);
      });
  }

  function handleRequest(queryParams: any, options: any) {
    return PatchManageApi.ListPatchAttachments(
      {
        PatchID: patchDetail?.PatchID,
        ...queryParams,
      },
      options,
    );
  }
  function handleDelete(id) {
    PatchManageApi.DeletePatchAttachment({
      ID: id,
    }).then(() => {
      actionRef.current?.reload();
    });
  }
  function getColumns() {
    return [
      {
        title: '文件名称',
        dataIndex: 'FileName',
        width: '30%',
        copyable: true,
      },
      {
        title: '创建人',
        dataIndex: 'Creator',
        width: '10%',
        copyable: true,
      },
      {
        title: '创建时间',
        dataIndex: 'CreatedAt',
        width: '10%',
        valueType: 'dateTime',
      },
      {
        title: '更新时间',
        dataIndex: 'UpdatedAt',
        width: '10%',
        valueType: 'dateTime',
      },
      {
        title: '来源',
        dataIndex: 'Source',
        width: '10%',
        valueType: 'dictSelect',
        fieldProps: {
          dictType: 'PatchAttachmentSource',
          showType: 'tag',
        },
      },
      {
        title: '操作',
        dataIndex: 'option',
        width: '10%',
        render: (text, record) => {
          return (
            <TcsButtonGroup
              maxNum={2}
              items={[
                {
                  text: '删除',
                  confirm: true,
                  confirmProps: {
                    title: '确定删除么',
                    onConfirm: () => handleDelete(record?.ID),
                  },
                },
                {
                  text: '下载',
                  onClick: () => {
                    window.open(`//registry.jiguang.woa.com${record?.DownloadPath}`, '_blank');
                  },
                },
              ]}
            />
          );
        },
      },
    ];
  }
  return (
    <TcsModal confirmLoading={loading} visible title="附件管理" width={1300} onCancel={onClose} footer={false}>
      <TcsCard title="文件上传">
        <TcsForm>
          <TcsForm.Item
            label="上传文件"
            rules={[
              {
                required: true,
                message: '请选择待上传的文件',
              },
            ]}
          >
            <UploadField onLoading={handleUploadLoading} onUploadSuccess={handleUploadSuccess} />
          </TcsForm.Item>
          <TcsButton
            type="primary"
            onClick={handleAddAttachment}
            loading={loading}
            disabled={!updateAuthorized && isAfter}
            tooltip={isAfter && !updateAuthorized && '只有负责人才能添加附件'}
          >
            确定添加附件
          </TcsButton>
        </TcsForm>
      </TcsCard>
      <TcsCard title="附件列表">
        <TcsTable
          actionRef={actionRef}
          rowKey="ID"
          request={handleRequest}
          columns={getColumns()}
          pagination={{ defaultPageSize: 5 }}
        />
      </TcsCard>
    </TcsModal>
  );
};

export default AttachmentManageModal;
