/*
 * @Author: lucyfang
 * @Date: 2024-12-10 10:51:18
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-12-17 14:23:54
 * @Description: 请输入注释信息
 */
import React, { useRef, useEffect, useState } from 'react';
import { TcsForm, TcsModal, TcsFormTextArea, TcsFormStaffSelect } from '@tencent/tcs-component';
import { PatchManageApi } from '@/common/api/patchManage';
interface IProps {
  onCancel: () => void;
  onSuccess: () => void;
  patchID: string;
}

const CancelPublishModal: React.FC<IProps> = ({ onCancel, onSuccess, patchID }) => {
  const formRef = useRef<any>();
  const jiguangUsename = (window as any).jiguang_username;
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    formRef.current?.setFieldsValue({ name: jiguangUsename });
  }, []);
  const handleConfirm = () => {
    formRef.current?.validateFields().then((res) => {
      setLoading(true);
      PatchManageApi.RecallMarketPatch({
        SubAccountUin: res?.name || '',
        PatchID: patchID,
        Reason: res?.reason || '',
      })
        .then((res) => {
          if (res) {
            onSuccess();
          }
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };
  return (
    <TcsModal
      title="撤回Patch发布公共市场"
      visible
      onCancel={onCancel}
      width={800}
      onOk={handleConfirm}
      confirmLoading={loading}
    >
      <TcsForm formRef={formRef}>
        <TcsFormTextArea
          label="撤回原因"
          name="reason"
          fieldProps={{ size: 'full' }}
          rules={[
            {
              required: true,
              message: '请填写撤回原因',
            },
          ]}
        />
        <TcsFormStaffSelect label="撤回人" name="name" readonly />
      </TcsForm>
    </TcsModal>
  );
};
export default CancelPublishModal;
