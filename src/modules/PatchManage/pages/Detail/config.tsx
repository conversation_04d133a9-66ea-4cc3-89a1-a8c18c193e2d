import { Button, DropdownBox, List, Popover, Tag } from '@tencent/tea-component';
import { TcsPopConfirm } from '@tencent/tcs-component';
import dayjs from 'dayjs';
import React from 'react';
import RenderDescription from './components/RenderDescription';
import RenderPatchVersion from './components/RenderPatchVersion/ index';

export const getIssueColumns = ({ getLookupByCode }) => [
  { key: 'IssueID', header: '缺陷单ID', width: 100, search: true },
  {
    key: 'IssueTitle',
    header: '缺陷标题',
    width: '20%',
    render: (record) => (
      <a
        href={`/page/defect_manage/__develop_center_next/iteration_manage/defect/detail?issue_id=${record?.IssueID}`}
        target="_blank"
        rel="noreferrer"
      >
        {record?.Issue?.Title ?? ''}
      </a>
    ),
  },
  {
    key: 'SeverityLevel',
    header: '严重程度',
    width: 100,
    render: (record) => {
      const lookup = getLookupByCode('Severity', record?.Issue?.SeverityLevel);
      if (lookup) {
        return <Tag style={{ color: lookup.Extra?.Color }}>{lookup.Name}</Tag>;
      }
      return record?.Issue?.SeverityLevel || '-';
    },
  },
  {
    key: 'IssueAppRel',
    header: '涉及产品',
    width: 100,
    render: (record) => {
      const issueAppRel = record?.IssueSolution?.IssueAppRel;
      const productList = issueAppRel?.map((item) => item?.ProductVersionInfo?.ProductInfo?.Code) ?? [];
      return Array.from(new Set(productList)).join(',');
    },
  },
  {
    key: 'ProductVersion',
    header: '涉及应用',
    width: '25%',
    render: (record) => {
      const issueAppRel = record?.IssueSolution?.IssueAppRel ?? [];

      return (
        <Popover
          placement="top"
          overlay={
            <DropdownBox>
              <List type="option">
                {issueAppRel.map((item) => {
                  const version = `${item?.ReleaseApplicationPackage?.ApplicationVersion ?? ''}-${
                    item?.ReleaseApplicationPackage?.BuildTime ?? ''
                  }-${item?.ReleaseApplicationPackage?.CommitID?.substring(0, 8) ?? ''}`;
                  return (
                    <List.Item key={item?.ApplicationName}>
                      {item?.ApplicationName}-{version}
                    </List.Item>
                  );
                })}
              </List>
            </DropdownBox>
          }
        >
          {issueAppRel.slice(0, 5).map((item) => (
            <div key={item?.ApplicationName}>{item?.ApplicationName};</div>
          ))}
          {issueAppRel?.length > 5 ? (
            <Popover
              trigger={'click'}
              placement="top"
              overlay={
                <DropdownBox>
                  <List type="option">
                    {issueAppRel.map((item) => (
                      <List.Item key={item?.ApplicationName}>{item?.ApplicationName}</List.Item>
                    ))}
                  </List>
                </DropdownBox>
              }
            >
              点击查看更多
            </Popover>
          ) : (
            ''
          )}
        </Popover>
      );
    },
  },
  {
    key: 'Status',
    header: '状态',
    width: 100,
    render: (record) => {
      const lookup = getLookupByCode('IssueStatus', record?.IssueSolution?.Status);
      if (lookup) {
        return <Tag style={{ color: lookup.Extra?.Color }}>{lookup.Name}</Tag>;
      }
      return record?.IssueSolution?.Status || '-';
    },
  },

  {
    key: 'CreatedAt',
    header: '创建时间',
    width: '20%',
    render: (record) => {
      if (record?.IssueSolution?.CreatedAt) {
        return dayjs(record?.IssueSolution?.CreatedAt).format('YYYY-MM-DD HH:mm:ss');
      }
      return '-';
    },
  },

  { key: 'Warning', header: '备注', width: '10%' },
  {
    key: 'operation',
    header: '操作',
    fixed: 'right' as any,
    width: 100,
    render: (record) => {
      if (record?.IssueSolution?.TapdUrl) {
        return (
          <a href={record?.IssueSolution?.TapdUrl} target="_blank" rel="noreferrer">
            跳转至Tapd单
          </a>
        );
      }
      return '-';
    },
  },
];

export const getPatchColumns = ({
  patchId,
  onloading,
  isPublished,
  getLookupByCode,
  isAfter,
  updateAuthorized,
}: {
  patchId: string;
  onloading: (boolean) => void;
  isPublished: boolean;
  getLookupByCode: (type, code) => any;
  isAfter: boolean;
  updateAuthorized: boolean;
}) => [
  { key: 'Code', header: '产品Code', width: 100, render: (record) => `${record?.ProductInfo?.Code ?? ''}` },
  {
    key: 'ProductVersion',
    header: '产品版本',
    width: 100,
    render: (record) => `${record?.ProductVersion?.Name ?? ''}`,
  },
  { key: 'ApplicationName', header: '应用名称', render: (record) => `${record?.ApplicationName ?? ''}` },
  {
    key: 'Arch',
    width: 150,
    header: '架构',
    render: (record) => {
      const lookup = getLookupByCode('PackageArch', record?.PatchPackage?.Arch);
      if (lookup) {
        return lookup?.Name;
      }
      return '-';
    },
  },
  { key: 'Baseline', header: '基线版本', render: (record) => `${record?.Details?.Baseline ?? ''}` },
  {
    key: 'Latest',
    header: 'Patch版本',
    render: (record) => (
      <RenderPatchVersion
        record={record}
        patchId={patchId}
        onloading={onloading}
        isPublished={isPublished}
        updateAuthorized={updateAuthorized}
        isAfter={isAfter}
      />
    ),
  },
  { key: 'Warning', header: '备注' },
];

export const getVerificationColumns = ({ onLoading, isPublished, getLookupByCode, handleNeedTailor }) => [
  { key: 'ID', header: 'ID', width: 100 },
  { key: 'BranchName', header: '制品分支' },
  { key: 'TagNum', header: '测试制品TAG' },
  {
    key: 'Status',
    header: '状态',
    render: (record) => {
      const lookup = getLookupByCode('PatchVerificationStatus', record?.Status);
      if (lookup) {
        return <Tag style={{ color: lookup.Extra?.Color }}>{lookup.Name}</Tag>;
      }
      return record?.IssueSolution?.Status || '-';
    },
  },
  {
    key: 'CreatedAt',
    header: '创建时间',
    render: (record) => {
      if (record?.CreatedAt) {
        return dayjs(record?.CreatedAt).format('YYYY-MM-DD HH:mm:ss');
      }
      return '-';
    },
  },
  {
    key: 'PackedAt',
    header: '出包时间',
    render: (record) => {
      if (record?.CreatedAt) {
        return dayjs(record?.CreatedAt).format('YYYY-MM-DD HH:mm:ss');
      }
      return '-';
    },
  },
  {
    key: 'Description',
    header: '描述',
    render: (record) => <RenderDescription onLoading={onLoading} record={record} isPublished={isPublished} />,
  },
  {
    key: 'Operation',
    header: '操作',
    render: (record) => {
      if (record?.WorkflowInstanceID && record?.WorkflowID) {
        return (
          <>
            <Button
              type="link"
              onClick={() => {
                window.open(
                  `/page/flow-design/flow-publish/exec?instance_id=${record?.WorkflowInstanceID}&workflow_id=${record?.WorkflowID}`,
                  '_blank',
                );
              }}
            >
              查看出包记录
            </Button>
            <TcsPopConfirm
              title="确定要裁剪？"
              onConfirm={() => handleNeedTailor(record)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link">确认裁剪</Button>
            </TcsPopConfirm>
          </>
        );
      }
      if (record?.DownloadUrl) {
        return (
          <a href={record?.DownloadUrl} target="_blank" rel="noreferrer">
            下载
          </a>
        );
      }
    },
  },
];
