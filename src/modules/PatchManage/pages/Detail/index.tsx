import { Alert, Table, message } from '@tencent/tea-component';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { getUrlParams } from '@/common/utils';
import { getPatchDetails, updatePatchApplicationRel } from '@/common/api/patchManage';
import { toListParamsCApi } from '@/common/api/api';
import { getVerificationColumns } from './config';
import PatchBasicInfo from './components/PatchBasicInfo';
import PublishModal from '../Common/PublishModal';
import ApplicationTable from './components/ApplicationTable';
import { autotip, pageable } from '@tencent/tea-component/lib/table/addons';
import IssueTable from './components/IssueTable';
import useLookup from '@/common/hookups/useLookup';
import { TcsCard, TcsLayout, TcsSpin } from '@tencent/tcs-component';
import Search from './Search';
import _ from 'lodash';
import moment from 'moment';
import { useAuthority, AuthApi } from '@tencent/tcsc-base';
import { isAfterTargetDate } from '../../utils';

const Detail = () => {
  const { history } = TcsLayout.useHistory();

  const urlParams = getUrlParams();
  const { getLookupByCode } = useLookup([]);

  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any>(null);
  const [publishModalVisible, setPublishModalVisible] = useState(false);
  const [verificationTableRecords, setVerificationTableRecords] = useState<any>([]);
  const [userList, setUserList] = useState([]);
  const [isAfter, setIsAfter] = useState(false);

  const isPublished = data?.Status === 'published';

  const verificationRecords = useMemo(
    () =>
      data?.Verifications?.map((item) => ({
        BranchName: item?.ArtifactBranch?.BranchName,
        TagNum: item?.VerificationTag?.TagNum,
        ...item,
      })),
    [data?.Verifications],
  );
  useEffect(() => {
    setVerificationTableRecords(verificationRecords);
  }, [verificationRecords]);

  const getDetail = useCallback(() => {
    setLoading(true);
    getPatchDetails({
      Patch: {
        _Filter: toListParamsCApi(
          { PatchID: urlParams?.patchId },
          {
            useEqFields: ['PatchID'],
          },
        )._Filter,
      },
    })
      .then((res) => {
        if (res?.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          setData(res?.GetPatch);
          if (res?.GetPatch?.CreatedAt) {
            const isAfterTemp = isAfterTargetDate(new Date(res.GetPatch.CreatedAt));
            setIsAfter(isAfterTemp); // 设置检查结果状态
          } else {
            setIsAfter(false);
          }
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }, [urlParams?.patchId]);

  const handlePublish = () => setPublishModalVisible(true);

  const handleSearch = (values) => {
    const findTableRecords = verificationRecords?.filter((item) => {
      const findIDTest =
        !values?.BranchName || item?.BranchName.toLowerCase()?.includes(values?.BranchName?.toLowerCase());
      const findLevelTes = !values?.TagNum || item?.TagNum === Number(values?.TagNum);
      const findStatus = !values?.Status || item?.Status?.includes(values?.Status);
      const findCreatedAt =
        !values?.CreatedAt ||
        (item?.CreatedAt >= moment(values?.CreatedAt[0])?.format('YYYY-MM-DD 00:00:00') &&
          item?.CreatedAt <= moment(values?.CreatedAt[1])?.format('YYYY-MM-DD 23:59:59'));

      return findIDTest && findLevelTes && findStatus && findCreatedAt;
    });
    if (_.isUndefined(findTableRecords)) {
      setVerificationTableRecords(verificationRecords);
    } else {
      setVerificationTableRecords(findTableRecords);
    }
  };
  const getUserList = useCallback(() => {
    AuthApi.ListResourceUserRoles({ Kind: 'Patch', Name: urlParams.patchId }).then((res) => {
      if (res?.PatchOwner?.length) setUserList(res.PatchOwner);
    });
  }, [urlParams?.patchId]);
  useEffect(() => {
    if (urlParams?.patchId) {
      getDetail();
      getUserList();
    }
  }, [getDetail, urlParams?.patchId]);

  const UpdateOwnerAction = [
    {
      Action: 'UpdatePatch',
      Kind: 'Patch',
      Resources: [urlParams?.patchId],
    },
  ];

  const { getAuthorized } = useAuthority([...UpdateOwnerAction]);
  const updateAuthorized = getAuthorized(UpdateOwnerAction);
  const handleNeedTailor = (record) => {
    updatePatchApplicationRel({
      PatchVerificationRel: {
        Variable: '{"NeedTailor":true}',
        _Filter: toListParamsCApi(
          { ID: record?.ID },
          {
            useEqFields: ['ID'],
          },
        )._Filter,
      },
    })
      .then((res) => {
        if (res?.Error) {
          return message.error({
            content: res?.Error?.Message,
          });
        }
        message.success({ content: '裁剪成功' });
      })
      .finally(() => {});
  };
  return (
    <TcsLayout title="Patch详情" customizeCard history={history}>
      <TcsSpin spinning={loading}>
        {data?.Status === 'invalid' && data?.Warning && <Alert type="warning">{data?.Warning}</Alert>}
        <PatchBasicInfo
          patchDetail={data}
          patchId={urlParams?.patchId}
          onLoading={setLoading}
          getDetail={getDetail}
          getUserList={getUserList}
          onPublish={handlePublish}
          updateAuthorized={updateAuthorized}
          userList={userList}
          isAfter={isAfter}
        />
        <IssueTable
          onLoading={setLoading}
          getDetail={getDetail}
          patchId={urlParams?.patchId}
          isPublished={isPublished}
          records={data?.SelectedIssues}
          MainProductCode={data?.MainProductCode}
          updateAuthorized={updateAuthorized}
          isAfter={isAfter}
        />
        <ApplicationTable
          isPublished={isPublished}
          patchId={urlParams?.patchId}
          onloading={setLoading}
          records={data?.PatchedApplications}
          getDetail={getDetail}
          detail={data}
          updateAuthorized={updateAuthorized}
          isAfter={isAfter}
        />
        <TcsCard title="测试包列表" collapsible>
          <Search onSearch={handleSearch} />
          <Table
            columns={getVerificationColumns({ onLoading: setLoading, isPublished, getLookupByCode, handleNeedTailor })}
            bordered="all"
            records={verificationTableRecords || []}
            addons={[
              pageable(),
              autotip({
                emptyText: '暂无数据',
              }),
            ]}
          />
        </TcsCard>
      </TcsSpin>
      {publishModalVisible && (
        <PublishModal
          onClose={() => setPublishModalVisible(false)}
          patchId={urlParams?.patchId}
          onSuccess={getDetail}
          detail={{ ...data, Applications: data?.PatchedApplications }}
        />
      )}
    </TcsLayout>
  );
};

export default Detail;
