import useLookup from '@/common/hookups/useLookup';
import { TcsCard, TcsFormDateRangePicker, TcsFormSelect, TcsFormText, TcsQueryFilter } from '@tencent/tcs-component';

import React from 'react';

export interface IProps {
  onSearch: (values: any) => void;
}

const Search: React.FC<IProps> = ({ onSearch }) => {
  const handleSearch = (values) => {
    onSearch(values);
  };
  const { lookups } = useLookup(['PatchVerificationStatus']);

  return (
    <TcsCard style={{ marginBottom: 20 }} bordered={true}>
      <TcsQueryFilter onSearch={handleSearch}>
        <TcsFormText label="制品分支" name="BranchName" />
        <TcsFormText label="制品Tag" name="TagNum" />
        <TcsFormSelect
          label="状态"
          name="Status"
          options={lookups?.PatchVerificationStatus?.map((item) => ({
            label: item.Name,
            value: item.Code,
          }))}
        />
        <TcsFormDateRangePicker label="出包时间" name="CreatedAt" />
      </TcsQueryFilter>
    </TcsCard>
  );
};

export default Search;
