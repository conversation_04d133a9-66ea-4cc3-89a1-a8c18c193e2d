import React from 'react';
import { Button, DropdownBox, List, Popover } from '@tencent/tea-component';
import styles from './index.module.scss';
import { PACKAGE_STATUS } from '@/common/config';
import { TcsColumns, TcsTable } from '@tencent/tcs-component';
import { DefectManagementRoutePath } from '@/common/routePath';
import { SolutionVersionSelect } from '@/common/components';

export const getColumns = ({
  onViewAssociatedDelivery,
  onPublish,
  onChangeSolutionVersion,
  artifactBranches,
  patchQueues,
}: {
  onViewAssociatedDelivery: (record: any) => void;
  onPublish: (record: any) => void;
  onChangeSolutionVersion: (solutionVersionUUID: string) => void;
  artifactBranches: any[];
  patchQueues: any[];
}) => {
  const patchQueueValueEnum = patchQueues.reduce((acc, cur) => {
    acc[cur.UUID] = { text: cur.Name }; // 使用 UUID 作为 key，Name 作为显示文本
    return acc;
  }, {});
  return [
    {
      dataIndex: 'PatchID',
      title: 'PatchID',
      fixed: 'left',
      width: '9%',
      disable: true,
      copyable: true,
      linkable: true,
      linkProps: {
        linkUrl: (text, record) => `${DefectManagementRoutePath.PATCH_DETAIL_PAGE}?patchId=${record?.PatchID}`,
      },
    },
    {
      dataIndex: 'Name',
      title: 'Patch名称',
      width: '10%',
      search: false,
    },
    {
      dataIndex: 'PatchName',
      title: 'Patch名称',
      hideInTable: true,
    },
    {
      dataIndex: 'PatchQueue.Name',
      title: '所属队列',
      width: '10%',
      valueEnum: patchQueueValueEnum,
      search: false,
    },
    {
      dataIndex: 'PatchQueueUUID',
      title: 'Patch队列',
      width: '10%',
      valueType: 'select',
      valueEnum: patchQueueValueEnum,
      hideInTable: true,
    },
    {
      dataIndex: 'SolutionName',
      title: '解决方案',
      width: '10%',
      search: false,
    },
    {
      dataIndex: 'SolutionVersionCode',
      title: '解决方案版本',
      width: '6%',
      search: false,
    },
    {
      dataIndex: 'SolutionVersionCode',
      title: '解决方案',
      hideInTable: true,
      renderFormItem: () => <SolutionVersionSelect />,
      fieldProps: {
        onChange(value) {
          onChangeSolutionVersion(value?.[1]);
        },
      },
    },

    {
      dataIndex: 'Arch',
      title: '架构',
      width: '7%',
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'PackageArch',
      },
    },
    {
      dataIndex: 'Status',
      title: '状态',
      width: '5%',
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'PatchStatus',
        showType: 'tag',
        mode: 'multiple',
        showSelectAll: true,
      },
    },
    {
      dataIndex: 'BranchName',
      title: '制品分支',
      width: '7%',
      search: false,
    },
    {
      dataIndex: 'ArtifactBranchUUID',
      title: '制品分支',
      width: '7%',
      hideInTable: true,
      valueType: 'select',
      valueEnum:
        artifactBranches?.reduce((acc, cur) => {
          acc[cur.UUID] = { text: cur.BranchName };
          return acc;
        }, {}) || {},
    },
    {
      dataIndex: 'BaselineTagNum',
      title: '初始制品Tag',
      width: '5%',
      search: false,
      render: (text, record) => (record?.BaselineTagNum > 0 ? record?.BaselineTagNum : '-'),
    },
    {
      dataIndex: 'PatchTagNum',
      title: 'Patch制品Tag',
      width: '5%',
      search: false,
      render: (text, record) => (record?.PatchTagNum > 0 ? record?.PatchTagNum : '-'),
    },
    {
      dataIndex: 'Issues',
      title: '缺陷个数',
      search: false,
      width: '4%',
      render: (text, record) => {
        const columns = [
          { dataIndex: 'IssueID', title: 'IssueID', width: 100 },
          {
            dataIndex: 'Title',
            title: '标题',
            width: 300,
            linkable: true,
            linkProps: {
              target: 'blank',
              linkUrl: (text, record) =>
                `/page/defect_manage/__develop_center_next/iteration_manage/defect/detail?issue_id=${record?.IssueID}`,
            },
          },
          {
            dataIndex: 'SeverityLevel',
            title: '严重程度',
            width: 100,
            valueType: 'dictSelect',
            fieldProps: {
              dictType: 'Severity',
              showType: 'tag',
            },
          },
        ];

        return (
          <Popover
            trigger={'click'}
            placement="bottom"
            overlayStyle={{ width: 600 }}
            overlay={
              <TcsTable
                columns={columns}
                dataSource={record?.Issues || []}
                options={false}
                scroll={{
                  x: 600,
                  y: 350,
                }}
              />
            }
          >
            <a>{`${record?.Issues?.length ?? 0}`}</a>
          </Popover>
        );
      },
    },
    {
      dataIndex: 'Stories',
      title: '需求个数',
      search: false,
      width: '4%',
      render: (text, record) => {
        const columns = [
          { dataIndex: 'IssueID', title: 'IssueID', width: 100 },
          {
            dataIndex: 'Title',
            title: '标题',
            width: 300,
            linkable: true,
            linkProps: {
              target: 'blank',
              linkUrl: (text, record) =>
                `/page/defect_manage/__develop_center_next/iteration_manage/story/detail?issue_id=${record?.IssueID}`,
            },
          },
          {
            dataIndex: 'Priority',
            title: '优先级',
            width: 100,
            valueType: 'dictSelect',
            fieldProps: {
              dictType: 'Priority',
              showType: 'tag',
            },
          },
        ];

        return (
          <Popover
            trigger={'click'}
            placement="bottom"
            overlayStyle={{ width: 600 }}
            overlay={
              <TcsTable
                columns={columns}
                dataSource={record?.Stories || []}
                options={false}
                scroll={{
                  x: 600,
                  y: 350,
                }}
              />
            }
          >
            <a>{`${record?.Stories?.length ?? 0}`}</a>
          </Popover>
        );
      },
    },
    {
      dataIndex: 'FuzzyProductCode',
      title: '涉及产品',
      width: '8%',
      render: (text, record) => (record?.Products || []).map((item) => item?.Code).join(','),
    },
    {
      dataIndex: 'APP',
      title: '涉及应用',
      width: '4%',
      search: false,
      render: (text, record) => (
        <Popover
          trigger={'click'}
          placement="bottom"
          overlay={
            <DropdownBox className={styles.dropdown}>
              <div style={{ maxHeight: 400 }}>
                <List type="option" className={styles.list}>
                  {(record?.Applications || []).map((item, index) => (
                    <List.Item key={index}>
                      {item?.Name}-{item?.ArtifactTag}
                    </List.Item>
                  ))}
                </List>
              </div>
            </DropdownBox>
          }
        >
          <a>{`${record?.Applications?.length ?? 0}`}</a>
        </Popover>
      ),
    },
    {
      dataIndex: 'Owner',
      title: '负责人',
      width: '8%',
      search: false,
      valueType: 'staffSelect',
    },
    {
      dataIndex: 'operation',
      title: '操作',
      fixed: 'right' as any,
      width: '10%',
      valueType: 'option',
      render(text, record) {
        return (
          <>
            {record?.Status === PACKAGE_STATUS.Draft && (
              <>
                <Button type="link" onClick={() => onPublish(record)}>
                  发布
                </Button>
              </>
            )}
            {record?.OperationSheetID && (
              <Button
                type="link"
                onClick={() => {
                  window.open(
                    `/page/product-market-jiguang/ops-sheet-manager/parent-sheet-detail?parentSheetId=${record.OperationSheetID}&sheetScene=patch`,
                    '_blank',
                  );
                }}
              >
                查看变更单
              </Button>
            )}
            {!record?.OperationSheetID && record?.PatchedApplications?.length > 0 && (
              <Button
                type="link"
                onClick={() => {
                  window.open(
                    `/page/product-market-jiguang/ops-sheet-manager/create-parent-sheet?patchId=${record?.patchId}&sheetScene=patch`,
                    '_blank',
                  );
                }}
              >
                完善变更单
              </Button>
            )}
            {record?.Status === PACKAGE_STATUS.Published && (
              <Button type="link" onClick={() => onViewAssociatedDelivery(record)}>
                查看关联交付单
              </Button>
            )}
          </>
        );
      },
    },
  ] as TcsColumns[];
};
