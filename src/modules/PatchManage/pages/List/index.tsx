/*
 * @Author: lucyfang
 * @Date: 2024-10-15 10:50:50
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-01-03 17:11:11
 * @Description: 请输入注释信息
 */
import React from 'react';

import { ExternalLink } from '@tencent/tea-component';

import { TcsLayout, TabItem, TcsTabs } from '@tencent/tcs-component';

import SingleArchPatchManage from './components/SingleArchPatchManage';
import MultiArchPatchManage from './components/MultiArchPatchManage';

const List = () => {
  const { history } = TcsLayout.useHistory();

  const items: TabItem[] = [
    {
      key: '1',
      label: '单架构Patch',
      children: <SingleArchPatchManage />,
    },
    {
      key: '2',
      label: '多架构Patch',
      children: <MultiArchPatchManage />,
    },
  ];

  return (
    <TcsLayout
      title="Patch管理"
      customizeCard
      fullHeight
      history={history}
      operation={
        <ExternalLink
          onClick={() => {
            window.open('https://jiguang.woa.com/page/tcsc_document/128084839512842240', '_blank');
          }}
        >
          操作指南
        </ExternalLink>
      }
    >
      <TcsTabs ceiling fullHeight defaultActiveKey="1" items={items} onChange={() => {}} />
    </TcsLayout>
  );
};
export default List;
