import React, { useEffect, useRef, useState, useCallback } from 'react';
import {
  createPatch,
  listDataSolutionVersions,
  listSolutionVersionArtifactBranches,
  listSolutionVersionArtifactTagsSimple,
  PatchManageApi,
} from '@/common/api/patchManage';
import useLookup from '@/common/hookups/useLookup';
import { Select, message, Radio } from '@tencent/tea-component';
import { toListParamsCApi } from '@/common/api/api';
import styles from './index.module.scss';
import { DefectManagementRoutePath } from '@/common/routePath';
import {
  TcsForm,
  TcsLayout,
  TcsModal,
  TcsFormText,
  TcsButton,
  TcsButtonGroup,
  TcsFormDictSelect,
  TcsFormStaffSelect,
} from '@tencent/tcs-component';
import { ISolution, ISolutionVersion, listSolution, listSolutionVersion } from '@/common/api/common';
import { QueueManageApi } from '@/common/api/periodicQueueManage';

interface CreatePatchModalProps {
  onClose: () => void;
  onSuccess: () => void;
}

const CreatePatchModal: React.FC<CreatePatchModalProps> = (props) => {
  const { history } = TcsLayout.useHistory();
  const { onClose } = props;
  const { lookups } = useLookup(['PackageArch']);
  const formRef = useRef<any>();

  const [solutionList, setSolutionList] = useState<ISolution[]>([]);
  const [solutionVersionList, setSolutionVersionList] = useState<ISolutionVersion[]>([]);
  const [artifactBranches, setArtifactBranches] = useState([]);
  const [artifactTags, setArtifactTags] = useState([]);
  const [dataVersionList, setDataVersionList] = useState<any[]>([]);
  const [patchQueues, setPatchQueues] = useState<any[]>([]);
  const [isDefaultQueue, setIsDefaultQueue] = useState(false);
  const [patchQueueUUID, setPatchQueueUUID] = useState('');
  const [statusValue, setStatusValue] = useState('02');
  const [showMainProduct, setShowMainProduct] = useState(false);
  const [showUpdateItem, setShowUpdateItem] = useState(false);
  const [mainProductList, setMainProductList] = useState([]);
  const [isCustomizingName, setIsCustomizingName] = useState(false);
  const [customPatchName, setCustomPatchName] = useState('');
  const [incrementalNum, setIncrementalNum] = useState(Number);
  const [branchList, setBranchList] = useState<any>([]);
  const [currentBranch, setCurrentBranch] = useState('master');
  const defaultNameRuleAll =
    '{SolutionCode}{SolutionVersionCode}-{MainProductCode}-{PatchType}-{IncrementalNum}-{UpdateItem}-{Arch}-{Date}';
  const currentJiguangName = window.jiguang_username;

  // 获取Patch队列列表
  useEffect(() => {
    formRef.current?.setFieldsValue({ Owner: currentJiguangName?.split(';') });
    QueueManageApi.ListPatchQueues({
      PageNo: 1,
      PageSize: 999,
      Order: [{ Key: 'UpdatedAt', Sort: 'DESC' }],
    }).then((res) => {
      if (res.data.length) {
        setPatchQueues(res.data);
      }
    });
  }, []);

  // 获取解决方案
  useEffect(() => {
    if (isDefaultQueue) {
      listSolution().then((res) => {
        if (res.Error) {
          return message.error({
            content: res.Error.Message,
          });
        }
        setSolutionList(res.ListSolutions || []);
      });
    }
  }, [isDefaultQueue]);

  // 获取数据快照分支列表
  useEffect(() => {
    // 当前选择的队列
    setBranchList([]);
    formRef?.current?.setFieldsValue({ Branch: 'master' });
    let SolutionVersionUUID = '';
    if (isDefaultQueue) {
      SolutionVersionUUID = formRef?.current?.getFieldsValue()?.SolutionVersionUUID;
    } else {
      SolutionVersionUUID = patchQueues?.find((item) => item?.UUID === patchQueueUUID)?.SolutionVersionUUID;
    }
    if (!SolutionVersionUUID) return;
    PatchManageApi.ListSolutionVersionDataBranches({ SolutionVersionID: SolutionVersionUUID, Status: '10' })
      .then((res) => {
        if (res?.ListSolutionVersionDataBranches?.length) {
          setBranchList(res.ListSolutionVersionDataBranches);
        }
      })
      .catch(() => {});
  }, [isDefaultQueue, patchQueueUUID, patchQueues, formRef?.current?.getFieldsValue()?.SolutionVersionUUID]);

  // 获取解决方案版本列表
  const getSolutionVersions = (value) => {
    setSolutionVersionList([]);
    listSolutionVersion({ SolutionUUID: value }).then((res) => {
      if (res.Error) {
        return message.error({
          content: res.Error.Message,
        });
      }
      setSolutionVersionList(res.ListSolutionVersions || []);
    });
  };

  // 获取制品分支列表
  const getArtifactBranches = (value) => {
    setArtifactBranches([]);
    listSolutionVersionArtifactBranches({
      SolutionVersionArtifactBranch: {
        _Filter: toListParamsCApi(
          { SolutionVersionID: value, Label: 'patch', BranchType: 'release' },
          {
            useEqFields: ['SolutionVersionID', 'BranchType'],
          },
        )._Filter,
        _Order: [{ Key: 'ID', Sort: 'ASC' }],
      },
    }).then((res) => {
      if (res.Error) {
        return message.error({
          content: res.Error.Message,
        });
      }
      setArtifactBranches(res.ListSolutionVersionArtifactBranches || []);
    });
  };

  // 获取初始制品TAG
  const getArtifactTagsSimple = useCallback(
    (ArtifactBranchUUID: string, filterData?: {}) => {
      const { SolutionVersionUUID, Arch } = isDefaultQueue ? formRef?.current?.getFieldsValue() : filterData;

      setArtifactTags([]);
      listSolutionVersionArtifactTagsSimple({
        SolutionVersionArtifactTag: {
          _Filter: toListParamsCApi(
            { SolutionVersionID: SolutionVersionUUID, ArtifactBranchUUID, Arch },
            {
              useEqFields: ['ArtifactBranchUUID', 'SolutionVersionID'],
            },
          )._Filter,
          _Order: [{ Key: 'TagNum', Sort: 'DESC' }],
        },
      }).then((res) => {
        if (res.Error) {
          return message.error({
            content: res.Error.Message,
          });
        }
        setArtifactTags(res.ListSolutionVersionArtifactTags || []);
      });
    },
    [isDefaultQueue],
  );

  // 获取数据快照版本列表
  useEffect(() => {
    setDataVersionList([]);
    const filterData = patchQueues.find((item) => item.UUID === patchQueueUUID);
    if (!filterData) return;
    const SolutionVersionUUID = isDefaultQueue
      ? formRef?.current?.getFieldsValue()?.SolutionVersionUUID
      : patchQueues?.find((item) => item?.UUID === patchQueueUUID)?.SolutionVersionUUID;
    if (!SolutionVersionUUID) return;
    listDataSolutionVersions({
      SolutionVersion: {
        _Filter: toListParamsCApi(
          { UUID: SolutionVersionUUID, Branch: currentBranch, Status: statusValue === 'all' ? undefined : statusValue },
          {
            useEqFields: ['UUID', 'Branch', 'Status'],
          },
        )._Filter?.concat([{ Key: 'Tag', Op: '!=', Value: '' }]),
        _Order: [
          {
            Key: 'TagNum',
            Sort: 'DESC',
          },
        ],
        _Select: ['id', 'tag', 'tag_num', 'audit_description', 'status'],
      },
    }).then((res) => {
      if (res?.Error) {
        return message.error({
          content: res.Error.Message,
        });
      }
      setDataVersionList(res?.ListSolutionVersions ?? []);
    });
  }, [
    isDefaultQueue,
    patchQueueUUID,
    patchQueues,
    statusValue,
    currentBranch,
    formRef?.current?.getFieldsValue()?.SolutionVersionUUID,
  ]);

  // 获取主产品
  const getMainProduct = (uuid, text) =>
    QueueManageApi.QueryAddableProduct({
      PatchQueueUUID: uuid,
    }).then((res) => {
      if (text === '通用队列') {
        setMainProductList(res.data);
      } else {
        setMainProductList(res.data.filter((item) => item?.IsAdded));
      }
    });

  const handleConfirm = () => {
    formRef?.current?.validateFields().then((values) => {
      // 如果不是默认队列，从 patchQueues 中获取额外的信息
      let extraInfo = {};
      if (!isDefaultQueue) {
        const queueInfo = patchQueues.find((item) => item.UUID === patchQueueUUID);
        if (queueInfo) {
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          extraInfo = {
            SolutionUUID: queueInfo?.SolutionUUID,
            SolutionVersionUUID: queueInfo?.SolutionVersionUUID,
            Arch: queueInfo?.Arch,
            ArtifactBranchUUID: queueInfo?.ArtifactBranchUUID,
          };
        }
      }

      // 合并 values 和额外的信息
      const payload = {
        ...values,
        ...extraInfo,
        Owners: values?.Owner?.join(';'),
        SolutionVersionID: values.SolutionVersionID ? Number(values.SolutionVersionID) : values.SolutionVersionID,
        Status: undefined,
        IncrementalNum: incrementalNum || undefined,
      };
      createPatch(payload)
        .then((res) => {
          const { Error, PatchID } = res;
          if (Error) {
            message.error({
              content: Error.Message,
            });
          } else {
            history.push(`${DefectManagementRoutePath.PATCH_DETAIL_PAGE}?patchId=${PatchID}`);
          }
        })
        .finally(() => {
          onClose();
        });
    });
  };
  // 队列选择，区分筛选框的展示
  const getSelectInfo = (value, text) => {
    formRef?.current?.setFieldsValue({
      Branch: '',
    });
    setPatchQueueUUID(value);
    setIsDefaultQueue(text === '通用队列');
    // 获取队列信息
    const queueInfo = patchQueues.find((item) => item.UUID === value);
    if (!queueInfo?.NamingRule) {
      message.error({
        content: `${text}下命名规则为空，无法预生成 patch 名称`,
      });
    }
    if (queueInfo) {
      const NamingRule = queueInfo?.NamingRule || defaultNameRuleAll;
      setShowMainProduct(NamingRule.includes('{MainProductCode}'));
      setShowUpdateItem(NamingRule.includes('{UpdateItem}'));
    }
    formRef?.current?.setFieldsValue({
      PatchType: '',
      Arch: '',
      ArtifactBranchUUID: '',
      BaselineTagID: '',
      SolutionVersionID: '',
      MainProductCode: '',
      UpdateItem: '',
      PatchName: '',
      SolutionUUID: '',
      SolutionVersionUUID: '',
    });
  };

  useEffect(() => {
    if (isDefaultQueue) {
      setIsDefaultQueue(true);
      formRef?.current?.setFieldsValue({
        SolutionUUID: '',
        SolutionVersionUUID: '',
        Arch: '',
        ArtifactBranchUUID: '',
        BaselineTagID: '',
        SolutionVersionID: '',
      });
    } else {
      const filterData = patchQueues.filter((item) => item.UUID === patchQueueUUID)[0];
      if (!filterData) return;
      getArtifactTagsSimple(filterData?.ArtifactBranchUUID, filterData);
      formRef?.current?.setFieldsValue({
        BaselineTagID: '',
        SolutionVersionID: '',
      });
    }
  }, [getArtifactTagsSimple, isDefaultQueue, patchQueueUUID, patchQueues]);

  const handleValueChange = (values, cons) => {
    if (values?.PatchName !== undefined) {
      return;
    }
    const queueInfo = patchQueues.find((item) => item.UUID === cons.PatchQueueUUID);
    // 判断是否是通用队列
    const isGeneralQueue = cons.PatchQueueUUID && queueInfo?.Name === '通用队列';
    // 获取当前队列的命名规则
    const nameRule = queueInfo?.NamingRule || defaultNameRuleAll;

    // 构建基础参数
    const params = {
      PatchType: cons.PatchType,
      PatchQueueUUID: cons.PatchQueueUUID,
    };

    // 检查基础参数是否都有值
    if (!params.PatchType || !params.PatchQueueUUID) {
      return; // 如果基础参数没有值，则直接返回
    }

    // 如果是通用队列，增加 SolutionVersionUUID 和 Arch
    if (isGeneralQueue) {
      if (!cons.SolutionVersionUUID || !cons.Arch) {
        return; // 如果通用队列需要的参数没有值，则直接返回
      }
      params.SolutionVersionUUID = cons.SolutionVersionUUID;
      params.Arch = cons.Arch;
    }

    if (nameRule.includes('{MainProductCode}')) {
      if (!cons.MainProductCode) {
        return; // 如果命名规则包含 MainProductCode 但表单中没有值，则直接返回
      }
      params.MainProductCode = cons.MainProductCode;
    }

    if (nameRule.includes('{UpdateItem}')) {
      if (!cons.UpdateItem) {
        return; // 如果命名规则包含 UpdateItem 但表单中没有值，则直接返回
      }
      params.UpdateItem = cons.UpdateItem;
    }

    // 调用生成 Patch 包名称的接口
    QueueManageApi.PreRenderPatchName(params).then((res) => {
      formRef?.current?.setFieldsValue({
        PatchName: res?.PatchName,
      });
      setCustomPatchName(res?.PatchName); // 更新自定义名称
      setIncrementalNum(res?.IncrementalNum);
    });
  };
  return (
    <TcsModal visible title="新建Patch" onCancel={onClose} onOk={handleConfirm} width={650}>
      <TcsForm
        formRef={formRef}
        className={styles.form}
        onValuesChange={(values, cons) => {
          handleValueChange(values, cons);
        }}
      >
        <TcsForm.Item
          label="Patch队列"
          name="PatchQueueUUID"
          rules={[
            {
              message: '请选择Patch队列',
              required: true,
            },
          ]}
        >
          <Select
            placeholder="请选择Patch队列"
            searchable
            size="full"
            appearance="button"
            options={patchQueues.map((item: any) => ({
              text: `${item.Name}`,
              value: item.UUID,
              content: item,
            }))}
            onChange={(value, con) => {
              getSelectInfo(value, con?.option?.text);
              getMainProduct(value, con?.option?.text);
            }}
            matchButtonWidth
          />
        </TcsForm.Item>

        <TcsFormDictSelect
          label="Patch类型"
          name="PatchType"
          placeholder="请选择Patch类型"
          rules={[
            {
              required: true,
              message: '请选择Patch类型',
            },
          ]}
          fieldProps={{
            dictType: 'PatchType',
            size: 'full',
            onChange: () => {
              formRef?.current?.setFieldsValue({
                MainProductCode: '',
                UpdateItem: '',
                PatchName: '',
              });
            },
          }}
        />
        {isDefaultQueue && (
          <>
            <TcsForm.Item
              label="解决方案"
              name="SolutionUUID"
              rules={[
                {
                  message: '请选择解决方案',
                  required: true,
                },
              ]}
            >
              <Select
                placeholder="请选择解决方案"
                searchable
                size="full"
                appearance="button"
                matchButtonWidth
                options={solutionList.map((item: any) => ({
                  text: `${item.Name}(${item.NameEN || ''})`,
                  value: item.UUID,
                }))}
                onChange={(value) => {
                  if (value) {
                    getSolutionVersions(value);
                  }
                  formRef?.current?.setFieldsValue({
                    SolutionVersionUUID: '',
                    Arch: '',
                    ArtifactBranchUUID: '',
                    BaselineTagID: '',
                    SolutionVersionID: '',
                    MainProductCode: '',
                    UpdateItem: '',
                    PatchName: '',
                  });
                }}
              />
            </TcsForm.Item>
            <TcsForm.Item
              label="解决方案版本"
              name="SolutionVersionUUID"
              rules={[
                {
                  message: '请选择解决方案版本',
                  required: true,
                },
              ]}
            >
              <Select
                placeholder="请选择解决方案版本"
                size="full"
                searchable
                appearance="button"
                matchButtonWidth
                options={solutionVersionList.map((item: any) => ({
                  text: item.Code,
                  value: item.UUID,
                }))}
                onChange={(value) => {
                  if (value) {
                    getArtifactBranches(value);
                  }
                  formRef?.current?.setFieldsValue({
                    Arch: '',
                    ArtifactBranchUUID: '',
                    BaselineTagID: '',
                    SolutionVersionID: '',
                    PatchName: '',
                  });
                }}
              />
            </TcsForm.Item>

            <TcsForm.Item
              label="架构"
              name="Arch"
              rules={[
                {
                  message: '请数据架构',
                  required: true,
                },
              ]}
            >
              <Select
                appearance="button"
                placeholder="请选择架构"
                size="full"
                matchButtonWidth
                clearable
                options={
                  lookups?.PackageArch?.map((item) => ({
                    value: item.Code,
                    text: item.Name,
                  })) || []
                }
                onChange={() => {
                  formRef?.current?.setFieldsValue({
                    BaselineTagID: '',
                    SolutionVersionID: '',
                    MainProductCode: '',
                    UpdateItem: '',
                    PatchName: '',
                  });
                }}
              />
            </TcsForm.Item>
            <TcsForm.Item
              label="制品分支"
              name="ArtifactBranchUUID"
              tooltip="制品分支数据来源于已添加标签【patch】的解决方案制品分支"
              rules={[
                {
                  message: '请选择制品分支',
                  required: true,
                },
              ]}
            >
              <Select
                appearance="button"
                placeholder="请选择制品分支"
                size="full"
                matchButtonWidth
                clearable
                options={artifactBranches?.map((item: any) => ({
                  text: item?.BranchName,
                  value: item?.UUID,
                  extra: item?.DataBranch,
                }))}
                onChange={(value) => {
                  getArtifactTagsSimple(value);
                }}
              />
            </TcsForm.Item>
          </>
        )}
        <TcsForm.Item
          label="初始制品Tag"
          name="BaselineTagID"
          rules={[
            {
              message: '请选择初始制品Tag',
              required: true,
            },
          ]}
        >
          <Select
            appearance="button"
            placeholder="请选择初始制品Tag"
            size="l"
            clearable
            searchable
            matchButtonWidth
            options={artifactTags?.map((item: any) => ({
              text: `${item?.TagNum}-${item?.TagDescription}`,
              value: item?.UUID,
            }))}
            onChange={() => {
              // getDataSolutionVersions(statusValue);
              formRef?.current?.setFieldsValue({
                SolutionVersionID: '',
              });
            }}
          />
        </TcsForm.Item>
        <TcsForm.Item
          label="数据快照分支"
          name="Branch"
          rules={[
            {
              message: '请选择数据快照分支',
              required: true,
            },
          ]}
        >
          <Select
            appearance="button"
            placeholder="请选择数据快照分支"
            size="l"
            clearable
            searchable
            matchButtonWidth
            options={branchList?.map((item) => ({ text: item.BranchName, value: item.BranchCode }))}
            onChange={(value) => {
              setCurrentBranch(value);
              formRef?.current?.setFieldsValue({
                SolutionVersionID: '',
              });
            }}
          />
        </TcsForm.Item>

        <TcsForm.Item label="数据快照版本状态" name="Status">
          <Radio.Group
            defaultValue="02"
            onChange={(value) => {
              setStatusValue(value);
              // getDataSolutionVersions(value);
              formRef?.current?.setFieldsValue({
                SolutionVersionID: '',
              });
            }}
          >
            <Radio name="02">正式</Radio>
            <Radio name="all">全部</Radio>
          </Radio.Group>
        </TcsForm.Item>
        <TcsForm.Item
          label="数据快照版本"
          name="SolutionVersionID"
          rules={[
            {
              message: '请选择数据快照版本',
              required: true,
            },
          ]}
        >
          <Select
            appearance="button"
            placeholder="请选择数据快照版本"
            size="l"
            matchButtonWidth
            clearable
            searchable
            options={
              dataVersionList.map((item) => ({
                value: String(item.ID),
                text: `${item?.TagNum ?? ''}-${item?.AuditDescription ?? ''}`,
              })) || []
            }
          />
        </TcsForm.Item>

        <TcsFormStaffSelect
          label="Patch单负责人"
          name="Owner"
          valueField="enName"
          fieldProps={{
            multiple: true,
          }}
          rules={[
            {
              required: true,
              message: '请选择Patch单负责人',
            },
          ]}
        />
        {/* 主产品 */}
        {showMainProduct && (
          <TcsForm.Item
            label="主产品"
            name="MainProductCode"
            rules={[
              {
                message: '请选择主产品',
                required: true,
              },
            ]}
          >
            <Select
              placeholder="请选择主产品"
              searchable
              size="full"
              appearance="button"
              options={mainProductList.map((item) => ({
                value: item?.Code,
                text: item?.Name,
              }))}
              matchButtonWidth
              onChange={() => {
                formRef?.current?.setFieldsValue({
                  PatchName: '',
                });
              }}
            />
          </TcsForm.Item>
        )}

        {/* 更新项名称 */}
        {showUpdateItem && (
          <TcsFormText
            label="更新项名称"
            name="UpdateItem"
            rules={[
              {
                required: true,
                message: '请输入更新项名称',
              },
            ]}
            fieldProps={{
              size: 'full',
              onChange: () => {
                formRef?.current?.setFieldsValue({
                  PatchName: '',
                });
              },
            }}
          />
        )}
        <TcsFormText
          label="Patch包名称"
          name="PatchName"
          rules={[
            {
              message: 'Patch包名称为必填项',
              required: true,
            },
          ]}
          tooltip="重新选择条件，Patch包名称会被重置"
          readonly={!isCustomizingName}
          fieldProps={{
            size: 'full',
          }}
          suffix={
            isCustomizingName ? (
              <TcsButtonGroup
                items={[
                  {
                    text: '确认',
                    confirm: true,
                    confirmProps: {
                      title: '确认修改',
                      onConfirm: () => {
                        setCustomPatchName(formRef?.current?.getFieldsValue()?.PatchName);
                        setIsCustomizingName(false);
                      },
                    },
                  },
                  {
                    text: '取消',
                    confirm: true,
                    confirmProps: {
                      title: '确认取消',
                      onConfirm: () => {
                        // 恢复原来的名称
                        formRef?.current?.setFieldsValue({ PatchName: customPatchName });
                        setIsCustomizingName(false);
                      },
                    },
                  },
                ]}
              />
            ) : (
              <TcsButton
                type="link"
                onClick={() => {
                  setIsCustomizingName(true);
                }}
              >
                自定义名称
              </TcsButton>
            )
          }
        />
      </TcsForm>
    </TcsModal>
  );
};
export default CreatePatchModal;
