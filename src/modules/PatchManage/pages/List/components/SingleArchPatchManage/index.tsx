import React, { useCallback, useEffect, useRef, useState } from 'react';

import { Button, Switch, message } from '@tencent/tea-component';
import { getColumns } from './config';
import { listPatch, listSolutionVersionArtifactBranches } from '@/common/api/patchManage';
import CreatePatchModal from '../CreatePatchModal';
import PublishModal from '../../../Common/PublishModal';
import { ActionType, TcsFormInstance, TcsLayout, TcsTable } from '@tencent/tcs-component';
import { tapi } from '@tencent/tcsc-base';
import { DefectManagementRoutePath } from '@/common/routePath';
import { QueueManageApi } from '@/common/api/periodicQueueManage';
import { getDeliveryInfosByFromType } from '@/common/api/commonApi';

const SingleArchPatchManage = () => {
  const relatedCache = localStorage.getItem('defect_manage/patch_manage/RelatedToMe');
  const [related, setRelated] = useState(relatedCache ? relatedCache === 'true' : false);

  const patchIdRef = useRef('');
  const patchData = useRef({});

  const { history } = TcsLayout.useHistory();

  const [createPatchVisible, setCreatePatchVisible] = useState(false);
  const [publishModalVisible, setPublishModalVisible] = useState(false);
  const actionRef = useRef<ActionType>();
  const [artifactBranches, setArtifactBranches] = useState<any[]>([]);
  const [patchQueues, setPatchQueues] = useState([]);

  const formRef = useRef<TcsFormInstance>();

  const handlePublish = (record) => {
    patchIdRef.current = record?.PatchID;
    patchData.current = record;
    setPublishModalVisible(true);
  };

  const handleCreatePatch = useCallback(() => {
    setCreatePatchVisible(true);
  }, []);

  const handleClose = useCallback(() => {
    setCreatePatchVisible(false);
  }, []);

  const handleViewAssociatedDelivery = (record) => {
    getDeliveryInfosByFromType({
      FromType: 'patch',
      FromKey: record.PatchID,
    })
      .then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          const deliveryList = res.DeliveryDetailInfo || [];
          if (deliveryList.length === 0) {
            message.warning({
              content: '当前缺陷单没有关联交付单',
            });
          } else {
            // 直接跳转到交付单详情页
            window.open(`${DefectManagementRoutePath.DELIVERY_DETAIL_PAGE}?uuid=${deliveryList[0].UUID}`, '_blank');
          }
        }
      })
      .finally(() => {});
  };

  const columns = getColumns({
    onViewAssociatedDelivery: handleViewAssociatedDelivery,
    onPublish: handlePublish,
    onChangeSolutionVersion: getArtifactBranches,
    artifactBranches,
    patchQueues,
  });

  useEffect(() => {
    QueueManageApi.ListPatchQueues({
      PageNo: 1,
      PageSize: 999,
      Order: [{ Key: 'UpdatedAt', Sort: 'DESC' }],
    }).then((res) => {
      if (res.data.length) {
        setPatchQueues(res.data);
      }
    });
  }, []);

  function handleRequest(queryParams) {
    const { Status, current, pageSize, PatchID, SolutionVersionCode, ...other } = queryParams;

    return listPatch({
      ...other,
      Status,
      PageSize: pageSize,
      PageNo: current,
      IsOwner: related,
      PatchIDList: PatchID ? [PatchID] : undefined,
      SolutionUUID: SolutionVersionCode?.[0],
      SolutionVersionUUID: SolutionVersionCode?.[1],
    }).then((res) => {
      if (res?.Error) {
        message.error({
          content: res.Error.Message,
        });
        return {
          data: [],
          total: 0,
        };
      }

      return {
        success: true,
        data: res?.List || [],
        total: res?.Total ?? 0,
      };
    });
  }

  // 获取制品分支列表
  function getArtifactBranches(value) {
    setArtifactBranches([]);
    formRef.current?.resetFields(['ArtifactBranchUUID']);
    if (value) {
      listSolutionVersionArtifactBranches({
        SolutionVersionArtifactBranch: {
          _Filter: tapi.toListParamsCApi(
            { SolutionVersionID: value, Label: 'patch', BranchType: 'release' },
            {
              useEqualFields: ['SolutionVersionID', 'BranchType'],
            },
          )._Filter,
          _Order: [{ Key: 'ID', Sort: 'ASC' }],
        },
      }).then((res) => {
        if (res.Error) {
          return message.error({
            content: res.Error.Message,
          });
        }
        setArtifactBranches(res.ListSolutionVersionArtifactBranches || []);
      });
    }
  }

  function handlePeriodicQueue() {
    history.push(`${DefectManagementRoutePath.PATCH_QUEUE_MANAGE_PAGE}`);
  }

  return (
    <>
      <TcsTable
        scrollInTable
        columns={columns}
        request={handleRequest}
        formRef={formRef}
        headerTitle={
          <>
            <Button type="primary" onClick={handleCreatePatch}>
              新建Patch
            </Button>
            <Button onClick={handlePeriodicQueue}>Patch队列管理</Button>
          </>
        }
        toolBarRender={() => (
          <>
            {/* <TcsSpace> */}
            <Switch
              defaultChecked
              value={related}
              onChange={(value) => {
                localStorage.setItem('defect_manage/patch_manage/RelatedToMe', String(value));
                setRelated(value);
                actionRef.current?.reload(true);
              }}
            >
              与我相关
            </Switch>
            {/* </TcsSpace> */}
          </>
        )}
        columnsState={{
          persistenceKey: 'defect_manage/patch_manage/list',
        }}
        search={{}}
        pagination={{}}
        scroll={{
          x: 2000,
        }}
        actionRef={actionRef}
      />

      {createPatchVisible && (
        <CreatePatchModal
          onClose={handleClose}
          onSuccess={() => {
            actionRef.current?.reload(true);
          }}
        />
      )}
      {publishModalVisible && (
        <PublishModal
          detail={patchData.current}
          onClose={() => setPublishModalVisible(false)}
          patchId={patchIdRef.current}
          onSuccess={() => {
            actionRef.current?.reload(true);
          }}
        />
      )}
    </>
  );
};
export default SingleArchPatchManage;
