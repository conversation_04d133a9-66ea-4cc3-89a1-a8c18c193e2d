/*
 * @Author: lucyfang
 * @Date: 2024-12-30 18:43:05
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-01-06 17:34:09
 * @Description: 请输入注释信息
 */
import React, { useRef, useState } from 'react';
import { TcsTable, TcsButton, ActionType, TcsColumns, TcsFormInstance, TcsPopConfirm } from '@tencent/tcs-component';
import { DropdownBox, List, Popover, message } from '@tencent/tea-component';
import styles from './index.module.scss';
import { PatchManageApi } from '@/common/api/patchManage';
import AssociatedPatchModal from '../AssociatedPatchModal';
import { DefectManagementRoutePath } from '@/common/routePath';

export interface IProps {
  patchMultiArchUUID: string;
  record: any;
}

const AssociatedPatchManage = ({ patchMultiArchUUID, record }) => {
  const formRef = useRef<TcsFormInstance>();
  const actionRef = useRef<ActionType>();
  const [visible, setVisible] = useState(false);
  const [list, setList] = useState([]);
  const [archList, setArchList] = useState<string[]>([]);
  const [buttonVisible, setButtonVisible] = useState(false);

  function handleRequest() {
    setButtonVisible(false);
    return PatchManageApi.ListAssociatedPatchesByPatchMultiArchUUID({ PatchMultiArchUUID: patchMultiArchUUID }).then(
      (res) => {
        setButtonVisible(true);
        if (res?.data?.length) {
          setList(res?.data);
        } else {
          setList([]);
        }
        return {
          data: res?.data || [],
          total: res?.data?.length || 0,
        };
      },
    );
  }
  const handleAdd = () => {
    const uniqueArchArray: string[] = Array.from(new Set(list?.map((item): string => item?.Arch)));
    setArchList(uniqueArchArray);
    setVisible(true);
  };
  const handleDelect = (record) => {
    PatchManageApi.DeleteAssociatedPatch({ PatchMultiArchUUID: patchMultiArchUUID, PatchID: record.PatchID }).then(
      () => {
        message.success({ content: '删除成功' });
        actionRef.current?.reload();
      },
    );
  };
  const columns = [
    {
      dataIndex: 'PatchID',
      title: 'PatchID',
      fixed: 'left',
      width: '10%',
      disable: true,
      copyable: true,
      linkable: true,
      linkProps: {
        linkUrl: (text, record) => `${DefectManagementRoutePath.PATCH_DETAIL_PAGE}?patchId=${record?.PatchID}`,
      },
    },
    {
      dataIndex: 'Name',
      title: 'Patch名称',
      width: '10%',
    },
    {
      dataIndex: ['PatchQueue', 'Name'],
      title: '所属队列',
      width: '10%',
    },
    {
      title: '架构',
      width: '10%',
      dataIndex: 'Arch',
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'PackageArch',
      },
    },
    {
      title: '状态',
      dataIndex: 'Status',
      width: '5%',
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'PatchStatus',
        showType: 'tag',
        mode: 'multiple',
        showSelectAll: true,
      },
    },
    {
      dataIndex: 'APP',
      title: '涉及应用',
      width: '5%',
      search: false,
      render: (text, record) => (
        <Popover
          trigger={'click'}
          placement="bottom"
          overlay={
            <DropdownBox className={styles.dropdown}>
              <div style={{ maxHeight: 400 }}>
                <List type="option" className={styles.list}>
                  {(record?.Applications || []).map((item, index) => (
                    <List.Item key={index}>
                      {item?.Name}-{item?.ArtifactTag}
                    </List.Item>
                  ))}
                </List>
              </div>
            </DropdownBox>
          }
        >
          <a>{`${record?.Applications?.length ?? 0}`}</a>
        </Popover>
      ),
    },
    {
      dataIndex: 'Owner',
      title: '负责人',
      width: '10%',
      search: false,
      valueType: 'staffSelect',
    },
    {
      dataIndex: 'operation',
      title: '操作',
      fixed: 'right' as any,
      width: '10%',
      valueType: 'option',
      render(text, record) {
        return (
          <TcsPopConfirm
            title="确定要删除吗？"
            onConfirm={(e) => {
              e?.stopPropagation();
              handleDelect(record);
            }}
            okText="确定"
            cancelText="取消"
          >
            <TcsButton type="link">删除</TcsButton>
          </TcsPopConfirm>
        );
      },
    },
  ] as TcsColumns[];

  return (
    <>
      <TcsTable
        actionRef={actionRef}
        scrollInTable
        columns={columns}
        headerTitle={
          buttonVisible && list?.length !== 2 ? (
            <TcsButton onClick={() => handleAdd()} type="primary">
              关联Patch
            </TcsButton>
          ) : null
        }
        request={handleRequest}
        formRef={formRef}
        pagination={{}}
        scroll={{
          x: 800,
        }}
        options={false}
      />
      {visible && (
        <AssociatedPatchModal
          onCancel={() => {
            setVisible(false);
          }}
          onSuccess={() => {
            setVisible(false);
            actionRef.current?.reload();
          }}
          archList={archList}
          record={record}
        />
      )}
    </>
  );
};
export default AssociatedPatchManage;
