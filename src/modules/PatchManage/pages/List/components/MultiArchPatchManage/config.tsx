/*
 * @Author: lucyfang
 * @Date: 2024-12-27 14:27:40
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-01-03 17:23:17
 * @Description: 请输入注释信息
 */
import React from 'react';
import { TcsColumns, TcsPopConfirm, TcsButton } from '@tencent/tcs-component';
import { SolutionVersionSelect } from '@/common/components';

export const getColumns = ({ handleDelect }: { handleDelect: (record: any) => void }) => {
  return [
    {
      dataIndex: 'Name',
      title: '多架构Patch名称',
      fixed: 'left',
    },
    { dataIndex: 'SolutionName', title: '解决方案名称', search: false },
    { dataIndex: 'SolutionVersionCode', title: '解决方案版本', search: false },
    {
      dataIndex: 'SolutionVersionUUID',
      title: '解决方案版本',
      hideInTable: true,
      renderFormItem: () => <SolutionVersionSelect />,
    },
    {
      dataIndex: 'PatchID',
      title: 'PatchID',
      hideInTable: true,
    },
    {
      dataIndex: 'Creator',
      title: '创建人',
      search: false,
      valueType: 'staffSelect',
    },
    {
      dataIndex: 'CreatedAt',
      title: '创建时间',
      valueType: 'dateTime',
      search: false,
    },
    {
      dataIndex: 'UpdatedAt',
      title: '最后更新时间',
      valueType: 'dateTime',
      search: false,
    },
    {
      dataIndex: 'operation',
      title: '操作',
      fixed: 'right' as any,
      width: '10%',
      valueType: 'option',
      render(text, record) {
        return (
          <div onClick={(e) => e.stopPropagation()}>
            <TcsPopConfirm
              title="确定要删除吗？"
              onConfirm={() => handleDelect(record)}
              okText="确定"
              cancelText="取消"
            >
              <TcsButton type="link">删除</TcsButton>
            </TcsPopConfirm>
          </div>
        );
      },
    },
  ] as TcsColumns[];
};
