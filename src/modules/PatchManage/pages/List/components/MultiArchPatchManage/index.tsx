/*
 * @Author: lucyfang
 * @Date: 2024-12-27 14:27:29
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-01-03 17:17:19
 * @Description: 请输入注释信息
 */
import React, { useRef, useState } from 'react';

import { getColumns } from './config';
import { ActionType, TcsFormInstance, TcsTable, TcsButton } from '@tencent/tcs-component';
import { PatchManageApi } from '@/common/api/patchManage';
import AddMultiArchModal from '../AddMultiArchModal';
import AssociatedPatchManage from '../AssociatedPatchManage';
const MultiArchPatchManage = () => {
  const formRef = useRef<TcsFormInstance>();
  const actionRef = useRef<ActionType>();
  const [visible, setVisible] = useState(false);

  function handleRequest(queryParams) {
    const { current, pageSize, PatchID, Name, SolutionVersionUUID } = queryParams;
    return PatchManageApi.ListMultiArchPatches({
      Name,
      PatchID,
      SolutionVersionUUID: SolutionVersionUUID?.[1],
      PageNo: current,
      PageSize: pageSize,
      Order: [
        {
          Key: 'CreatedAt',
          Sort: 'DESC',
        },
      ],
    });
  }
  const handleAdd = () => {
    setVisible(true);
  };
  const handleDelect = (record) => {
    PatchManageApi.DeleteMultiArchPatch({
      PatchMultiArchUUID: record.PatchMultiArchUUID,
    }).then((res) => {
      if (res) {
        actionRef.current?.reload();
      }
    });
  };

  return (
    <>
      <TcsTable
        rowKey="PatchMultiArchUUID"
        scrollInTable
        columns={getColumns({ handleDelect })}
        headerTitle={
          <TcsButton onClick={handleAdd} type="primary">
            新建
          </TcsButton>
        }
        request={handleRequest}
        formRef={formRef}
        search={{ labelWidth: 100 }}
        pagination={{}}
        scroll={{
          x: 800,
        }}
        actionRef={actionRef}
        expandable={{
          expandRowByClick: true,
          expandedRowRender(record) {
            return <AssociatedPatchManage record={record} patchMultiArchUUID={record.PatchMultiArchUUID} />;
          },
        }}
      />
      {visible && (
        <AddMultiArchModal
          onCancel={() => setVisible(false)}
          onSuccess={() => {
            setVisible(false);
            actionRef.current?.reload();
          }}
        />
      )}
    </>
  );
};
export default MultiArchPatchManage;
