/*
 * @Author: lucyfang
 * @Date: 2024-12-30 14:37:30
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-01-16 11:19:00
 * @Description: 请输入注释信息
 */
import React, { useState, useRef, useEffect } from 'react';
import { Select, message } from '@tencent/tea-component';
import { TcsForm, TcsFormText, TcsModal, TcsFormSelect, TcsSpace, TcsButton } from '@tencent/tcs-component';
import { ISolution, ISolutionVersion, listSolution, listSolutionVersion } from '@/common/api/common';
import { PatchManageApi, AssociatedPatch } from '@/common/api/patchManage';
interface IProps {
  onCancel: () => void;
  onSuccess: () => void;
}

const AddMultiArchModal: React.FC<IProps> = ({ onCancel, onSuccess }) => {
  const formRef = useRef<any>();
  const [solutionList, setSolutionList] = useState<ISolution[]>([]);
  const [solutionVersionList, setSolutionVersionList] = useState<ISolutionVersion[]>([]);
  const [x86PatchList, setX86PatchList] = useState<AssociatedPatch[]>([]);
  const [armPatchList, setArmPatchPatchList] = useState<AssociatedPatch[]>([]);
  const [solutionUUID, setSolutionUUID] = useState('');
  const [solutionVersionUUID, setSolutionVersionUUID] = useState('');
  const [loading, setLoading] = useState(false);
  const [upDateLoading, setUpDateLoading] = useState(false);
  // 获取解决方案
  useEffect(() => {
    listSolution().then((res) => {
      if (res.Error) {
        return message.error({
          content: res.Error.Message,
        });
      }
      setSolutionList(res.ListSolutions || []);
    });
  }, []);

  // 获取多架构patch信息
  useEffect(() => {
    if (solutionUUID && solutionVersionUUID) {
      fetchPatches();
    }
  }, [solutionUUID, solutionVersionUUID]);
  // 获取解决方案版本列表
  const getSolutionVersions = (value) => {
    setSolutionUUID(value);
    // 重置数据
    setSolutionVersionUUID('');
    setSolutionVersionList([]);
    setX86PatchList([]);
    setArmPatchPatchList([]);
    listSolutionVersion({ SolutionUUID: value }).then((res) => {
      if (res.Error) {
        return message.error({
          content: res.Error.Message,
        });
      }
      setSolutionVersionList(res.ListSolutionVersions || []);
    });
  };
  const handleConfirm = () => {
    formRef.current?.validateFields().then((values: any) => {
      setLoading(true);
      PatchManageApi.CreateMultiArchPatch({
        ...values,
      })
        .then(() => {
          message.success({ content: '新建成功' });
          onSuccess();
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };
  const fetchPatches = () => {
    // 重置数据
    setX86PatchList([]);
    setArmPatchPatchList([]);
    setUpDateLoading(true);
    PatchManageApi.ListNeedAssociatedPatch({
      SolutionUUID: solutionUUID,
      SolutionVersionUUID: solutionVersionUUID,
      Arches: ['rhel.amd64', 'rhel.arm64'],
    })
      .then((res) => {
        if (res?.AssociatedPatches?.length) {
          setX86PatchList(res.AssociatedPatches?.filter((item) => item?.Arch === 'rhel.amd64'));
          setArmPatchPatchList(res.AssociatedPatches?.filter((item) => item?.Arch === 'rhel.arm64'));
        }
      })
      .finally(() => {
        setUpDateLoading(false);
      });
  };
  // 创建patch
  const handleCreate = () => {
    formRef.current?.validateFields(['SolutionVersionUUID', 'SolutionUUID']).then(() => {
      window.open(`/page/defect_manage/__flow-design/patch_manage`, '_blank');
    });
  };
  // 刷新
  const handleUpdate = () => {
    fetchPatches();
  };

  return (
    <TcsModal
      title="新建多架构Patch"
      visible
      onCancel={onCancel}
      onOk={handleConfirm}
      confirmLoading={loading}
      width={780}
    >
      <TcsForm formRef={formRef}>
        <TcsForm.Item
          label="解决方案"
          name="SolutionUUID"
          rules={[
            {
              message: '请选择解决方案',
              required: true,
            },
          ]}
        >
          <Select
            clearable
            placeholder="请选择解决方案"
            searchable
            size="full"
            appearance="button"
            matchButtonWidth
            options={solutionList.map((item: any) => ({
              text: `${item.Name}(${item.NameEN || ''})`,
              value: item.UUID,
            }))}
            onChange={(value) => {
              if (value) {
                getSolutionVersions(value);
              }
              formRef?.current?.setFieldsValue({
                SolutionVersionUUID: '',
                X86PatchID: '',
                ARMPatchID: '',
              });
            }}
          />
        </TcsForm.Item>
        <TcsForm.Item
          label="解决方案版本"
          name="SolutionVersionUUID"
          rules={[
            {
              message: '请选择解决方案版本',
              required: true,
            },
          ]}
        >
          <Select
            clearable
            placeholder="请选择解决方案版本"
            size="full"
            searchable
            appearance="button"
            matchButtonWidth
            options={solutionVersionList.map((item: any) => ({
              text: item.Code,
              value: item.UUID,
            }))}
            onChange={(value) => {
              if (value) {
                setSolutionVersionUUID(value);
              }
              formRef?.current?.setFieldsValue({
                X86PatchID: '',
                ARMPatchID: '',
              });
            }}
          />
        </TcsForm.Item>
        <TcsFormText
          label="多架构Patch名称"
          name="Name"
          placeholder="请输入多架构Patch名称"
          rules={[
            {
              message: '请输入多架构Patch名称',
              required: true,
            },
          ]}
          fieldProps={{ size: 'full' }}
        />
        <TcsFormSelect
          width={450}
          label="x86架构Patch"
          name="X86PatchID"
          placeholder="请选择x86架构Patch"
          fieldProps={{
            options: x86PatchList?.map((item: any) => ({
              label: item.PatchName,
              value: item.PatchID,
            })),
            showSearch: true,
          }}
          formItemProps={{
            suffix: (
              <>
                <TcsSpace>
                  <TcsButton onClick={handleCreate}>去创建</TcsButton>
                  <TcsButton onClick={handleUpdate} loading={upDateLoading}>
                    刷新
                  </TcsButton>
                </TcsSpace>
              </>
            ),
          }}
        />
        <TcsFormSelect
          width={450}
          searchable
          placeholder="请选择arm架构Patch"
          label="arm架构Patch"
          name="ARMPatchID"
          fieldProps={{
            options: armPatchList?.map((item: any) => ({
              label: item.PatchName,
              value: item.PatchID,
            })),
            showSearch: true,
          }}
          formItemProps={{
            suffix: (
              <>
                <TcsSpace>
                  <TcsButton onClick={handleCreate}>去创建</TcsButton>
                  <TcsButton onClick={handleUpdate} loading={upDateLoading}>
                    刷新
                  </TcsButton>
                </TcsSpace>
              </>
            ),
          }}
        />
      </TcsForm>
    </TcsModal>
  );
};
export default AddMultiArchModal;
