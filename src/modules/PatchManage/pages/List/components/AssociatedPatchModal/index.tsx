/*
 * @Author: lucyfang
 * @Date: 2024-12-30 14:37:30
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-01-16 11:15:28
 * @Description: 请输入注释信息
 */
import React, { useState, useRef, useEffect } from 'react';
import { message } from '@tencent/tea-component';
import { TcsForm, TcsModal, TcsFormSelect, TcsSpace, TcsButton, TcsSpin } from '@tencent/tcs-component';
import { PatchManageApi } from '@/common/api/patchManage';
import { AssociatedArch } from '../../../../constant';
interface IProps {
  onCancel: () => void;
  onSuccess: () => void;
  archList: string[];
  record: any;
}

const AssociatedPatchModal: React.FC<IProps> = ({ onCancel, onSuccess, archList, record }) => {
  const formRef = useRef<any>();
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [x86PatchList, setX86PatchList] = useState<any[]>([]);
  const [armPatchList, setArmPatchList] = useState<any[]>([]);

  const handleConfirm = () => {
    formRef?.current?.validateFields().then((res) => {
      const { ARMPatchID, X86PatchID } = res;
      if (!ARMPatchID && !X86PatchID) {
        return message.warning({ content: '请选择Patch' });
      }
      setConfirmLoading(true);
      PatchManageApi.AddAssociatedPatch({
        ARMPatchID: ARMPatchID,
        X86PatchID: X86PatchID,
        PatchMultiArchUUID: record.PatchMultiArchUUID,
      })
        .then(() => {
          onSuccess();
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };
  const fetchPatches = async () => {
    setLoading(true);
    try {
      let filterArchs = [...AssociatedArch];

      if (archList.length > 0) {
        filterArchs = filterArchs.filter((arch) => !archList.includes(arch));
      }
      const allPatches = await PatchManageApi.ListNeedAssociatedPatch({
        SolutionUUID: record.SolutionUUID,
        SolutionVersionUUID: record.SolutionVersionUUID,
        Arches: filterArchs,
      });
      setX86PatchList(allPatches?.AssociatedPatches?.filter((item) => item?.Arch === 'rhel.amd64'));
      setArmPatchList(allPatches?.AssociatedPatches?.filter((item) => item?.Arch === 'rhel.arm64'));
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchPatches();
  }, [archList]);

  // 创建patch
  const handleCreate = () => {
    formRef.current?.validateFields().then(() => {
      window.open(`/page/defect_manage/__flow-design/patch_manage`, '_blank');
    });
  };
  // 刷新
  const handleUpdate = () => {
    fetchPatches();
  };

  return (
    <TcsModal
      title="关联Patch"
      visible
      onCancel={onCancel}
      onOk={handleConfirm}
      width={750}
      confirmLoading={confirmLoading}
    >
      <TcsSpin spinning={loading}>
        <TcsForm formRef={formRef}>
          {!(archList?.length && archList?.[0] === 'rhel.amd64') && (
            <TcsFormSelect
              placeholder="请选择x86架构Patch"
              label="x86架构Patch"
              name="X86PatchID"
              fieldProps={{
                size: 'l',
                options: x86PatchList?.map((item: any) => ({
                  label: item.PatchName,
                  value: item.PatchID,
                })),
                showSearch: true,
              }}
              formItemProps={{
                suffix: (
                  <>
                    <TcsSpace>
                      <TcsButton onClick={handleCreate}>去创建</TcsButton>
                      <TcsButton onClick={handleUpdate} loading={loading}>
                        刷新
                      </TcsButton>
                    </TcsSpace>
                  </>
                ),
              }}
            />
          )}
          {!(archList?.length && archList?.[0] === 'rhel.arm64') && (
            <TcsFormSelect
              placeholder="请选择arm架构Patch"
              label="arm架构Patch"
              name="ARMPatchID"
              fieldProps={{
                size: 'l',
                options: armPatchList?.map((item: any) => ({
                  label: item.PatchName,
                  value: item.PatchID,
                })),
                showSearch: true,
              }}
              formItemProps={{
                suffix: (
                  <>
                    <TcsSpace>
                      <TcsButton onClick={handleCreate}>去创建</TcsButton>
                      <TcsButton onClick={handleUpdate} loading={loading}>
                        刷新
                      </TcsButton>
                    </TcsSpace>
                  </>
                ),
              }}
            />
          )}
        </TcsForm>
      </TcsSpin>
    </TcsModal>
  );
};
export default AssociatedPatchModal;
