import React, { useState, useEffect, useRef } from 'react';
import { message } from '@tencent/tea-component';
import { publishPatch } from '@/common/api/patchManage';
import SiteManage from '../QueueDetail/components/SiteManage';
import { TcsSpin, TcsModal, TcsForm, TcsFormText, TcsCard, TcsFormSwitch } from '@tencent/tcs-component';
import { IterationManage } from '@/common/api/iterationManage.api';

interface Site {
  ClientUUID: string;
  ClientName: string;
  ProjectSite: {
    SiteUUID: string;
    SiteType: string;
    SiteName: string;
    IsAdded: boolean;
  }[];
}

const PublishModal = ({
  patchId,
  onClose,
  onSuccess,
  detail,
}: {
  patchId: string;
  onClose: () => void;
  onSuccess: () => void;
  detail: any;
}) => {
  const [loading, setLoading] = useState(false);
  const [infoLoading, setInfoLoaing] = useState(false);
  const [siteList, setSiteList] = useState<Site[]>([]);
  const formRef = useRef<any>();
  useEffect(() => {
    setInfoLoaing(true);
    if (detail?.SolutionVersionUUID) {
      IterationManage.ListProjectClientSite({
        SolutionVersionID: String(detail.SolutionVersionUUID),
        Arch: detail.Arch,
        Applications: detail.Applications?.map((item) => item?.Name || item?.ApplicationName),
        IsFullSites: true,
        PatchID: patchId,
        ShowPatchSites: false,
      })
        .then((res) => {
          const updatedData = res.ClientInfos.map((client) => ({
            ...client,
            PatchQueueSite: client.ProjectSite.map((site) => ({
              ...site,
              IsAdded: true,
            })),
          }));
          setSiteList(updatedData as any);
        })
        .finally(() => setInfoLoaing(false));
    }
  }, [detail.Arch, detail.UUID, detail.DeliverySiteRel, detail.Applications, patchId, detail.SolutionVersionUUID]);

  const handlePublish = () => {
    const selectSiteUUIDS = siteList.reduce<string[]>((acc, client) => {
      const clientUUIDS = (client as any)?.PatchQueueSite?.filter((site) => site?.IsAdded).map((site) => site.SiteUUID);
      return acc.concat(clientUUIDS);
    }, []);
    formRef.current?.validateFields().then((res) => {
      if (selectSiteUUIDS.length) {
        setLoading(true);
        publishPatch({
          PatchID: patchId,
          Name: res.Name,
          NeedSheet: true,
          SiteUUIDList: selectSiteUUIDS,
          NeedCreateTag: detail?.PatchType === 'CumulativePatch' ? res?.NeedCreateTag : true,
        })
          .then((res) => {
            if (res.Error) {
              message.error({
                content: res.Error.Message,
              });
            } else {
              message.success({ content: '发布成功' });
              onSuccess();
            }
          })
          .finally(() => {
            setLoading(false);
            onClose();
          });
      } else {
        message.error({
          content: '至少选择一个局点',
        });
      }
    });
  };

  function handleSiteListChange(newSiteList: Site[]) {
    setSiteList(newSiteList);
  }
  return (
    <TcsModal visible title="发布Patch" onCancel={onClose} width={950} onOk={handlePublish} confirmLoading={loading}>
      <TcsCard>
        <TcsForm formRef={formRef} style={{ width: 500, marginBottom: 10 }}>
          <TcsFormText
            label="Patch名称"
            name="Name"
            rules={[
              {
                required: true,
                message: '请输入Patch名称',
              },
            ]}
            fieldProps={{
              size: 'full',
            }}
            initialValue={detail?.Name}
          />
          {detail?.PatchType === 'CumulativePatch' && (
            <TcsFormSwitch
              name="NeedCreateTag"
              label="是否打制品Tag"
              checkedChildren=""
              unCheckedChildren=""
              initialValue={false}
            />
          )}
        </TcsForm>
      </TcsCard>
      <TcsCard>
        <TcsSpin spinning={infoLoading}>
          <SiteManage siteList={siteList as any} onSiteListChange={handleSiteListChange as any} />
        </TcsSpin>
      </TcsCard>
    </TcsModal>
  );
};

export default PublishModal;
