import React, { useEffect, useRef, useState, useCallback } from 'react';
import {
  TcsForm,
  TcsLayout,
  TcsModal,
  TcsFormText,
  TcsFormSelect,
  TcsSpace,
  TcsFormTextArea,
  TcsButton,
  TcsFormStaffSelect,
} from '@tencent/tcs-component';
import { message } from '@tencent/tea-component';
import { ISolution, ISolutionVersion, listSolution, listSolutionVersion } from '@/common/api/common';
import { listSolutionVersionArtifactBranches } from '@/common/api/patchManage';
import { toListParamsCApi } from '@/common/api/api';
import useLookup from '@/common/hookups/useLookup';
import { DefectManagementRoutePath } from '@/common/routePath';

import { QueueManageApi } from '@/common/api/periodicQueueManage';
import NameRuleForm from '../../../QueueDetail/components/NameRuleForm';

interface IProps {
  onClose: () => void;
  onSuccess: () => void;
  patchQueueUUID?: string;
  editData?: {
    Name: string;
    SolutionUUID: string;
    SolutionVersionUUID: string;
    Arch: string;
    ArtifactBranchUUID?: string;
    Description?: string;
  };
}
interface SolutionVersion {
  solutionVersionCode: string;
  solutionVersionUUID: string;
}
const CreatQueue: React.FC<IProps> = (props) => {
  const { history } = TcsLayout.useHistory();
  const { onClose, onSuccess, patchQueueUUID, editData } = props;
  const isEditMode = !!patchQueueUUID; // 根据是否传入 patchQueueUUID 判断是否为编辑模式
  const formRef = useRef<any>();
  const [solutionList, setSolutionList] = useState<ISolution[]>([]);
  const [solutionVersionList, setSolutionVersionList] = useState<ISolutionVersion[]>([]);
  const [artifactBranches, setArtifactBranches] = useState([]);
  const [solutionVersion, setSolutionVersion] = useState<SolutionVersion>({
    solutionVersionCode: '',
    solutionVersionUUID: '',
  });
  const [loading, setLoading] = useState(false);
  const { lookups } = useLookup(['PackageArch']);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const currentJiguangName = window.jiguang_username;

  useEffect(() => {
    listSolution().then((res) => {
      if (res.Error) {
        return message.error({
          content: res.Error.Message,
        });
      }
      setSolutionList(res.ListSolutions || []);
    });
    formRef.current?.setFieldsValue({ Owner: currentJiguangName?.split(';') });
  }, []);
  // 获取制品分支列表
  const getArtifactBranches = useCallback(
    (value, showLoading = false) => {
      if (showLoading) {
        setLoading(true);
        if (loading) return;
      }
      setArtifactBranches([]);
      listSolutionVersionArtifactBranches({
        SolutionVersionArtifactBranch: {
          _Filter: toListParamsCApi(
            { SolutionVersionID: value, Label: 'patch', BranchType: 'release' },
            {
              useEqFields: ['SolutionVersionID', 'BranchType'],
            },
          )._Filter,
          _Order: [{ Key: 'ID', Sort: 'ASC' }],
        },
      }).then((res) => {
        if (res.Error) {
          return message.error({
            content: res.Error.Message,
          });
        }
        setArtifactBranches(res.ListSolutionVersionArtifactBranches || []);
        if (showLoading) {
          setLoading(false);
        }
      });
    },
    [loading],
  ); // 依赖项数组，如果有其他依赖项也需要包含在数组中

  useEffect(() => {
    if (isEditMode && editData) {
      // 如果是编辑模式且传入了编辑数据，则设置表单的初始值
      formRef.current?.setFieldsValue(editData);
      // 仅在编辑模式下获取关联制品分支列表
      if (editData.SolutionVersionUUID) {
        getArtifactBranches(editData.SolutionVersionUUID);
      }
    }
  }, [editData, isEditMode, getArtifactBranches]);

  // 获取解决方案版本列表
  const getSolutionVersions = (value) => {
    setSolutionVersionList([]);
    listSolutionVersion({ SolutionUUID: value }).then((res) => {
      if (res.Error) {
        return message.error({
          content: res.Error.Message,
        });
      }
      setSolutionVersionList(res.ListSolutionVersions || []);
    });
  };

  // 创建制品分支
  function handleCreate() {
    formRef.current?.validateFields(['Name', 'SolutionUUID', 'Arch', 'SolutionVersionUUID']).then(() => {
      const tenantId = window.jiguang_currentNs;
      const url = `/page/product_center/workspace/solution/tag_list?pageType=2&solutionVersionCode=${solutionVersion?.solutionVersionCode}&solutionVersionUUID=${solutionVersion?.solutionVersionUUID}&tenantId=${tenantId}`;
      window.open(url, '_blank');
    });
  }

  // 刷新
  function handleUpdate() {
    formRef.current?.validateFields(['Arch', 'SolutionVersionUUID']).then(() => {
      getArtifactBranches(solutionVersion.solutionVersionUUID, true);
    });
  }
  // 确认新建队列
  function handleConfirm() {
    formRef.current?.validateFields().then((value) => {
      setConfirmLoading(true);
      const { Name, Arch, SolutionVersionUUID, ArtifactBranchUUID, Description, nameRule, Owner } = value;

      QueueManageApi.CreatePatchQueue({
        Name,
        Arch,
        SolutionVersionUUID,
        ArtifactBranchUUID,
        Description,
        NamingRule: nameRule || '',
        Owners: Owner?.join(';'),
      })
        .then((res) => {
          const { Error, PatchQueueUUID } = res;
          if (Error) {
            message.error({
              content: Error.Message,
            });
          } else {
            history.push(`${DefectManagementRoutePath.PATCH_QUEUE_DEYTAIL_PAGE}?patchQueueUUID=${PatchQueueUUID}`);
            onSuccess();
          }
        })
        .finally(() => {
          setConfirmLoading(false);
          onClose();
        });
    });
  }
  return (
    <TcsModal
      visible
      title="新建队列"
      onCancel={onClose}
      onOk={handleConfirm}
      width={650}
      confirmLoading={confirmLoading}
    >
      <TcsForm formRef={formRef} initialValues={isEditMode ? editData : {}}>
        <TcsFormText
          label="队列名称"
          name="Name"
          rules={[
            {
              required: true,
              message: '请输入队列名称',
            },
          ]}
          disabled={isEditMode}
          fieldProps={{
            size: 'full',
          }}
        />
        <TcsFormSelect
          name="SolutionUUID"
          label="解决方案"
          rules={[
            {
              message: '请选择解决方案',
              required: true,
            },
          ]}
          disabled={isEditMode}
          fieldProps={{
            size: 'full',
            options: solutionList.map((item) => ({
              label: `${item.Name}(${item.NameEN || ''})`,
              value: item.UUID,
            })),
            onChange: (value) => {
              if (value) {
                getSolutionVersions(value);
              }
              formRef.current?.resetFields(['SolutionVersionUUID', 'Arch', 'ArtifactBranchUUID']);
            },
            showSearch: true,
          }}
        />
        <TcsFormSelect
          name="SolutionVersionUUID"
          label="解决方案版本"
          rules={[
            {
              message: '请选择解决方案版本',
              required: true,
            },
          ]}
          disabled={isEditMode}
          fieldProps={{
            size: 'full',
            options: solutionVersionList.map((item: any) => ({
              value: item.UUID,
              label: item.Code,
            })),
            onChange: (value: string, context: any) => {
              if (value) {
                getArtifactBranches(value);
                setSolutionVersion({
                  solutionVersionCode: context,
                  solutionVersionUUID: value,
                });
              }
              formRef.current?.resetFields(['Arch', 'ArtifactBranchUUID']);
            },
          }}
        />

        <TcsFormSelect
          name="Arch"
          label="架构"
          rules={[
            {
              message: '请选择架构',
              required: true,
            },
          ]}
          disabled={isEditMode}
          fieldProps={{
            size: 'full',
            options:
              lookups?.PackageArch.map((item: any) => ({
                label: item.Name,
                value: item.Code,
              })) || [],
          }}
        />

        <TcsFormSelect
          name="ArtifactBranchUUID"
          label="关联制品分支"
          rules={[
            {
              message: '请选择',
              required: true,
            },
          ]}
          tooltip="制品分支数据来源于已添加标签【patch】的解决方案制品分支"
          fieldProps={{
            size: 'full',
            options: artifactBranches?.map((item: any) => ({
              label: item?.BranchName,
              value: item?.UUID,
              extra: item?.DataBranch,
            })),
          }}
          formItemProps={{
            suffix: (
              <>
                <TcsSpace>
                  <TcsButton onClick={handleCreate}>去创建</TcsButton>
                  <TcsButton onClick={handleUpdate} loading={loading}>
                    刷新
                  </TcsButton>
                </TcsSpace>
              </>
            ),
          }}
        />
        <NameRuleForm formRef={formRef} isCreatePatch={true} />
        <TcsFormStaffSelect
          label="Patch队列负责人"
          name="Owner"
          valueField="enName"
          fieldProps={{
            multiple: true,
          }}
          rules={[
            {
              required: true,
              message: '请选择Patch队列负责人',
            },
          ]}
        />

        <TcsFormTextArea
          label="描述"
          name="Description"
          fieldProps={{
            size: 'full',
          }}
          rules={[{}]}
        />
      </TcsForm>
    </TcsModal>
  );
};
export default CreatQueue;
