import React from 'react';
import { ProColumns, TcsButtonGroup } from '@tencent/tcs-component';
import { DefectManagementRoutePath } from '@/common/routePath';
import { SolutionVersionSelect } from '@/common/components';

export function getTemplateManageColumns({ handleEdit }): ProColumns[] {
  return [
    {
      width: 150,
      dataIndex: 'Name',
      title: '队列名称',
      linkable: true,
      linkProps: {
        linkUrl: (text, record) =>
          `${DefectManagementRoutePath.PATCH_QUEUE_DEYTAIL_PAGE}?patchQueueUUID=${record.UUID}`,
      },
    },
    {
      width: 150,
      dataIndex: 'SolutionName',
      title: '解决方案名称',
      hideInSearch: true,
    },
    {
      title: '解决方案版本',
      dataIndex: 'SolutionVersionName',
      width: 150,
      renderFormItem: () => <SolutionVersionSelect />,
    },
    {
      width: 150,
      dataIndex: 'Arch',
      title: '架构',
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'PackageArch',
      },
    },
    {
      width: 150,
      dataIndex: 'SolutionVersionArtifactBranch.BranchName',
      title: '关联制品分支',
      hideInSearch: true,
      linkable: true,
      linkProps: {
        linkUrl: (text, record) => {
          const tenantId = window.jiguang_currentNs;
          return `/page/product_center/workspace/solution/tag_list?pageType=2&solutionVersionCode=${record?.SolutionVersion?.Code}&solutionVersionUUID=${record?.SolutionVersion?.UUID}&tenantId=${tenantId}`;
        },
        target: 'blank',
      },
    },
    {
      width: 150,
      dataIndex: 'SiteCount',
      title: '关联局点个数',
      hideInSearch: true,
      render: (value, record) => record.SiteCount || 0,
    },
    {
      width: 150,
      dataIndex: 'CreatedAt',
      title: '创建时间',
      hideInSearch: true,
      valueType: 'dateTime',
    },
    {
      width: 150,
      dataIndex: 'UpdatedAt',
      title: '最后更新时间',
      hideInSearch: true,
      valueType: 'dateTime',
    },
    {
      dataIndex: 'operation',
      title: '操作',
      width: 90,
      fixed: 'right',
      hideInSearch: true,
      render: (text, record) => (
        <TcsButtonGroup
          items={[
            {
              text: '编辑',
              onClick: () => handleEdit(record),
            },
          ]}
        />
      ),
    },
  ];
}
