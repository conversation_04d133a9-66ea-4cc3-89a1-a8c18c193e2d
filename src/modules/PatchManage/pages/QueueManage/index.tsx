import React, { useCallback, useRef, useState } from 'react';
import { TcsLayout, TcsTable, TcsActionType } from '@tencent/tcs-component';
import { getTemplateManageColumns } from './config';
import { Button } from '@tencent/tea-component';
import CreatQueue from './components/CreatQueue';
import { QueueManageApi } from '@/common/api/periodicQueueManage';
import { DefectManagementRoutePath } from '@/common/routePath';

const QueueManage = () => {
  const { history } = TcsLayout.useHistory();
  const actionRef = useRef<TcsActionType>();
  const [createQueueVisible, setCreateQueueVisible] = useState(false);

  // 获取队列数据
  function handleRequest(queryParams: any, options: any) {
    const { current: pageIndex, pageSize, SolutionVersionName, ...otherParams } = queryParams;
    return QueueManageApi.ListPatchQueues(
      {
        ...otherParams,
        PageNo: pageIndex,
        PageSize: pageSize,
        SolutionUUID: SolutionVersionName?.[0],
        SolutionVersionUUID: SolutionVersionName?.[1],
        Order: [{ Key: 'UpdatedAt', Sort: 'DESC' }],
      },
      options,
    );
  }
  const handleAdd = useCallback(() => {
    setCreateQueueVisible(true);
  }, []);
  const handleClose = useCallback(() => {
    setCreateQueueVisible(false);
  }, []);
  function handleEdit(record) {
    history.push(`${DefectManagementRoutePath.PATCH_QUEUE_DEYTAIL_PAGE}?patchQueueUUID=${record.UUID}`);
  }

  return (
    <>
      <TcsLayout title={'Patch队列管理'} history={history} customizeCard fullHeight>
        <TcsTable
          columns={getTemplateManageColumns({ handleEdit })}
          rowKey="ID"
          search={{
            labelWidth: 80,
          }}
          request={handleRequest}
          scroll={{ x: 1150 }}
          actionRef={actionRef}
          headerTitle={
            <Button key={1} type="primary" onClick={handleAdd}>
              新建
            </Button>
          }
          pagination={{}}
        />
        {createQueueVisible && <CreatQueue onClose={handleClose} onSuccess={() => actionRef.current?.reload()} />}
      </TcsLayout>
    </>
  );
};
export default QueueManage;
