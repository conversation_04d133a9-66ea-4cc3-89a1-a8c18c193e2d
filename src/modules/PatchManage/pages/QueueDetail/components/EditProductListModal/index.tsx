import React, { useState, useRef } from 'react';
import { TcsModal, TcsTable, TcsFormInstance, TcsActionType } from '@tencent/tcs-component';

import { QueueManageApi } from '@/common/api/periodicQueueManage';
import { getProductModalColumns } from './config';

export interface IProps {
  onClose: () => void;
  patchQueueUUID: string;
  siteUUID?: string;
  productInfo: { ProductCode: string; ProductUUID: string }[];
}

const EditProductModal: React.FC<IProps> = (props) => {
  const { onClose, patchQueueUUID, siteUUID } = props;
  const [localInfo, setLocalInfo] = useState(props.productInfo);
  const formRef = useRef<TcsFormInstance>();
  const actionRef = useRef<TcsActionType>();

  function handleRequest(queryParams: any, options: any) {
    const { current: pageIndex, pageSize, ...otherParams } = queryParams;
    return QueueManageApi.QueryAddableProduct(
      {
        PageNo: pageIndex,
        PageSize: pageSize,
        PatchQueueUUID: patchQueueUUID,
        ...otherParams,
      },
      options,
    ).then((res) => {
      const filterData = res.data.filter((item) => item?.IsAdded);
      if (localInfo?.length) {
        filterData.forEach((filteredItem) => {
          // 检查 UUID 是否存在于 ProductInfo 数组的 ProductUUID 中
          const isProductInProductInfo = localInfo.some((productInfo) => productInfo.ProductUUID === filteredItem.UUID);
          // 如果存在，则在相应的过滤后的产品数据中添加 isBlackList 字段并设置为 true
          if (isProductInProductInfo) {
            // eslint-disable-next-line no-param-reassign
            filteredItem.isBlackList = true;
          }
        });
      }
      res.data = filterData;
      return res;
    });
  }
  function handleBlackList(record) {
    // 防止重复添加
    if (!localInfo.some((product) => product.ProductUUID === record.UUID)) {
      const newLocalInfo = [...localInfo, { ProductCode: record.Code, ProductUUID: record.UUID }];
      setLocalInfo(newLocalInfo);
      handleConfirm(newLocalInfo);
    }
  }
  function handleCancelBlackList(record) {
    // 移除指定的产品
    const newLocalInfo = localInfo.filter((product) => product.ProductUUID !== record.UUID);
    setLocalInfo(newLocalInfo);
    handleConfirm(newLocalInfo);
  }

  function handleConfirm(newInfo) {
    QueueManageApi.UpdatePatchQueueSiteInfo({
      ProductInfo: newInfo,
      PatchQueueUUID: patchQueueUUID,
      SiteUUID: siteUUID || '',
      DeletedAt: 0,
    }).then(() => {
      actionRef.current?.reload();
    });
  }
  return (
    <TcsModal title="关联产品列表" width={1200} visible onCancel={onClose} footer={<></>}>
      <TcsTable
        request={handleRequest}
        search={{}}
        rowKey="UUID"
        columns={getProductModalColumns({ handleBlackList, handleCancelBlackList })}
        formRef={formRef}
        actionRef={actionRef}
        pagination={{ defaultPageSize: 10, current: 1 }}
      />
    </TcsModal>
  );
};
export default EditProductModal;
