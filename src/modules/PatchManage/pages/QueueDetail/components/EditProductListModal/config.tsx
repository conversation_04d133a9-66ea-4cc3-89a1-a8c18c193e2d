import { ProColumns, TcsButtonGroup } from '@tencent/tcs-component';
import { Tag } from '@tencent/tea-component';
import React from 'react';

export function getProductModalColumns({ handleBlackList, handleCancelBlackList }): ProColumns[] {
  return [
    {
      width: 150,
      dataIndex: 'Name',
      title: '产品名称',
    },

    {
      width: 150,
      dataIndex: 'NameEN',
      title: '英文简称',
      hideInSearch: true,
    },
    {
      width: 150,
      dataIndex: 'isBlackList',
      title: '产品黑名单',
      hideInSearch: true,
      render: (text, record) => (record?.isBlackList ? <Tag theme="error">是</Tag> : <Tag theme="primary">否</Tag>),
    },
    {
      width: 150,
      dataIndex: 'Code',
      title: '产品Code',
    },
    {
      width: 150,
      dataIndex: 'Category',
      title: '产品分类',
      hideInSearch: true,
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'ProductCategory',
        showType: 'tag',
        mode: 'multiple',
        showSelectAll: true,
      },
    },
    {
      width: 150,
      dataIndex: 'Source',
      title: '产品来源',
      hideInSearch: true,
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'ProductSource',
        showType: 'tag',
        mode: 'multiple',
        showSelectAll: true,
      },
    },
    {
      dataIndex: 'operation',
      title: '操作',
      width: 150,
      fixed: 'right',
      hideInSearch: true,
      render: (text, record) => (
        <TcsButtonGroup
          items={[
            {
              hidden: record?.isBlackList,
              text: '添加黑名单',
              confirm: true,
              confirmProps: {
                title: '确认要添加产品黑名单？',
                onConfirm: () => handleBlackList(record),
              },
            },
            {
              hidden: !record?.isBlackList,
              text: '移除黑名单',
              confirm: true,
              confirmProps: {
                title: '确认要移除产品黑名单？',
                onConfirm: () => handleCancelBlackList(record),
              },
            },
          ]}
        />
      ),
    },
  ];
}
