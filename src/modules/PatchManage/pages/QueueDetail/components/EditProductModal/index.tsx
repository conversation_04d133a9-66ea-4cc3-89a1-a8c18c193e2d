import React, { useState, useRef } from 'react';
import { TcsModal, TcsTable, TcsFormInstance, TcsActionType } from '@tencent/tcs-component';

import { QueueManageApi } from '@/common/api/periodicQueueManage';
import { getProductModalColumns } from './config';

export interface IProps {
  onClose: () => void;
  onSuccess: () => void;
  patchQueueUUID: string;
  siteUUID?: string;
}

const EditProductModal: React.FC<IProps> = (props) => {
  const { onClose, patchQueueUUID, siteUUID, onSuccess } = props;
  const formRef = useRef<TcsFormInstance>();
  const actionRef = useRef<TcsActionType>();
  const [loading, setLoading] = useState(false);
  const [productList, setProductList] = useState<any[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<any[]>();

  function handleRequest(queryParams: any, options: any) {
    const { current: pageIndex, pageSize, ...otherParams } = queryParams;
    return QueueManageApi.QueryAddableProduct(
      {
        PageNo: pageIndex,
        PageSize: pageSize,
        PatchQueueUUID: patchQueueUUID,
        SiteUUID: siteUUID,
        ...otherParams,
      },
      options,
    ).then((res) => {
      setProductList(res.data);
      // 如果 selectedProducts 为空，那么初始化它
      if (!selectedProducts) {
        const initialSelectedProducts = res.data.filter((item) => item.IsAdded);
        setSelectedProducts(initialSelectedProducts);
      }
      return res;
    });
  }

  // 当任何复选框的选中状态发生变化时触发
  const handleChange = (selectedRowKeys: React.Key[], selectedRows: any[]) => {
    const newSelectedProducts = [
      // 保留之前选中但不在当前页的项
      ...selectedProducts.filter((product) => !productList.some((item) => item.UUID === product.UUID)),
      // 添加当前页新选中的项
      ...selectedRows,
    ];
    setSelectedProducts(newSelectedProducts);
  };

  // 确认更新
  function handleConfirm() {
    setLoading(true);
    const productInfo = selectedProducts!.map((item) => ({
      ProductUUID: item.UUID,
      ProductCode: item.Code,
    }));
    if (siteUUID) {
      QueueManageApi.UpdatePatchQueueSiteInfo({
        ProductInfo: productInfo,
        PatchQueueUUID: patchQueueUUID,
        SiteUUID: siteUUID,
        DeletedAt: 0,
      }).then(() => {
        onSuccess();
        setLoading(false);
      });
    } else {
      QueueManageApi.UpdatePatchQueue({
        ProductInfo: productInfo,
        UUID: patchQueueUUID,
      }).then(() => {
        onSuccess();
        setLoading(false);
      });
    }
  }

  return (
    <TcsModal title="关联产品列表" width={950} visible onCancel={onClose} onOk={handleConfirm} confirmLoading={loading}>
      <TcsTable
        request={handleRequest}
        search={{}}
        rowKey="UUID"
        rowSelection={{
          type: 'checkbox',
          selectedRowKeys: selectedProducts?.map((item) => item.UUID) || [],
          onChange: handleChange,
        }}
        tableAlertOptionRender={() => {}}
        columns={getProductModalColumns()}
        formRef={formRef}
        actionRef={actionRef}
        pagination={{ defaultPageSize: 10, current: 1 }}
      />
    </TcsModal>
  );
};
export default EditProductModal;
