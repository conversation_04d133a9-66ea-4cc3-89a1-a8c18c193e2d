import React from 'react';
import { ProColumns, TcsButtonGroup } from '@tencent/tcs-component';
import { Tag } from '@tencent/tea-component';

export function getSiteColumns({ handleEdit }): ProColumns[] {
  return [
    {
      width: '15%',
      dataIndex: 'SiteName',
      title: '局点名称',
      hideInSearch: true,
    },
    {
      width: '15%',
      dataIndex: 'ClientName',
      title: '客户名称',
      hideInSearch: true,
    },
    {
      width: '15%',
      dataIndex: 'Labels',
      title: '局点标签',
      hideInSearch: true,
      render: (text, record) => {
        if (record.Labels) {
          return <Tag>{text}</Tag>;
        }
        return '-';
      },
    },
    {
      width: '10%',
      dataIndex: 'EnableAutoPlan',
      title: '规划更新方式',
      hideInSearch: true,
      render: (text, record) => {
        if (record?.EnableAutoPlan === 1 && record?.AutoPlanMode === 'Part') {
          return '部分更新';
        }
        if (record?.EnableAutoPlan === 1 && record?.AutoPlanMode === 'Full') {
          return '全量更新';
        }
        return '手动更新';
      },
    },
    {
      dataIndex: 'Creator',
      title: '创建人',
      width: '15%',
      search: false,
      valueType: 'staffSelect',
    },
    {
      dataIndex: 'Modifier',
      title: '最后编辑人',
      width: '15%',
      search: false,
      valueType: 'staffSelect',
    },
    {
      title: '创建时间',
      dataIndex: 'CreatedAt',
      width: '10%',
      search: false,
      valueType: 'dateTime',
    },
    {
      title: '最后更新时间',
      dataIndex: 'UpdatedAt',
      width: '10%',
      search: false,
      valueType: 'dateTime',
    },
    {
      dataIndex: 'operation',
      title: '操作',
      width: '20%',
      hideInSearch: true,
      render: (text, record) => (
        <TcsButtonGroup
          wrap={true}
          items={[
            {
              text: '查看关联产品',
              onClick: () => handleEdit(record),
            },
          ]}
        />
      ),
    },
  ];
}
