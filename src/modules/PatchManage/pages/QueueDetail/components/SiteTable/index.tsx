import React, { useRef, useState } from 'react';
import { getSiteColumns } from './config';
import { TcsTable, TcsActionType, TcsButtonGroup } from '@tencent/tcs-component';
import AddSiteModal from '../AddSiteModal';
import { QueueManageApi } from '@/common/api/periodicQueueManage';
import EditProductListModal from '../EditProductListModal';
import AndonSiteTable from '../AndonSiteTable';
import { message } from '@tencent/tea-component';

interface Iprops {
  patchQueueUUID: string;
  updateAuthorized?: boolean;
  isAfter: boolean;
}
const SiteTable: React.FC<Iprops> = (props) => {
  const { patchQueueUUID, updateAuthorized, isAfter } = props;
  const actionRef = useRef<TcsActionType>();
  const [visible, setVisible] = useState(false);
  const [editVisible, setEditVisible] = useState(false);
  const [siteUUID, setSiteUUID] = useState('');
  const [productInfo, setProductInfo] = useState([]);
  const [delectSite, setDelectSite] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [confirmLoading, setConfirmLoading] = useState(false);

  // 确定批量删除
  function handleDeleatAll() {
    setConfirmLoading(true);
    QueueManageApi.PatchQueueSiteOperation({
      PatchQueueUUID: patchQueueUUID,
      Operation: 'Remove',
      SiteUUIDList: selectedRowKeys,
    })
      .then(() => {
        setDelectSite(false);
        actionRef.current?.reload(true);
        setSelectedRowKeys([]); // 重置 selectedRowKeys
      })
      .catch((error) => {
        message.error({
          content: error.message,
        });
      })
      .finally(() => {
        setConfirmLoading(false);
      });
  }
  // 新建局点
  function handleAdd() {
    setVisible(true);
  }
  // 取消添加局点
  function handleClose() {
    setVisible(false);
  }
  // 确认添加局点
  function handleConfirm() {
    actionRef.current?.reload(true);
    setVisible(false);
  }
  // 取消黑名单编辑
  function handleCloseEdit() {
    setEditVisible(false);
    actionRef.current?.reload(true);
  }

  // 编辑黑名单
  function handleEdit(record) {
    setSiteUUID(record?.SiteUUID || '');
    setEditVisible(true);
    setProductInfo(record?.ProductInfo || []);
  }

  function handleRequest(queryParams: any, options: any) {
    const { current: pageIndex, pageSize } = queryParams;
    return QueueManageApi.GetPatchQueueSiteList(
      {
        PageNo: pageIndex,
        PageSize: pageSize,
        PatchQueueUUID: patchQueueUUID,
      },
      options,
    );
  }

  return (
    <>
      <TcsTable
        columns={getSiteColumns({ handleEdit })}
        rowKey="SiteUUID"
        scroll={{ x: 1150 }}
        actionRef={actionRef}
        request={handleRequest}
        headerTitle={
          <>
            <TcsButtonGroup
              type="button"
              items={[
                {
                  text: '新增关联局点',
                  onClick: () => handleAdd(),
                  hidden: delectSite,
                  type: 'primary',
                  disabled: isAfter && !updateAuthorized,
                  tooltip: isAfter && !updateAuthorized ? '负责人才能新增关联局点' : '',
                },
                {
                  text: '批量删除关联局点',
                  onClick: () => setDelectSite(true),
                  hidden: delectSite,
                  type: 'primary',
                  disabled: isAfter && !updateAuthorized,
                  tooltip: isAfter && !updateAuthorized ? '负责人才能批量删除关联局点' : '',
                },
                {
                  text: '确定',
                  confirm: true,
                  confirmProps: {
                    title: '确定要批量删除吗',
                    onConfirm: () => handleDeleatAll(),
                  },
                  hidden: !delectSite,
                  type: 'primary',
                  disabled: selectedRowKeys.length === 0,
                  tooltip: selectedRowKeys.length ? '' : '请选择局点',
                  loading: confirmLoading,
                },
                {
                  text: '取消',
                  onClick: () => setDelectSite(false),
                  hidden: !delectSite,
                  type: 'primary',
                },
              ]}
            />
          </>
        }
        pagination={{ defaultPageSize: 5 }}
        rowSelection={
          delectSite
            ? {
                type: 'checkbox',
                selectedRowKeys,
                onChange: (selectedRowKeys: string[]) => {
                  setSelectedRowKeys(selectedRowKeys);
                },
              }
            : undefined
        }
        expandable={{
          rowExpandable: (record) => !!record.AndonSiteInfos?.length,
          expandedRowRender(record) {
            return <AndonSiteTable record={record} />;
          },
        }}
      />
      {visible && <AddSiteModal onClose={handleClose} onSuccess={handleConfirm} patchQueueUUID={patchQueueUUID} />}
      {editVisible && (
        <EditProductListModal
          onClose={handleCloseEdit}
          patchQueueUUID={patchQueueUUID}
          siteUUID={siteUUID}
          productInfo={productInfo}
        />
      )}
    </>
  );
};
export default SiteTable;
