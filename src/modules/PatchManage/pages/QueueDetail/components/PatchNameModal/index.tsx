import React, { useRef } from 'react';
import { TcsModal, TcsForm } from '@tencent/tcs-component';
import NameRuleForm from '../NameRuleForm';
import { QueueManageApi } from '@/common/api/periodicQueueManage';

interface IProps {
  onClose: () => void;
  onSuccess: () => void;
  initialNameRule?: string;
  patchQueueUUID: string;
}

const PatchNameModal: React.FC<IProps> = (props) => {
  const { onClose, onSuccess, initialNameRule, patchQueueUUID } = props;
  const formRef = useRef<any>();

  function handleConfirm() {
    const nameRule = formRef.current?.getFieldsValue()?.nameRule;
    QueueManageApi.UpdatePatchQueue({
      UUID: patchQueueUUID,
      NamingRule: nameRule,
    }).then(() => {
      onSuccess();
    });
  }

  return (
    <>
      <TcsModal title="修改Patch命名规则" visible onCancel={onClose} onOk={handleConfirm}>
        <TcsForm formRef={formRef}>
          <NameRuleForm formRef={formRef} initialNameRule={initialNameRule} />
        </TcsForm>
      </TcsModal>
    </>
  );
};

export default PatchNameModal;
