import React, { useState, useEffect, useCallback } from 'react';
import { TcsForm, TcsFormText } from '@tencent/tcs-component';
import { Checkbox } from '@tencent/tea-component';

interface IProps {
  formRef: any;
  initialNameRule?: string;
  isCreatePatch?: boolean;
}

enum NameRule {
  SolutionCode = 'SolutionCode',
  SolutionVersionCode = 'SolutionVersionCode',
  MainProductCode = 'MainProductCode',
  PatchType = 'PatchType',
  IncrementalNum = 'IncrementalNum',
  UpdateItem = 'UpdateItem',
  Arch = 'Arch',
  Date = 'Date',
}

const NameRuleSelector: React.FC<IProps> = (props) => {
  const { formRef, initialNameRule, isCreatePatch } = props;
  const [nameRule, setNameRule] = useState<string[]>([]);
  const [template, setTemplate] = useState<string[]>([]);
  // 假数据
  const SolutionCode = 'TCE';
  const SolutionVersionCode = '3.10.11';
  const MainProductCode = 'Tcenter';
  const PatchType = 'Patch';
  const IncrementalNum = '1';
  const UpdateItem = 'SafeUpdates';
  const Arch = 'rhel.amd64';
  const Date = '202405';
  const defaultNameRuleAll =
    '{SolutionCode}{SolutionVersionCode}-{MainProductCode}-{PatchType}-{IncrementalNum}-{UpdateItem}-{Arch}-{Date}';

  // 设置初始的命名规则字符串
  useEffect(() => {
    const initialRuleString = initialNameRule || defaultNameRuleAll;
    const parsedTemplate = initialRuleString.split(/({.*?})/).filter(Boolean);
    const parsedNameRule = parsedTemplate
      .filter((item) => item.startsWith('{'))
      .map((item) => item.replace(/[{}]/g, ''));
    setTemplate(parsedTemplate);
    setNameRule(parsedNameRule);
    formRef.current?.setFieldsValue({ nameRule: initialRuleString });
  }, [formRef, initialNameRule]);

  const generateExampleName = useCallback(
    (ruleString: string) => {
      if (!ruleString) return;

      const replacements = {
        SolutionVersionCode,
        SolutionCode,
        MainProductCode,
        PatchType,
        IncrementalNum,
        UpdateItem,
        Arch,
        Date,
      };

      // 根据新的规则字符串生成示例名称
      const exampleName = ruleString
        .split(/({.*?})/)
        .filter(Boolean)
        .map((item) => {
          if (item.startsWith('{')) {
            const key = item.replace(/[{}]/g, '');
            return replacements[key] || '';
          }
          return item;
        })
        .join('');

      formRef.current?.setFieldsValue({ exampleName });
    },
    [SolutionVersionCode, SolutionCode, MainProductCode, PatchType, IncrementalNum, UpdateItem, Arch, Date],
  );

  useEffect(() => {
    if (formRef.current) {
      const nameRule = formRef.current.getFieldValue('nameRule');
      generateExampleName(nameRule);
    }
  }, [formRef, generateExampleName]);

  useEffect(() => {
    // 生成新的命名规则字符串
    const parsedTemplate = defaultNameRuleAll.split(/({.*?})/).filter(Boolean);
    const newRuleString = parsedTemplate
      .map((item) => {
        if (item.startsWith('{')) {
          const key = item.replace(/[{}]/g, '');
          return nameRule.includes(key) ? `{${key}}` : '';
        }
        return item;
      })
      .join('');

    // 移除多余的连接符
    const cleanedRuleString = newRuleString.replace(/-{2,}/g, '-').replace(/^-|-$/g, '');

    formRef.current?.setFieldsValue({ nameRule: cleanedRuleString });
    generateExampleName(cleanedRuleString); // 重新生成示例名称
  }, [nameRule, template, formRef, generateExampleName]);

  function handleSelect(value) {
    setNameRule(value);
  }

  return (
    <>
      <TcsForm.Item
        label="Patch单命名规则"
        name="nameRule"
        tooltip={
          isCreatePatch ? (
            <p>当您使用此Patch队列创建 Patch 单时，Patch 单名称将会使用指定的命名规则进行生成</p>
          ) : (
            <>
              <p style={{ marginBottom: 5 }}>
                解决方案：若解决方案版本中包含解决方案Code（如TCE的解决方案版本为TCE3.10.0），则无需选择解决方案,否则需要选择解决方案（如TCS的解决方案版本为*******）
              </p>
              <p style={{ marginBottom: 5 }}>主产品：Patch包会针对特定产品制作，此时可指定该产品为主产品。 </p>
              <p>更新项名称：用于标记本次Patch变更所解决的问题。</p>
            </>
          )
        }
      >
        <Checkbox.Group value={nameRule} onChange={(value) => handleSelect(value)}>
          <Checkbox
            name={NameRule.SolutionCode}
            value={nameRule.includes(NameRule.SolutionCode)}
            onChange={(checked) =>
              handleSelect(
                checked
                  ? [...nameRule, NameRule.SolutionCode]
                  : nameRule.filter((item) => item !== NameRule.SolutionCode),
              )
            }
          >
            解决方案
          </Checkbox>
          <Checkbox name={NameRule.SolutionVersionCode} value disabled>
            解决方案版本
          </Checkbox>
          <Checkbox
            name={NameRule.MainProductCode}
            value={nameRule.includes(NameRule.MainProductCode)}
            onChange={(checked) =>
              handleSelect(
                checked
                  ? [...nameRule, NameRule.MainProductCode]
                  : nameRule.filter((item) => item !== NameRule.MainProductCode),
              )
            }
          >
            主产品
          </Checkbox>
          <Checkbox name={NameRule.PatchType} value disabled>
            包类型
          </Checkbox>
          <Checkbox name={NameRule.IncrementalNum} value disabled>
            包递增号
          </Checkbox>
          <Checkbox
            name={NameRule.UpdateItem}
            value={nameRule.includes(NameRule.UpdateItem)}
            onChange={(checked) =>
              handleSelect(
                checked ? [...nameRule, NameRule.UpdateItem] : nameRule.filter((item) => item !== NameRule.UpdateItem),
              )
            }
          >
            更新项名称
          </Checkbox>
          <Checkbox name={NameRule.Arch} value disabled>
            系统架构
          </Checkbox>
          <Checkbox name={NameRule.Date} value disabled>
            日期
          </Checkbox>
        </Checkbox.Group>
      </TcsForm.Item>
      <TcsFormText label="Patch单名称示例" name="exampleName" readonly />
    </>
  );
};

export default NameRuleSelector;
