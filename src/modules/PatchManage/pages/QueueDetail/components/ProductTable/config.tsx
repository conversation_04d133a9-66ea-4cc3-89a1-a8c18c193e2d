import { ProColumns } from '@tencent/tcs-component';
export function getProductColumns(): ProColumns[] {
  return [
    {
      width: 150,
      dataIndex: 'Name',
      title: '产品名称',
      hideInSearch: true,
    },
    {
      width: 150,
      dataIndex: 'Code',
      valueType: 'dictSelect',
      title: '产品Code',
      hideInSearch: true,
    },
    {
      width: 150,
      dataIndex: 'Category',
      title: '产品分类',
      hideInSearch: true,
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'ProductCategory',
        showType: 'tag',
        mode: 'multiple',
        showSelectAll: true,
      },
    },
    {
      width: 150,
      dataIndex: 'Source',
      title: '产品来源',
      hideInSearch: true,
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'ProductSource',
        showType: 'tag',
        mode: 'multiple',
        showSelectAll: true,
      },
    },
  ];
}
