/*
 * @Author: lucyfang
 * @Date: 2024-08-05 10:21:06
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-11-12 15:23:04
 * @Description: 请输入注释信息
 */
import React, { useRef, useState } from 'react';
import { getProductColumns } from './config';
import { TcsTable, TcsActionType } from '@tencent/tcs-component';
import { Button } from '@tencent/tea-component';
import EditProductModal from '../EditProductModal';
import { QueueManageApi } from '@/common/api/periodicQueueManage';

interface Iprops {
  patchQueueUUID: string;
  updateAuthorized?: boolean;
  isAfter: boolean;
}
const ProductTable: React.FC<Iprops> = (props) => {
  const { patchQueueUUID, updateAuthorized, isAfter } = props;
  const actionRef = useRef<TcsActionType>();
  const [visible, setVisible] = useState(false);

  function handleRequest(queryParams: any, options: any) {
    const { current: pageIndex, pageSize } = queryParams;
    return QueueManageApi.QueryAddableProduct(
      {
        PageNo: pageIndex,
        PageSize: pageSize,
        PatchQueueUUID: patchQueueUUID,
      },
      options,
    ).then((res) => {
      const filterData = res.data.filter((item) => item?.IsAdded);
      res.data = filterData;
      return res;
    });
  }
  // 新建产品
  function handleAdd() {
    setVisible(true);
  }
  // 取消添加产品
  function handleClose() {
    setVisible(false);
  }
  // 确认添加产品
  function handleConfirm() {
    actionRef.current?.reload();
    setVisible(false);
  }

  return (
    <>
      <TcsTable
        columns={getProductColumns()}
        rowKey="ID"
        scroll={{ x: 1150 }}
        actionRef={actionRef}
        request={handleRequest}
        headerTitle={
          <Button
            key={1}
            type="primary"
            onClick={handleAdd}
            disabled={isAfter && !updateAuthorized}
            tooltip={isAfter && !updateAuthorized && '负责人才能修改产品列表'}
          >
            修改产品列表
          </Button>
        }
        pagination={{}}
      />
      {visible && <EditProductModal onClose={handleClose} onSuccess={handleConfirm} patchQueueUUID={patchQueueUUID} />}
    </>
  );
};
export default ProductTable;
