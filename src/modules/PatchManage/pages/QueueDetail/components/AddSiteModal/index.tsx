import React, { useEffect, useState } from 'react';
import { TcsModal, TcsSpin } from '@tencent/tcs-component';
import { Alert } from '@tencent/tea-component';
import { QueueManageApi } from '@/common/api/periodicQueueManage';
import SiteManage from '../SiteManage';
interface Site {
  ClientUUID: string;
  ClientName: string;
  PatchQueueSite: {
    SiteUUID: string;
    SiteName: string;
    IsAdded: boolean;
  }[];
}
interface IProps {
  onClose: () => void;
  onSuccess: () => void;
  patchQueueUUID: string;
}
const AddSite: React.FC<IProps> = (props) => {
  const [loading, setLoading] = useState(false);
  const [infoLoading, setInfoLoaing] = useState(false);
  const { onClose, onSuccess, patchQueueUUID } = props;
  const [siteList, setSiteList] = useState<Site[]>([]);

  useEffect(() => {
    setInfoLoaing(true);
    QueueManageApi.QueryAddableSite({
      PatchQueueUUID: patchQueueUUID,
    })
      .then((res) => {
        if (res?.SiteList.length) {
          setSiteList(res.SiteList);
        }
      })
      .finally(() => setInfoLoaing(false));
  }, [patchQueueUUID]);
  function handleSiteListChange(newSiteList: Site[]) {
    setSiteList(newSiteList);
  }
  // 确认新增局点
  function handleConfirm() {
    setLoading(true);
    const selectSiteUUIDS = siteList.reduce((acc, client) => {
      const clientUUIDS = client.PatchQueueSite.filter((site) => site?.IsAdded).map((site) => site.SiteUUID);
      return acc.concat(clientUUIDS);
    }, []);
    QueueManageApi.UpdatePatchQueueSiteRel({
      PatchQueueUUID: patchQueueUUID,
      SiteUUIDList: selectSiteUUIDS,
    })
      .then(() => {
        onSuccess();
      })
      .finally(() => setLoading(false));
  }
  return (
    <TcsModal title="关联局点列表" width={950} visible onCancel={onClose} onOk={handleConfirm} confirmLoading={loading}>
      <Alert type="warning">
        <p>可用局点状态：此处仅支持处于交付中和售后的局点</p>
      </Alert>
      <TcsSpin spinning={infoLoading}>
        <SiteManage siteList={siteList} onSiteListChange={handleSiteListChange} disableInitialSelected={true} />
      </TcsSpin>
    </TcsModal>
  );
};
export default AddSite;
