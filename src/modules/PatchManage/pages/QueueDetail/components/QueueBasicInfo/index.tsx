/*
 * @Author: lucyfang
 * @Date: 2024-08-05 10:21:06
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-11-12 17:08:00
 * @Description: 请输入注释信息
 */
import React, { useEffect, useState, useRef } from 'react';
import { TcsSchemaForm, TcsCard, TcsColumns, TcsButtonGroup } from '@tencent/tcs-component';
import PatchNameModal from '../PatchNameModal';
import { AuthApi } from '@tencent/tcsc-base';

interface IProps {
  queueDetail: any;
  loading: boolean;
  onReload: () => void;
  updateAuthorized?: boolean;
  userList?: any;
  getUserList: () => void;
  patchQueueUUID: string;
  changeLoading: (boolean) => void;
  isAfter: boolean;
  onUpdateAuthorized: () => void;
}

const QueueBasicInfo: React.FC<IProps> = (props) => {
  const {
    queueDetail,
    loading,
    onReload,
    getUserList,
    userList,
    updateAuthorized,
    patchQueueUUID,
    changeLoading,
    isAfter,
  } = props;
  const [isEdit, setIsEdit] = useState(false);
  const [visible, setVisible] = useState(false);
  const [columns, setColumns] = useState([]);
  const formRef = useRef<any>();

  // 存储原始表单值的状态
  const [originalValues, setOriginalValues] = useState({});
  // 存储当前表单值的状态
  const [formValues, setFormValues] = useState({});
  useEffect(() => {
    if (queueDetail) {
      const columns: TcsColumns[] = [
        {
          dataIndex: 'Name',
          title: '队列名称',
          fieldProps: {
            readonly: true,
          },
        },
        {
          dataIndex: 'SolutionName',
          title: '解决方案',
          fieldProps: {
            readonly: true,
          },
        },
        {
          dataIndex: 'SolutionVersion',
          title: '解决方案版本',
          fieldProps: {
            readonly: true,
          },
        },
        {
          dataIndex: 'Arch',
          title: '架构',
          valueType: 'dictSelect',
          fieldProps: {
            readonly: true,
            dictType: 'PackageArch',
          },
        },
        {
          dataIndex: 'BranchName',
          title: '关联制品信息构',
          fieldProps: {
            readonly: true,
          },
        },
        {
          dataIndex: 'Description',
          title: '描述',
          fieldProps: {
            readonly: true,
          },
        },
        {
          dataIndex: 'NamingRule',
          title: 'Patch单命名规则',
          fieldProps: {
            readonly: true,
          },
        },
        {
          dataIndex: 'Owner',
          title: 'Patch队列负责人',
          valueType: 'staffSelect',
          fieldProps: {
            readonly: true,
            multiple: true,
          },
          formItemProps: {
            rules: [
              {
                required: true,
                message: '请选择Patch队列负责人',
              },
            ],
          },
        },
      ];
      const defaultColumns = columns.map((item) => ({
        formItemProps: {
          colProps: {
            xs: 12,
            sm: 12,
            lg: 8,
            xxl: 6,
          },
        },
        ...item,
      }));
      const initialValues = {
        ...queueDetail,
        SolutionVersion: queueDetail?.SolutionVersion?.Code,
        BranchName: `${queueDetail?.SolutionVersionArtifactBranch?.BranchName}(${queueDetail?.SolutionVersionArtifactBranch?.BranchType})`,
        NamingRule: generateNamingRuleDisplay(queueDetail?.NamingRule),
        Owner: userList || [],
      };
      setColumns(defaultColumns);
      // 保留原始数据
      formRef.current?.setFieldsValue(initialValues);
      setOriginalValues(initialValues);
      setFormValues(initialValues);
    }
  }, [queueDetail, userList]);

  function handlePatchName() {
    setVisible(true);
  }
  function handleClose() {
    setVisible(false);
  }
  function handleSuccess() {
    setVisible(false);
    onReload();
    // 重新加载页面
  }
  function handleEdit() {
    setIsEdit(true);
  }
  function handleConfirm() {
    formRef?.current?.validateFields().then((values) => {
      changeLoading(true);
      const originUsers = userList;
      const newUsers = values?.Owner;
      const usersToAdd = newUsers.filter((user) => !originUsers.includes(user));
      const usersToRemove = originUsers.filter((user) => !newUsers.includes(user));
      const updateBatch = [];

      if (usersToAdd.length > 0) {
        updateBatch.push({
          UserSubAccountUin: usersToAdd,
          RoleName: 'PatchQueueOwner',
          AddScopes: [
            {
              Kind: 'Patch',
              Values: [patchQueueUUID],
            },
          ],
        });
      }

      if (usersToRemove.length > 0) {
        updateBatch.push({
          UserSubAccountUin: usersToRemove,
          RoleName: 'PatchQueueOwner',
          RemoveScopes: [
            {
              Kind: 'Patch',
              Values: [patchQueueUUID],
            },
          ],
        });
      }

      if (updateBatch.length > 0) {
        AuthApi.BatchUpdateUserRoleScope({
          Items: updateBatch,
        }).then(() => {
          getUserList();
          setIsEdit(false);
          changeLoading(false);
        });
      } else {
        setIsEdit(false);
        changeLoading(false);
      }
    });
  }
  function handleCancel() {
    // 恢复原始值
    setFormValues(originalValues);
    setIsEdit(false);
  }
  // 根据 NamingRule 生成展示字符串
  const generateNamingRuleDisplay = (namingRule) => {
    if (!namingRule) return;
    const replacements = {
      SolutionCode: '解决方案',
      SolutionVersionCode: '解决方案版本',
      MainProductCode: '主产品',
      PatchType: '包类型',
      IncrementalNum: '递增号',
      UpdateItem: '更新项',
      Arch: '架构',
      Date: '创建时间',
    };

    return namingRule.replace(/{(.*?)}/g, (_, key) => `{${replacements[key] || key}}`);
  };
  return (
    <>
      <TcsCard
        title="基本信息"
        loading={loading}
        extra={
          <TcsButtonGroup
            items={[
              {
                text: '修改Patch包命名规则',
                onClick: handlePatchName,
                disabled: isAfter && !updateAuthorized,
                tooltip: isAfter && !updateAuthorized ? '负责人才能修改Patch命名规则' : '',
              },
              {
                text: '编辑',
                onClick: handleEdit,
                hidden: isEdit,
                disabled: isAfter && !updateAuthorized,
                tooltip: isAfter && !updateAuthorized ? '负责人才能编辑' : '',
              },
              {
                text: '确定',
                confirm: true,
                hidden: !isEdit,
                confirmProps: {
                  title: '确认修改',
                  onConfirm: handleConfirm,
                },
              },
              { text: '取消', onClick: handleCancel, hidden: !isEdit },
            ]}
          />
        }
      >
        <TcsSchemaForm
          formRef={formRef}
          columns={columns}
          grid={true}
          initialValues={formValues}
          key={isEdit ? 'edit-mode' : 'view-mode'} // 强制重新渲染
          readonly={!isEdit}
        />
      </TcsCard>

      {visible && (
        <PatchNameModal
          onClose={handleClose}
          onSuccess={handleSuccess}
          initialNameRule={queueDetail?.NamingRule || ''}
          patchQueueUUID={queueDetail?.UUID || ''}
        />
      )}
    </>
  );
};
export default QueueBasicInfo;
