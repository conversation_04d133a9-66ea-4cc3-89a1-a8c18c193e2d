import React, { useEffect, useState, useMemo, useRef } from 'react';
import styles from './index.module.scss';
import { Checkbox, SearchBox, Button, Text } from '@tencent/tea-component';

interface Site {
  ClientUUID: string;
  ClientName: string;
  PatchQueueSite: {
    SiteUUID: string;
    SiteName: string;
    IsAdded: boolean;
  }[];
}
interface IProps {
  siteList: Site[];
  onSiteListChange: (newSiteList: Site[]) => void;
  disableInitialSelected?: boolean; // 判断组件是否开启禁止勾选
}

const SiteManage: React.FC<IProps> = (props) => {
  const [searchValue, setSearchValue] = useState<{ name?: string }>({});
  const { siteList: propSiteList, onSiteListChange, disableInitialSelected } = props;
  const [localSiteList, setLocalSiteList] = useState<Site[]>([]);
  const initialSelectedSites = useRef<{ [key: string]: boolean }>({});
  // 提取初始化选择局点
  const getInitialSelectedSites = (siteList) => {
    const initialSelected = {};
    siteList.forEach((client) => {
      client.PatchQueueSite.forEach((site) => {
        if (site.IsAdded) {
          initialSelected[site.SiteUUID] = true;
        }
      });
    });
    return initialSelected;
  };

  useEffect(() => {
    setLocalSiteList(propSiteList);

    // 记录初始化时选择的局点
    const initialSelected = getInitialSelectedSites(propSiteList);

    // 仅在组件首次挂载时执行，记录初始化时选择的局点
    if (disableInitialSelected && Object.keys(initialSelectedSites.current).length === 0) {
      initialSelectedSites.current = initialSelected;
    }
  }, [propSiteList]);

  // 当本地选择结果发生变化时，调用父组件的回调函数
  const updateSiteList = (newSiteList: Site[]) => {
    setLocalSiteList(newSiteList);
    onSiteListChange(newSiteList); // 将新的选择结果传递给父组件
  };

  // 选择客户
  function handleChangeGroup(client) {
    // 检查当前客户端的所有局点是否已经全选
    const isAllSelected = client.PatchQueueSite.every(
      (site) => site.IsAdded || (disableInitialSelected && initialSelectedSites.current[site.SiteUUID]),
    );

    // 根据当前是否全选来设置新的选中状态
    const newIsAddedValue = !isAllSelected;

    // 更新当前客户端的 PatchQueueSite 数组中的 IsAdded 属性
    const updatedPatchQueueSite = client.PatchQueueSite.map((site) => ({
      ...site,
      IsAdded: disableInitialSelected
        ? initialSelectedSites.current[site.SiteUUID] || newIsAddedValue
        : newIsAddedValue, // 保持初始化已选择的项，并设置新的选中状态
    }));
    // 更新 siteList 状态，只修改当前操作的客户端

    const newSiteList = localSiteList.map((item) =>
      item.ClientUUID === client.ClientUUID ? { ...item, PatchQueueSite: updatedPatchQueueSite } : item,
    );
    updateSiteList(newSiteList);
  }

  // 全选
  function handleSelectAll() {
    const newSiteList = localSiteList.map((client) => ({
      ...client,
      PatchQueueSite: client.PatchQueueSite.map((site) => ({
        ...site,
        IsAdded: true, // 设置所有局点的 IsAdded 为 true
      })),
    }));
    updateSiteList(newSiteList);
  }
  // 反选
  function handleInverseSelect() {
    const newSiteList = localSiteList.map((client) => ({
      ...client,
      PatchQueueSite: client.PatchQueueSite.map((site) => ({
        ...site,
        IsAdded: disableInitialSelected
          ? initialSelectedSites.current[site.SiteUUID]
            ? true
            : !site.IsAdded
          : !site.IsAdded, // 保持初始化已选择的项，并反转未初始化选择的项
      })),
    }));
    updateSiteList(newSiteList);
  }
  // 选择局点
  function handleSiteClick(clientUUID, siteUUID) {
    // 找到对应的客户端和局点
    const client = localSiteList.find((client) => client.ClientUUID === clientUUID);
    const site = client?.PatchQueueSite.find((site) => site.SiteUUID === siteUUID);
    // 如果找到了局点，切换其 IsAdded 状态
    if (site) {
      const newIsAddedValue = !site.IsAdded;
      handleSiteChange(clientUUID, siteUUID, newIsAddedValue);
    }
  }
  // 选择局点
  function handleSiteChange(clientUUID, siteUUID, checked) {
    const newSiteList = localSiteList.map((client) =>
      client.ClientUUID === clientUUID
        ? {
            ...client,
            PatchQueueSite: client.PatchQueueSite.map((site) =>
              site.SiteUUID === siteUUID ? { ...site, IsAdded: checked } : site,
            ),
          }
        : client,
    );
    // 使用 updateSiteList 更新选择结果，并通知父组件
    updateSiteList(newSiteList);
  }

  const filterSiteList = useMemo(() => {
    if (Object.keys(searchValue).length) {
      const params: { name?: string } = searchValue;
      return localSiteList.filter((item) => {
        if (item.ClientName.includes(params.name!)) {
          return true;
        }
        const includeSite = item.PatchQueueSite?.filter?.((site) => site.SiteName.includes(params.name!)) || [];
        return includeSite.length > 0;
      });
    }
    return localSiteList;
  }, [localSiteList, searchValue]);
  return (
    <div style={{ maxHeight: 550, overflow: 'auto' }}>
      <div style={{ marginBottom: 5, display: 'flex', justifyContent: 'space-between' }}>
        <div>
          <Button onClick={handleSelectAll} type="weak" style={{ marginRight: 5, marginBottom: 5 }}>
            全选
          </Button>
          <Button onClick={handleInverseSelect} type="weak" style={{ marginRight: 5, marginBottom: 5 }}>
            反选
          </Button>
        </div>
        <SearchBox
          size="l"
          onClear={() => setSearchValue({})}
          onChange={(value) =>
            setSearchValue({
              name: value,
            })
          }
          placeholder="可输入客户名称/局点名称进行搜索"
        />
      </div>
      {filterSiteList.map((client) => {
        // 计算是否有任何 PatchQueueSite 的 IsAdded 为 true
        const isAddedExists = client.PatchQueueSite.some((site) => site?.IsAdded);
        // 计算是否所有 PatchQueueSite 的 IsAdded 都为 true
        const isAllAdded = client.PatchQueueSite.every((site) => site?.IsAdded);
        // indeterminate 状态为 true 当且仅当至少有一个 IsAdded 为 true 且不是所有的都为 true
        const indeterminate = isAddedExists && !isAllAdded;
        // checked 状态为 true 当且仅当所有的 IsAdded 都为 true
        const checked = isAllAdded;
        return (
          <div key={client.ClientUUID} className={styles.add_rel_region__group}>
            <div className={styles.add_rel_region__group_title}>
              <Checkbox indeterminate={indeterminate} value={checked} onChange={() => handleChangeGroup(client)}>
                {client.ClientName}
              </Checkbox>
            </div>
            <Checkbox.Group value={client.PatchQueueSite.filter((site) => site?.IsAdded).map((site) => site.SiteUUID)}>
              {client.PatchQueueSite.map((site) => (
                <Checkbox
                  name={site.SiteUUID}
                  key={site.SiteUUID}
                  value={site?.IsAdded}
                  onClick={() => handleSiteClick(client.ClientUUID, site.SiteUUID)}
                  disabled={disableInitialSelected && initialSelectedSites.current[site.SiteUUID]} // 根据参数禁用初始化时已经选择的项
                >
                  {site.SiteName}
                  {site?.SiteArch?.split(';').length > 1 && <Text style={{ fontWeight: 600 }}>【一云多芯局点】</Text>}
                </Checkbox>
              ))}
            </Checkbox.Group>
          </div>
        );
      })}
    </div>
  );
};

export default SiteManage;
