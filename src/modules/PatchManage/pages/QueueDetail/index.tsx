/*
 * @Author: lucyfang
 * @Date: 2024-08-05 10:21:06
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-11-12 15:24:29
 * @Description: 请输入注释信息
 */
import React, { useCallback, useEffect, useState } from 'react';
import { TcsCard, TcsLayout, TabItem, TcsTabs } from '@tencent/tcs-component';
import { getUrlParams } from '@/common/utils';
import QueueBasicInfo from './components/QueueBasicInfo';
import SiteTable from './components/SiteTable';
import ProductTable from './components/ProductTable';
import { QueueManageApi } from '@/common/api/periodicQueueManage';
import { message } from '@tencent/tea-component';
import { useAuthority, AuthApi } from '@tencent/tcsc-base';
import { isAfterTargetDate } from '../../utils';

const QueueDetail = () => {
  const { history } = TcsLayout.useHistory();
  const urlParams = getUrlParams();
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any>(null);
  const [patchQueueUUID, setPatchQueueUUId] = useState('');
  const [userList, setUserList] = useState([]);
  const [isAfter, setIsAfter] = useState(false);

  const getDetail = useCallback(() => {
    setLoading(true);
    QueueManageApi.GetPatchQueueDetail({
      PatchQueueUUID: urlParams.patchQueueUUID,
    })
      .then((res) => {
        if (res?.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          setData(res);
          if (res?.CreatedAt) {
            const isAfterTemp = isAfterTargetDate(new Date(res.CreatedAt));
            setIsAfter(isAfterTemp); // 设置检查结果状态
          } else {
            setIsAfter(false);
          }
        }
      })
      .finally(() => {
        setLoading(false);
      });
    setPatchQueueUUId(urlParams?.patchQueueUUID);
  }, [urlParams?.patchQueueUUID]);
  const getUserList = useCallback(() => {
    AuthApi.ListResourceUserRoles({ Kind: 'Patch', Name: urlParams.patchQueueUUID }).then((res) => {
      if (res?.PatchQueueOwner?.length) setUserList(res.PatchQueueOwner);
    });
  }, [urlParams?.patchQueueUUID]);

  useEffect(() => {
    if (urlParams?.patchQueueUUID) {
      getDetail();
      getUserList();
    }
  }, [getDetail, urlParams?.patchQueueUUID]);

  function handleReload() {
    getDetail();
    getUserList();
  }
  const UpdateOwnerAction = [
    {
      Action: 'UpdatePatchQueue',
      Kind: 'Patch',
      Resources: [urlParams?.patchQueueUUID],
    },
  ];
  const { getAuthorized } = useAuthority([...UpdateOwnerAction]);
  const updateAuthorized = getAuthorized(UpdateOwnerAction);
  const items: TabItem[] = [
    {
      key: '1',
      label: '关联局点',
      children: <SiteTable isAfter={isAfter} patchQueueUUID={patchQueueUUID} updateAuthorized={updateAuthorized} />,
    },
    {
      key: '2',
      label: '关联产品',
      children: <ProductTable isAfter={isAfter} patchQueueUUID={patchQueueUUID} updateAuthorized={updateAuthorized} />,
    },
  ];

  return (
    <TcsLayout title="Patch队列详情" customizeCard history={history}>
      <QueueBasicInfo
        patchQueueUUID={patchQueueUUID}
        updateAuthorized={updateAuthorized}
        userList={userList}
        queueDetail={data}
        loading={loading}
        onReload={handleReload}
        getUserList={getUserList}
        changeLoading={(value) => {
          setLoading(value);
        }}
        isAfter={isAfter}
      />
      <TcsCard>
        <TcsTabs defaultActiveKey="1" items={items} />
      </TcsCard>
    </TcsLayout>
  );
};
export default QueueDetail;
