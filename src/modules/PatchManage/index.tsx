/*
 * @Author: super<PERSON>
 * @Date: 2023-03-17 15:25:49
 * @LastEditors: superfeng
 * @LastEditTime: 2023-04-10 16:59:46
 * @Description: 请输入注释信息
 */
import React from 'react';
import { Route } from 'react-router-dom';
import List from './pages/List';
import { DefectManagementRoutePath, withRouteBasename } from '@/common/routePath';
import Detail from './pages/Detail';
import QueueManage from './pages/QueueManage';
import QueueDetail from './pages/QueueDetail';
import './index.less';

const Index = () => {
  const routesConfig = [
    {
      path: withRouteBasename('/patch_manage'),
      component: List,
      exact: true,
    },
    {
      path: DefectManagementRoutePath.PATCH_DETAIL_PAGE,
      component: Detail,
      exact: true,
    },
    {
      path: DefectManagementRoutePath.PATCH_QUEUE_MANAGE_PAGE,
      component: QueueManage,
    },
    {
      path: DefectManagementRoutePath.PATCH_QUEUE_DEYTAIL_PAGE,
      component: QueueDetail,
    },
  ];

  // useEffect(() => {
  //   const script2 = document.createElement('script');
  //   window.qpilotAiParams = {
  //     qpilotId: '5341',
  //     admin: window.jiguang_username,
  //     theme: '',
  //     logo: '',
  //     model: '',
  //     openFAQ: true,
  //     extra: '',
  //   };
  //   script2.src = 'https://guild-1251316161.cos.ap-guangzhou.myqcloud.com/qpilot/qpilotQa.js';
  //   script2.id = 'qpilotQa';
  //   script2.async = true;
  //   document.body.appendChild(script2);
  //   return () => {
  //     delete window.qpilotAiParams;
  //     document.body.removeChild(script2);
  //     document.querySelector('#qaDialogContent')?.remove();
  //     document.querySelector('#qpilotQaLogo')?.remove();
  //   };
  // }, []);

  return (
    <>
      {routesConfig.map((item, index) => (
        <Route key={index} component={item.component} path={item.path} exact={item.exact} />
      ))}
    </>
  );
};
export default Index;
