/*
 * @Author: lucyfang
 * @Date: 2024-11-12 14:54:33
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-11-13 18:17:14
 * @Description: 请输入注释信息
 */
/**
 * 判断传入的时间是否在指定时间之后
 * @param inputDate 需要比较的时间
 * @param targetDate 目标日期和时间（默认为2024年11月13日22点北京时间）
 * @returns 如果传入的时间在目标时间之后，返回true；否则返回false
 */
export function isAfterTargetDate(inputDate: Date, targetDate: Date = new Date('2024-11-13T22:00:00+08:00')): boolean {
  return inputDate > targetDate;
}
