import React, { useEffect } from 'react';
import { Route, useLocation } from 'react-router-dom';
import { DefectManagementRoutePath, withRouteBasename } from '@/common/routePath';
import BoardList from './pages/Board';
import HistoryList from './pages/History/List';
import HistoryDetail from './pages/History/Detail';
import PatchExport from './pages/PatchExport';
import IssueBoardDetail from './pages/Board/IssueScene/Detail';
import SiteBoardDetail from './pages/Board/SiteScene/Detail';

const Index = () => {
  const routesConfig = [
    {
      path: withRouteBasename('/defect_history'),
      component: HistoryList,
      exact: true,
    },
    {
      path: withRouteBasename('/defect_board'),
      component: BoardList,
      exact: true,
    },
    {
      path: DefectManagementRoutePath.PACKAGE_HISTORY_DETAIL_PAGE,
      component: HistoryDetail,
      exact: true,
    },
    // 从出包历史跳转到批量出包页面
    {
      path: DefectManagementRoutePath.PATCH_EXPORT_FROM_HISTORY_PAGE,
      component: PatchExport,
      exact: true,
    },
    // 从出包看板跳转到批量出包页面
    {
      path: DefectManagementRoutePath.PATCH_EXPORT_FROM_BOARD_PAGE,
      component: PatchExport,
      exact: true,
    },
    {
      path: DefectManagementRoutePath.ISSUE_BOARD_DETAIL_PAGE,
      component: IssueBoardDetail,
      exact: true,
    },
    {
      path: DefectManagementRoutePath.SITE_BOARD_DETAIL_PAGE,
      component: SiteBoardDetail,
      exact: true,
    },
  ];

  const location = useLocation();

  useEffect(() => {
    if (!location.pathname.includes('defect_board')) {
      localStorage.removeItem('activeId');
    }
  }, [location]);

  return (
    <>
      {routesConfig.map((item, index) => (
        <Route key={index} component={item.component} path={item.path} exact={item.exact} />
      ))}
    </>
  );
};
export default Index;
