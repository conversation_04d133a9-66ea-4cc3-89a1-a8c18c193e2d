import { StatusTip, Tag } from '@tencent/tea-component';
import dayjs from 'dayjs';
import React from 'react';
import { ShowMore } from '../../Components/ShowMore';

export const getColumns = ({ getLookupByCode, onDetail }) => [
  {
    key: 'ID',
    header: '出包ID',
    width: 150,
  },
  {
    key: 'IssueID',
    header: '关联缺陷单号',
    render: (record) => {
      const issueList = record?.IssueList ?? [];
      return (
        <ShowMore
          records={issueList}
          maxLength={8}
          handleClick={(item) =>
            window.open(
              `/page/defect_manage/__develop_center_next/iteration_manage/defect/detail?issue_id=${item}`,
              '_blank',
            )
          }
        />
      );
    },
  },
  {
    key: 'Status',
    header: '出包状态',
    width: 150,
    render: (record) => {
      const { Status } = record;
      const lookup = getLookupByCode('PackageOutRecordStatus', Status);
      if (lookup) {
        return (
          <Tag style={{ color: lookup.Extra?.Color }}>
            {Status === 'Running' && <StatusTip status="loading" loadingText="" />}
            {lookup.Name}
          </Tag>
        );
      }
      return Status || '-';
    },
  },
  {
    key: 'SiteStatus',
    header: '出包局点状态',
    width: 350,
    render: (record) => {
      const packageSiteStatusCount = record?.PackageSiteStatusCount;
      const {
        PackingCount = 0,
        PackSuccessCount = 0,
        TotalCount = 0,
        ToPackCount = 0,
        PackFailedCount = 0,
      } = packageSiteStatusCount;
      return `总计(${TotalCount})/待出包(${ToPackCount})/出包中(${PackingCount})/出包成功(${PackSuccessCount})/出包失败(${PackFailedCount})`;
    },
  },
  {
    key: 'SolutionVersion',
    header: '解决方案版本/架构',
    render: (record) => {
      const solutionVersionList = Array.from(
        new Set(
          record?.SolutionVersionArchList?.map((item) => {
            const arch = item?.Arch ? getLookupByCode('PackageArch', item.Arch) : undefined;
            return `${item?.SolutionVersion}-${arch?.Name || '-'}`;
          }),
        ),
      );
      return <ShowMore records={solutionVersionList} maxLength={8} />;
    },
  },
  {
    key: 'Creator',
    header: '处理人',
    width: 150,
  },
  {
    key: 'StartTime',
    header: '出包起始时间',
    width: 150,
    render: (record) => {
      if (record.StartTime) {
        return dayjs(record.StartTime)
          .format('YYYY-MM-DD HH:mm:ss')
          .split(' ')
          .map((item) => <div key={item}>{item}</div>);
      }
    },
  },
  {
    key: 'operation',
    header: '操作',
    fixed: 'right',
    width: 150,
    render: (record: any) => <a onClick={() => onDetail(record)}>查看详情</a>,
  },
];
