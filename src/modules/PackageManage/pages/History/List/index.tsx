import React from 'react';
import { getColumns } from './config';
import { listPackageOutRecords } from '@/common/api/packageManage';
import { DefectManagementRoutePath } from '@/common/routePath';
import ListWrapper from '../../Components/ListWrapper';
import Search from '../../Components/Search';
import { TcsLayout } from '@tencent/tcs-component';

const HistoryList = () => {
  const { history } = TcsLayout.useHistory();
  const handleDetail = (record) => {
    history.push(`${DefectManagementRoutePath.PACKAGE_HISTORY_DETAIL_PAGE}?RecordID=${record?.RecordID}`);
  };

  return (
    <TcsLayout title="出包历史" fullHeight customizeCard history={history}>
      <ListWrapper
        listRecords={listPackageOutRecords}
        onDetail={handleDetail}
        getColumns={getColumns}
        handleFormatRecords={(res) => res?.Items}
        type="history"
        showExport
        minWidth={1600}
        usePolling
      >
        <Search
          searchKeys={{
            SolutionVersion: 'SolutionVersionUUID',
            Arch: 'Arch',
            IssueID: 'IssueID',
            Status: 'PackageOutRecordStatus',
            ID: 'ID',
            StartTime: 'StartTime',
          }}
        />
      </ListWrapper>
    </TcsLayout>
  );
};

export default HistoryList;
