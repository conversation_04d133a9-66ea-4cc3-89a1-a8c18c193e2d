import { Card, Col, Form, Row, TabPanel, Tabs } from '@tencent/tea-component';
import React, { useCallback, useState } from 'react';
import { getUrlParams } from '@/common/utils';
import { getPackageOutRecordDetailIssue, getPackageOutRecordDetailSite } from '@/common/api/packageManage';
import dayjs from 'dayjs';
import ListWrapper from '../../Components/ListWrapper';
import { getColumns as getIssueColumns } from '../../Board/IssueScene/List/config';
import { getColumns as getSiteColumns } from '../../Board/SiteScene/List/config';
import { DefectManagementRoutePath } from '@/common/routePath';
import useLookup from '@/common/hookups/useLookup';
import { TcsCard, TcsLayout } from '@tencent/tcs-component';

const tabs = [
  { id: 'issue', label: '缺陷单视角' },
  { id: 'site', label: '局点视角' },
];

const Detail = () => {
  const urlParams = getUrlParams();
  const { history } = TcsLayout.useHistory();
  const { RecordID } = urlParams;
  const [recordDetail, setRecordDetail] = useState<any>();
  const { getLookupByCode } = useLookup([]);

  const [urlState, setUrlState] = TcsLayout.useUrlState(() => urlParams);
  const initialActiveId = urlState?.activeId as string;
  const [activeId, setActiveId] = useState(initialActiveId || 'issue');

  const listIssueRecords = useCallback(
    () =>
      getPackageOutRecordDetailIssue({ RecordID }).then((res) => {
        if (res) {
          setRecordDetail(res);
        }
        return res;
      }),
    [RecordID],
  );
  const listSiteRecords = useCallback(
    () =>
      getPackageOutRecordDetailSite({ RecordID }).then((res) => {
        if (res) {
          setRecordDetail(res);
        }
        return res;
      }),
    [RecordID],
  );

  const handleIssueDetail = (records) => {
    history.push(
      `${DefectManagementRoutePath.ISSUE_BOARD_DETAIL_PAGE}?RecordID=${RecordID}&Scene=history&IssueID=${records?.IssueID}`,
    );
  };

  const handleSiteDetail = (records) => {
    history.push(
      `${DefectManagementRoutePath.SITE_BOARD_DETAIL_PAGE}?RecordID=${RecordID}&Scene=history&SiteUUID=${records?.SiteUUID}&SolutionVersionUUID=${records?.SolutionVersionUUID}&Arch=${records?.Arch}`,
    );
  };

  const lookup = getLookupByCode('PackageOutRecordStatus', recordDetail?.Status);

  return (
    <TcsLayout title="出包详情" customizeCard history={history}>
      <Card>
        <Card.Body title="基本信息">
          <Form layout="fixed" style={{ width: '100%' }}>
            <Row>
              <Col lg={8} md={24}>
                <Form.Item label="创建者">
                  <Form.Text>{recordDetail?.Creator}</Form.Text>
                </Form.Item>
              </Col>
              <Col lg={8} md={24}>
                <Form.Item label="开始时间">
                  <Form.Text>{dayjs(recordDetail?.StartTime).format('YYYY-MM-DD HH:mm:ss')}</Form.Text>
                </Form.Item>
              </Col>
              <Col lg={8} md={24}>
                <Form.Item label="结束时间">
                  <Form.Text>
                    {recordDetail?.EndTime ? dayjs(recordDetail?.EndTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
                  </Form.Text>
                </Form.Item>
              </Col>
              <Col lg={8} md={24}>
                <Form.Item label="出包状态">
                  <Form.Text>{lookup ? lookup.Name : recordDetail?.Status || '-'}</Form.Text>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Card.Body>
      </Card>

      <TcsCard>
        <Tabs
          tabs={tabs}
          style={{ marginTop: 10 }}
          activeId={activeId}
          onActive={({ id }) => {
            setUrlState({ activeId: id });
            setActiveId(id);
          }}
        >
          <TabPanel id="issue">
            <ListWrapper
              type="history"
              listRecords={listIssueRecords}
              getColumns={getIssueColumns}
              handleFormatRecords={(res) => res?.PackageIssueList}
              onDetail={handleIssueDetail}
              showRelated={false}
              minWidth={1200}
            />
          </TabPanel>
          <TabPanel id="site">
            <ListWrapper
              type="history"
              listRecords={listSiteRecords}
              getColumns={getSiteColumns}
              handleFormatRecords={(res) => res?.PackageSiteInfos}
              onDetail={handleSiteDetail}
              showRelated={false}
              minWidth={1200}
            />
          </TabPanel>
        </Tabs>
      </TcsCard>
    </TcsLayout>
  );
};

export default Detail;
