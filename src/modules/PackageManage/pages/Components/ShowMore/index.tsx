import { Button, DropdownBox, List, Popover } from '@tencent/tea-component';
import React from 'react';

export interface IShowMoreProps {
  records: string[];
  maxLength: number;
  handleClick?: (item: string) => void;
}

export const ShowMore = (IShowMoreProps) => {
  const { records, maxLength, handleClick = undefined } = IShowMoreProps;
  const onClickCallback = handleClick ? handleClick : undefined;
  const htmlTag = handleClick ? 'a' : 'span';
  return (
    <div>
      {records?.slice(0, maxLength).map((item, index) =>
        React.createElement(
          htmlTag,
          {
            key: item + index,
            onClick: () => onClickCallback(item),
          },
          `${item}${index < records.length - 1 ? ';' : ''} `,
        ),
      )}
      {records?.length > maxLength && (
        <Popover
          trigger={'click'}
          placement="top"
          overlay={
            <DropdownBox>
              <List type="option">
                {records.map((item) => (
                  <List.Item key={item} onClick={() => onClickCallback(item)}>
                    {React.createElement(htmlTag, {}, item)}
                  </List.Item>
                ))}
              </List>
            </DropdownBox>
          }
        >
          <div>
            <Button type="link">点击查看更多</Button>
          </div>
        </Popover>
      )}
    </div>
  );
};

export default ShowMore;
