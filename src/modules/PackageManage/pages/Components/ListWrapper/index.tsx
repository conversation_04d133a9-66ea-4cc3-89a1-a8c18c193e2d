import React, { useEffect, useState, useRef } from 'react';
import { message, <PERSON>ton, Card, Justify, Switch, Table } from '@tencent/tea-component';
import useLookup from '@/common/hookups/useLookup';
import { getUrlParams } from '@/common/utils';
import { autotip, pageable, scrollable } from '@tencent/tea-component/lib/table/addons';
import { Filter, toListParamsCApi } from '@/common/api/api';
import { DefectManagementRoutePath } from '@/common/routePath';
import moment from 'moment';
import { unstable_batchedUpdates } from 'react-dom';
import { TcsLayout } from '@tencent/tcs-component';

interface ListWrapperProps {
  listRecords: any;
  onDetail: (record: any) => void;
  children?: React.ReactNode;
  getColumns: ({ onDetail, getLookupByCode }) => any;
  handleFormatRecords: (res) => any;
  filter?: Filter[];
  type: 'history' | 'board';
  showExport?: boolean;
  showRelated?: boolean;
  minWidth?: number;
  bordered?: boolean | 'all';
  usePolling?: boolean;
}

const formattedUrlParams = (urlParams) => {
  const { StartTime, Status } = urlParams;
  if (StartTime && !Array.isArray(StartTime)) {
    const timeArr = StartTime.split(',');
    // eslint-disable-next-line no-param-reassign
    urlParams.StartTime = [moment(new Date(timeArr[0])), moment(new Date(timeArr[1]))] as any;
  }
  if (Status && !Array.isArray(Status)) {
    // eslint-disable-next-line no-param-reassign
    urlParams.Status = Status.split(',');
  }
  return urlParams;
};

const formattedQueryParams = (urlParams) => {
  // eslint-disable-next-line no-param-reassign
  delete urlParams.__breadcrumbCode__;
  // eslint-disable-next-line no-param-reassign
  delete urlParams.__breadcrumbTitle__;
  delete urlParams.pageIndex;
  const url = JSON.parse(JSON.stringify(urlParams));
  const { StartTime } = url;
  if (StartTime) {
    url.StartTime = [
      `${moment(new Date(StartTime[0])).format('YYYY-MM-DD')} 00:00:00`,
      `${moment(new Date(StartTime[1])).format('YYYY-MM-DD')} 23:59:59`,
    ] as any;
  }
  return url;
};

const ListWrapper = (props: ListWrapperProps) => {
  const {
    listRecords,
    children,
    getColumns,
    handleFormatRecords,
    onDetail,
    filter,
    type,
    showExport,
    showRelated = true,
    minWidth = 2000,
    bordered,
    usePolling = false,
  } = props;

  const [related, setRelated] = useState(localStorage.getItem('defect_manage/package_manage/RelatedToMe') === 'true');
  const urlParams = formattedUrlParams(getUrlParams());
  const { history } = TcsLayout.useHistory();
  const [queryParams, setQueryParams] = useState<any>(() => formattedQueryParams(urlParams));
  const [, setUrlState] = TcsLayout.useUrlState(urlParams || {}, { navigateMode: 'replace' });
  const { getLookupByCode } = useLookup([]);
  const [pageInfo, setPageInfo] = useState({
    pageSize: parseInt(urlParams?.pageSize || '20', 10),
    pageIndex: parseInt(urlParams?.pageIndex || '1', 10),
    total: 0,
  });
  const [loading, setLoading] = useState(false);
  const [records, setRecords] = useState<any[]>([]);
  const timeoutIdRef = useRef<NodeJS.Timeout | null>(null);
  const isMountedRef = useRef(true);

  const searchFunction = (values) => {
    setQueryParams(() => {
      const { Status, StartTime } = values;
      const test = {
        ...values,
        StartTime:
          StartTime?.length > 0
            ? [
                `${moment(StartTime?.[0])?.format('YYYY-MM-DD')} 00:00:00`,
                `${moment(StartTime?.[1])?.format('YYYY-MM-DD')}  23:59:59`,
              ]
            : undefined,
        Status: Status && !Array.isArray(Status) ? [Status] : Status,
      };
      return test;
    });
    setPageInfo((info) => ({ ...info, pageIndex: 1 }));
    setUrlState(values);
  };

  const searchElement = React.Children.map(children, (child) => {
    if (React.isValidElement(child) && child.type?.displayName === 'SearchComponent') {
      return React.cloneElement(child, {
        onSearch: searchFunction,
        initialValues: {
          ...urlParams,
          StartTime: urlParams?.StartTime
            ? [moment(urlParams?.StartTime?.[0]), moment(urlParams?.StartTime?.[1])]
            : undefined,
        },
      } as any);
    }
    return child;
  });

  useEffect(() => {
    const getPackageRecords = (isLoading = true) => {
      if (isLoading) setLoading(true);
      const queryParamsClone = { ...queryParams };
      let starTimeFilter = [];
      const { StartTime } = queryParamsClone;
      if (StartTime && StartTime.length === 2) {
        starTimeFilter = [
          { Key: 'StartTime', Op: '>=', Value: moment(StartTime[0]).format('YYYY-MM-DD HH:mm:ss') },
          { Key: 'StartTime', Op: '<=', Value: moment(StartTime[1]).format('YYYY-MM-DD HH:mm:ss') },
        ];
        delete queryParamsClone.StartTime;
      }
      listRecords({
        RelatedToMe: related,
        PageSize: pageInfo.pageSize,
        PageNo: pageInfo.pageIndex,
        Filters: toListParamsCApi(queryParamsClone, {
          page: false,
          useEqFields: ['ID', 'IssueID', 'Arch', 'SolutionVersionUUID', 'SolutionVersionID'],
          retainEmptyString: false,
        })._Filter?.concat(filter ?? [], starTimeFilter),
      })
        .then((res) => {
          if (!isMountedRef.current) return;
          if (res?.Error) {
            message.error({ content: res.Error.Message });
          } else {
            const formattedRecords = handleFormatRecords(res) ?? [];
            setRecords(formattedRecords);
            setPageInfo((info) => ({ ...info, total: res?.Total ?? 0 }));
            // Re-evaluate the need for polling after updating records
            if (usePolling && formattedRecords.some((record) => record.Status === 'Running')) {
              timeoutIdRef.current = setTimeout(() => getPackageRecords(false), 10000);
            }
          }
        })
        .finally(() => {
          isLoading && setLoading(false);
        });
    };

    isMountedRef.current = true;

    getPackageRecords();
    return () => {
      isMountedRef.current = false;
      if (timeoutIdRef.current) clearTimeout(timeoutIdRef.current);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pageInfo.pageIndex, pageInfo.pageSize, queryParams, related, usePolling]);

  // Remove the separate useEffect for polling
  const handleExport = () => {
    const url =
      type === 'history'
        ? DefectManagementRoutePath.PATCH_EXPORT_FROM_HISTORY_PAGE
        : DefectManagementRoutePath.PATCH_EXPORT_FROM_BOARD_PAGE;
    history.push(url);
  };

  const columns = getColumns({ onDetail, getLookupByCode });

  return (
    <div className="tcs-layout__table_wrap">
      {searchElement}
      <Card className="tcs-layout__table_card">
        <Card.Body>
          <Table.ActionPanel>
            <Justify
              left={
                showExport && (
                  <Button type="primary" onClick={handleExport}>
                    批量出包
                  </Button>
                )
              }
              right={
                showRelated && (
                  <Switch
                    defaultChecked
                    value={related}
                    onChange={(value) => {
                      unstable_batchedUpdates(() => {
                        setRelated(value);
                      });
                      localStorage.setItem('defect_manage/package_manage/RelatedToMe', String(value));
                      setPageInfo((info) => ({ ...info, pageIndex: 1 }));
                    }}
                  >
                    与我相关
                  </Switch>
                )
              }
            />
          </Table.ActionPanel>
          <Table
            columns={columns}
            records={records}
            bordered={bordered}
            addons={[
              pageable({
                recordCount: pageInfo.total,
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                onPagingChange: (query) => {
                  setPageInfo((info) => ({ ...info, ...query }));
                  setUrlState(query);
                },
              }),
              autotip({ isLoading: loading }),
              scrollable({ minWidth }),
            ]}
          />
        </Card.Body>
      </Card>
    </div>
  );
};

export default ListWrapper;
