import { ProForm, QueryFilter, SolutionVersionSelect } from '@/common/components';
import { Card, DatePicker, Input, Select, SelectMultiple } from '@tencent/tea-component';
import styles from './index.module.scss';
import React from 'react';
// import { ISolutionVersion, listSolutionVersions } from '@/common/api/deliveryManage';
import useLookup from '@/common/hookups/useLookup';
import { convertEmptyStringToUndefined } from '@/modules/PackageManage/utils';

const { RangePicker } = DatePicker;

export interface IProps {
  onSearch?: (values: any) => void;
  initialValues?: any;
  searchKeys: Record<string, string>;
}

const Search: React.FC<IProps> = ({ onSearch, initialValues = {}, searchKeys }) => {
  const { lookups } = useLookup(['PackageArch', 'DeliveryStatus', 'SiteType', 'PackageOutRecordStatus']);

  const handleSearch = (values) => {
    const newValues = convertEmptyStringToUndefined(values);
    onSearch?.(newValues);
  };

  // useEffect(() => {
  //   listSolutionVersions({}).then((res) => {
  //     if (res?.Error) {
  //       message.error({
  //         content: res.Error.Message,
  //       });
  //     } else {
  //       // setSolutionVersionList(res?.ListSolutionVersions || []);
  //     }
  //   });
  // }, []);

  return (
    <Card style={{ marginBottom: 5 }} className="tcs-layout__table_search">
      <Card.Body className={styles.search}>
        <QueryFilter onSearch={handleSearch} initialValues={initialValues}>
          {searchKeys.ClientName && (
            <ProForm.Item label="客户名" dataIndex="ClientName" key="ClientName">
              <Input placeholder="请输入客户名" />
            </ProForm.Item>
          )}
          {searchKeys.SiteName && (
            <ProForm.Item label="局点名" dataIndex="SiteName" key="SiteName">
              <Input placeholder="请输入局点名" />
            </ProForm.Item>
          )}
          {searchKeys.SolutionVersion && (
            <ProForm.Item label="解决方案版本" dataIndex={searchKeys.SolutionVersion} key="SolutionVersionUUID">
              {/* <Select
                placeholder="请选择解决方案版本"
                searchable
                clearable
                appearance="button"
                matchButtonWidth
                options={solutionVersionList.map((item: any) => ({
                  text: item.Code,
                  value: item.UUID,
                }))}
              /> */}
              <SolutionVersionSelect style={{ width: '100%' }} />
            </ProForm.Item>
          )}
          {searchKeys.Arch && (
            <ProForm.Item label="架构" dataIndex="Arch" key="Arch">
              <Select
                appearance="button"
                placeholder="请选择架构"
                matchButtonWidth
                clearable
                options={
                  lookups?.PackageArch?.map((item) => ({
                    value: item.Code,
                    text: item.Name,
                  })) || []
                }
              />
            </ProForm.Item>
          )}
          {searchKeys.IssueID && (
            <ProForm.Item label="缺陷单ID" dataIndex="IssueID" key="IssueID">
              <Input placeholder="请输入缺陷单ID" />
            </ProForm.Item>
          )}
          {searchKeys.Status && (
            <ProForm.Item label="出包状态" dataIndex="Status" key="Status">
              <SelectMultiple
                appearance="button"
                placeholder="请选择出包状态"
                matchButtonWidth
                allOption={{
                  text: '全选',
                  value: 'all',
                }}
                clearable
                options={
                  lookups?.[searchKeys.Status]?.map((item) => ({
                    value: item.Code,
                    text: item.Name,
                  })) || []
                }
              />
            </ProForm.Item>
          )}
          {searchKeys.ApplicationName && (
            <ProForm.Item label="应用名称" dataIndex="ApplicationName" key="ApplicationName">
              <Input placeholder="请输入应用名称" />
            </ProForm.Item>
          )}
          {searchKeys.ID && (
            <ProForm.Item label="出包ID" dataIndex="ID" key="ID">
              <Input placeholder="请输入出包ID" />
            </ProForm.Item>
          )}
          {searchKeys.StartTime && (
            <ProForm.Item label="出包时间" dataIndex="StartTime" key="StartTime">
              <RangePicker />
            </ProForm.Item>
          )}
          {searchKeys.SiteType && (
            <ProForm.Item label={'局点类型'} dataIndex="SiteType" key="SiteType">
              <Select
                placeholder="请选择"
                size="full"
                matchButtonWidth
                appearance="button"
                options={
                  lookups?.SiteType?.map((item) => ({
                    value: item.Code,
                    text: item.Name,
                  })) || []
                }
              />
            </ProForm.Item>
          )}
        </QueryFilter>
      </Card.Body>
    </Card>
  );
};

export default Search;
Search.displayName = 'SearchComponent';
