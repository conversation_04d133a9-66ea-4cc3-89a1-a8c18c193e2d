import { ExternalLink, Tag } from '@tencent/tea-component';
import React from 'react';

export const getColumns = ({ handleViewExportFlowList, getLookupByCode }) => [
  {
    key: 'ClientName',
    header: '客户名称',
  },
  {
    key: 'SiteName',
    header: '局点名称',
  },
  {
    key: 'SiteType',
    header: '局点类型',
    render(record) {
      const { SiteType } = record;
      const lookup = getLookupByCode('SiteType', SiteType);
      if (lookup) {
        return <Tag style={{ color: lookup.Extra?.Color }}>{lookup.Name}</Tag>;
      }
      return SiteType || '-';
    },
  },
  {
    header: '出包状态',
    key: 'Status',
    render(record) {
      const { Status } = record;
      const lookup = getLookupByCode('DeliveryStatus', Status);
      if (lookup) {
        return <Tag style={{ color: lookup.Extra?.Color }}>{lookup.Name}</Tag>;
      }
      return Status || '-';
    },
  },
  {
    header: '物料链接',
    key: 'MaterialURL',
    render: (record) => {
      if (record?.MaterialURL) {
        return (
          <a href={record?.MaterialURL} target="_blank" rel="noreferrer" download>
            点击下载
          </a>
        );
      }
      return '-';
    },
  },
  {
    key: 'Operation',
    header: '操作',
    render: (record) => {
      if (record?.WorkflowInstances) {
        return <a onClick={() => handleViewExportFlowList(record)}>查看出包记录</a>;
      }
      return '-';
    },
  },
];

export const getExpandColumns = () => [
  {
    key: 'AnDonSiteInfo.EnglishName',
    header: '局点信息库局点',
  },
  {
    key: 'IssueTitle',
    header: 'Antool变更单号',
    render(record) {
      if (record.AntoolProcessInstanceID) {
        let baseUrl = '';
        if (window.location.hostname.startsWith('dev.') || window.location.hostname.startsWith('pre.')) {
          baseUrl = `https://test-antool.woa.com/micro/antool-change/detail?appId=69&formKey=changeformnew&processInstanceId=${record.AntoolProcessInstanceID}`;
        } else {
          baseUrl = `https://antool.woa.com/micro/antool-change/detail?appId=69&formKey=changeformnew&processInstanceId=${record.AntoolProcessInstanceID}`;
        }
        return <ExternalLink href={baseUrl}>跳转至Antool</ExternalLink>;
      }

      return '-';
    },
  },
  {
    key: 'Status',
    header: '状态',
  },
  {
    key: 'Phase',
    header: '所处阶段',
  },
];

export const getApplicationColumns = () => [
  {
    key: 'ApplicationName',
    header: '应用名称',
  },
  {
    key: 'ApplicationVersion',
    header: '制品版本',
  },
];
