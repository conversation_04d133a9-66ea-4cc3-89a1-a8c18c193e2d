import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Card, Col, Form, Row, Table, message } from '@tencent/tea-component';
import { autotip, expandable, scrollable } from '@tencent/tea-component/lib/table/addons';
import { getApplicationColumns, getColumns, getExpandColumns } from './config';
import { getUrlParams } from '@/common/utils';
import { getPackageIssueDetailInfo, getPackageOutRecordDetailIssue } from '@/common/api/packageManage';
import useLookup from '@/common/hookups/useLookup';
import WorkflowListModal from '@/modules/DeliveryManage/pages/List/components/WorkflowListModal';
import { TcsLayout } from '@tencent/tcs-component';

const Detail = () => {
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const workflowListRef = useRef<any>();
  const urlParams = getUrlParams();
  const { RecordID, Scene, IssueID, IssueRelID } = urlParams;
  const [detail, setDetail] = useState<any>();
  const { getLookupByCode } = useLookup([]);
  const [loading, setLoading] = useState(false);
  const { history } = TcsLayout.useHistory();
  useEffect(() => {
    setLoading(true);
    if (Scene === 'history') {
      getPackageOutRecordDetailIssue({ RecordID })
        .then((res) => {
          if (res?.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            const issueList = (res as any)?.PackageIssueList;
            const currentIssueInfo = issueList.filter((item) => item?.IssueID === IssueID);
            setDetail(currentIssueInfo?.[0]);
          }
        })
        .then(() => {
          setLoading(false);
        });
    } else {
      getPackageIssueDetailInfo({ IssueSolutionArchRelUUID: IssueRelID })
        .then((res) => {
          if (res?.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            setDetail(res?.PackageIssueInfo);
          }
        })
        .then(() => {
          setLoading(false);
        });
    }
  }, [IssueID, IssueRelID, RecordID, Scene]);

  const handleViewExportFlowList = (record) => {
    workflowListRef.current.show(record);
  };

  const deliverySiteInfos = useMemo(() => {
    const siteInfos = detail?.DeliverySiteInfos?.map((item) => ({
      WorkflowInstanceID: item?.WorkflowInstances,
      ...item,
    }));
    return siteInfos;
  }, [detail?.DeliverySiteInfos]);

  return (
    <TcsLayout history={history} title="出包缺陷详情" customizeCard>
      <Card>
        <Card.Body title="基本信息">
          <Form layout="fixed" style={{ width: '100%' }}>
            <Row>
              <Col lg={8} md={24}>
                <Form.Item label="缺陷单标题">
                  <Form.Text>{detail?.Title}</Form.Text>
                </Form.Item>
              </Col>
              <Col lg={8} md={24}>
                <Form.Item label="缺陷单ID">
                  <Form.Text>{detail?.IssueID}</Form.Text>
                </Form.Item>
              </Col>
              <Col lg={8} md={24}>
                <Form.Item label="解决方案版本">
                  <Form.Text>{detail?.SolutionVersion}</Form.Text>
                </Form.Item>
              </Col>
              <Col lg={8} md={24}>
                <Form.Item label="架构">
                  <Form.Text>{detail?.Arch}</Form.Text>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Card.Body>
      </Card>
      <Card>
        <Card.Body title="出包局点清单">
          <Table
            columns={getColumns({ handleViewExportFlowList, getLookupByCode })}
            records={deliverySiteInfos ?? []}
            addons={[
              scrollable({
                maxHeight: 380,
              }),
              autotip({
                isLoading: loading,
              }),

              expandable({
                expandedKeys,
                shouldRecordExpandable: (record) => record?.DeliverySiteAntoolRels,
                onExpandedKeysChange: (keys) => setExpandedKeys(keys),
                render(record) {
                  return <Table columns={getExpandColumns()} records={record?.DeliverySiteAntoolRels ?? []} />;
                },
                gapCell: 1,
              }),
            ]}
          />
        </Card.Body>
      </Card>
      <Card>
        <Card.Body title="出包应用清单">
          <Table
            columns={getApplicationColumns()}
            records={detail?.IssueApplications ?? []}
            addons={[
              autotip({
                isLoading: loading,
              }),
              scrollable({
                maxHeight: 380,
              }),
            ]}
          />
        </Card.Body>
      </Card>
      <WorkflowListModal ref={workflowListRef} />
    </TcsLayout>
  );
};
export default Detail;
