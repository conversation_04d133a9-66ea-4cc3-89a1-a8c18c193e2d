import React from 'react';
import { getColumns } from './config';
import { listPackageIssueList } from '@/common/api/packageManage';
import { DefectManagementRoutePath } from '@/common/routePath';

import ListWrapper from '../../../Components/ListWrapper';
import Search from '../../../Components/Search';
import { TcsLayout } from '@tencent/tcs-component';

const IssueScene = ({ filter }: { filter?: any }) => {
  const { history } = TcsLayout.useHistory();
  const handleDetail = (record) => {
    history.push(
      `${DefectManagementRoutePath.ISSUE_BOARD_DETAIL_PAGE}?Scene=board&IssueRelID=${record?.IssueSolutionArchRelUUID}`,
    );
  };

  return (
    <ListWrapper
      type="board"
      listRecords={listPackageIssueList}
      getColumns={getColumns}
      handleFormatRecords={(res) => res?.PackageIssueList}
      onDetail={handleDetail}
      filter={filter}
      minWidth={1200}
    >
      <Search
        searchKeys={{
          SolutionVersion: 'SolutionVersionID',
          Arch: 'Arch',
          IssueID: 'IssueID',
          Status: 'DeliveryStatus',
          ApplicationName: 'ApplicationName',
        }}
      />
    </ListWrapper>
  );
};

export default IssueScene;
