import { Tag } from '@tencent/tea-component';
import React from 'react';
import ShowMore from '../../../Components/ShowMore';

export const getColumns = ({ onDetail, getLookupByCode }) => [
  {
    key: 'IssueID',
    header: '缺陷单ID',
    width: 150,
  },
  {
    key: 'Title',
    header: '缺陷单标题',
    width: '30%',
    render: (record) => (
      <a
        onClick={() =>
          window.open(
            `/page/defect_manage/__develop_center_next/iteration_manage/defect/detail?issue_id=${record?.IssueID}`,
            '_blank',
          )
        }
      >
        {record?.Title}
      </a>
    ),
  },
  {
    key: 'SolutionVersion',
    header: '解决方案版本',
    width: 150,
  },
  {
    key: 'Arch',
    header: '架构',
    width: 150,
    render(record) {
      if (record?.Arch) {
        const lookup = getLookupByCode('PackageArch', record.Arch);
        return lookup?.Name || '-';
      }
      return '-';
    },
  },
  {
    key: 'Application',
    header: '出包应用',
    width: '30%',
    render: (record) => {
      const applications = (record?.IssueApplications ?? []).map((item) => item?.ApplicationName);
      return <ShowMore records={applications} maxLength={5} />;
    },
  },
  {
    key: 'Status',
    header: '出包状态',
    width: 150,
    render: (record) => {
      const lookup = getLookupByCode('DeliveryStatus', record?.Status);
      if (lookup) {
        return <Tag style={{ color: lookup.Extra?.Color }}>{lookup.Name}</Tag>;
      }
      return record?.Status || '-';
    },
  },
  {
    key: 'operation',
    header: '操作',
    fixed: 'right',
    width: 150,
    render: (record) => <a onClick={() => onDetail(record)}>查看详情</a>,
  },
];
