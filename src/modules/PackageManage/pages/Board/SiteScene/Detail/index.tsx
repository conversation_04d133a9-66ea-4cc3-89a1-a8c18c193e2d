import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Card, Col, Form, Row, Table, message } from '@tencent/tea-component';
import { getColumns } from './config';
import { getPackageOutRecordDetailSite, getPackageSiteIssueDetailInfo } from '@/common/api/packageManage';
import { getUrlParams } from '@/common/utils';
import useLookup from '@/common/hookups/useLookup';
import { toListParamsCApi } from '@/common/api/api';
import WorkflowListModal from '@/modules/DeliveryManage/pages/List/components/WorkflowListModal';
import { autotip, filterable, pageable } from '@tencent/tea-component/lib/table/addons';
import { TcsLayout } from '@tencent/tcs-component';

const ALL_VALUE = '__ALL__';
const Detail = () => {
  const [detail, setDetail] = useState<any>();
  const [status, setStatus] = useState<string[]>();
  const { history } = TcsLayout.useHistory();
  const [loading, setLoading] = useState(false);
  const workflowListRef = useRef<any>();
  const { lookups, getLookupByCode } = useLookup(['DeliveryStatus']);
  const urlParams = getUrlParams();
  const { Arch, SolutionVersionUUID, SiteUUID, RecordID, Scene } = urlParams;
  const [pageInfo, setPageInfo] = useState({
    pageSize: 20,
    pageIndex: 1,
    total: 0,
  });

  const deliveryIssueInfos = useMemo(() => {
    const issueInfos = detail?.IssueInfos?.map((item) => ({
      WorkflowInstanceID: item?.WorkflowInstances,
      ...item,
    }));
    return issueInfos;
  }, [detail]);

  const handleViewExportFlowList = (record) => {
    workflowListRef.current.show(record);
  };

  useEffect(() => {
    setLoading(true);
    let Filters;
    if (status) {
      Filters = toListParamsCApi({ Status: Array.isArray(status) ? status : [status] })._Filter;
    }
    if (Scene === 'history') {
      getPackageOutRecordDetailSite({ RecordID, Filters, PageSize: pageInfo.pageSize, PageNo: pageInfo.pageIndex })
        .then((res: any) => {
          if (res?.Error) {
            return message.error({ content: res?.Error });
          }

          const packageSiteInfos = res?.PackageSiteInfos;
          const currentSite = packageSiteInfos?.filter((item) => {
            const { PackageSiteSolutionArchIssuesInfos } = item;
            const currentArchIssueInfo = PackageSiteSolutionArchIssuesInfos?.[0];

            return (
              currentArchIssueInfo?.Arch === Arch &&
              currentArchIssueInfo?.SolutionVersionUUID === SolutionVersionUUID &&
              item?.SiteUUID === SiteUUID
            );
          });

          const siteDetail = {
            ...currentSite?.[0],
            Arch: currentSite?.[0]?.PackageSiteSolutionArchIssuesInfos?.[0]?.Arch,
            SolutionVersion: currentSite?.[0]?.PackageSiteSolutionArchIssuesInfos?.[0]?.SolutionVersion,
            IssueInfos: currentSite?.[0]?.PackageSiteSolutionArchIssuesInfos?.[0]?.IssueInfos,
          };
          setDetail(siteDetail);
          setPageInfo((info) => ({
            ...info,
            total: res?.Total ?? 0,
          }));
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      getPackageSiteIssueDetailInfo({
        Arch,
        SolutionVersionUUID,
        SiteUUID,
        Filters,
        PageSize: pageInfo.pageSize,
        PageNo: pageInfo.pageIndex,
      })
        .then((res) => {
          if (res?.Error) {
            return message.error({ content: res?.Error });
          }
          setDetail(res?.PackageSiteInfo);
          setPageInfo((info) => ({
            ...info,
            total: res?.Total ?? 0,
          }));
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [Arch, RecordID, Scene, SiteUUID, SolutionVersionUUID, status, pageInfo.pageIndex, pageInfo.pageSize]);

  return (
    <TcsLayout title="出包详情" customizeCard history={history}>
      <Card>
        <Card.Body title="基本信息">
          <Form layout="fixed" style={{ width: '100%' }}>
            <Row>
              <Col lg={8} md={24}>
                <Form.Item label="客户名称">
                  <Form.Text>{detail?.ClientName}</Form.Text>
                </Form.Item>
              </Col>
              <Col lg={8} md={24}>
                <Form.Item label="局点名称">
                  <Form.Text>{detail?.SiteName}</Form.Text>
                </Form.Item>
              </Col>
              <Col lg={8} md={24}>
                <Form.Item label="解决方案版本">
                  <Form.Text>{detail?.SolutionVersion}</Form.Text>
                </Form.Item>
              </Col>
              <Col lg={8} md={24}>
                <Form.Item label="架构">
                  <Form.Text>{detail?.Arch}</Form.Text>
                </Form.Item>
              </Col>
              <Col lg={8} md={24}>
                <Form.Item label="出包状态">
                  <Form.Text>{getLookupByCode('DeliveryStatus', detail?.Status)?.Name ?? '-'}</Form.Text>
                </Form.Item>
              </Col>
              <Col lg={8} md={24} />
            </Row>
          </Form>
        </Card.Body>
      </Card>
      <Card>
        <Card.Body title="关联缺陷单清单">
          <Table
            columns={getColumns({ getLookupByCode, handleViewExportFlowList })}
            records={deliveryIssueInfos ?? []}
            addons={[
              autotip({
                isLoading: loading,
              }),
              pageable({
                recordCount: pageInfo.total,
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                onPagingChange: (query) => {
                  setPageInfo((pageInfo) => ({
                    ...pageInfo,
                    ...query,
                  }));
                },
              }),
              filterable({
                type: 'multiple',
                column: 'Status',
                onChange: setStatus,
                all: {
                  value: ALL_VALUE,
                  text: '全部',
                },
                options:
                  lookups?.DeliveryStatus?.map((item) => ({
                    value: item.Code,
                    text: item.Name,
                  })) ?? [],
              }),
            ]}
          />
        </Card.Body>
        <WorkflowListModal ref={workflowListRef} />
      </Card>
    </TcsLayout>
  );
};
export default Detail;
