import { Tag } from '@tencent/tea-component';
import React from 'react';

export const getColumns = ({ getLookupByCode, handleViewExportFlowList }) => [
  {
    key: 'IssueID',
    header: '缺陷单ID',
    width: 120,
  },
  {
    key: 'Title',
    header: '标题',
    width: '35%',
    render: (record) => (
      <a
        onClick={() =>
          window.open(
            `/page/defect_manage/__develop_center_next/iteration_manage/defect/detail?issue_id=${record?.IssueID}`,
            '_blank',
          )
        }
      >
        {record?.Title}
      </a>
    ),
  },

  {
    key: 'Status',
    header: '出包状态',
    width: 120,
    render: (record) => {
      const { Status } = record;
      const lookup = getLookupByCode('DeliveryStatus', Status);
      if (lookup) {
        return <Tag style={{ color: lookup.Extra?.Color }}>{lookup.Name}</Tag>;
      }
      return Status || '-';
    },
  },
  {
    key: 'IssueApplications',
    header: '关联应用',
    width: '35%',
    render: (record) => {
      const apps = record?.IssueApplications ?? [];
      return apps.map((item) => item?.ApplicationName).join(';');
    },
  },
  {
    key: 'operation',
    header: '操作',
    width: 120,
    render: (record) => {
      if (record?.WorkflowInstances) {
        return <a onClick={() => handleViewExportFlowList(record)}>查看出包记录</a>;
      }
      return '-';
    },
  },
];
