import React from 'react';
import { getColumns } from './config';
import { DefectManagementRoutePath } from '@/common/routePath';
import { listPackageSiteIssueList } from '@/common/api/packageManage';
import ListWrapper from '../../../Components/ListWrapper';
import Search from '../../../Components/Search';
import { TcsLayout } from '@tencent/tcs-component';

const SiteScene = ({ filter }: { filter?: any }) => {
  const { history } = TcsLayout.useHistory();
  const handleDetail = (record) => {
    history.push(
      `${DefectManagementRoutePath.SITE_BOARD_DETAIL_PAGE}?SiteUUID=${record?.SiteUUID}&SolutionVersionUUID=${record?.SolutionVersionUUID}&Arch=${record?.Arch}`,
    );
  };

  return (
    <ListWrapper
      type="board"
      listRecords={listPackageSiteIssueList}
      getColumns={getColumns}
      handleFormatRecords={(res) => res?.PackageSiteList}
      onDetail={handleDetail}
      filter={filter}
      minWidth={1200}
      bordered="all"
    >
      <Search
        searchKeys={{
          SolutionVersion: 'SolutionVersionID',
          Arch: 'Arch',
          ClientName: 'ClientName',
          SiteName: 'SiteName',
          SiteType: 'SiteType',
          Status: 'DeliveryStatus',
        }}
      />
    </ListWrapper>
  );
};

export default SiteScene;
