import { Tag } from '@tencent/tea-component';
import React from 'react';

const commonStyle = (showBorderAndMargin) => ({
  borderBottom: showBorderAndMargin ? '1px solid #e7eaef' : 'unset',
  marginBottom: showBorderAndMargin ? 10 : 0,
  paddingBottom: showBorderAndMargin ? 10 : 0,
});

export const getColumns = ({ onDetail, getLookupByCode }) => [
  {
    key: 'ClientName',
    header: '客户名称',
    width: '20%',
  },
  {
    key: 'SiteName',
    header: '局点名称',
    width: '20%',
  },
  {
    key: 'SiteType',
    header: '局点类型',
    width: '150',
    render(record) {
      const { SiteType } = record;
      const lookup = getLookupByCode('SiteType', SiteType);
      if (lookup) {
        return <Tag style={{ color: lookup.Extra?.Color }}>{lookup.Name}</Tag>;
      }
      return SiteType || '-';
    },
  },
  {
    key: 'Status',
    header: '出包状态',
    width: '120',
    render: (record) => {
      const { PackageSiteSolutionArchIssuesInfos } = record;
      const length = PackageSiteSolutionArchIssuesInfos?.length;

      return PackageSiteSolutionArchIssuesInfos?.map((item, index) => {
        const showBorderAndMargin = index < length - 1;
        const lookup = getLookupByCode('DeliveryStatus', item?.Status);
        return (
          <div key={item?.SolutionVersion} style={commonStyle(showBorderAndMargin)}>
            {lookup ? <Tag style={{ color: lookup.Extra?.Color }}>{lookup.Name}</Tag> : '-'}
          </div>
        );
      });
    },
  },
  {
    key: 'SolutionVersion',
    header: '解决方案版本',
    width: '250',
    render: (record) => {
      const { PackageSiteSolutionArchIssuesInfos } = record;
      const length = PackageSiteSolutionArchIssuesInfos?.length;

      return PackageSiteSolutionArchIssuesInfos?.map((item, index) => {
        const showBorderAndMargin = index < length - 1;
        return (
          <div key={item?.SolutionVersion} style={commonStyle(showBorderAndMargin)}>
            {item?.SolutionVersion}
          </div>
        );
      });
    },
  },
  {
    key: 'Arch',
    header: '架构',
    width: '150',
    render: (record) => {
      const { PackageSiteSolutionArchIssuesInfos } = record;
      const length = PackageSiteSolutionArchIssuesInfos?.length;

      return PackageSiteSolutionArchIssuesInfos?.map((item, index) => {
        const showBorderAndMargin = index < length - 1;
        const arch = getLookupByCode('PackageArch', item?.Arch);
        return (
          <div key={item?.Arch + index} style={commonStyle(showBorderAndMargin)}>
            {arch?.Name || '-'}
          </div>
        );
      });
    },
  },
  {
    key: 'operation',
    header: '操作',
    fixed: 'right',
    width: 150,
    // (record) => <a onClick={() => onDetail(record)}>查看详情</a>,
    render: (record) => {
      const { PackageSiteSolutionArchIssuesInfos } = record;
      const length = PackageSiteSolutionArchIssuesInfos?.length;

      return PackageSiteSolutionArchIssuesInfos?.map((item, index) => {
        const showBorderAndMargin = index < length - 1;
        const currentSolution = {
          Arch: item?.Arch,
          SolutionVersion: item?.SolutionVersion,
          SolutionVersionUUID: item?.SolutionVersionUUID,
        };
        return (
          <div key={item?.Arch + index} style={commonStyle(showBorderAndMargin)}>
            <a onClick={() => onDetail({ ...record, ...currentSolution })}>查看详情</a>
          </div>
        );
      });
    },
  },
];
