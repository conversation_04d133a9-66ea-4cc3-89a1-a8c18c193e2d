import React, { useState } from 'react';
import IssueSceneList from './IssueScene/List';
import SiteSceneList from './SiteScene/List';
import { TcsLayout, TcsTabs } from '@tencent/tcs-component';

export const TabWrapper = () => {
  const [activeId, setActiveId] = useState(() => localStorage.getItem('defect_board_activeId') || 'issue');

  return (
    <TcsTabs
      ceiling
      fullHeight
      onChange={(activeId) => {
        localStorage.setItem('defect_board_activeId', activeId);
        setActiveId(activeId);
      }}
      activeKey={activeId as string}
    >
      <TcsTabs.TabPane tab="缺陷单视角" key="issue" id="issue">
        <div className="tcs-layout__table_wrap">
          <IssueSceneList />
        </div>
      </TcsTabs.TabPane>
      <TcsTabs.TabPane tab="局点视角" key="site" id="site">
        <div className="tcs-layout__table_wrap">
          <SiteSceneList />
        </div>
      </TcsTabs.TabPane>
    </TcsTabs>
  );
};
const BoardList = () => {
  const { history } = TcsLayout.useHistory();
  return (
    <TcsLayout history={history} customizeCard fullHeight={false} title={'出包看板'}>
      <TabWrapper />
    </TcsLayout>
  );
};

export default BoardList;
