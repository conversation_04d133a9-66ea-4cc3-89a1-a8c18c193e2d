import { <PERSON>ff<PERSON>, <PERSON><PERSON>, <PERSON>, message } from '@tencent/tea-component';
import React, { useRef, useState } from 'react';
import PreSelectionIssue, { IPreSelectionIssueRef } from './components/PreSelectionIssue';
import SelectIssueModal, { ISelectIssueModalRef } from './components/SelectIssueModal';
import { IIssueProblems, IIssueSelectionsResponse, createPackageOutRecord } from '@/common/api/patchExport';
import SiteSequencesModal, { ISiteSequencesRef } from './components/SiteSequencesModal';
import { DefectManagementRoutePath } from '@/common/routePath';
import { TcsLayout } from '@tencent/tcs-component';
const PatchExport = () => {
  const selectIssueModalRef = useRef<ISelectIssueModalRef>(null);
  const siteSequencesModalRef = useRef<ISiteSequencesRef>(null);
  const preSelectionIssueRef = useRef<IPreSelectionIssueRef>(null);
  const { history } = TcsLayout.useHistory();
  // 所有预选的缺陷单列表
  const [preSelectionIssues, setPreSelectionIssues] = useState<any[]>([]);
  const [issueProblems, setIssueProblems] = useState<IIssueProblems[]>([]);
  const [preSelectionIssueKeys, setPreSelectionIssueKeys] = useState<string[]>([]);
  const [siteIssueSequence, setSiteIssueSequence] = useState<Record<string, IIssueSelectionsResponse[]>>({});

  function handleAddIssue() {
    selectIssueModalRef?.current?.show(preSelectionIssueKeys);
  }

  function handleSelectIssueConfirm(records: any[], issueProblems: IIssueProblems[], selectedRowKeys) {
    setIssueProblems(issueProblems);
    setPreSelectionIssueKeys(selectedRowKeys);
    const selectionIssues = [...preSelectionIssues, ...records];
    setPreSelectionIssues(selectionIssues.filter((item) => selectedRowKeys.includes(item.IssueSolutionArchUUID)));
  }

  function handleBack() {
    history.go(-1);
  }

  function handleViewSiteSequence() {
    const selections = preSelectionIssueRef.current?.getSelectionIssues();
    if (selections?.length) {
      siteSequencesModalRef?.current?.show(selections, siteIssueSequence);
    } else {
      message.error({
        content: '请先选择要出包的缺陷单',
      });
    }
  }

  function handleExport() {
    const selections = preSelectionIssueRef.current?.getSelectionIssues();
    if (!selections?.length) {
      return message.error({
        content: '请先选择要出包的缺陷单',
      });
    }

    createPackageOutRecord({
      IssueSelections: selections.map((item) => ({
        ...item,
        // 如果有选中的局点的话，则将IsAllSites置为false（即使SiteSelections里面的局点是全量局点）
        IsAllSites: !item.SiteSelections,
      })),
      Sequences: Object.values(siteIssueSequence).flat(),
    }).then((res) => {
      if (res.Error) {
        message.error({
          content: res.Error.Message,
        });
      } else {
        message.success({
          content: '已启动出包',
        });
        // TODO: 实际需要跳转到出包详情页面，待出包详情页面开发完再跳转
        history.push(DefectManagementRoutePath.PACKAGE_HISTORY_PAGE);
      }
    });
  }

  function handleSiteSequenceConfirm(siteIssueSequence) {
    setSiteIssueSequence(siteIssueSequence);
  }
  return (
    <TcsLayout title="批量出包" history={history} fullHeight>
      <Card full>
        <Card.Body>
          <div style={{ height: '100%', overflow: 'auto' }}>
            <div style={{ marginBottom: 10 }}>
              <Button type="primary" onClick={handleAddIssue}>
                添加缺陷
              </Button>
            </div>
            <PreSelectionIssue
              ref={preSelectionIssueRef}
              preSelectionIssues={preSelectionIssues}
              issueProblems={issueProblems}
              onRemoveIssue={(IssueSolutionArchUUID) => {
                setPreSelectionIssues((list) =>
                  list.filter((item) => item.IssueSolutionArchUUID !== IssueSolutionArchUUID),
                );
                setPreSelectionIssueKeys((keys) => keys.filter((key) => key !== IssueSolutionArchUUID));
                setSiteIssueSequence((data) => {
                  const result: Record<string, IIssueSelectionsResponse[]> = {};
                  Object.keys(data).forEach((key) => {
                    const issues = data[key].filter((item) => item.IssueSolutionArchUUID !== IssueSolutionArchUUID);
                    if (issues.length > 0) {
                      result[key] = issues;
                    }
                  });
                  return result;
                });
              }}
            />

            <SelectIssueModal ref={selectIssueModalRef} onConfirm={handleSelectIssueConfirm} />
            <SiteSequencesModal ref={siteSequencesModalRef} onConfirm={handleSiteSequenceConfirm} />
          </div>
        </Card.Body>
        <Card.Footer>
          <Affix offsetBottom={0} style={{ position: 'absolute', bottom: 2, textAlign: 'center', width: '100%' }}>
            <div style={{ textAlign: 'center' }}>
              <Button type="primary" style={{ marginRight: 10 }} onClick={handleExport}>
                出包
              </Button>
              <Button type="weak" style={{ marginRight: 10 }} onClick={handleViewSiteSequence}>
                查看局点出包顺序
              </Button>
              <Button type="weak" onClick={handleBack}>
                返回
              </Button>
            </div>
          </Affix>
        </Card.Footer>
      </Card>
    </TcsLayout>
  );
};

export default PatchExport;
