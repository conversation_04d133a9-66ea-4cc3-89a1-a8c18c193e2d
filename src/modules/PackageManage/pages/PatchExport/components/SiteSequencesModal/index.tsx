import { IIssueSelectionsResponse, ISiteIssueSequenceParams, getSiteIssueSequence } from '@/common/api/patchExport';
import { ProModal } from '@/common/components';
import Loading from '@/common/components/Loading';
import { Card, Col, Row, Table, Text, Tree, message } from '@tencent/tea-component';
import React, { useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { getColumns } from './config';

export interface ISiteSequencesRef {
  show: (
    // 用户选中的缺陷单
    selectionIssues: ISiteIssueSequenceParams[],
    // 局点关联缺陷单的排序规则
    siteIssueSequence?: Record<string, IIssueSelectionsResponse[]>,
  ) => void;
}

export interface IProps {
  onConfirm: (sequences: Record<string, IIssueSelectionsResponse[]>) => void;
}

const SiteSequencesModal: React.ForwardRefRenderFunction<ISiteSequencesRef, IProps> = ({ onConfirm }, ref) => {
  const [selectionIssues, setSelectionIssues] = useState<ISiteIssueSequenceParams[]>([]);
  // const [siteIssueSequence, setSiteIssueSequence] = useState<Record<string, IIssueSelectionsResponse[]>>({});
  const siteIssueSequenceRef = useRef<Record<string, IIssueSelectionsResponse[]>>({});
  const [tableData, setTableData] = useState<IIssueSelectionsResponse[]>([]);
  const [data, setData] = useState<IIssueSelectionsResponse[]>([]);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [update, setUpdate] = useState({});
  const [activeId, setActiveId] = useState<string>();
  const columns = getColumns({
    onMove: handleMove,
    dataLength: tableData.length,
  });

  useImperativeHandle(
    ref,
    () => ({
      show(selectionIssues: ISiteIssueSequenceParams[], siteIssueSequence = {}) {
        setSelectionIssues(selectionIssues);
        siteIssueSequenceRef.current = siteIssueSequence;
        setVisible(true);
      },
    }),
    [],
  );

  useEffect(() => {
    if (visible) {
      setLoading(true);
      getSiteIssueSequence({
        IssueSelections: selectionIssues,
      })
        .then((res) => {
          if (res.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            setData(res.Sequences);
          }
        })
        .catch((error) => {
          message.error({
            content: error.message,
          });
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [selectionIssues, visible]);

  const leftTreeData = useMemo(() => {
    if (data?.length) {
      const clientNames: string[] = [];
      const siteUUIDs: string[] = [];
      const clientMaps: Record<
        string,
        Array<{
          SiteUUID: string;
          SiteName: string;
        }>
      > = {};
      data.forEach((item) => {
        if (!clientNames.includes(item.ClientName)) {
          clientNames.push(item.ClientName);
          clientMaps[item.ClientName] = [];
        }
        if (!siteUUIDs.includes(item.SiteUUID)) {
          clientMaps[item.ClientName].push({
            SiteUUID: item.SiteUUID,
            SiteName: item.SiteName,
          });
          siteUUIDs.push(item.SiteUUID);
        }
      });
      return clientNames.map((item) => {
        const siteList = clientMaps[item];
        return {
          id: item,
          content: item,
          children: siteList.map((site) => ({
            id: site.SiteUUID,
            content: site.SiteName,
          })),
        };
      });
    }
    return [];
  }, [data]);

  useEffect(() => {
    // 选中第一个局点
    if (!activeId && leftTreeData?.length) {
      setActiveId(leftTreeData[0].children[0].id);
    }
  }, [leftTreeData, activeId]);

  function handleConfirm() {
    onConfirm(siteIssueSequenceRef.current);
    setVisible(false);
  }

  function handleCancel() {
    setVisible(false);
  }

  function handleActive(ids: string[], context) {
    if (context.data.level === 1) {
      setActiveId(ids[0]);
    }
  }

  function handleMove(direction: 'up' | 'down', record: any) {
    if (!siteIssueSequenceRef.current) {
      siteIssueSequenceRef.current = {};
    }
    if (!siteIssueSequenceRef.current[activeId!]) {
      siteIssueSequenceRef.current[activeId!] = tableData;
    }
    const data = siteIssueSequenceRef.current[activeId!];
    const index = data.findIndex((item) => item.IssueID === record.IssueID);
    if (index < 0) {
      return message.error({
        content: '请联系TCSC小助手',
      });
    }
    // 排序
    const otherIndex = direction === 'up' ? index - 1 : index + 1;
    const other = data[otherIndex];
    data[otherIndex] = data[index];
    data[index] = other;
    data.forEach((item, index) => {
      data[index].Sequence = index;
    });
    siteIssueSequenceRef.current[activeId!] = data;
    setUpdate({});
  }

  useEffect(() => {
    if (activeId && data) {
      let tableData = data
        .filter((item) => item.SiteUUID === activeId)
        .sort((pre, next) => pre.Sequence - next.Sequence);
      // 如果之前用户已对缺陷单进行排序，则应该以排序后的规则为准
      if (siteIssueSequenceRef.current?.[activeId]) {
        tableData = siteIssueSequenceRef.current?.[activeId];
      }
      setTableData(tableData);
    }
  }, [activeId, data, update]);

  return (
    <ProModal
      visible={visible}
      confirmLoading={loading}
      title="局点管理缺陷单出包顺序"
      onOk={handleConfirm}
      onCancel={handleCancel}
      size={1200}
    >
      <Loading loading={loading}>
        <Row>
          <Col span={6}>
            <Card bordered>
              <Card.Body title="局点列表">
                {leftTreeData?.length ? (
                  <Tree
                    defaultExpandAll
                    data={leftTreeData}
                    fullActivable
                    activeIds={activeId ? [activeId] : []}
                    activable
                    onActive={handleActive}
                  />
                ) : (
                  <Text>暂无数据</Text>
                )}
              </Card.Body>
            </Card>
          </Col>
          <Col span={18}>
            <Card bordered>
              <Card.Body title="缺陷单出包顺序">
                <Table columns={columns} records={tableData} />
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Loading>
    </ProModal>
  );
};

export default React.forwardRef(SiteSequencesModal);
