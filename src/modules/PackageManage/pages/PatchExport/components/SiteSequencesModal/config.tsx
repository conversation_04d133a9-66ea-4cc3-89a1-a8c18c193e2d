import { Button } from '@tencent/tea-component';
import React from 'react';
export const getColumns = ({
  onMove,
  dataLength,
}: {
  onMove: (direction: 'up' | 'down', record: any) => void;
  dataLength: number;
}) => [
  {
    header: '缺陷单ID',
    key: 'IssueID',
    width: '15%',
  },
  {
    header: '缺陷单标题',
    key: 'Title',
    width: '30%',
    render(record) {
      return (
        <a
          onClick={() => {
            window.open(
              `/page/defect_manage/__develop_center_next/iteration_manage/defect/detail?issue_id=${record.IssueID}`,
              '_blank',
            );
          }}
        >
          {record?.Title}
        </a>
      );
    },
  },
  {
    header: '解决方案版本',
    key: 'SolutionVersion',
    width: '15%',
  },
  {
    header: '架构',
    key: 'Arch',
    width: '10%',
  },
  {
    header: '操作',
    key: 'operation',
    width: '15%',
    render(record, rowKey, recordIndex) {
      return (
        <>
          {recordIndex !== 0 && (
            <Button type="link" onClick={() => onMove('up', record)}>
              上移
            </Button>
          )}
          {recordIndex < dataLength - 1 && (
            <Button type="link" onClick={() => onMove('down', record)}>
              下移
            </Button>
          )}
        </>
      );
    },
  },
];
