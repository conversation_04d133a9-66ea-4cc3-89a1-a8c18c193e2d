import useLookup from '@/common/hookups/useLookup';
import React, { useEffect, useImperativeHandle, useState } from 'react';
import { getColumns } from './config';
import { Table, message } from '@tencent/tea-component';
import {
  IAvailableIssuesResponse,
  IIssueProblems,
  checkIssuePackageProblems,
  listAvailableIssues,
} from '@/common/api/patchExport';
import Search from './Search';
import { unstable_batchedUpdates } from 'react-dom';
import { TcsCard, TcsModal } from '@tencent/tcs-component';

export interface ISelectIssueModalRef {
  show: (defaultSelectedRowKeys: string[]) => void;
}

export interface ISelectIssueModalProps {
  onConfirm: (result: any[], issueProblems: IIssueProblems[], selectedRowKeys: string[]) => void;
}

const defaultPageInfo = {
  PageNo: 1,
  // 一页多显示几条，方便用户勾选
  PageSize: 50,
  Total: 0,
};

const defaultFilterParams = {
  PackageStatus: ['NeedPackPackage', 'Failed'],
};

const SelectIssueModal: React.ForwardRefRenderFunction<ISelectIssueModalRef, ISelectIssueModalProps> = (
  { onConfirm },
  ref,
) => {
  const [visible, setVisible] = useState(false);
  const { getLookupByCode } = useLookup([]);
  const columns = getColumns({ getLookupByCode });
  const [filterParams, setFilterParams] = useState<any>({ ...defaultFilterParams });
  const [dataSource, setDataSource] = useState<IAvailableIssuesResponse[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedRecords, setSelectedRecords] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [pageInfo, setPageInfo] = useState<{ PageNo: number; PageSize: number; Total: number }>({
    ...defaultPageInfo,
  });
  useEffect(() => {
    if (visible) {
      setLoading(true);
      const params = {
        PageNo: pageInfo.PageNo,
        PageSize: pageInfo.PageSize,
        ...filterParams,
      };
      delete params.Solution;
      listAvailableIssues(params)
        .then((res) => {
          if (res.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            setDataSource(res.Issues);
            setPageInfo((info) => ({
              ...info,
              Total: res.Total,
            }));
          }
        })
        .catch((error) => {
          message.error({
            content: error.message,
          });
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [visible, pageInfo.PageNo, pageInfo.PageSize, filterParams]);

  useImperativeHandle(
    ref,
    () => ({
      show: (defaultSelectedRowKeys: string[]) => {
        setVisible(true);
        setSelectedRowKeys(defaultSelectedRowKeys);
      },
    }),
    [],
  );

  function handleCancel() {
    setVisible(false);
    setPageInfo({
      ...defaultPageInfo,
    });
    setFilterParams({
      ...defaultFilterParams,
    });
    setSelectedRecords([]);
    setSelectedRowKeys([]);
  }

  function handleOk() {
    if (selectedRowKeys.length === 0) {
      return message.warning({
        content: '请选择要出包的缺陷单',
      });
    }
    setLoading(true);
    checkIssuePackageProblems({
      IssueSolutionArchUUIDs: selectedRowKeys,
    })
      .then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          onConfirm(selectedRecords, res.IssueProblems, selectedRowKeys);
          handleCancel();
        }
      })
      .catch((error) => {
        message.error({
          content: error.message,
        });
      })
      .finally(() => {
        setLoading(false);
      });
  }

  function handleSearch(values: any) {
    unstable_batchedUpdates(() => {
      setFilterParams(values);
      setPageInfo((info) => ({
        ...info,
        PageNo: 1,
      }));
    });
  }

  return (
    <TcsModal
      title="选择缺陷单"
      onCancel={handleCancel}
      onOk={handleOk}
      visible={visible}
      width={1350}
      confirmLoading={loading}
      destroyOnClose
    >
      <Search onSearch={handleSearch} />
      <TcsCard bordered>
        <Table
          columns={columns}
          records={dataSource || []}
          recordKey="IssueSolutionArchUUID"
          addons={[
            Table.addons.selectable({
              targetColumnKey: 'IssueID',
              onChange: (keys, context) => {
                // keys 包含跨页码的数据，但context.selectRecords只包含当前页的，所以需要做数据合并
                const records = [...selectedRecords];
                context.selectedRecords.forEach((item) => {
                  if (!selectedRowKeys.includes(item.IssueSolutionArchUUID)) {
                    records.push(item);
                  }
                });
                setSelectedRowKeys(keys);
                setSelectedRecords(records.filter((item) => keys.includes(item.IssueSolutionArchUUID)));
              },
              value: selectedRowKeys,
            }),
            Table.addons.pageable({
              pageIndex: pageInfo.PageNo,
              pageSize: pageInfo.PageSize,
              recordCount: pageInfo.Total || 0,
              onPagingChange: ({ pageIndex, pageSize }) => {
                setPageInfo((info) => ({
                  ...info,
                  PageNo: pageIndex! || info.PageNo,
                  PageSize: pageSize! || info.PageSize,
                }));
              },
            }),
            Table.addons.autotip({
              isLoading: loading,
            }),
            Table.addons.scrollable({
              maxHeight: 500,
              minWidth: 2200,
            }),
          ]}
        />
      </TcsCard>
    </TcsModal>
  );
};

export default React.forwardRef(SelectIssueModal);
