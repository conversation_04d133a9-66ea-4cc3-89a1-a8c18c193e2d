import { Bubble, ExternalLink, Icon, Tag, Text } from '@tencent/tea-component';
import dayjs from 'dayjs';
import React from 'react';
export const getColumns = ({ getLookupByCode }: { getLookupByCode: (type, code) => any }) => [
  {
    header: '缺陷单ID',
    key: 'IssueID',
    width: '10%',
    fixed: 'left',
  },
  {
    header: '缺陷单标题',
    key: 'Title',
    width: '25%',
    render(record) {
      return (
        <a
          onClick={() => {
            window.open(
              `/page/defect_manage/__develop_center_next/iteration_manage/defect/detail?issue_id=${record.IssueID}`,
              '_blank',
            );
          }}
        >
          {record?.Title}
        </a>
      );
    },
  },
  {
    header: '解决方案版本',
    key: 'SolutionVersion',
    width: '12%',
  },
  {
    header: '架构',
    key: 'Arch',
    width: '10%',
  },
  {
    key: 'Status',
    header: '缺陷单状态',
    width: '14%',
    render(record) {
      const { Status } = record;
      const lookup = getLookupByCode('IssueSolutionArchStatus', Status);
      if (lookup) {
        return <Tag style={{ color: lookup.Extra?.Color }}>{lookup.Name}</Tag>;
      }
      return Status || '-';
    },
  },
  {
    key: 'SeverityLevel',
    header: '严重程度',
    width: '10%',
    render(record) {
      const { SeverityLevel } = record;
      const lookup = getLookupByCode('Severity', SeverityLevel);
      if (lookup) {
        return <Tag style={{ color: lookup.Extra?.Color }}>{lookup.Name}</Tag>;
      }
      return SeverityLevel || '-';
    },
  },
  {
    key: 'Priority',
    header: '优先级',
    width: '10%',
    render(record) {
      const { Priority } = record;
      const lookup = getLookupByCode('Priority', Priority);
      if (lookup) {
        return <Tag style={{ color: lookup.Extra?.Color }}>{lookup.Name}</Tag>;
      }
      return Priority || '-';
    },
  },
  {
    key: 'DeliveryID',
    header: '关联交付单',
    width: '12%',
    render(record) {
      const { DeliveryID } = record;
      if (!DeliveryID) {
        return '-';
      }
      const url = `/page/defect_manage/__flow-design/delivery_manage/detail?uuid=${DeliveryID}`;
      return (
        <Text copyable={{ text: url }}>
          <ExternalLink href={url}>点击查看</ExternalLink>
        </Text>
      );
    },
  },
  {
    key: 'OperationSheetUUID',
    header: '关联变更单',
    width: '10%',
    render(record) {
      const { OperationSheetUUID } = record || {};
      if (!OperationSheetUUID) {
        return '-';
      }
      const url = `/page/product-market-jiguang/ops-sheet-manager/parent-sheet-detail?parentSheetId=${OperationSheetUUID}`;
      return (
        <Text copyable={{ text: url }}>
          <ExternalLink href={url}>点击查看</ExternalLink>
        </Text>
      );
    },
  },
  {
    key: 'TAPD',
    header: '关联TAPD',
    width: '10%',
    render(record) {
      const { TAPDUrl } = record;
      if (!TAPDUrl) {
        return '-';
      }
      return (
        <Text copyable={{ text: TAPDUrl }}>
          <ExternalLink href={TAPDUrl}>点击查看</ExternalLink>
        </Text>
      );
    },
  },
  {
    key: 'TAPDStatus',
    header: 'TAPD状态',
    width: '10%',
    render(record) {
      const { TAPDStatus } = record;
      const lookup = getLookupByCode('IssueStatus', TAPDStatus);
      if (lookup) {
        return <Tag style={{ color: lookup.Extra?.Color }}>{lookup.Name}</Tag>;
      }
      return TAPDStatus || '-';
    },
  },
  {
    key: 'DeliverType',
    header: '出包类型',
    width: '14%',
    render(record) {
      const { DeliverType } = record;
      const lookup = getLookupByCode('DeliverType', DeliverType);
      if (lookup) {
        return lookup.Name;
      }
      return DeliverType || '-';
    },
  },
  {
    key: 'PackageStatus',
    width: '12%',
    header: () => (
      <>
        出包状态
        <Bubble content="一个缺陷单可能关联多个交付单，会产生多个出包状态">
          <Icon type="info" />
        </Bubble>
      </>
    ),
    render(record) {
      const { PackageStatus } = record;
      const status = PackageStatus ? PackageStatus.split(',') : [];
      if (status.length) {
        return status.map((s) => {
          const lookup = getLookupByCode('DeliveryStatus', s);
          if (lookup) {
            return (
              <Tag key={s} style={{ color: lookup.Extra?.Color, marginRight: 5 }}>
                {lookup.Name}
              </Tag>
            );
          }
          return '';
        });
      }
      return '-';
    },
  },
  {
    key: 'SiteStatus',
    header: '出包局点状态',
    width: 350,
    render: (record) => {
      const packageSiteStatusCount = record?.SiteStatusCount;
      const {
        PackingCount = 0,
        PackSuccessCount = 0,
        TotalCount = 0,
        ToPackCount = 0,
        PackFailedCount = 0,
      } = packageSiteStatusCount;
      return `总计(${TotalCount})/待出包(${ToPackCount})/出包中(${PackingCount})/出包成功(${PackSuccessCount})/出包失败(${PackFailedCount})`;
    },
  },
  {
    key: 'CreatedAt',
    header: '创建时间',
    width: '15%',
    render(record) {
      const { CreatedAt } = record;
      if (CreatedAt) {
        return dayjs(CreatedAt).format('YYYY-MM-DD HH:mm:ss');
      }
      return '-';
    },
  },
];
