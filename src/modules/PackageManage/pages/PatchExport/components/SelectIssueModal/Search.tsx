import { ISolution, listSolution, listSolutionVersion, ISolutionVersion } from '@/common/api/common';
import { ProForm, QueryFilter } from '@/common/components';
import useLookup from '@/common/hookups/useLookup';
import { Card, DatePicker, Input, Select, SelectMultiple, message } from '@tencent/tea-component';
import React, { useEffect, useMemo, useRef, useState } from 'react';

export interface ISearchProps {
  onSearch: (values: any) => void;
}

const Search: React.FC<ISearchProps> = ({ onSearch }) => {
  const [solutionList, setSolutionList] = useState<ISolution[]>([]);
  const [solutionVersionList, setSolutionVersionList] = useState<ISolutionVersion[]>([]);
  const [selectedSolutionUUID, setSelectedSolutionUUID] = useState<string>();
  const formRef = useRef<any>(null);
  const { lookups } = useLookup([
    'PackageArch',
    'DeliveryStatus',
    'IssueStatus',
    'DeliverType',
    'IssueSolutionArchStatus',
  ]);
  useEffect(() => {
    listSolution().then((res) => {
      if (res.Error) {
        message.error({
          content: res.Error.Message,
        });
      } else {
        setSolutionList(res.ListSolutions);
      }
    });
  }, []);

  useEffect(() => {
    if (selectedSolutionUUID) {
      listSolutionVersion({
        SolutionUUID: selectedSolutionUUID,
      }).then((res) => {
        if (res.Error) {
        } else {
          setSolutionVersionList(res.ListSolutionVersions || []);
        }
      });
    } else {
      setSolutionVersionList([]);
    }
  }, [selectedSolutionUUID]);
  function handleSearch(values) {
    onSearch(values);
    setSelectedSolutionUUID(values?.Solution);
  }

  const solutionVersionRules = useMemo(() => {
    if (selectedSolutionUUID) {
      return {
        rules: {
          required: {
            value: true,
            message: '请选择解决方案版本',
          },
        },
      };
    }
    return {};
  }, [selectedSolutionUUID]);

  return (
    <Card bordered>
      <Card.Body>
        <QueryFilter
          defaultSpread
          onSearch={handleSearch}
          proFormRef={formRef}
          initialValues={{ PackageStatus: ['NeedPackPackage', 'Failed'] }}
        >
          <ProForm.Item label="解决方案" dataIndex="Solution">
            <Select
              appearance="button"
              matchButtonWidth
              size="m"
              searchable
              options={solutionList.map((item) => ({
                value: item.UUID,
                text: item.Name,
              }))}
              onChange={(value) => {
                setSelectedSolutionUUID(value);
                formRef.current.setFieldValue('SolutionVersion', '');
              }}
            />
          </ProForm.Item>
          <ProForm.Item label="解决方案版本" dataIndex="SolutionVersion" {...solutionVersionRules}>
            <Select
              appearance="button"
              searchable
              size="m"
              matchButtonWidth
              placeholder="请先选择解决方案"
              options={
                selectedSolutionUUID
                  ? solutionVersionList.map((item) => ({
                      value: item.Code,
                      text: item.Code,
                    }))
                  : []
              }
            />
          </ProForm.Item>
          <ProForm.Item label="架构" dataIndex="Arch">
            <Select
              appearance="button"
              searchable
              size="m"
              matchButtonWidth
              options={
                lookups?.PackageArch?.map((item) => ({
                  value: item.Code,
                  text: item.Name,
                })) || []
              }
            />
          </ProForm.Item>

          <ProForm.Item label="缺陷单ID" dataIndex="IssueID">
            <Input placeholder="请输入缺陷单ID" />
          </ProForm.Item>
          <ProForm.Item label="缺陷单标题" dataIndex="Title">
            <Input placeholder="请输入缺陷单标题" />
          </ProForm.Item>
          <ProForm.Item label="TAPD链接/单号" dataIndex="TAPD">
            <Input placeholder="请输入TAPD链接/单号" />
          </ProForm.Item>
          <ProForm.Item label="出包状态" dataIndex="PackageStatus">
            <SelectMultiple
              appearance="button"
              searchable
              size="m"
              matchButtonWidth
              allOption={{
                text: '全选',
                value: 'all',
              }}
              options={
                lookups?.DeliveryStatus?.map((item) => ({
                  value: item.Code,
                  text: item.Name,
                })) || []
              }
            />
          </ProForm.Item>
          <ProForm.Item label="出包类型" dataIndex="DeliverType">
            <SelectMultiple
              appearance="button"
              searchable
              size="m"
              allOption={{
                text: '全选',
                value: 'all',
              }}
              matchButtonWidth
              options={
                lookups?.DeliverType?.map((item) => ({
                  value: item.Code,
                  text: item.Name,
                })) || []
              }
            />
          </ProForm.Item>
          <ProForm.Item label="缺陷单状态" dataIndex="Status">
            <SelectMultiple
              appearance="button"
              searchable
              size="m"
              allOption={{
                text: '全选',
                value: 'all',
              }}
              matchButtonWidth
              options={
                lookups?.IssueSolutionArchStatus?.map((item) => ({
                  value: item.Code,
                  text: item.Name,
                })) || []
              }
            />
          </ProForm.Item>
          <ProForm.Item label="Tapd单状态" dataIndex="TapdStatus">
            <SelectMultiple
              appearance="button"
              searchable
              size="m"
              allOption={{
                text: '全选',
                value: 'all',
              }}
              matchButtonWidth
              options={
                lookups?.IssueStatus?.map((item) => ({
                  value: item.Code,
                  text: item.Name,
                })) || []
              }
            />
          </ProForm.Item>
          <ProForm.Item label="创建时间" dataIndex="CreatedAt">
            <DatePicker.RangePicker placeholder="请选择创建时间范围" />
          </ProForm.Item>
        </QueryFilter>
      </Card.Body>
    </Card>
  );
};

export default Search;
