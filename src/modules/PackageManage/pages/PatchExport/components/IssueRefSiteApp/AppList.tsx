import { Table, message } from '@tencent/tea-component';
import React, { useEffect, useState } from 'react';
import { getAppColumns } from './config';
import { listIssueAppRels } from '@/common/api/patchExport';
import Loading from '@/common/components/Loading';
import { ISelectedIssue } from '../types';

export interface IProps {
  selectedIssue: ISelectedIssue;
}

const AppList: React.FC<IProps> = ({ selectedIssue }) => {
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const columns = getAppColumns();
  const [pageInfo, setPageInfo] = useState<{
    current: number;
    pageSize: number;
    total?: number;
  }>({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  useEffect(() => {
    if (selectedIssue?.issue?.IssueSolutionArchUUID) {
      setLoading(true);
      listIssueAppRels({
        IssueSolutionRelUUID: selectedIssue?.issue?.IssueSolutionArchUUID,
        current: pageInfo.current,
        pageSize: pageInfo.pageSize,
      })
        .then((res) => {
          if (res.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            setDataSource(res.ListIssueAppRels || []);
            setPageInfo((info) => ({
              ...info,
              total: res.Total || 0,
            }));
          }
        })
        .catch((error) => {
          message.error({
            content: error.message,
          });
        })
        .finally(() => {
          setLoading(false);
        });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedIssue?.issue?.IssueSolutionArchUUID, pageInfo.current, pageInfo.pageSize]);

  return (
    <Loading loading={loading}>
      <Table
        records={dataSource}
        columns={columns}
        addons={[
          Table.addons.pageable({
            pageIndex: pageInfo.current,
            pageSize: pageInfo.pageSize,
            recordCount: pageInfo.total || 0,
            onPagingChange: ({ pageIndex, pageSize }) => {
              setPageInfo((info) => ({
                ...info,
                current: pageIndex!,
                pageSize: pageSize!,
              }));
            },
          }),
        ]}
      />
    </Loading>
  );
};

export default AppList;
