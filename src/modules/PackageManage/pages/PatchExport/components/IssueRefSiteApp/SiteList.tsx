import { Button, Justify, Table, message } from '@tencent/tea-component';
import React, { useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { getSiteColumns } from './config';
import { ISelectedIssue } from '../types';
import { ISiteInfo, listIssueRelatedSites } from '@/common/api/patchExport';
import useLookup from '@/common/hookups/useLookup';

export interface IProps {
  selectedIssue: ISelectedIssue;
}

export interface ISiteListRef {
  getSite: () => any;
}

const SiteList: React.ForwardRefRenderFunction<ISiteListRef, IProps> = ({ selectedIssue }, ref) => {
  const [checkedSiteIds, setCheckedSiteIds] = useState<string[]>([]);
  const [records, setRecords] = useState<ISiteInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const { getLookupByCode } = useLookup([]);

  useEffect(() => {
    if (selectedIssue?.issue?.IssueSolutionArchUUID) {
      setLoading(true);
      listIssueRelatedSites({
        IssueSolutionArchUUID: selectedIssue.issue.IssueSolutionArchUUID,
      })
        .then((res) => {
          if (res.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            setRecords(res.SiteInfos || []);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [selectedIssue]);

  const dataSource = useMemo(() => {
    if (records?.length) {
      const dataSource: any[] = [
        {
          ...records[0],
        },
      ];
      if (records.length === 1) {
        dataSource[0].RowSpan = 1;
      } else {
        let startIndex = 0;
        let size = 1;
        let clientName = records[0].ClientName;
        for (let i = 1; i < records.length; i++) {
          dataSource.push(records[i]);
          if (records[i].ClientName === clientName) {
            size += 1;
          } else {
            dataSource[startIndex].RowSpan = size;
            startIndex = i;
            size = 1;
            clientName = records[i].ClientName;
          }
          if (i === records.length - 1) {
            dataSource[startIndex].RowSpan = size;
          }
        }
      }

      return dataSource;
    }
    return [];
  }, [records]);

  const clientCheckedStatus = useMemo(() => {
    const clientInfo: Record<string, any> = {};
    dataSource.forEach((item) => {
      if (!clientInfo[item.ClientName]) {
        clientInfo[item.ClientName] = {
          siteIds: [],
          checkedSiteIds: [],
        };
      }
      if (!item.Available) {
        return;
      }
      clientInfo[item.ClientName].siteIds.push(item.SiteUUID);
      if (checkedSiteIds?.includes(item.SiteUUID)) {
        clientInfo[item.ClientName].checkedSiteIds.push(item.SiteUUID);
      }
    });
    return clientInfo;
  }, [dataSource, checkedSiteIds]);

  const columns = getSiteColumns({
    onCheckClient: handleCheckClient,
    clientCheckedStatus,
    checkedSiteIds,
    onCheckSite: handleCheckSite,
    getLookupByCode,
  });

  useEffect(() => {
    // 如果当前查看的缺陷单状态是全选，则选中所有的局点
    if (selectedIssue.status === 'checked') {
      setCheckedSiteIds(records.filter((item) => item.Available).map((item) => item.SiteUUID));
    } else if (selectedIssue.checkedSiteList?.length) {
      setCheckedSiteIds(selectedIssue.checkedSiteList.map((item) => item.SiteUUID));
    }
  }, [selectedIssue, records]);

  useImperativeHandle(
    ref,
    () => ({
      getSite() {
        const availableSites = dataSource
          .filter((item) => item.Available)
          .map((item) => {
            const site = { ...item };
            delete site.RowSpan;
            return site;
          });

        return {
          checkedSiteList: availableSites.filter((item) => checkedSiteIds.includes(item.SiteUUID)),
          isAllSites: availableSites.length === checkedSiteIds.length,
        };
      },
    }),
    [dataSource, checkedSiteIds],
  );

  function handleCheckClient(checked: boolean, record: any) {
    const clientAllSiteIds = dataSource
      .filter((item) => item.ClientName === record.ClientName && item.Available)
      .map((item) => item.SiteUUID);
    if (checked) {
      setCheckedSiteIds((ids = []) => {
        const newIds = [...ids];
        clientAllSiteIds.forEach((id) => {
          if (!newIds.includes(id)) {
            newIds.push(id);
          }
        });
        return newIds;
      });
    } else {
      setCheckedSiteIds((ids = []) => {
        const newIds = [...ids];
        clientAllSiteIds.forEach((id) => {
          const index = newIds.indexOf(id);
          if (index !== -1) {
            newIds.splice(index, 1);
          }
        });
        return newIds;
      });
    }
  }

  function handleCheckSite(checked: boolean, record: any) {
    if (checked) {
      if (!checkedSiteIds.includes(record.SiteUUID)) {
        setCheckedSiteIds((ids) => [...ids, record.SiteUUID]);
      }
    } else {
      if (checkedSiteIds.includes(record.SiteUUID)) {
        setCheckedSiteIds((ids) => ids.filter((id) => id !== record.SiteUUID));
      }
    }
  }

  function handleSelectAll() {
    setCheckedSiteIds(records.filter((item) => item.Available).map((item) => item.SiteUUID));
  }

  function handleInvertSelect() {
    setCheckedSiteIds((ids) =>
      records.filter((item) => item.Available && !ids.includes(item.SiteUUID)).map((item) => item.SiteUUID),
    );
  }

  return (
    <>
      <Table.ActionPanel>
        <Justify
          left={
            <>
              <Button onClick={handleSelectAll}>全选</Button>
              <Button onClick={handleInvertSelect}>反选</Button>
            </>
          }
        />
      </Table.ActionPanel>
      <Table
        columns={columns}
        records={dataSource}
        addons={[
          Table.addons.mergeable({
            rowSpan: (columnIndex, recordIndex, context) => {
              if (columnIndex === 0) {
                return context.record.RowSpan || 0;
              }
              return 1;
            },
          }),
          Table.addons.scrollable({
            maxHeight: 800,
          }),
          Table.addons.autotip({
            isLoading: loading,
          }),
        ]}
      />
    </>
  );
};

export default React.forwardRef(SiteList);
