import { Tabs, <PERSON>b<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Text } from '@tencent/tea-component';
import React, { useImperativeHandle, useRef, useState } from 'react';
import SiteList, { ISiteListRef } from './SiteList';
import { ISelectedIssue } from '../types';
import AppList from './AppList';

export interface IIssueRefSiteAppProps {
  onConfirm: (params: { siteInfo: any; IssueSolutionArchRelUUID: string }) => void;
}

export interface IIssueRefSiteAppRef {
  show: (record: any) => void;
}

const tabs = [
  {
    id: 'site',
    label: '关联局点',
  },
  {
    id: 'app',
    label: '关联应用',
  },
];

const IssueRefSiteApp: React.ForwardRefRenderFunction<IIssueRefSiteAppRef, IIssueRefSiteAppProps> = (
  { onConfirm },
  ref,
) => {
  const [visible, setVisible] = useState(false);
  const [selectedIssue, setSelectedIssue] = useState<ISelectedIssue>();
  const siteListRef = useRef<ISiteListRef>();

  useImperativeHandle(
    ref,
    () => ({
      show: (record: ISelectedIssue) => {
        setVisible(true);
        setSelectedIssue(record);
      },
    }),
    [],
  );

  function handleCancel() {
    setVisible(false);
  }

  function handleConfirm() {
    const siteInfo = siteListRef.current?.getSite();
    onConfirm({
      siteInfo,
      IssueSolutionArchRelUUID: selectedIssue?.issue.IssueSolutionArchRelUUID,
    });
    setVisible(false);
  }
  return (
    <Drawer
      visible={visible}
      title={
        <Text overflow tooltip>
          selectedIssue?.issue?.Title
        </Text>
      }
      size="l"
      showMask
      onClose={handleCancel}
      footer={
        <div style={{ textAlign: 'center' }}>
          <Button type="primary" style={{ marginRight: 10 }} onClick={handleConfirm}>
            确认
          </Button>
          <Button type="weak" onClick={handleCancel}>
            取消
          </Button>
        </div>
      }
    >
      <Tabs tabs={tabs}>
        <TabPanel id="site">{selectedIssue && <SiteList selectedIssue={selectedIssue} ref={siteListRef} />}</TabPanel>
        <TabPanel id="app">{selectedIssue && <AppList selectedIssue={selectedIssue} />}</TabPanel>
      </Tabs>
    </Drawer>
  );
};

export default React.forwardRef(IssueRefSiteApp);
