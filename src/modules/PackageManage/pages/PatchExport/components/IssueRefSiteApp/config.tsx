import React from 'react';
import { Checkbox, Tag, Tooltip } from '@tencent/tea-component';
import { getAppVersion } from '@/common/utils';

export const getSiteColumns = ({
  onCheckClient,
  onCheckSite,
  clientCheckedStatus,
  checkedSiteIds,
  getLookupByCode,
}: {
  onCheckClient: (checked: boolean, record: any) => void;
  onCheckSite: (checked: boolean, record: any) => void;
  clientCheckedStatus: Record<string, any>;
  checkedSiteIds: string[];
  getLookupByCode: (type: string, code: string) => any;
}) => [
  {
    header: '客户',
    key: 'ClientName',
    width: '30%',
    render(record: any) {
      const props: {
        value?: boolean;
        indeterminate?: boolean;
      } = {};
      const status = clientCheckedStatus[record.ClientName];
      if (status) {
        if (status.checkedSiteIds.length === status.siteIds.length && status.checkedSiteIds.length !== 0) {
          props.value = true;
        } else if (status.checkedSiteIds.length === 0) {
          props.value = false;
        } else {
          props.indeterminate = true;
        }
      }
      return (
        <Checkbox disabled={status.siteIds.length === 0} onChange={(value) => onCheckClient(value, record)} {...props}>
          {record.ClientName}
        </Checkbox>
      );
    },
  },
  {
    header: '局点',
    key: 'SiteName',
    width: '30%',
    render(record: any) {
      const disabled = !record.Available;

      const el = (
        <Checkbox
          disabled={disabled}
          onChange={(value) => onCheckSite(value, record)}
          value={!!checkedSiteIds?.includes(record.SiteUUID) && !disabled}
        >
          {record.SiteName}
        </Checkbox>
      );

      if (disabled) {
        return <Tooltip title={record.Message}>{el}</Tooltip>;
      }
      return el;
    },
  },
  {
    header: '出包状态',
    key: 'Status',
    width: '20%',
    render(record) {
      const { Status } = record;
      const lookup = getLookupByCode('DeliveryStatus', Status);
      if (lookup) {
        return <Tag style={{ color: lookup.Extra?.Color }}>{lookup.Name}</Tag>;
      }
      return Status || '-';
    },
  },
  {
    header: '局点类型',
    key: 'SiteType',
    width: '20%',
  },
];

export const getAppColumns = () => [
  {
    header: '应用名称',
    key: 'ApplicationName',
  },
  {
    header: '制品版本',
    key: 'ReleaseApplicationPackage',
    render(record) {
      return record.ReleaseApplicationPackage ? getAppVersion(record.ReleaseApplicationPackage) : '-';
    },
  },
];
