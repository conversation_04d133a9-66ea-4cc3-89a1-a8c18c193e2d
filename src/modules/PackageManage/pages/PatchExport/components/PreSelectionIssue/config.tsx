import { B<PERSON>ble, Button, Checkbox, ExternalLink, Icon, Tag, Text } from '@tencent/tea-component';
import dayjs from 'dayjs';
import React from 'react';

function getCheckboxProps(status: 'checked' | 'unchecked' | 'indeterminate') {
  const props: {
    value?: boolean;
    indeterminate?: boolean;
  } = {};
  if (status === 'indeterminate') {
    props.indeterminate = true;
  } else {
    props.value = status === 'checked';
  }
  return props;
}

export const getColumns = ({
  onRemove,
  onViewSiteApp,
  getLookupByCode,
  onCheckedAll,
  checkAllStatus,
  onCheckedItem,
}: {
  onRemove: (record: any) => void;
  onViewSiteApp: (record: any) => void;
  getLookupByCode: (type, code) => any;
  onCheckedAll: (checked: boolean) => void;
  checkAllStatus: 'checked' | 'unchecked' | 'indeterminate';
  onCheckedItem: (checked: boolean, record: any) => void;
}) => [
  {
    header: (
      <Checkbox onChange={onCheckedAll} {...getCheckboxProps(checkAllStatus)}>
        缺陷单ID
      </Checkbox>
    ),
    key: 'issue.IssueID',
    width: '10%',
    fixed: 'left',
    render: (record) => (
      <Checkbox {...getCheckboxProps(record.status)} onChange={(value) => onCheckedItem(value, record)}>
        {record.issue.IssueID}
      </Checkbox>
    ),
  },

  {
    header: '缺陷单标题',
    key: 'issue.Title',
    width: '30%',
    render(record) {
      return (
        <a
          onClick={() => {
            window.open(
              `/page/defect_manage/__develop_center_next/iteration_manage/defect/detail?issue_id=${record.issue.IssueID}`,
              '_blank',
            );
          }}
        >
          {record.issue.Title}
        </a>
      );
    },
  },
  {
    header: '解决方案版本',
    key: 'issue.SolutionVersion',
    width: '10%',
  },
  {
    header: '架构',
    key: 'issue.Arch',
    width: '10%',
  },
  {
    key: 'issue.Status',
    header: '缺陷单状态',
    width: '14%',
    render(record) {
      const { Status } = record.issue || {};
      const lookup = getLookupByCode('IssueSolutionArchStatus', Status);
      if (lookup) {
        return <Tag style={{ color: lookup.Extra?.Color }}>{lookup.Name}</Tag>;
      }
      return Status || '-';
    },
  },
  {
    key: 'issue.SeverityLevel',
    header: '严重程度',
    width: '10%',
    render(record) {
      const { SeverityLevel } = record.issue;
      const lookup = getLookupByCode('Severity', SeverityLevel);
      if (lookup) {
        return <Tag style={{ color: lookup.Extra?.Color }}>{lookup.Name}</Tag>;
      }
      return SeverityLevel || '-';
    },
  },
  {
    key: 'Priority',
    header: '优先级',
    width: '10%',
    render(record) {
      const { Priority } = record.issue;
      const lookup = getLookupByCode('Priority', Priority);
      if (lookup) {
        return <Tag style={{ color: lookup.Extra?.Color }}>{lookup.Name}</Tag>;
      }
      return Priority || '-';
    },
  },
  {
    key: 'issue.DeliveryID',
    header: '关联交付单',
    width: '10%',
    render(record) {
      const { DeliveryID } = record.issue || {};
      if (!DeliveryID) {
        return '-';
      }
      const url = `/page/defect_manage/__flow-design/delivery_manage/detail?uuid=${DeliveryID}`;
      return (
        <Text copyable={{ text: url }}>
          <ExternalLink href={url}>点击查看</ExternalLink>
        </Text>
      );
    },
  },
  {
    key: 'issue.OperationSheetUUID',
    header: '关联变更单',
    width: '12%',
    render(record) {
      const { OperationSheetUUID } = record.issue || {};
      if (!OperationSheetUUID) {
        return '-';
      }
      const url = `/page/product-market-jiguang/ops-sheet-manager/parent-sheet-detail?parentSheetId=${OperationSheetUUID}`;
      return (
        <Text copyable={{ text: url }}>
          <ExternalLink href={url}>点击查看</ExternalLink>
        </Text>
      );
    },
  },
  {
    key: 'issue.TAPD',
    header: '关联TAPD',
    width: '12%',
    render(record) {
      const { TAPDUrl } = record.issue || {};
      if (!TAPDUrl) {
        return '-';
      }
      return (
        <Text copyable={{ text: TAPDUrl }}>
          <ExternalLink href={TAPDUrl}>点击查看</ExternalLink>
        </Text>
      );
    },
  },
  {
    key: 'issue.TAPDStatus',
    header: 'TAPD状态',
    width: '12%',
    render(record) {
      const { TAPDStatus } = record.issue || {};
      const lookup = getLookupByCode('IssueStatus', TAPDStatus);
      if (lookup) {
        return <Tag style={{ color: lookup.Extra?.Color }}>{lookup.Name}</Tag>;
      }
      return TAPDStatus || '-';
    },
  },
  {
    key: 'issue.DeliverType',
    header: '出包类型',
    width: '12%',
    render(record) {
      const { DeliverType } = record.issue || {};
      const lookup = getLookupByCode('DeliverType', DeliverType);
      if (lookup) {
        return lookup.Name;
      }
      return DeliverType || '-';
    },
  },
  {
    key: 'issue.PackageStatus',
    width: '14%',
    header: () => (
      <>
        出包状态
        <Bubble content="一个缺陷单可能关联多个交付单，会产生多个出包状态">
          <Icon type="info" />
        </Bubble>
      </>
    ),
    render(record) {
      const { PackageStatus } = record.issue || {};
      const status = PackageStatus ? PackageStatus.split(',') : [];
      if (status.length) {
        return status.map((s) => {
          const lookup = getLookupByCode('DeliveryStatus', s);
          if (lookup) {
            return (
              <Tag key={s} style={{ color: lookup.Extra?.Color, marginRight: 5 }}>
                {lookup.Name}
              </Tag>
            );
          }
          return '';
        });
      }
      return '-';
    },
  },
  {
    key: 'issue.SiteStatus',
    header: '出包局点状态',
    width: 350,
    render: (record) => {
      const packageSiteStatusCount = record?.issue?.SiteStatusCount || {};
      const {
        PackingCount = 0,
        PackSuccessCount = 0,
        TotalCount = 0,
        ToPackCount = 0,
        PackFailedCount = 0,
      } = packageSiteStatusCount;
      return `总计(${TotalCount})/待出包(${ToPackCount})/出包中(${PackingCount})/出包成功(${PackSuccessCount})/出包失败(${PackFailedCount})`;
    },
  },
  {
    key: 'issue.CreatedAt',
    header: '创建时间',
    width: '15%',
    render(record) {
      const { CreatedAt } = record.issue || {};
      if (CreatedAt) {
        return dayjs(CreatedAt).format('YYYY-MM-DD HH:mm:ss');
      }
      return '-';
    },
  },
  {
    header: '操作',
    key: 'operation',
    width: '15%',
    fixed: 'right',
    render(record) {
      return (
        <>
          <Button type="link" onClick={() => onViewSiteApp(record)}>
            关联局点/应用
          </Button>
          <Button type="link" onClick={() => onRemove(record)}>
            移除
          </Button>
        </>
      );
    },
  },
];
