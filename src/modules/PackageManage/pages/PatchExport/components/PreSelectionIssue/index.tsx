// import { IPropsBug } from '@/common/api/interationManage';
import { List, Table } from '@tencent/tea-component';
import React, { useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { getColumns } from './config';
import useLookup from '@/common/hookups/useLookup';
import { ISelectedIssue } from '../types';
import IssueRefSiteApp, { IIssueRefSiteAppRef } from '../IssueRefSiteApp';
import { IIssueProblems, ISiteIssueSequenceParams } from '@/common/api/patchExport';
import { TcsCard } from '@tencent/tcs-component';

// import styles from './index.module.less';

export interface IPreSelectionIssue {
  preSelectionIssues: any[];
  onRemoveIssue: (IssueSolutionArchUUID: string) => void;
  issueProblems: IIssueProblems[];
}

// 获取用户勾选的所有缺陷单信息
export interface IPreSelectionIssueRef {
  getSelectionIssues: () => ISiteIssueSequenceParams[];
}

const PreSelectionIssue: React.ForwardRefRenderFunction<IPreSelectionIssueRef, IPreSelectionIssue> = (
  { preSelectionIssues, onRemoveIssue, issueProblems },
  ref,
) => {
  const { getLookupByCode } = useLookup([]);

  const [selectedIssues, setSelectedIssues] = useState<ISelectedIssue[]>([]);
  const selectedIssuesRef = useRef<ISelectedIssue[]>([]);
  const issueRefSiteAppRef = useRef<IIssueRefSiteAppRef>(null);
  const [pageInfo, setPageInfo] = useState({
    pageSize: 10,
    pageIndex: 1,
  });

  useEffect(() => {
    const selectedIssueIds = selectedIssuesRef.current.map((item) => item.issue.IssueSolutionArchUUID);
    let newSelectedIssues = [...selectedIssuesRef.current];
    const problemObj: Record<string, IIssueProblems['Problems']> = {};
    issueProblems?.forEach((item) => {
      problemObj[item.IssueSolutionArchUUID] = item.Problems;
    });
    preSelectionIssues.forEach((item) => {
      if (!selectedIssueIds.includes(item.IssueSolutionArchUUID)) {
        selectedIssueIds.push(item.IssueSolutionArchUUID);
        newSelectedIssues.push({
          issue: item,
          status: 'checked',
          // 缺陷单是否存在问题
          problems: problemObj[item.IssueSolutionArchUUID],
        });
      }
    });
    const preSelectionIssueIds = preSelectionIssues.map((item) => item.IssueSolutionArchUUID);
    newSelectedIssues = newSelectedIssues.filter((item) =>
      preSelectionIssueIds.includes(item.issue.IssueSolutionArchUUID),
    );
    selectedIssuesRef.current = newSelectedIssues;
    setSelectedIssues(newSelectedIssues);
    setPageInfo((info) => ({
      ...info,
      pageIndex: 1,
    }));
  }, [preSelectionIssues, issueProblems]);

  const checkAllStatus = useMemo(() => {
    let hasIndeterminate = false;
    let hasUnchecked = false;
    let hasChecked = false;
    for (const issue of selectedIssues) {
      if (issue.status === 'indeterminate') {
        hasIndeterminate = true;
        break;
      }
      if (issue.status === 'checked') {
        hasChecked = true;
      }
      if (issue.status === 'unchecked') {
        hasUnchecked = true;
      }
    }
    // 如果有半选的或者同时包含全选和未选的，都是半选状态
    if (hasIndeterminate || (hasChecked && hasUnchecked)) {
      return 'indeterminate';
    }
    if (hasChecked) {
      return 'checked';
    }
    return 'unchecked';
  }, [selectedIssues]);

  const columns = getColumns({
    onRemove: handleRemove,
    onViewSiteApp: handleViewSiteApp,
    getLookupByCode,
    onCheckedAll: handleCheckedAll,
    checkAllStatus,
    onCheckedItem: handleCheckedItem,
  });

  // 移除
  function handleRemove(record: any) {
    const selectedIssues = selectedIssuesRef.current.filter(
      (item) => record.issue.IssueSolutionArchUUID !== item.issue.IssueSolutionArchUUID,
    );
    setSelectedIssues(selectedIssues);
    selectedIssuesRef.current = selectedIssues;
    onRemoveIssue(record.issue.IssueSolutionArchUUID);
  }

  function handleViewSiteApp(record) {
    issueRefSiteAppRef.current?.show(record);
  }

  function handleCheckedAll(checked: boolean) {
    const newSelectedIssues = selectedIssuesRef.current.map((issue) => {
      const issueData: ISelectedIssue = {
        ...issue,
        status: checked ? 'checked' : 'unchecked',
      };
      return issueData;
    });
    setSelectedIssues(newSelectedIssues);
    selectedIssuesRef.current = newSelectedIssues;
  }

  function handleCheckedItem(checked: boolean, record: any) {
    const newSelectedIssues = selectedIssuesRef.current.map((issue) => {
      if (issue.issue.IssueSolutionArchUUID === record.issue.IssueSolutionArchUUID) {
        const newIssue: ISelectedIssue = {
          ...issue,
          status: checked ? 'checked' : 'unchecked',
          checkedSiteList: undefined,
        };
        return newIssue;
      }
      return issue;
    });
    setSelectedIssues(newSelectedIssues);
    selectedIssuesRef.current = newSelectedIssues;
  }

  function handleChangeSiteConfirm({ siteInfo, IssueSolutionArchRelUUID }) {
    const newSelectedIssues = selectedIssuesRef.current.map((issue) => {
      if (issue.issue.IssueSolutionArchRelUUID === IssueSolutionArchRelUUID) {
        const issueData: ISelectedIssue = {
          ...issue,
          checkedSiteList: siteInfo?.checkedSiteList,
        };
        if (!siteInfo?.checkedSiteList?.length) {
          issueData.status = 'unchecked';
        } else if (siteInfo.isAllSites) {
          issueData.status = 'checked';
        } else {
          issueData.status = 'indeterminate';
        }
        return issueData;
      }
      return issue;
    });
    setSelectedIssues(newSelectedIssues);
    selectedIssuesRef.current = newSelectedIssues;
  }

  useImperativeHandle(
    ref,
    () => ({
      getSelectionIssues() {
        return selectedIssuesRef.current
          .filter((item) => item.status !== 'unchecked')
          .map((item) => ({
            IssueSolutionArchUUID: item.issue.IssueSolutionArchUUID,
            IsAllSites: item.status === 'checked',
            SiteSelections: item.checkedSiteList,
          }));
      },
    }),
    [],
  );

  return (
    <TcsCard title="预选缺陷" subTitle="(支持调整勾选缺陷单及关联局点信息)" bordered>
      <Table
        records={selectedIssues}
        columns={columns}
        recordKey={(record) => record?.issue?.IssueSolutionArchUUID}
        addons={[
          Table.addons.pageable({
            pageSize: pageInfo.pageSize,
            pageIndex: pageInfo.pageIndex,
            onPagingChange: (query) => {
              setPageInfo((info) => ({
                ...info,
                ...query,
              }));
            },
          }),
          Table.addons.scrollable({
            maxHeight: 'calc(100vh - 400px)',
            minWidth: 2100,
          }),
          Table.addons.autotip({
            emptyText: '请点击左上角添加缺陷按钮选择要出包的缺陷',
          }),
          Table.addons.rowtooltip({
            tooltip: (record) => {
              if (record.problems?.length) {
                return (
                  <div style={{ width: 500 }}>
                    <span style={{ margin: '10px 0 20px 0', color: '#ffffff' }}>当前缺陷存在以下问题:</span>
                    <List type="number" style={{ color: '#ffffff' }}>
                      {record.problems.map((item) => (
                        <List.Item key={item.Message}>{item.Message}</List.Item>
                      ))}
                    </List>
                  </div>
                );
              }
              return null;
            },
          }),

          Table.addons.injectable({
            row: (props, context) => {
              // 如果当前缺陷单存在问题，则将行文字显示为红色
              if (context.record.problems?.length) {
                return {
                  style: {
                    backgroundColor: '#ffb9b9',
                  },
                };
              }
              return {};
            },
          }),
        ]}
      />
      <IssueRefSiteApp onConfirm={handleChangeSiteConfirm} ref={issueRefSiteAppRef} />
    </TcsCard>
  );
};

export default React.forwardRef(PreSelectionIssue);
