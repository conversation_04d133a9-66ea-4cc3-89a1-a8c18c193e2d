import { DefectManagementRoutePath } from '@/common/routePath';
import React from 'react';
import { Route } from 'react-router-dom';
import Detail from './pages/Detail';
import List from './pages/List';
import ReleaseVersion from './pages/ReleaseVersionDetail';
import ProductLevelApproval from './pages/ReleaseVersionDetail/components/ReleaseVersionFlow/components/DeveloperApproval/components/ProductLevelApproval';

const RequirementIterationMange = () => {
  const routesConfig = [
    {
      path: DefectManagementRoutePath.REQUIREMENT_ITERATION_LIST_PAGE,
      component: List,
      exact: true,
    },
    {
      path: DefectManagementRoutePath.REQUIREMENT_ITERATION_DETAIL_PAGE,
      component: Detail,
      exact: true,
    },
    {
      path: DefectManagementRoutePath.REQUIREMENT_ITERATION_REAlEASE_VERSION_PAGE,
      component: ReleaseVersion,
      exact: true,
    },
    {
      path: DefectManagementRoutePath.REQUIREMENT_ITERATION_REAlEASE_VERSION_PRODUCT_PAGE,
      component: ProductLevelApproval,
      exact: true,
    },
  ];

  return (
    <>
      {routesConfig.map((item, index) => (
        <Route key={index} component={item.component} path={item.path} exact={item.exact} />
      ))}
    </>
  );
};

export default RequirementIterationMange;
