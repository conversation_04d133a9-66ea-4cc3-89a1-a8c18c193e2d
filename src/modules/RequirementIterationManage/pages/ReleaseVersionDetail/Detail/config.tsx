import { getEllipsisBubbleText } from '@/common/utils/tools';

const getBaseColumns = () => [
  {
    title: '发布说明',
    dataIndex: 'Description',
    key: 'Description',
  },
  {
    title: '解决方案版本',
    dataIndex: ['SolutionVersion', 'Code'],
    key: 'SolutionVersion',
  },
  {
    title: '架构',
    dataIndex: 'Archs',
    key: 'Archs',
  },
  {
    title: '发布产品列表',
    dataIndex: 'ReleaseProducts',
    render(text, record) {
      return getEllipsisBubbleText(record?.ReleaseProducts?.map((item) => item?.Product?.Name).join(';')) || '-';
    },
  },
  {
    title: '审批负责人',
    dataIndex: 'Creator',
  },
  {
    title: '审批管理员',
    dataIndex: 'Owners',
    render: (text, record) => record.Owners?.Owners?.map((item) => item).join(';') || '-',
  },

  {
    title: '发起时间',
    dataIndex: 'CreatedAt',
    valueType: 'dateTime',
  },

  {
    title: '文档负责人',
    dataIndex: ['Owners', 'DocumentOwner'],
    key: 'DocumentOwner',
  },

  {
    title: '出包负责人',
    dataIndex: ['Owners', 'CMO'],
    key: 'CMO',
  },

  {
    title: '部署验证负责人',
    dataIndex: ['Owners', 'ReleaseTester'],
    key: 'ReleaseTester',
  },
  {
    title: '总监审批负责人',
    dataIndex: ['Owners', 'Manager'],
    key: 'Manager',
  },
  {
    title: 'GM审批负责人',
    dataIndex: ['Owners', 'GM'],
    key: 'GM',
  },
];

export default getBaseColumns;
