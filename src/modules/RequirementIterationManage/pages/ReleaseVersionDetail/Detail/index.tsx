import {
  GetVersionReleaseApprovalSheetIProps,
  GetVersionReleaseApprovalSheetWithDetail,
  RequirementIterationManage,
  UpdateVersionReleaseApprovalStatus,
} from '@/common/api/requirementIteration';
import ReleaseVersionContext from '@/common/context/release';
import { getUrlParams } from '@/common/utils';
import { TcsCard, TcsDescriptions, TcsLayout, TcsPopConfirm, TcsSpin } from '@tencent/tcs-component';
import {
  Alert,
  Bubble,
  Button,
  Col,
  ExternalLink,
  Icon,
  MetricsBoard,
  Row,
  Text,
  Tooltip,
  message,
} from '@tencent/tea-component';
import React, { useContext, useEffect, useState } from 'react';
import CreateReleaseVersion from '../../Detail/components/CreateReleaseVersion';
import ReleaseVersionFlow from '../components/ReleaseVersionFlow';
import { StageStatusType } from '../constants';
import getBaseColumns from './config';
import styles from './index.module.scss';

const ReleaseVersionDetail = () => {
  const { history } = TcsLayout.useHistory();
  //   const columns: any = getColumns({});
  const urlParams = getUrlParams();
  const [baseData, setBaseData] = useState<any>();
  const [approvalStage, setApprovalStage] = useState<GetVersionReleaseApprovalSheetIProps.ApprovalStage[]>();
  const [spinning, setSpinning] = useState<boolean>(false);
  const { updateData, getApprovalStages, isManage, baseInfo } = useContext(ReleaseVersionContext);
  const [statisticalReport, setStatisticalReport] = useState<GetVersionReleaseApprovalSheetIProps.StatisticalReport>();
  const [editModalVisible, setEditModalVisible] = useState<boolean>(false);
  // 检查当前的流程是都已经在产品或以后阶段了
  // const [checkCurrentStageToProduct, setCheckCurrentStageToProduct] = useState<boolean>(false);

  const boardData = [
    {
      title: '功能需求',
      value: `${statisticalReport?.StoryReport?.Total || 0}`,
    },
    {
      title: '已完成需求总数',
      value: `${statisticalReport?.StoryReport?.ClosedCount || 0}`,
    },
    {
      title: '免测功能总数',
      value: `${statisticalReport?.StoryReport?.TestFreeCount || 0}/${statisticalReport?.StoryReport?.Total || 0}`,
    },
    {
      title: '免测功能占比',
      value: `${statisticalReport?.StoryReport?.TestFreeRatio || 0}`,
    },
  ];
  const boardIssueData = [
    {
      title: '缺陷总数',
      value: `${statisticalReport?.BugReport?.Total || 0}`,
    },
    {
      title: '已关闭缺陷数',
      value: `${statisticalReport?.BugReport?.TotalClosedCount || 0}`,
    },
    {
      title: '缺陷关闭率',
      value: `${
        statisticalReport?.BugReport?.TotalClosedRatio !== 0
          ? statisticalReport?.BugReport?.TotalClosedRatio.toFixed(2)
          : 0 || 0
      }%`,
    },
    {
      title: '致命缺陷',
      value: `${statisticalReport?.BugReport?.ClosedFatalCount || 0}/${statisticalReport?.BugReport?.FatalCount || 0}`,
    },
  ];
  const boardgongdanData = [
    {
      title: '严重缺陷',
      value: `${statisticalReport?.BugReport?.ClosedSeriousCount || 0}/${
        statisticalReport?.BugReport?.SeriousCount || 0
      }`,
    },
    {
      title: '安全漏洞总数',
      value: `${statisticalReport?.BugReport?.SecurityCount || 0}`,
    },
    {
      title: '已修复漏洞数',
      value: `${statisticalReport?.BugReport?.ClosedSecurityCount || 0}`,
    },

    {
      title: '安全漏洞修复率',
      value: `${
        statisticalReport?.BugReport?.SecurityClosedRatio !== 0
          ? statisticalReport?.BugReport?.SecurityClosedRatio.toFixed(2)
          : 0 || 0
      }%`,
    },
  ];
  const fetchData = async () => {
    setSpinning(true);
    try {
      const { GetVersionReleaseApprovalSheet, Error } = await GetVersionReleaseApprovalSheetWithDetail({
        UUID: urlParams.approval_id,
      });
      if (Error) {
        return message.error({ content: Error.Message });
      }

      setSpinning(false);
      setBaseData(GetVersionReleaseApprovalSheet);

      // const filterProductStageOrder: number =
      //   GetVersionReleaseApprovalSheet?.ApprovalStages?.find((item) => item.Name === 'Product')?.StageOrder || 0;
      // // 拿到当前阶段为驳回或者正在进行中阶段
      // const checkCurrentStageOrder: number =
      //   GetVersionReleaseApprovalSheet?.ApprovalStages?.find(
      //     (item) => item.Status === StageStatusType.beenRejected || item.Status === StageStatusType.processing,
      //   )?.StageOrder || 0;
      // if (filterProductStageOrder >= checkCurrentStageOrder) {
      //   setCheckCurrentStageToProduct(false);
      // } else {
      //   setCheckCurrentStageToProduct(true);
      // }
      setStatisticalReport(GetVersionReleaseApprovalSheet.StatisticalReport);
      setApprovalStage(GetVersionReleaseApprovalSheet?.ApprovalStages);
      getApprovalStages(GetVersionReleaseApprovalSheet);
    } catch (error) {
      console.error(error);
    }
  };
  const handleEdit = () => {
    setEditModalVisible(true);
  };
  const handleCancelOrReStart = async (value) => {
    try {
      const { Error, Message } = await UpdateVersionReleaseApprovalStatus({
        Status: value,
        ReleaseApprovalSheetUUID: urlParams.approval_id,
      });
      if (Error) {
        return message.error({ content: Error.Message });
      }
      message.success({ content: Message });
      fetchData();
    } catch (error) {
      console.error(error);
    }
  };

  const handleUpdateReport = () => {
    setSpinning(true);
    RequirementIterationManage.UpdateVersionReleaseStatisticalReport({
      ReleaseApprovalSheetUUID: urlParams.approval_id,
    })
      .then(({ StatisticalReport }) => {
        setStatisticalReport(StatisticalReport);
      })
      .finally(() => {
        setSpinning(false);
      });
  };

  useEffect(() => {
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [updateData]);

  return (
    <TcsSpin spinning={spinning}>
      <TcsLayout
        title="版本发布"
        history={history}
        customizeCard
        operation={
          <Text theme="warning" style={{ fontSize: 12 }}>
            当前发布评审功能为beta版本，若有疑问和建议，请联系georgehli、wentinglliu。
            <ExternalLink
              href="https://jiguang.woa.com/page/tcsc_document/142293638669828096"
              style={{ marginRight: 10 }}
            >
              操作手册
            </ExternalLink>
          </Text>
        }
      >
        {baseInfo?.Status === StageStatusType.approved ? (
          <Alert type="success" style={{ marginTop: 8 }}>
            当前发布评审流程已全部完成，若对审批流程内容有任何疑问可联系审批负责人 @{baseInfo?.Creator}
          </Alert>
        ) : (
          <Alert>本次版本发布审批流程有任何疑问可联系审批负责人 @{baseInfo?.Creator}。</Alert>
        )}
        <TcsDescriptions
          collapsible={true}
          title="基本信息"
          cardBordered={false}
          column={3}
          // @ts-ignore
          columns={getBaseColumns()}
          dataSource={baseData}
          extra={
            <>
              {baseData?.Status === StageStatusType.approved || (
                <>
                  <TcsPopConfirm
                    title={baseData?.Status === StageStatusType.canceled ? '确定要重新发布？' : '确定要取消发布？'}
                    onConfirm={() =>
                      handleCancelOrReStart(
                        baseData?.Status === StageStatusType.canceled
                          ? StageStatusType.processing
                          : StageStatusType.canceled,
                      )
                    }
                  >
                    <Button type="error" disabled={!isManage}>
                      <Bubble content={isManage ? '' : '您不是审批管理员，无权限操作'}>
                        {baseData?.Status === StageStatusType.canceled ? '重新发布' : '取消发布'}
                      </Bubble>
                    </Button>
                  </TcsPopConfirm>
                  <Tooltip title={isManage ? '' : '您不是审批管理员，无权限操作'}>
                    <Button type="primary" onClick={handleEdit} disabled={!isManage}>
                      编辑
                    </Button>
                  </Tooltip>
                </>
              )}
            </>
          }
        />
        <TcsDescriptions
          title="功能统计信息"
          extra={
            <Button type="link" onClick={handleUpdateReport}>
              更新数据
              <Icon type="refresh" />
            </Button>
          }
          cardBordered={false}
          column={4}
          collapsible={true}
        >
          <Row showSplitLine>
            {boardData?.map((item) => (
              <Col key={item.title}>
                <MetricsBoard title={item.title} value={item.value} key={item.title} className={styles.board} />
              </Col>
            ))}
          </Row>

          <Row showSplitLine>
            {boardIssueData?.map((item) => (
              <Col key={item.title}>
                <MetricsBoard title={item.title} value={item.value} key={item.title} className={styles.board} />
              </Col>
            ))}
          </Row>

          <Row showSplitLine>
            {boardgongdanData?.map((item) => (
              <Col key={item.title}>
                <MetricsBoard title={item.title} value={item.value} key={item.title} className={styles.board} />
              </Col>
            ))}
          </Row>
        </TcsDescriptions>
        <TcsCard>
          {baseData?.Status === StageStatusType.canceled && (
            <Alert type="warning">已取消本次发布，发布流程已不可操作，若想重新发布，请点击【重新发布】按钮！</Alert>
          )}
          <ReleaseVersionFlow
            approvalId={urlParams.approval_id}
            approvalStage={approvalStage}
            iterationUUID={urlParams.iteration_id}
          />
        </TcsCard>
        <CreateReleaseVersion
          isVisible={editModalVisible}
          onClose={() => setEditModalVisible(false)}
          record={baseData}
          // checkCurrentStageToProduct={checkCurrentStageToProduct}
        />
      </TcsLayout>
    </TcsSpin>
  );
};

export default ReleaseVersionDetail;
