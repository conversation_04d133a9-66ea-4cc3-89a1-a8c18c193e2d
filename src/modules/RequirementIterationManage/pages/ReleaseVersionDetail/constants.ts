export const NowStageNameType = {
  WorkItemRange: '选择功能列表',
  Document: '文档负责人审批',
  ProductManager: '产品经理审批',
  Product: '产品负责人审批',
  ProductTester: '产品测试审批',
  ProductDeveloper: '产品研发审批',
  IntegrationTester: '集测负责人审批',
  ProductDirector: '产品总监审批',
  CMO: '出包负责人审批',
  ReleaseTester: '部署验证审批',
  Manager: '总监审批',
  GM: 'GM审批',
};

export enum ProductLevelStepName {
  ProductConfirm = 'ProductConfirm',
  ProductManager = 'ProductManager',
  ProductTester = 'ProductTester',
  ProductDeveloper = 'ProductDeveloper',
  ProductDirector = 'ProductDirector',
  // 新增为了判断是不是审批详情
  All = 'all',
}

export const ProductLevelStageNameOptions = [
  {
    value: 'ProductConfirm',
    label: '选择发布功能',
  },
  {
    value: 'ProductManager',
    label: '产品经理',
  },
  {
    value: 'ProductTester',
    label: '测试负责人',
  },
  {
    value: 'ProductDeveloper',
    label: '研发责任人',
  },
  {
    value: 'ProductDirector',
    label: '产品总监',
  },
];

export enum ManagerStepName {
  Manager = 'Manager',
  GM = 'GM',
}
// 产品总监二次审批的Type
export enum productDirectorApprovalType {
  agree = 'agree',
  disagree = 'disagree',
  reject = 'rejected',
}

export enum StageStatusType {
  processing = 'processing',
  waiting = 'waiting',
  rejected = 'rejected',
  beenRejected = 'beenRejected',
  approved = 'approved',
  skipped = 'skipped',
  canceled = 'canceled',
  doubleConfirmation = 'doubleConfirmation',
  denied = 'denied',
}

export enum ApprovalResultType {
  approval = 'approval',
  disApproval = 'disApproval',
  undeveloped = 'undeveloped',
}

export enum ApprovalLevelType {
  global = 'global',
  product = 'product',
}

export const BranchType = {
  release: '正式',
  site: '项目定制',
  test: '测试',
};
export enum ArchType {
  arm = 'rhel.arm64',
  amd = 'rhel.amd64',
}

export const SELF_SECURITYCECK_URL =
  'registry.aurora.tencent.com/packages/0/00/all/1711443747/%E9%99%84%E4%BB%B6%EF%BC%9A%E4%BA%A7%E5%93%81%E5%AE%89%E5%85%A8%E8%AF%84%E4%BC%B0%E8%A1%A8.xlsx?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKID88vVSJwMEuNcliT0JSP9ex2xf2B8sQjn%2F20240326%2Fdefault%2Fs3%2Faws4_request&X-Amz-Date=20240326T090227Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=64d23eda0d1b8b315066060a715a5fddc6a1614c23eb7689fd39e335a20066f4';

export const URL_CHECKOUT =
  /https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._+~#=]{2,256}\.[a-z]{2,6}\b(?:[-a-zA-Z0-9@:%_+.~#?&//=]*)/;

export const TCE_SPACE = 'b4e51313d53b44a5b5f5d04caaab717d';
