import { ChangeVersionApprovalTaskOwner } from '@/common/api/requirementIteration';
import ReleaseVersionContext from '@/common/context/release';
import { TcsForm, TcsFormStaffSelect, TcsModal } from '@tencent/tcs-component';
import { message } from '@tencent/tea-component';
import React, { useContext, useMemo, useRef, useState } from 'react';
import { NowStageNameType, ProductLevelStepName } from '../../constants';
export interface IProps {
  isVisible: boolean;
  onClose: () => void;
  nowStageName: string;
  taskId: string;
  nowOwners?: string;
}
const ChangeApprovalStageOwner = ({ isVisible, onClose, nowStageName, taskId, nowOwners }) => {
  const [form] = TcsForm.useForm();
  const formRef = useRef<any>();
  const { handleUpdateSuccess } = useContext(ReleaseVersionContext);
  const [loading, setLoading] = useState<boolean>(false);

  const handleChangeOwner = () => {
    formRef.current?.validateFields().then((formData) => {
      setLoading(true);
      ChangeVersionApprovalTaskOwner({
        ApprovalStepUUID: taskId,
        Owners: nowStageName === ProductLevelStepName.ProductDeveloper ? formData?.Owners?.join(';') : formData?.Owners,
      })
        .then((res) => {
          if (res.Error) {
            // handleUpdateSuccess();
            return message.error({ content: res.Error.Message });
          }
          message.success({ content: res.Message });
          handleCancel();
          setLoading(false);
          handleUpdateSuccess();
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };
  const handleCancel = () => {
    formRef?.current?.resetFields();
    onClose();
  };
  const productDeveloperOwners = useMemo(() => {
    if (nowOwners) {
      return nowOwners?.split(';');
    }
    return [];
  }, [nowOwners]);
  return (
    <TcsModal
      title={`${NowStageNameType[nowStageName]}`}
      visible={isVisible}
      onCancel={handleCancel}
      onOk={handleChangeOwner}
      confirmLoading={loading}
      destroyOnClose
    >
      <TcsForm form={form} formRef={formRef}>
        <TcsFormStaffSelect
          label={`${NowStageNameType[nowStageName]}`}
          name="Owners"
          rules={[
            {
              required: true,
              message: '请选择审批人员',
            },
          ]}
          initialValue={nowStageName === ProductLevelStepName.ProductDeveloper ? productDeveloperOwners : ''}
          fieldProps={{ multiple: nowStageName === ProductLevelStepName.ProductDeveloper }}
        />
      </TcsForm>
    </TcsModal>
  );
};
export default ChangeApprovalStageOwner;
