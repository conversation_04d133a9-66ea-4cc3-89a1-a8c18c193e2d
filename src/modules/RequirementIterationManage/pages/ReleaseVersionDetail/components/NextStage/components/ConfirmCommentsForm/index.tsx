import { TcsForm, TcsFormTextArea, TcsModal } from '@tencent/tcs-component';
import React from 'react';
export interface IProps {
  isVisible: boolean;
  onClose: () => void;
  handleConfirm: (formData) => void;
  confirmLoading: boolean;
}
const ConfirmCommentsForm = ({ isVisible, onClose, handleConfirm, confirmLoading }: IProps) => {
  const [form] = TcsForm.useForm();
  const handleCancel = () => {
    onClose?.();
  };
  const handleConfirmForm = () => {
    const formData = form.getFieldsValue();
    handleConfirm(formData);
  };
  return (
    <TcsModal
      visible={isVisible}
      title="审批通过"
      onCancel={handleCancel}
      onOk={handleConfirmForm}
      confirmLoading={confirmLoading}
    >
      <TcsForm form={form}>
        <TcsFormTextArea
          label="通过说明"
          name="Comments"
          placeholder="请输入审批通过说明"
          fieldProps={{ size: 'full' }}
        />
      </TcsForm>
    </TcsModal>
  );
};
export default ConfirmCommentsForm;
