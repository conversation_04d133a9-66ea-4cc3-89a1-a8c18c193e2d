import { FinishApprovalTask } from '@/common/api/requirementIteration';
import ReleaseVersionContext from '@/common/context/release';
import { TcsButton, TcsPopConfirm } from '@tencent/tcs-component';
import { message } from '@tencent/tea-component';
import React, { useContext, useRef, useState } from 'react';
import { NowStageNameType, StageStatusType } from '../../constants';
import { ConclusionType, checkFormData } from '../../utils';
import ChangeApprovalStageOwner from '../ChangeApprovalStageOwner';
import RejectModal from '../RejectModal';
import ConfirmCommentsForm from './components/ConfirmCommentsForm';
export interface IProps {
  isReject?: boolean;
  changeOwner?: boolean;
  NowStageName: string;
  formRef?: any;
  extraInfo?: any;
  isApproval?: boolean;
  leaderApproval?: boolean;
}
const NextStage: React.FC<IProps> = ({
  isReject = true,
  NowStageName,
  changeOwner = true,
  formRef,
  extraInfo,
  isApproval = true,
  leaderApproval = false,
}) => {
  const [rejectVisible, setRejectVisible] = useState<boolean>(false);
  const [approvalVisible, setApprovalVisible] = useState<boolean>(false);
  const [confirmVisible, setConfirmVisible] = useState<boolean>(false);
  const { handleUpdateSuccess, stageInfo, baseInfo } = useContext(ReleaseVersionContext);
  const commentsRef = useRef<string>('');
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  const handleApproved = async () => {
    try {
      const getParams = async () => {
        if (formRef?.current) {
          const values = await formRef.current.validateFields();
          return extraInfo ? { ...values, ...extraInfo } : values;
        }
        return extraInfo || null;
      };
      const params = await getParams();
      if (!params) return;
      if (NowStageName === NowStageNameType.CMO && Array.isArray(extraInfo) && extraInfo.length === 0) {
        return message.error({ content: '请选择制品Tag!' });
      }
      handleNextStage(params);
    } catch (error) {
      console.error('Form validation failed:', error);
    }
  };
  const handleConfirm = (value) => {
    commentsRef.current = value?.Comments;
    handleNextStage(extraInfo);
  };
  const handleNextStage = (values) => {
    const checkValues = checkFormData(values);
    const params =
      checkValues === ConclusionType.ReleaseWithInjuries
        ? {
            ...values,
            RiskAssessmentResult: {
              Conclusion: values?.Conclusion,
              ScopeOfInfluence: values?.ScopeOfInfluence,
            },
          }
        : values;
    setConfirmLoading(true);
    FinishApprovalTask({
      ApprovalStepUUID: stageInfo?.UUID || '',
      Status: 'approved',
      Comments: commentsRef.current,
      Content: params,
    })
      .then(({ Error, Message }) => {
        if (Error) {
          return message.error({ content: Error.Message });
        }
        message.success({ content: Message });
        handleUpdateSuccess();
      })
      .catch((err) => {
        console.error(err);
      })
      .finally(() => {
        setConfirmVisible(false);
        setConfirmLoading(false);
      });
  };

  const handleReject = () => {
    setRejectVisible(true);
  };

  const handleChangeOwner = () => {
    setApprovalVisible(true);
  };

  return (
    <div>
      {stageInfo?.Status === StageStatusType.processing && baseInfo?.Status !== StageStatusType.canceled && (
        <>
          <hr />
          <section style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            {changeOwner && <TcsButton onClick={handleChangeOwner}>转交审批人</TcsButton>}
            {isApproval && (
              <div>
                {leaderApproval ? (
                  <TcsButton type="primary" style={{ marginLeft: 10 }} onClick={() => setConfirmVisible(true)}>
                    确认通过
                  </TcsButton>
                ) : (
                  <TcsPopConfirm
                    title="请仔细检查审批内容，确认进入下一步审批？"
                    onConfirm={handleApproved}
                    disabled={!isApproval}
                  >
                    <TcsButton type="primary" style={{ marginLeft: 10 }} disabled={!isApproval}>
                      确认通过
                    </TcsButton>
                  </TcsPopConfirm>
                )}
              </div>
            )}

            {isReject && (
              <TcsButton type="primary" style={{ marginLeft: 10 }} onClick={handleReject}>
                驳回
              </TcsButton>
            )}
          </section>

          <RejectModal
            isVisible={rejectVisible}
            onClose={() => setRejectVisible(false)}
            NowStageName={NowStageName}
            nowStageContent={formRef ? formRef?.current?.getFieldsValue() : extraInfo}
          />
          <ChangeApprovalStageOwner
            isVisible={approvalVisible}
            onClose={() => setApprovalVisible(false)}
            nowStageName={NowStageName}
            taskId={stageInfo?.UUID}
            nowOwners={stageInfo?.Owners}
          />
          <ConfirmCommentsForm
            isVisible={confirmVisible}
            onClose={() => setConfirmVisible(false)}
            handleConfirm={handleConfirm}
            confirmLoading={confirmLoading}
          />
        </>
      )}
    </div>
  );
};
export default NextStage;
