.stepper {
  :global {
    .tea-step__item {
      padding-right: 60px !important;

      .tea-step__arrow {
        width: 0 !important
      }

      .tea-step__num::before {
        color: #fff !important;
      }

      .approved {
        background-color: #0abf5b !important;
        border-color: #0abf5b !important;
      }

      .processing {
        background-color: #006eff !important;
        border-color: #006eff !important;
      }

      .rejected {
        background-color: #ff7c2d !important;
        border-color: #ff7c2d !important;
      }

      .beenRejected {
        background-color: #e54545 !important;
        border-color: #e54545 !important;

      }

      .denied {
        background-color: #e54545 !important;
        border-color: #e54545 !important;
      }


      .waiting::before {
        color: #002da0 !important;

      }


    }

    // .is-current {
    //   .tea-step__num::before {
    //     color: #002da0 !important;
    //   }
    // }

  }
}