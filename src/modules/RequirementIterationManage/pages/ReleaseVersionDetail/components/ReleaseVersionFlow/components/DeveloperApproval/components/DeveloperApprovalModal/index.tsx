import { FinishApprovalTask } from '@/common/api/requirementIteration';
import { DefectManagementRoutePath } from '@/common/routePath';
import UploadField from '@/modules/PatchManage/pages/Detail/components/UploadField';
import {
  ApprovalResultType,
  ProductLevelStepName,
  StageStatusType,
  TCE_SPACE,
  URL_CHECKOUT,
  productDirectorApprovalType,
} from '@/modules/RequirementIterationManage/pages/ReleaseVersionDetail/constants';
import { ConclusionType, checkFormData } from '@/modules/RequirementIterationManage/pages/ReleaseVersionDetail/utils';
import {
  TcsButton,
  TcsCard,
  TcsForm,
  TcsFormDatePicker,
  TcsFormRadio,
  TcsFormText,
  TcsFormTextArea,
  TcsLayout,
} from '@tencent/tcs-component';
import { Button, ExternalLink, Form, Icon, Text, message } from '@tencent/tea-component';
import React, { useEffect, useRef, useState } from 'react';
import RejectModal from '../../../../../RejectModal';
import UpdateStepContent from '../../../UpdateStepContent';
import ProductWorkItemList from '../ProductLevelApproval/components/ProductWorkItemList';
import RiskAssessmentRadio from '../RiskAssessmentRadio';
export interface IProps {
  record: any;
  sheetBaseInfo: any;
  detailPage: boolean;
  workItemsList?: Record<string, any>;
  bugReportEqualNothing: boolean;
  functionReport?: any;
  handleEditSuccess: () => void;
}
export interface WorkItemStatus {
  IssueType?: string;
  SecurityBug?: boolean;
  Status?: string;
  SeverityLevel?: string;
  OpenedOnly?: boolean;
}
const DeveloperApprovalModal: React.FC<IProps> = ({
  record,
  sheetBaseInfo,
  detailPage,
  workItemsList,
  bugReportEqualNothing,
  functionReport,
  handleEditSuccess,
}) => {
  const producManageFormRef = useRef<any>();
  const productTestFormRef = useRef<any>();
  const productDevelopFormRef = useRef<any>();
  const productDirectorFormRef = useRef<any>();
  const [rejectVisible, setRejectVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [productManageForm] = TcsForm.useForm();
  const [productDevelopForm] = TcsForm.useForm();
  const [productTestForm] = TcsForm.useForm();
  const [productDirectorForm] = TcsForm.useForm();
  const [approvalResult, setApprovalResult] = useState<number | undefined>();
  const [doubleConfirmResult, setDoubleConfirmResult] = useState<string>('');
  const [featureComplete, setFeatureComplete] = useState<string>('');
  const [documentsComplete, setDocumentsComplete] = useState<string>('');
  const [currentStep, setCurrentStep] = useState<string>('');
  const { history } = TcsLayout.useHistory();
  const [rejectFormData, setRejectFormData] = useState<any>();
  const [workItemVisible, setWorkItemVisible] = useState<boolean>(false);
  const [workItemStatus, setWorkItemStatus] = useState<any>();
  const [functionTest, setFunctionTest] = useState<string>('');
  const [performanceTest, setPerformanceTest] = useState<string>('');
  const [haTest, setHaTest] = useState<string>('');
  const [stabilityTest, setStabilityTest] = useState<string>('');
  const [apiCompatibility, setApiCompatibility] = useState<string>('');
  const [editManageState, setEditManageState] = useState<{
    manage: boolean;
    test: boolean;
    develop: boolean;
  }>({
    manage: false,
    test: false,
    develop: false,
  });
  const [approvalStepInfo, setApprovalStepInfo] = useState<Record<string, any>>({});

  const finishApproval = (values, Status?: string) => {
    setLoading(true);
    FinishApprovalTask({
      ApprovalStepUUID: record?.currentStepData?.UUID || '',
      Status: Status || StageStatusType.approved,
      Content: values,
      // 其他字段根据需要添加
    })
      .then(({ Error }) => {
        if (Error) {
          setLoading(false);
          return message.error({ content: Error.Message });
        }
        history.push(
          `${DefectManagementRoutePath.REQUIREMENT_ITERATION_REAlEASE_VERSION_PAGE}?approval_id=${sheetBaseInfo?.UUID}&iteration_id=${sheetBaseInfo?.IterationUUID}`,
        );
      })
      .finally(() => {
        setLoading(false);
      });
  };
  const handleConfirm = () => {
    if (currentStep === ProductLevelStepName.ProductManager) {
      producManageFormRef.current?.validateFields()?.then(finishApproval);
    }
    if (currentStep === ProductLevelStepName.ProductTester) {
      productTestFormRef.current?.validateFields().then(finishApproval);
    }
    if (currentStep === ProductLevelStepName.ProductDeveloper) {
      productDevelopFormRef.current?.validateFields().then(finishApproval);
    }
    if (currentStep === ProductLevelStepName.ProductDirector) {
      productDirectorFormRef.current?.validateFields().then(finishApproval);
    }
  };

  const handleReject = () => {
    let formType;
    if (currentStep === ProductLevelStepName.ProductManager) {
      formType = producManageFormRef;
    } else if (currentStep === ProductLevelStepName.ProductTester) {
      formType = productTestFormRef;
    } else if (currentStep === ProductLevelStepName.ProductDeveloper) {
      formType = productDevelopFormRef;
    } else if (currentStep === ProductLevelStepName.ProductDirector) {
      formType = productDirectorFormRef;
    }
    const values = formType.current?.getFieldsValue();
    setRejectVisible(true);
    setRejectFormData(values);
  };
  const handleValuesChange = (nowChange, allValues) => {
    const checkResult = checkFormData(allValues);
    setApprovalResult(checkResult);
  };

  const handleCheckWorkItemDetail = ({
    IssueType = 'bug',
    SeverityLevel = '',
    OpenedOnly = false,
    SecurityBug = false,
  }: WorkItemStatus) => {
    setWorkItemVisible(true);
    setWorkItemStatus({ IssueType, SeverityLevel, OpenedOnly, SecurityBug });
  };

  useEffect(() => {
    if (record) {
      setCurrentStep(record?.currentStepData?.Name || ProductLevelStepName.All);
      const approvalSteps = record?.ApprovalSteps || [];
      const approvalContents = approvalSteps.map((step) => step?.Content);
      const [
        productConfirmContent,
        productManageContent,
        productTestContent,
        productDevelopContent,
        productDirectorContent,
      ] = approvalContents;
      console.log('productConfirmContent', productConfirmContent);
      productManageForm.setFieldsValue(productManageContent);
      productTestForm.setFieldsValue(productTestContent);
      productDevelopForm.setFieldsValue(productDevelopContent);
      productDirectorForm.setFieldsValue(productDirectorContent);
      setFeatureComplete(productManageContent?.FeatureComplete);
      setDocumentsComplete(productManageContent?.ReleaseDocumentsComplete);
      setFunctionTest(productTestContent?.FunctionTestResult);
      setPerformanceTest(productTestContent?.PerformanceTestResult);
      setApiCompatibility(productTestContent?.APICompatibility);
      setHaTest(productTestContent?.HATestResult);
      setStabilityTest(productTestContent?.StabilityTestResult);
      setDoubleConfirmResult(productDirectorContent?.DoubleConfirmation);
      const obj = {};
      record?.ApprovalSteps.forEach((item) => {
        obj[item.Name] = item;
      });
      setApprovalStepInfo(obj);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [record, editManageState]);

  const handleUploadLoading = (isLoading) => {
    setLoading(isLoading);
  };
  // 不可以编辑的情况 1.当前阶段不是产品经理阶段
  const jiguangUsename = (window as any).jiguang_username;
  // 审批管理员/负责人/当前步骤的负责人
  const stepOwner = record?.currentStepData?.Owners?.includes(jiguangUsename);
  const releaseVersionOwner = sheetBaseInfo?.Owners?.Owners?.includes(jiguangUsename);
  const releaseVersionCreator = sheetBaseInfo?.Creator?.includes(jiguangUsename);
  const resultOwner = stepOwner || releaseVersionOwner || releaseVersionCreator;
  const productManageDisabled = !(resultOwner && currentStep === ProductLevelStepName.ProductManager);
  const productTestDisabled = !(resultOwner && currentStep === ProductLevelStepName.ProductTester);
  const productDevelopDisabled = !(currentStep === ProductLevelStepName.ProductDeveloper && resultOwner);
  const productDirectorDisabled = !(currentStep === ProductLevelStepName.ProductDirector && resultOwner);
  // const alertInfoShow = !detailPage && !stepOwner && currentStep === ProductLevelStepName.All;

  const getFooterData = () => {
    if (currentStep === ProductLevelStepName.All || detailPage) {
      return null;
    }
    if (resultOwner) {
      return (
        <Form.Action style={{ display: 'flex', justifyContent: 'center' }}>
          <TcsButton
            type="primary"
            onClick={handleConfirm}
            loading={loading}
            disabled={approvalResult === ConclusionType.RefuseToRelease}
          >
            确认
          </TcsButton>

          <TcsButton onClick={handleReject}>驳回</TcsButton>
        </Form.Action>
      );
    }
  };

  const handleManageEdit = (value) => {
    setEditManageState((editManageState) => ({ ...editManageState, manage: value }));
  };
  const handleTestEdit = (value) => {
    setEditManageState((editManageState) => ({ ...editManageState, test: value }));
  };
  const handleDevelopEdit = (value) => {
    setEditManageState((editManageState) => ({ ...editManageState, develop: value }));
  };

  // todo:1.如果是在审批阶段，可以card折叠起来。2.如果是查看详情页面，必须都展开
  // todo:驳回的弹窗
  return (
    <>
      {/* {!bugReportEqualNothing && !detailPage && (
        <Alert type="warning" style={{ marginTop: 8 }}>
          <div>
            当前产品下存在缺陷单未关闭，未关闭总数：{workItemsList?.NotTotalCount}条，其中安全漏洞：
            {workItemsList?.NotClosedSecurityCount}条，严重缺陷：
            {workItemsList?.NotClosedSeriousCount}条，致命缺陷：{workItemsList?.NotCloseFatalCount}条。
            评审结论不能选择无风险，正常发布。请仔细检查评审内容！
          </div>
        </Alert>
      )} */}

      <TcsCard
        title="产品经理审批"
        collapsible={currentStep !== ProductLevelStepName.All}
        subTitle={`审批人：${approvalStepInfo[ProductLevelStepName.ProductManager]?.Owners} 审批完成时间：${
          approvalStepInfo[ProductLevelStepName.ProductManager]?.CompletedAt || '-'
        }`}
        extra={
          approvalStepInfo[ProductLevelStepName.ProductManager]?.Status === StageStatusType.approved &&
          detailPage && (
            <UpdateStepContent
              handleEdit={handleManageEdit}
              handleConfirm={handleEditSuccess}
              form={producManageFormRef}
              stepInfo={approvalStepInfo[ProductLevelStepName.ProductManager]}
              sheetBaseInfo={sheetBaseInfo}
            />
          )
        }
      >
        <TcsForm
          formRef={producManageFormRef}
          form={productManageForm}
          onValuesChange={handleValuesChange}
          readonly={productManageDisabled && !editManageState.manage}
        >
          <TcsFormRadio
            label="功能是否完备"
            name="FeatureComplete"
            options={[
              {
                label: '功能完备',
                value: ApprovalResultType.approval,
              },
              {
                label: '功能缺失',
                value: ApprovalResultType.disApproval,
              },
            ]}
            rules={[
              {
                required: true,
                message: '请选择功能是否完备',
              },
            ]}
            onChange={setFeatureComplete}
          />
          {featureComplete === ApprovalResultType.disApproval && (
            <TcsFormTextArea
              label="缺失内容"
              name="FeatureContent"
              rules={[
                {
                  required: true,
                  message: '请输入缺失详情',
                },
              ]}
            />
          )}

          <TcsFormRadio
            label="文档是否完备"
            name="ReleaseDocumentsComplete"
            options={[
              {
                label: '文档完备',
                value: ApprovalResultType.approval,
              },
              {
                label: '文档缺失',
                value: ApprovalResultType.disApproval,
              },
            ]}
            rules={[
              {
                required: true,
                message: '请选择文档是否完备',
              },
            ]}
            onChange={setDocumentsComplete}
          />
          {documentsComplete === ApprovalResultType.disApproval && (
            <TcsFormTextArea
              label="缺失内容"
              name="DocumentsContent"
              rules={[
                {
                  required: true,
                  message: '请输入缺失详情',
                },
              ]}
            />
          )}
          <RiskAssessmentRadio
            formData={approvalStepInfo[ProductLevelStepName.ProductManager]?.Content}
            bugReportEqualZero={bugReportEqualNothing}
            functionReport={functionReport}
            readonly={productManageDisabled && !editManageState.manage}
            editModel={editManageState.manage}
          />
        </TcsForm>
      </TcsCard>

      {(currentStep === ProductLevelStepName.ProductDeveloper ||
        currentStep === ProductLevelStepName.ProductDirector ||
        currentStep === ProductLevelStepName.All ||
        (currentStep === ProductLevelStepName.ProductTester && !detailPage)) && (
        <TcsCard
          title="产品测试审批"
          collapsible={currentStep !== ProductLevelStepName.All}
          subTitle={`审批人：${approvalStepInfo[ProductLevelStepName.ProductTester]?.Owners} 审批完成时间：${
            approvalStepInfo[ProductLevelStepName.ProductTester]?.CompletedAt || '-'
          }`}
          extra={
            approvalStepInfo[ProductLevelStepName.ProductTester]?.Status === StageStatusType.approved &&
            detailPage && (
              <UpdateStepContent
                handleEdit={handleTestEdit}
                handleConfirm={handleEditSuccess}
                form={productTestFormRef}
                stepInfo={approvalStepInfo[ProductLevelStepName.ProductTester]}
                sheetBaseInfo={sheetBaseInfo}
              />
            )
          }
        >
          <TcsForm
            formRef={productTestFormRef}
            form={productTestForm}
            onValuesChange={handleValuesChange}
            readonly={productTestDisabled && !editManageState.test}
          >
            <TcsFormRadio
              label="功能测试"
              name="FunctionTestResult"
              options={[
                { label: '验证通过', value: ApprovalResultType.approval },
                { label: '验证未完成', value: ApprovalResultType.disApproval },
                { label: '测试未展开', value: ApprovalResultType.undeveloped },
              ]}
              rules={[
                {
                  required: true,
                  message: '请选择功能测试是否通过',
                },
              ]}
              onChange={setFunctionTest}
            />
            {functionTest === ApprovalResultType.undeveloped && (
              <TcsFormTextArea
                label="原因"
                name="FunctionTestUndevelopedContent"
                rules={[
                  {
                    required: true,
                    message: '请输入未测试原因',
                  },
                ]}
              />
            )}
            <TcsFormRadio
              label="云API兼容性"
              name="APICompatibility"
              options={[
                { label: '验证通过', value: ApprovalResultType.approval },
                { label: '验证未完成', value: ApprovalResultType.disApproval },
                { label: '测试未展开', value: ApprovalResultType.undeveloped },
              ]}
              rules={[
                {
                  required: true,
                  message: '请选择功能测试是否通过',
                },
              ]}
              tooltip={{
                icon: <Icon type="help" />,
                title: (
                  <ExternalLink href="https://doc.weixin.qq.com/doc/w3_ABEAvwYbACoqDt9ZlZiQhqa2SvZw1?scode=AJEAIQdfAAoeb7CxyFAKQAhwbdAFw">
                    查看说明文档
                  </ExternalLink>
                ),
              }}
              onChange={setApiCompatibility}
            />
            {apiCompatibility === ApprovalResultType.undeveloped && (
              <TcsFormTextArea
                label="原因"
                name="ApiUndevelopedContent"
                rules={[
                  {
                    required: true,
                    message: '请输入未测试原因',
                  },
                ]}
              />
            )}
            <TcsFormRadio
              label="是否开展性能测试"
              name="PerformanceTestResult"
              options={[
                { label: '是', value: ApprovalResultType.approval },
                { label: '否', value: ApprovalResultType.disApproval },
                { label: '不涉及', value: ApprovalResultType.undeveloped },
              ]}
              rules={[
                {
                  required: true,
                  message: '请选择性能测试是否通过',
                },
              ]}
              onChange={setPerformanceTest}
            />
            {performanceTest === ApprovalResultType.undeveloped && (
              <TcsFormTextArea
                label="原因"
                name="PerformanceTestUndevelopedContent"
                rules={[
                  {
                    required: true,
                    message: '请输入未测试原因',
                  },
                ]}
              />
            )}
            <TcsFormRadio
              label="高可用测试"
              name="HATestResult"
              options={[
                { label: '验证通过', value: ApprovalResultType.approval },
                { label: '验证未完成', value: ApprovalResultType.disApproval },
                { label: '测试未展开', value: ApprovalResultType.undeveloped },
              ]}
              rules={[
                {
                  required: true,
                  message: '请选择高可用测试是否通过',
                },
              ]}
              onChange={setHaTest}
            />
            {haTest === ApprovalResultType.undeveloped && (
              <TcsFormTextArea
                label="原因"
                name="HATestUndevelopedContent"
                rules={[
                  {
                    required: true,
                    message: '请输入未测试原因',
                  },
                ]}
              />
            )}
            <TcsFormRadio
              label="稳定性测试"
              name="StabilityTestResult"
              options={[
                { label: '验证通过', value: ApprovalResultType.approval },
                { label: '验证未完成', value: ApprovalResultType.disApproval },
                { label: '测试未展开', value: ApprovalResultType.undeveloped },
              ]}
              rules={[
                {
                  required: true,
                  message: '请选择稳定性测试是否通过',
                },
              ]}
              onChange={setStabilityTest}
            />
            {stabilityTest === ApprovalResultType.undeveloped && (
              <TcsFormTextArea
                label="原因"
                name="StabilityTestUndevelopedContent"
                rules={[
                  {
                    required: true,
                    message: '请输入未测试原因',
                  },
                ]}
              />
            )}
            {productTestDisabled ? (
              <Form.Item label="测试报告">
                <Form.Text>
                  <a
                    href={approvalStepInfo[ProductLevelStepName.ProductTester]?.Content?.TestDocumentURL}
                    target="_blank"
                    rel="noreferrer"
                  >
                    <Icon type="externallink" />
                  </a>
                  <Text
                    copyable={{
                      text: `${approvalStepInfo[ProductLevelStepName.ProductTester]?.Content?.TestDocumentURL}`,
                    }}
                  />
                </Form.Text>
              </Form.Item>
            ) : (
              <TcsFormText
                label="测试报告"
                name="TestDocumentURL"
                tooltip="请填写正确的在线测试报告，开头必须以http/https开头"
                rules={[
                  {
                    required: true,
                    message: '请填写在线测试报告',
                  },
                  {
                    validator(_: any, value: string) {
                      const regexp = URL_CHECKOUT;

                      if (regexp.test(value)) {
                        return Promise.resolve();
                      }
                      return '请填写正确格式的在线测试报告';
                    },
                  },
                ]}
                width="md"
                placeholder="请填写在线测试报告"
              />
            )}
            <TcsFormText
              label="国际化能力"
              name="InternationalCompetence"
              rules={[
                {
                  required: true,
                  message: '请填写国际化能力',
                },
              ]}
              width="md"
              placeholder="请填写国际化能力"
            />
            <RiskAssessmentRadio
              formData={approvalStepInfo[ProductLevelStepName.ProductTester]?.Content}
              bugReportEqualZero={bugReportEqualNothing}
              functionReport={functionReport}
              readonly={productTestDisabled && !editManageState.test}
              editModel={editManageState.test}
            />
          </TcsForm>
        </TcsCard>
      )}

      {((currentStep === ProductLevelStepName.ProductDeveloper && !detailPage) ||
        currentStep === ProductLevelStepName.ProductDirector ||
        currentStep === ProductLevelStepName.All) && (
        <TcsCard
          title="产品研发审批"
          collapsible={currentStep !== ProductLevelStepName.All}
          // defaultCollapsed={currentStep !== ProductLevelStepName.ProductManager && !detailPage}
          subTitle={`审批人：${approvalStepInfo[ProductLevelStepName.ProductDeveloper]?.Owners} 审批完成时间：${
            approvalStepInfo[ProductLevelStepName.ProductDeveloper]?.CompletedAt
          }`}
          extra={
            approvalStepInfo[ProductLevelStepName.ProductDeveloper]?.Status === StageStatusType.approved &&
            detailPage && (
              <UpdateStepContent
                handleEdit={handleDevelopEdit}
                handleConfirm={handleEditSuccess}
                form={productDevelopFormRef}
                stepInfo={approvalStepInfo[ProductLevelStepName.ProductDeveloper]}
                sheetBaseInfo={sheetBaseInfo}
              />
            )
          }
        >
          <TcsForm
            formRef={productDevelopFormRef}
            form={productDevelopForm}
            onValuesChange={handleValuesChange}
            readonly={productDevelopDisabled && !editManageState.develop}
          >
            {productDevelopDisabled ? (
              <>
                <Form.Item label="安全自检评估项">
                  <Form.Text>
                    <Text copyable>
                      {approvalStepInfo[ProductLevelStepName.ProductDeveloper]?.Content?.SelfSecurityCheckDetail}
                    </Text>
                  </Form.Text>
                </Form.Item>

                {window.jiguang_currentNs !== TCE_SPACE && (
                  <>
                    <Form.Item label="产品技术规范与管理流程标准评估">
                      <Form.Text>
                        <a
                          href={`https://registry.jiguang.woa.com${
                            approvalStepInfo[ProductLevelStepName.ProductDeveloper]?.Content?.ProductStandard?.url
                          }${
                            approvalStepInfo[ProductLevelStepName.ProductDeveloper]?.Content?.ProductStandard?.search
                          }`}
                          rel="noreferrer"
                          download
                        >
                          点击下载查看
                        </a>
                      </Form.Text>
                    </Form.Item>
                    <Form.Item label="可交付可运维评估表">
                      <Form.Text>
                        <a
                          href={`https://registry.jiguang.woa.com${
                            approvalStepInfo[ProductLevelStepName.ProductDeveloper]?.Content
                              ?.DeliveryOperationAndMaintenance?.url
                          }${
                            approvalStepInfo[ProductLevelStepName.ProductDeveloper]?.Content
                              ?.DeliveryOperationAndMaintenance?.search
                          }`}
                          rel="noreferrer"
                          download
                        >
                          点击下载查看
                        </a>
                      </Form.Text>
                    </Form.Item>
                  </>
                )}
              </>
            ) : (
              <>
                <TcsFormText
                  label="安全自检评估项"
                  name="SelfSecurityCheckDetail"
                  rules={[
                    {
                      required: true,
                      message: '请上传安全自检评估',
                    },
                  ]}
                  fieldProps={{
                    size: 'l',
                  }}
                  tooltip=" 由于*********版本安全团队只针对部分产品做了安全评估，如果该产品不在
                  【BHSaas、云审计、Tcenter、RIO、TDSQL 、TSF 、OSP
                  、TAP、TKE、CLS、VPN】范围中，则评估结果统一填写：【不涉及：安全侧已针对TCE3.10.11版本的11款重点产品进行了安全评估审核，经评估风险可控，XXXX产品不在安全评估范围内】"
                  placeholder="请填写安全自检评估"
                  extra={
                    <>
                      <span style={{ color: 'green' }}>
                        在线填写地址：
                        <a
                          href="https://csigsec.woa.com/evaluateTaskList"
                          rel="noreferrer"
                          style={{ marginLeft: 5 }}
                          target="_blank"
                        >
                          <Icon type="externallink" />
                        </a>
                        ； 填写指引:
                        <a
                          href="https://iwiki.woa.com/pages/viewpage.action?pageId=4006982673"
                          rel="noreferrer"
                          target="_blank"
                          style={{ marginLeft: 5 }}
                        >
                          <Icon type="externallink" />
                        </a>
                        。 填写完成后得到的URL附到此处； 注：iwiki指引中回填写qflow即填写到此处即可。
                      </span>
                    </>
                  }
                />

                {window.jiguang_currentNs !== TCE_SPACE && (
                  <>
                    <TcsForm.Item
                      label="产品技术规范与管理流程标准评估"
                      name="ProductStandard"
                      rules={[
                        {
                          required: true,
                          message: '请上传产品技术规范与管理流程标准评估',
                        },
                      ]}
                      extra={
                        <div style={{ color: 'green' }}>
                          《产品研发规范评估表》请前往下载重新评估后上传
                          <a
                            href="https://csig.lexiangla.com/teams/k100097/docs/9f4627321e7411eb9ecb467baa4d1ca7?company_from=csig"
                            rel="noreferrer"
                            target="_blank"
                            style={{ marginLeft: 5 }}
                          >
                            <Icon type="externallink" />
                          </a>
                        </div>
                      }
                    >
                      <UploadField onLoading={handleUploadLoading} />
                    </TcsForm.Item>
                    <TcsForm.Item
                      label="可交付可运维评估表"
                      name="DeliveryOperationAndMaintenance"
                      rules={[
                        {
                          required: true,
                          message: '请上传可交付运维评估表',
                        },
                      ]}
                      extra={
                        <div style={{ color: 'green' }}>
                          《产品可交付可运维评估表》：请根据产品是否已接入TCS选择评估表。 接入TCS产品请使用：
                          <a
                            href="https://csig.lexiangla.com/teams/k100097/docs/ac457710455111eeaf032a590756c809?company_from=csig"
                            rel="noreferrer"
                            target="_blank"
                            style={{ marginLeft: 5 }}
                          >
                            <Icon type="externallink" />
                          </a>
                          。非接入TCS产品请使用：
                          <a
                            href="https://csig.lexiangla.com/teams/k100097/docs/9f253d9c1e7411eb95c6e60a86e7f4dc?company_from=csig"
                            rel="noreferrer"
                            target="_blank"
                            style={{ marginLeft: 5 }}
                          >
                            <Icon type="externallink" />
                          </a>
                        </div>
                      }
                    >
                      <UploadField onLoading={handleUploadLoading} />
                    </TcsForm.Item>
                  </>
                )}
              </>
            )}
            <TcsFormRadio
              label="是否完成售前接入"
              name="PreSaleFinished"
              options={[
                { label: '已完成', value: ApprovalResultType.approval },
                { label: '未完成', value: ApprovalResultType.disApproval },
              ]}
              rules={[
                {
                  required: true,
                  message: '请选择是否完成售前接入',
                },
              ]}
              extra={
                !productDevelopDisabled && (
                  <span style={{ color: 'green' }}>
                    完成售前产品及解决方案定义接入，将支持解决方案进行售前报价，售前管理。操作指引：
                    <a
                      href="https://iwiki.woa.com/p/1614660851"
                      rel="noreferrer"
                      target="_blank"
                      style={{ marginLeft: 5 }}
                    >
                      <Icon type="externallink" />
                    </a>
                  </span>
                )
              }
            />

            <TcsFormRadio
              label="是否认可测试结论"
              name="TestResult"
              options={[
                { label: '认可', value: ApprovalResultType.approval },
                { label: '不认可', value: ApprovalResultType.disApproval },
              ]}
              rules={[
                {
                  required: true,
                  message: '请选择是否认可测试结论',
                },
              ]}
            />

            {!workItemsList?.NotClosedSeriousCount || (
              <TcsFormDatePicker
                name="SeriousBugExpectedFixDate"
                label="严重缺陷完成时间"
                rules={[
                  {
                    required: true,
                    message: '请选择预计完成时间',
                  },
                ]}
                placeholder="请选择预计完成时间"
                suffix={
                  <Button
                    type="link"
                    onClick={() => handleCheckWorkItemDetail({ SeverityLevel: 'serious', OpenedOnly: true })}
                    style={{ marginLeft: 5 }}
                  >
                    未关闭：{workItemsList?.NotClosedSeriousCount}条
                  </Button>
                }
              />
            )}
            {!workItemsList?.NotCloseFatalCount || (
              <TcsFormDatePicker
                name="FatalBugsExpectedFixDate"
                label="致命缺陷完成时间"
                rules={[
                  {
                    required: true,
                    message: '请选择预计完成时间',
                  },
                ]}
                placeholder="请选择预计完成时间"
                suffix={
                  <Button
                    type="link"
                    onClick={() => handleCheckWorkItemDetail({ SeverityLevel: 'fatal', OpenedOnly: true })}
                    style={{ marginLeft: 5 }}
                  >
                    未关闭：{workItemsList?.NotCloseFatalCount}条
                  </Button>
                }
              />
            )}
            {!workItemsList?.NotClosedSecurityCount || (
              <TcsFormDatePicker
                name="SecurityBugsExpectedFixDate"
                label="安全漏洞完成时间"
                rules={[
                  {
                    required: true,
                    message: '请选择预计完成时间',
                  },
                ]}
                placeholder="请选择预计完成时间"
                suffix={
                  <Button
                    type="link"
                    onClick={() => handleCheckWorkItemDetail({ SecurityBug: true, OpenedOnly: true })}
                    style={{ marginLeft: 5 }}
                  >
                    {workItemsList?.NotClosedSecurityCount}条
                  </Button>
                }
              />
            )}

            <RiskAssessmentRadio
              formData={approvalStepInfo[ProductLevelStepName.ProductDeveloper]?.Content}
              bugReportEqualZero={bugReportEqualNothing}
              functionReport={functionReport}
              readonly={productDevelopDisabled && !editManageState.develop}
              editModel={editManageState.develop}
            />
          </TcsForm>
        </TcsCard>
      )}
      {((currentStep === ProductLevelStepName.ProductDirector && !detailPage) ||
        (currentStep === ProductLevelStepName.All &&
          approvalStepInfo[ProductLevelStepName.ProductDirector]?.Status !== StageStatusType.skipped)) && (
        <TcsCard
          title="产品总监审批"
          collapsible={currentStep !== ProductLevelStepName.All}
          subTitle={`审批人：${approvalStepInfo[ProductLevelStepName.ProductDirector]?.Owners} 审批完成时间：${
            approvalStepInfo[ProductLevelStepName.ProductDirector]?.CompletedAt || '-'
          }`}
        >
          <TcsForm
            formRef={productDirectorFormRef}
            form={productDirectorForm}
            onValuesChange={handleValuesChange}
            readonly={productDirectorDisabled}
          >
            <>
              <TcsFormRadio
                name="DoubleConfirmation"
                label="总监审批结论"
                // 1.完全拒绝发布 2.本次不发布，（需要提示） 只能去驳回
                options={[
                  { label: '同意发布', value: productDirectorApprovalType.agree },
                  { label: '本次不发布', value: productDirectorApprovalType.reject },
                ]}
                rules={[
                  {
                    required: true,
                    message: '请选择审批结论',
                  },
                ]}
                onChange={setDoubleConfirmResult}
                extra={
                  doubleConfirmResult === productDirectorApprovalType.reject && !detailPage ? (
                    <Text theme="warning">当前审批结论为本次不发布，请确认好之后，再点击确认！</Text>
                  ) : (
                    ''
                  )
                }
              />
              {doubleConfirmResult === productDirectorApprovalType.disagree && (
                <TcsFormTextArea
                  name="Comments"
                  label="原因"
                  rules={[
                    {
                      required: true,
                      message: '请填写原因',
                    },
                  ]}
                  placeholder="请填写原因"
                />
              )}
              {doubleConfirmResult === productDirectorApprovalType.reject && (
                <TcsFormTextArea
                  name="RejectComments"
                  label="原因"
                  rules={[
                    {
                      required: true,
                      message: '请填写原因',
                    },
                  ]}
                  placeholder="请填写原因"
                />
              )}
            </>
          </TcsForm>
        </TcsCard>
      )}
      <div>{getFooterData()}</div>
      <RejectModal
        isVisible={rejectVisible}
        onClose={() => setRejectVisible(false)}
        NowStageName="Product"
        productApprovalStep={record}
        nowStageContent={rejectFormData}
        sheetBaseInfo={sheetBaseInfo}
      />
      <ProductWorkItemList
        isVisible={workItemVisible}
        onClose={() => setWorkItemVisible(false)}
        workItemType={workItemStatus}
        sheetBaseInfo={sheetBaseInfo}
        productStepInfo={approvalStepInfo[ProductLevelStepName.ProductDeveloper]}
      />
    </>
  );
};
export default DeveloperApprovalModal;
