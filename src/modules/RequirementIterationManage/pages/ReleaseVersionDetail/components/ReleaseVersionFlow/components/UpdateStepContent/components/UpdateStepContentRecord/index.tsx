import { RequirementIterationManage } from '@/common/api/requirementIteration';
import { TcsModal, TcsTable } from '@tencent/tcs-component';
import React from 'react';
export interface IProps {
  isVisible: boolean;
  toggle: () => void;
  stepInfo: any;
}
const UpdateStapContentRecord = ({ isVisible, toggle, stepInfo }: IProps) => {
  const handleCancel = () => {
    toggle();
  };
  const columns = [
    {
      title: '操作人',
      dataIndex: 'CreateUser',
    },
    {
      title: '更新时间',
      dataIndex: 'UpdateAt',
      valueType: 'dateTime',
    },
  ];
  const fetchData = (params) =>
    RequirementIterationManage.ListVersionApprovalStepMessage({
      PageNo: params.current,
      PageSize: params.pageSize,
      StepUUID: stepInfo.UUID,
    });

  return (
    <TcsModal title="更新记录" visible={isVisible} onCancel={handleCancel} footer={false} destroyOnClose width={800}>
      <TcsTable columns={columns} request={fetchData} pagination={{ pageSize: 5 }} />
    </TcsModal>
  );
};
export default UpdateStapContentRecord;
