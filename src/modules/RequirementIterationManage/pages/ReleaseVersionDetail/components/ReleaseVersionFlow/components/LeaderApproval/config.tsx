import dayjs from 'dayjs';
import React from 'react';

const getCloumns = () => [
  {
    title: '产品',
    dataIndex: 'ProductName',
    mergeRowSpan: true,
  },
  {
    title: '审批步骤',
    dataIndex: 'Description',
  },

  {
    title: '审批人',
    dataIndex: 'Modifier',
  },

  {
    title: '主要影响',
    dataIndex: 'ScopeOfInfluence',
  },
  {
    title: '预计修复时间',
    dataIndex: 'ExpectedFixDate',
    render(text, record) {
      const { SeriousBugExpectedFixDate, FatalBugsExpectedFixDate, SecurityBugsExpectedFixDate } = record.Time || {};

      if (SeriousBugExpectedFixDate || FatalBugsExpectedFixDate || SecurityBugsExpectedFixDate) {
        return (
          <div>
            {SeriousBugExpectedFixDate && <div>严重缺陷完成时间: {changeTimeFormat(SeriousBugExpectedFixDate)}</div>}
            {FatalBugsExpectedFixDate && <div>致命缺陷完成时间: {changeTimeFormat(FatalBugsExpectedFixDate)}</div>}
            {SecurityBugsExpectedFixDate && (
              <div>安全漏洞完成时间: {changeTimeFormat(SecurityBugsExpectedFixDate)}</div>
            )}
          </div>
        );
      }
      return '-';
    },
  },
];

export default getCloumns;
const changeTimeFormat = (date) => dayjs(date).format('YYYY-MM-DD ');
