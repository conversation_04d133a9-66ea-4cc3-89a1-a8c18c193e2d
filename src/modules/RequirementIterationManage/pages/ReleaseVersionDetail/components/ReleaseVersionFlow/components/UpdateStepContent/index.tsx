import { RequirementIterationManage } from '@/common/api/requirementIteration';
import ReleaseVersionContext from '@/common/context/release';
import { TcsButton } from '@tencent/tcs-component';
import { message, Tooltip } from '@tencent/tea-component';
import React, { useContext, useEffect, useState } from 'react';
import { StageStatusType } from '../../../../constants';
import UpdateStapContentRecord from './components/UpdateStepContentRecord';
export interface IProps {
  handleEdit: (value: boolean) => void;
  handleConfirm: (index?: number) => void;
  form?: any;
  stepInfo: any;
  stepContent?: any;
  sheetBaseInfo?: any;
}
const UpdateStepContent = ({ handleEdit, handleConfirm, form, stepInfo, stepContent, sheetBaseInfo }: IProps) => {
  const [editState, setEditState] = useState<boolean>(false);
  const [updateRecordVisible, setUpdateContentVisible] = useState<boolean>(false);
  const [editDisabled, setEditDisabled] = useState<boolean>(false);
  const handleClick = () => {
    setEditState(true);
    handleEdit(true);
  };
  const { baseInfo } = useContext(ReleaseVersionContext);
  const handleCancel = () => {
    setEditState(false);
    handleEdit(false);
  };

  const handleOk = async (): Promise<void> => {
    try {
      let values: Record<string, any> = {};
      if (form?.current) {
        values = await form.current.validateFields();
      }
      const mergedData = form?.current ? (stepContent ? { ...values, ...stepContent } : values) : stepContent;
      if (mergedData && Object.keys(mergedData).length > 0) {
        updateContent(mergedData);
      }
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };
  const updateContent = (values) => {
    RequirementIterationManage.UpdateVersionApprovalStepMessage({
      StepUUID: stepInfo.UUID,
      Content: values,
      Comments: '',
    })
      .then((res) => {
        if (res.Error) {
          return message.error({ content: res.Error.massage });
        }
        message.success({ content: '更新审批结论成功' });
        handleConfirm();
      })
      .finally(() => {
        handleCancel();
      });
  };

  const handleCheckUpdateRecord = () => {
    setUpdateContentVisible(true);
  };
  useEffect(() => {
    if (stepInfo) {
      const jiguangUsename = (window as any).jiguang_username;
      // 审批管理员/负责人/当前步骤的负责人
      const stepOwner = stepInfo?.Owners?.includes(jiguangUsename);
      const sheetOwner = sheetBaseInfo?.Owners?.Owners?.includes(jiguangUsename);
      const baseState =
        baseInfo.State === StageStatusType.approved || sheetBaseInfo?.State === StageStatusType.approved;
      setEditDisabled((stepOwner || sheetOwner) && !baseState);
    }
  }, [stepInfo, baseInfo, sheetBaseInfo]);
  return (
    <>
      {!editState ? (
        <TcsButton onClick={handleClick} type="link" disabled={!editDisabled}>
          <Tooltip title={editDisabled ? '' : '您不是当前流程的审批负责人，无权限更新'}>更新审批内容</Tooltip>
        </TcsButton>
      ) : (
        <>
          <TcsButton onClick={handleOk} type="primary">
            确认
          </TcsButton>
          <TcsButton onClick={handleCancel} style={{ marginLeft: 10 }}>
            取消
          </TcsButton>
        </>
      )}
      <TcsButton type="link" onClick={handleCheckUpdateRecord} style={{ marginLeft: 10 }}>
        更新记录
      </TcsButton>
      <UpdateStapContentRecord
        isVisible={updateRecordVisible}
        toggle={() => setUpdateContentVisible(false)}
        stepInfo={stepInfo}
      />
    </>
  );
};
export default UpdateStepContent;
