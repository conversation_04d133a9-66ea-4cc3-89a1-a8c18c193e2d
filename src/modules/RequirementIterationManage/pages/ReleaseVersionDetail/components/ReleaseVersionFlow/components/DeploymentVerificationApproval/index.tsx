import ReleaseVersionContext from '@/common/context/release';
import { TcsForm, TcsFormRadio, TcsFormText, TcsFormTextArea } from '@tencent/tcs-component';
import { Form, FormText, Icon, Text } from '@tencent/tea-component';
import React, { useContext, useEffect, useRef, useState } from 'react';
import { ApprovalResultType, StageStatusType, URL_CHECKOUT } from '../../../../constants';
import { checkStageDisabled } from '../../../../utils';
import NextStage from '../../../NextStage';
import UpdateStepContent from '../UpdateStepContent';
export interface IProps {
  handleEditSuccess: (index) => void;
}
const DeploymentVerificationApproval = ({ handleEditSuccess }: IProps) => {
  const [standard, setStandard] = useState<string>('');
  const [verification, setVerification] = useState<string>('');
  const [delivery, setDelivery] = useState<string>('');
  const [dilatation, setDilatation] = useState<string>('');
  const [form] = TcsForm.useForm();
  const formRef = useRef<any>();
  const { stageOwner, stageInfo, baseInfo, isManage, stageDetail } = useContext(ReleaseVersionContext);
  const ownerLimit = stageOwner || isManage;
  const [formData, setFormData] = useState<Record<string, string>>();
  const [modeling, setModeling] = useState<string>('');
  const [quotation, setQuotation] = useState<string>('');
  const [editContentState, setEditContentState] = useState<boolean>(false);

  const handleEditContent = (value) => {
    setEditContentState(value);
  };

  useEffect(() => {
    if (stageInfo) {
      const contentDetail = stageInfo?.Content;
      form.setFieldsValue(contentDetail);
      setFormData(contentDetail);
      const { VerificationResult, Standard, DeliveryVerificationResult, Dilatation, Modeling, Quotation } =
        contentDetail || {};
      // setStandard(stageInfo?.[0]?.Content?.isstandard);
      setVerification(VerificationResult);
      setStandard(Standard);
      setDelivery(DeliveryVerificationResult);
      // setPerformance(PerformanceTestResult);
      setDilatation(Dilatation);
      setModeling(Modeling);
      setQuotation(Quotation);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [stageInfo]);
  const isDisabled = checkStageDisabled(stageOwner, isManage, stageDetail, baseInfo);
  const options = [
    { label: '达标', value: ApprovalResultType.approval },
    { label: '未达标', value: ApprovalResultType.disApproval },
    { label: '测试未展开', value: ApprovalResultType.undeveloped },
  ];
  const productOptions = [
    {
      label: '是',
      value: ApprovalResultType.approval,
    },
    {
      label: '否',
      value: ApprovalResultType.disApproval,
    },
  ];

  return (
    <>
      {/* {!stageOwner && stageDetail?.Status !== StageStatusType.approved && (
        <Alert type="warning"> 您不是操作负责人，无审批权限!</Alert>
      )} */}
      {stageInfo?.Status === StageStatusType.approved && (
        <div style={{ float: 'right' }}>
          <UpdateStepContent
            handleConfirm={() => handleEditSuccess(stageDetail.StageOrder)}
            handleEdit={handleEditContent}
            stepInfo={stageInfo}
            form={formRef}
          />
        </div>
      )}
      <TcsForm form={form} formRef={formRef} readonly={isDisabled && !editContentState}>
        <TcsFormRadio
          name="VerificationResult"
          label="部署验证是否通过"
          options={options}
          rules={[
            {
              required: true,
              message: '请选择验证是否通过',
            },
          ]}
          onChange={setVerification}
        />
        {(verification === ApprovalResultType.disApproval || verification === ApprovalResultType.disApproval) && (
          <TcsFormTextArea
            name="VerficationDisapprovalDetails"
            label="说明"
            placeholder="请填写说明信息"
            rules={[
              {
                required: true,
                message: '请填写说明信息',
              },
            ]}
          />
        )}

        <TcsFormRadio
          name="Standard"
          label="是否满足四化要求"
          options={[
            { label: '达标', value: ApprovalResultType.approval },
            { label: '未达标', value: ApprovalResultType.disApproval },
            { label: '不涉及', value: ApprovalResultType.undeveloped },
          ]}
          rules={[
            {
              required: true,
              message: '请选择是否满足四化要求',
            },
          ]}
          onChange={setStandard}
        />
        {(standard === ApprovalResultType.disApproval || standard === ApprovalResultType.undeveloped) && (
          <TcsFormTextArea
            name="StandardFailureDetails"
            label="说明"
            placeholder="请填写说明信息"
            rules={[
              {
                required: true,
                message: '请输入说明信息',
              },
            ]}
          />
        )}
        <TcsFormRadio
          name="DeliveryVerificationResult"
          label="准出验证是否通过"
          options={options}
          rules={[
            {
              required: true,
              message: '请选择准出验证是否通过',
            },
          ]}
          onChange={setDelivery}
        />
        {(delivery === ApprovalResultType.disApproval || delivery === ApprovalResultType.undeveloped) && (
          <TcsFormTextArea
            name="DeliveryFailureDetails"
            label="说明"
            placeholder="请填写说明信息"
            rules={[
              {
                required: true,
                message: '请输入说明信息',
              },
            ]}
          />
        )}
        {/* <TcsFormRadio
          name="PerformanceTestResult"
          label="性能验证是否通过"
          options={[
            { label: '达标', value: ApprovalResultType.approval },
            { label: '未达标', value: ApprovalResultType.disApproval },
            { label: '不涉及', value: ApprovalResultType.undeveloped },
          ]}
          rules={[
            {
              required: true,
              message: '请选择性能验证是否通过',
            },
          ]}
          onChange={setPerformance}
        />
        {(performance === ApprovalResultType.disApproval || performance === ApprovalResultType.undeveloped) && (
          <TcsFormTextArea
            name="PerformanceTestDetails"
            label="说明"
            placeholder="请填写说明信息"
            rules={[
              {
                required: true,
                message: '请输入说明信息',
              },
            ]}
          />
        )} */}
        <TcsFormRadio
          name="Dilatation"
          label="扩容验证是否通过"
          options={options}
          rules={[
            {
              required: true,
              message: '请选择扩容验证是否通过',
            },
          ]}
          onChange={setDilatation}
        />
        {(dilatation === ApprovalResultType.disApproval || dilatation === ApprovalResultType.undeveloped) && (
          <TcsFormTextArea
            name="DilatationDetails"
            label="说明"
            placeholder="请填写说明信息"
            rules={[
              {
                required: true,
                message: '请输入说明信息',
              },
            ]}
          />
        )}
        {isDisabled && !editContentState ? (
          <Form.Item label="文档链接">
            <Form.Text>
              <a href={formData?.VerificationReport} target="_blank" rel="noreferrer">
                链接 <Icon type="externallink" />
              </a>
              <Text copyable={{ text: `${formData?.VerificationReport}` }} />
            </Form.Text>
          </Form.Item>
        ) : (
          <TcsFormText
            name="VerificationReport"
            label="验证报告"
            tooltip="请填写正确的在线验证报告，必须以http/https开头"
            rules={[
              {
                required: true,
                message: '请填写在线测试报告',
              },
              {
                validator(_: any, value: string) {
                  const regexp = URL_CHECKOUT;

                  if (regexp.test(value)) {
                    return Promise.resolve();
                  }
                  return '请填写正确格式的在线测试报告';
                },
              },
            ]}
            placeholder="请输入验证报告在线文档链接"
            fieldProps={{
              size: 'l',
            }}
          />
        )}
        <br />
        {!isDisabled && (
          <Form.Item>
            <FormText>
              <Text theme="label">
                以下两个审批内容（是否完成建模和是否支持产品报价）请找 jgmiao(缪家国) 进行确认后再填写！
              </Text>
            </FormText>
          </Form.Item>
        )}

        <TcsFormRadio
          name="Modeling"
          label="是否完成建模"
          options={productOptions}
          rules={[
            {
              required: true,
              message: '请选择是否完成建模',
            },
          ]}
          tooltip={
            <a href="https://jiguang.woa.com/page/tcsc_document/122984064614707200" target="_blank" rel="noreferrer">
              产品建模流程
            </a>
          }
          extra={
            modeling === ApprovalResultType.disApproval && (
              <span style={{ color: 'red' }}>未完成产品建模不允许确认通过</span>
            )
          }
          onChange={setModeling}
        />
        {modeling === ApprovalResultType.approval &&
          (isDisabled && !editContentState ? (
            <Form.Item label="产品建模">
              <Form.Text>
                <a href={formData?.ModelingInfo} target="_blank" rel="noreferrer">
                  链接 <Icon type="externallink" />
                </a>
                <Text copyable={{ text: `${formData?.ModelingInfo}` }} />
              </Form.Text>
            </Form.Item>
          ) : (
            <TcsFormText
              name="ModelingInfo"
              label="产品建模"
              fieldProps={{
                size: 'l',
              }}
              rules={[
                {
                  required: true,
                  message: '请填写对应建模链接',
                },
              ]}
              placeholder="请填写对应建模链接"
            />
          ))}
        <TcsFormRadio
          name="Quotation"
          label="是否支持产品报价"
          options={productOptions}
          rules={[
            {
              required: true,
              message: '请选择是否支持产品报价',
            },
          ]}
          extra={
            quotation === ApprovalResultType.disApproval && (
              <span style={{ color: 'red' }}>未支持产品报价不允许确认通过</span>
            )
          }
          tooltip={
            <a href="https://jiguang.woa.com/page/tcsc_document/122980020813869056" target="_blank" rel="noreferrer">
              业务规划流程
            </a>
          }
          onChange={setQuotation}
        />

        {quotation === ApprovalResultType.approval &&
          (isDisabled && !editContentState ? (
            <Form.Item label="产品报价">
              <Form.Text>
                <a href={formData?.QuotationInfo} target="_blank" rel="noreferrer">
                  链接 <Icon type="externallink" />
                </a>
                <Text copyable={{ text: `${formData?.QuotationInfo}` }} />
              </Form.Text>
            </Form.Item>
          ) : (
            <TcsFormText
              name="QuotationInfo"
              label="产品报价"
              fieldProps={{
                size: 'l',
              }}
              rules={[
                {
                  required: true,
                  message: '请填写对应业务规划链接',
                },
              ]}
              placeholder="请填写对应业务规划链接"
            />
          ))}
      </TcsForm>
      <NextStage
        NowStageName="ReleaseTester"
        formRef={formRef}
        changeOwner={ownerLimit}
        isApproval={ownerLimit && modeling === ApprovalResultType.approval && quotation === ApprovalResultType.approval}
        isReject={ownerLimit}
      />
    </>
  );
};
export default DeploymentVerificationApproval;
