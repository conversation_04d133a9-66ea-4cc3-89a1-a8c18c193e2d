import ReleaseVersionContext from '@/common/context/release';
import { TcsCard, TcsTable } from '@tencent/tcs-component';
import { Alert, Form } from '@tencent/tea-component';
import React, { useContext, useEffect, useState } from 'react';
import { ManagerStepName, StageStatusType } from '../../../../constants';
import NextStage from '../../../NextStage';
import getCloumns from './config';
export interface IProps {
  nowStageName: string;
}
const LeaderApproval = ({ nowStageName }: IProps) => {
  const { stageInfo, stageDetail, baseInfo, stageOwner, isManage } = useContext(ReleaseVersionContext);
  const ownerLimit = stageOwner || isManage;

  const {
    Total,
    ClosedFatalCount,
    ClosedSecurityCount,
    ClosedSeriousCount,
    FatalCount,
    SecurityCount,
    SeriousCount,
    TotalClosedCount,
    TotalClosedRatio,
  } = baseInfo?.StatisticalReport?.BugReport || {
    Total: 0,
    ClosedFatalCount: 0,
    ClosedSecurityCount: 0,
    ClosedSeriousCount: 0,
    FatalCount: 0,
    SecurityCount: 0,
    SeriousCount: 0,
    TotalClosedCount: 0,
    TotalClosedRatio: 0,
  };
  const [dataSource, setDataSource] = useState<any>([]);
  const [rejectProductData, setRejectProductData] = useState<Record<string, any>[]>([]);

  const rejectColumns = [
    {
      title: '产品',
      dataIndex: 'ProductName',
    },
    {
      title: '原因',
      dataIndex: 'Reason',
    },
    {
      title: '操作人',
      dataIndex: 'Modifier',
    },
  ];

  const transformDataForTable = (data): any[] =>
    data?.flatMap((item) =>
      item.TaskRiskAssessments?.map((taskRiskAssessment) => ({
        ...taskRiskAssessment.AssessmentResult,
        StageName: item.Stage.Description,
        ProductName: taskRiskAssessment.Step?.Product?.Name,
        Modifier: taskRiskAssessment.Step?.Modifier,
        Description: taskRiskAssessment?.Step?.Description,
        Time: taskRiskAssessment?.Step?.Content,
      })),
    );
  const rejectProductForTable = (data): any[] =>
    data
      ?.flatMap((item) =>
        item.NoPublishAssessments?.map((rejectData) => ({
          ProductName: rejectData.Step?.Product?.Name,
          Modifier: rejectData.Step?.Modifier,
          Reason: rejectData.Step.Content.RejectComments,
        })),
      )
      ?.filter(Boolean);

  useEffect(() => {
    if (stageInfo) {
      const { Content } = stageInfo;
      if (stageInfo?.Name === ManagerStepName.Manager || stageInfo?.Name === ManagerStepName.GM) {
        if (Array.isArray(Content)) {
          const result = transformDataForTable(Content);
          setDataSource(result);
          setRejectProductData(rejectProductForTable(Content));
        } else {
          setDataSource([]);
          setRejectProductData([]);
        }
      }
    }
  }, [stageInfo]);

  return (
    <>
      {stageDetail?.Status === StageStatusType.processing && (
        <div>
          {stageInfo?.Content?.length !== 0 ? (
            <Alert>
              {`审批说明：本次发布解决方案版本 ${baseInfo?.SolutionVersion?.Code}， 涉及 ${
                baseInfo?.ReleaseProducts?.length
              } 个产品， ${baseInfo?.StatisticalReport?.StoryReport?.Total} 个需求。 共有缺陷  ${Total}
          个， 未关闭的缺陷 ${Total - TotalClosedCount} 个（致命缺陷 ${FatalCount - ClosedFatalCount}个， 严重缺陷 ${
                SeriousCount - ClosedSeriousCount
              } 个， 安全漏洞 ${SecurityCount - ClosedSecurityCount} 个），缺陷关闭率 ${TotalClosedRatio}%。
          本次发布可能存在风险，如下是存在未修复缺陷的产品责任人评估结果，请审批！`}
            </Alert>
          ) : (
            <Alert>本次发布无未关闭的缺陷单， 产品责任人评估无安全风险！</Alert>
          )}
        </div>
      )}
      {stageDetail?.Status === StageStatusType.approved && (
        <div style={{ marginBottom: 10 }}>
          <Form>
            <Form.Item label="审批说明">
              <Form.Text>{stageInfo?.Comments || '-'}</Form.Text>
            </Form.Item>
          </Form>
        </div>
      )}
      {!rejectProductData?.length || (
        <TcsCard title="不发布产品">
          <div style={{ height: 300 }}>
            <TcsTable
              columns={rejectColumns}
              dataSource={rejectProductData}
              options={false}
              cardBordered
              scrollInTable={true}
            />
          </div>
        </TcsCard>
      )}

      <TcsCard title="带伤发布详情">
        <div style={{ height: 500 }}>
          <TcsTable columns={getCloumns()} dataSource={dataSource} options={false} cardBordered scrollInTable={true} />
        </div>
      </TcsCard>
      <div style={{ marginTop: 10 }}>
        <NextStage
          changeOwner={false}
          NowStageName={nowStageName}
          extraInfo={stageInfo?.Content}
          leaderApproval={true}
          isReject={ownerLimit}
          isApproval={ownerLimit}
        />
      </div>
    </>
  );
};
export default LeaderApproval;
