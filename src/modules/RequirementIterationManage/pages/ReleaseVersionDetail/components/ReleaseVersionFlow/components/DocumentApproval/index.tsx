import ReleaseVersionContext from '@/common/context/release';
import { TcsForm, TcsFormText } from '@tencent/tcs-component';
import { Form, Icon, Text } from '@tencent/tea-component';
import React, { useContext, useEffect, useRef, useState } from 'react';
import { StageStatusType, URL_CHECKOUT } from '../../../../constants';
import { checkStageDisabled } from '../../../../utils';
import NextStage from '../../../NextStage';
import UpdateStepContent from '../UpdateStepContent';
export interface IProps {
  handleEditSuccess: (index) => void;
}
const DocumentApproval: React.FC<IProps> = ({ handleEditSuccess }) => {
  const [form] = TcsForm.useForm();
  const formRef = useRef<any>();
  const { stageOwner, stageInfo, baseInfo, isManage, stageDetail } = useContext(ReleaseVersionContext);
  const [formData, setFormData] = useState<Record<string, string>>();
  const isDisabled = checkStageDisabled(stageOwner, isManage, stageDetail, baseInfo);
  const [editContentState, setEditContentState] = useState<boolean>(false);

  const handleEditContent = (value) => {
    setEditContentState(value);
  };
  useEffect(() => {
    if (stageInfo) {
      form.setFieldsValue(stageInfo?.Content);
      setFormData(stageInfo?.Content);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [stageInfo]);

  return (
    <>
      {stageInfo?.Status === StageStatusType.approved && (
        <div style={{ float: 'right' }}>
          <UpdateStepContent
            handleConfirm={() => handleEditSuccess(stageDetail.StageOrder)}
            handleEdit={handleEditContent}
            stepInfo={stageInfo}
            form={formRef}
          />
        </div>
      )}

      <TcsForm form={form} formRef={formRef} readonly={isDisabled && !editContentState}>
        {isDisabled && !editContentState ? (
          <Form.Item label="文档链接">
            <Form.Text>
              <span>
                {formData?.DocumentURL ? (
                  <span>
                    <a href={formData?.DocumentURL} target="_blank" rel="noreferrer">
                      链接
                      <Icon type="externallink" />
                    </a>
                    <Text copyable={{ text: `${formData?.DocumentURL}` }} />
                  </span>
                ) : (
                  '-'
                )}
              </span>
            </Form.Text>
          </Form.Item>
        ) : (
          <TcsFormText
            label="文档链接"
            name="DocumentURL"
            tooltip="请输入正确的在线文档，开头必须以http/https开头"
            extra={isDisabled ? '' : '所有私有化文档都需要上传至私有化文档中心，请提供对应的文档链接'}
            rules={[
              {
                required: true,
                message: '请填写在线文档链接',
              },
              {
                validator(_: any, value: string) {
                  const regexp = URL_CHECKOUT;

                  if (regexp.test(value)) {
                    return Promise.resolve();
                  }
                  return '请填写正确格式的文档链接';
                },
              },
            ]}
            placeholder="请填写在线文档链接"
            fieldProps={{
              size: 'l',
            }}
          />
        )}
        <TcsFormText
          label="国际化文档完善度"
          name="InternationalDocumentComplete"
          rules={[
            {
              required: true,
              message: '请填写国际化文档完善度',
            },
          ]}
          placeholder="请填写国际化文档完善度"
          fieldProps={{
            size: 'l',
          }}
        />
      </TcsForm>

      <NextStage
        NowStageName="Document"
        formRef={formRef}
        isApproval={stageOwner || isManage}
        changeOwner={stageOwner || isManage}
      />
    </>
  );
};

export default DocumentApproval;
