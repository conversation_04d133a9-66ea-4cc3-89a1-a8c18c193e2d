import { FinishApprovalTask, GetProductReleaseWorkItemsReport } from '@/common/api/requirementIteration';
import ReleaseVersionContext from '@/common/context/release';
import {
  ProductLevelStepName,
  StageStatusType,
} from '@/modules/RequirementIterationManage/pages/ReleaseVersionDetail/constants';
import { formatPercentage } from '@/modules/RequirementIterationManage/pages/ReleaseVersionDetail/utils';
import { TcsButton, TcsModal, TcsPopConfirm } from '@tencent/tcs-component';
import { Icon, message } from '@tencent/tea-component';
import React, { useContext } from 'react';
import SelectIssueList from '../../../SelectIssueList';
export interface IProps {
  isVisible: boolean;
  toggle: () => void;
  iterationUUID: string;
  approvalId: string;
  productUUID: string;
  stepUUID: string;
  record: any;
}
const SelectProductIssue = ({
  isVisible,
  toggle,
  iterationUUID,
  approvalId,
  productUUID,
  stepUUID,
  record,
}: IProps) => {
  const handleCancel = () => {
    toggle();
  };
  const { handleUpdateSuccess } = useContext(ReleaseVersionContext);
  const handleNextConfirm = async () => {
    const { BugReport, Error } = await GetProductReleaseWorkItemsReport({
      ProductUUID: productUUID,
      ApprovalSheetUUID: approvalId,
    });
    if (Error) {
      return message.error({ content: Error.Message });
    }
    const {
      SeriousCount,
      ClosedSeriousCount,
      SecurityClosedRatio,
      FatalCount,
      ClosedFatalCount,
      Total,
      SecurityCount,
      TotalClosedRatio,
    } = BugReport;
    // 严重Bug修复率
    const seriousBugRatio = formatPercentage(ClosedSeriousCount, SeriousCount);
    // 致命bug修复率
    const fatalBugRatio = formatPercentage(ClosedFatalCount, FatalCount);
    // // 1. bug 关闭率 >=95%
    // // 2. 致命和严重 bug 修复率=100%
    // // 3. 安全工单关闭率 =100%

    // 如果不满足上面的需求，需要让用户二次确认
    // 计算需求是否有未完成的
    if (
      (Total !== 0 && TotalClosedRatio <= 95) ||
      (SeriousCount !== 0 && seriousBugRatio !== 100) ||
      (FatalCount !== 0 && fatalBugRatio !== 100) ||
      (SecurityCount !== 0 && SecurityClosedRatio !== 100)
    ) {
      TcsModal.confirm({
        title: (
          <div>
            <Icon type="warning" size="s" />
            当前选择的缺陷列表关闭率不符合条件，请再次确认是否需要通过？
          </div>
        ),
        content: <div>关闭条件：bug 关闭率大于95% ，致命和严重 bug 修复率=100% ， 安全工单关闭率 =100%</div>,
        onOk() {
          handleOk();
        },
      });
    } else {
      handleOk();
    }
  };
  const handleOk = () => {
    FinishApprovalTask({
      ApprovalStepUUID: stepUUID,
      Status: StageStatusType.approved,
      Comments: '',
      Content: {},
    }).then(({ Error }) => {
      if (Error) {
        return message.error({ content: Error.Message });
      }
      handleCancel();
      handleUpdateSuccess();
    });
  };
  return (
    <TcsModal
      title="选择发布列表"
      visible={isVisible}
      onCancel={handleCancel}
      width={1200}
      footer={
        record?.currentStepData?.Name === ProductLevelStepName.ProductConfirm ? (
          <>
            <TcsPopConfirm title="确认已添加好到所需发布功能，并进入下一步审批？" onConfirm={handleNextConfirm}>
              <TcsButton type="primary">审批通过</TcsButton>
            </TcsPopConfirm>
            <TcsButton onClick={handleCancel}>取消</TcsButton>
          </>
        ) : (
          false
        )
      }
      destroyOnClose
    >
      <SelectIssueList iterationUUID={iterationUUID} approvalId={approvalId} productUUID={productUUID} />
    </TcsModal>
  );
};
export default SelectProductIssue;
