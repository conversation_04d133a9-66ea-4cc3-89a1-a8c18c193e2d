import { toList<PERSON>aramsCApi } from '@/common/api/api';
import { ListIteration } from '@/common/api/interationManage';
import { AddWorkItemsToReleaseList } from '@/common/api/requirementIteration';
import useLookup from '@/common/hookups/useLookup';
import { CreateButtonEnterMap, IssueTypeConfig } from '@/modules/IterationManage/config';
import { TcsModal, TcsSpace, TcsTable } from '@tencent/tcs-component';
import { message } from '@tencent/tea-component';
import React, { useState } from 'react';
import getColumns from './config';
export interface IProps {
  isVisible: boolean;
  onClose: () => void;
  type: string;
  approvalId: string;
  handleAddSuccess: () => void;
  iterationUUID: string;
  selectTableData: any;
  productUUID: string;
}
const AddIssueToRelease = ({
  isVisible,
  onClose,
  type,
  approvalId,
  handleAddSuccess,
  iterationUUID,
  selectTableData,
  productUUID,
}) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };
  const { getLookupByCode, lookups } = useLookup(['IssueStatus']);

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    rowSelectable: (record: any) => {
      if (selectTableData?.find((item) => item.IssueID === record?.IssueID)) {
        return false;
      }
      return true;
    },
  };

  const handleAddIssueToRelease = async () => {
    try {
      const { Error, Message } = await AddWorkItemsToReleaseList({
        IssueIDList: selectedRowKeys,
        IssueType: IssueTypeConfig[type],
        ReleaseApprovalSheetUUID: approvalId,
      });
      if (Error) {
        return message.error({ content: Error.Message });
      }
      handleAddSuccess();
      handleCancel();
      message.success({ content: Message });
    } catch (error) {
      console.error(error);
    }
  };
  const fetchData = async (params) => {
    const { Error, Items, Total } = await ListIteration(
      {
        IssueType: IssueTypeConfig[type],
        RelatedToMe: false,
        TenantUUID: (window as any).jiguang_currentNs,
        FilterExpr: toListParamsCApi(
          {
            IterationUUID: iterationUUID,
            Title: params?.Title,
            IssueID: params?.IssueID,
            Status: params?.Status,
            ProductUUID: productUUID,
          },
          {
            useEqFields: ['IssueID', 'Type', 'IterationUUID', 'Status', 'ProductUUID'],
          },
        )._Filter?.filter((item) => item.Value !== '' && item.Value !== '%%'),

        PageNo: params?.current,
        PageSize: params?.pageSize,
      },
      type,
    );
    if (Error) {
      return message.error({ content: Error.Message });
    }
    const tableData = Items?.map((item) => ({
      ...item,
      key: item.IssueID,
    }));
    return {
      data: tableData || [],
      success: true,
      total: Total,
    };
  };
  const handleCancel = () => {
    onClose();
    setSelectedRowKeys([]);
  };

  return (
    <TcsModal
      visible={isVisible}
      title={`待发布${CreateButtonEnterMap[type]}列表`}
      onCancel={handleCancel}
      width={1000}
      destroyOnClose
      onOk={handleAddIssueToRelease}
    >
      <div style={{ height: 600 }}>
        <TcsTable
          // @ts-ignore
          columns={getColumns({ type, getLookupByCode, lookups })}
          rowKey="IssueID"
          cardBordered={true}
          pagination={{ defaultPageSize: 8 }}
          request={fetchData}
          search={{
            filterType: 'light',
          }}
          scrollInTable={true}
          rowSelection={rowSelection}
          tableAlertRender={({ selectedRowKeys, onCleanSelected }) => (
            <TcsSpace>
              <span>
                已选 {selectedRowKeys.length} 项
                <a style={{ marginInlineStart: 8 }} onClick={onCleanSelected}>
                  取消选择
                </a>
              </span>
            </TcsSpace>
          )}
        />
      </div>
    </TcsModal>
  );
};
export default AddIssueToRelease;
