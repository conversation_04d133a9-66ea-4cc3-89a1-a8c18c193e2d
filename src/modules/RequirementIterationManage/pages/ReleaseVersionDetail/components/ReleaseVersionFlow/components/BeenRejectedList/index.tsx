import { TcsCard, TcsModal, TcsTable } from '@tencent/tcs-component';
import dayjs from 'dayjs';
import React from 'react';
import { NowStageNameType } from '../../../../constants';
export interface IProps {
  beenRejectVisible: boolean;
  onClose: () => void;
  dataSource: any;
}
export interface IColumnsProps {
  // 区分被驳回和驳回
  type?: 'reject' | 'beenReject';
}
const getColumns = ({ type }: IColumnsProps) => [
  {
    title: '驳回操作人',
    dataIndex: 'Creator',
  },

  {
    title: '驳回阶段',
    dataIndex: 'BeenRejectStage',
    render: (text, record) =>
      type === 'reject'
        ? NowStageNameType[record?.BeenRejectedStage?.Name]
        : NowStageNameType[record?.OriginStage?.Name],
  },

  {
    title: '驳回原因',
    dataIndex: 'Comments',
  },

  {
    title: '驳回时间',
    dataIndex: 'CreatedAt',
    render: (text, record) => {
      if (record?.CreatedAt) {
        return dayjs(record?.CreatedAt).format('YYYY-MM-DD HH:mm:ss');
      }
      return '-';
    },
  },
];
const BeenRejectedList = ({ beenRejectVisible, onClose, dataSource }: IProps) => (
  <TcsModal visible={beenRejectVisible} onCancel={onClose} width="xl" footer={false} destroyOnClose>
    <TcsCard title="被驳回记录">
      <div style={{ height: 400 }}>
        <TcsTable
          dataSource={dataSource?.BeenRejectedHistories || []}
          columns={getColumns({ type: 'beenReject' })}
          options={false}
          cardBordered
          scrollInTable
        />
      </div>
    </TcsCard>

    <TcsCard title="驳回记录">
      <div style={{ height: 400 }}>
        <TcsTable
          dataSource={dataSource?.RejectedHistories || []}
          columns={getColumns({ type: 'reject' })}
          options={false}
          cardBordered
          scrollInTable
        />
      </div>
    </TcsCard>
  </TcsModal>
);
export default BeenRejectedList;
