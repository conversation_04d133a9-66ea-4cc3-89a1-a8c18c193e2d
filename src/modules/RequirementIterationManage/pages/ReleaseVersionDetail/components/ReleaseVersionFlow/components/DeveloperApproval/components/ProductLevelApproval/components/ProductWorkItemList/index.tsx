import { ListVersionReleaseWorkItems } from '@/common/api/requirementIteration';
import { TcsTable } from '@tencent/tcs-component';
import { Drawer, message } from '@tencent/tea-component';
import React, { useState } from 'react';
import { getColumns } from './config';
export interface workItemTypeProps {
  IssueType: string;
  SecurityBug: boolean;
  SeverityLevel: string;
  OpenedOnly: boolean;
}
export interface IProps {
  isVisible: boolean;
  onClose: () => void;
  productStepInfo: any;
  sheetBaseInfo: any;
  workItemType: workItemTypeProps;
}
const ProductWorkItemList = ({ isVisible, onClose, productStepInfo, sheetBaseInfo, workItemType }: IProps) => {
  const [loading, setLoading] = useState<boolean>(false);

  const fetchData = async (params) => {
    setLoading(true);
    try {
      const { Error, Total, Bugs, Stories } = await ListVersionReleaseWorkItems({
        IssueType: workItemType.IssueType,
        ApprovalSheetUUID: sheetBaseInfo?.UUID,
        SolutionVersionUUID: sheetBaseInfo?.SolutionVersionUUID,
        ProductUUID: productStepInfo?.ProductUUID,
        SecurityBugOnly: workItemType?.SecurityBug || false,
        OpenedOnly: workItemType?.OpenedOnly || false,
        PageNo: params.current,
        PageSize: params.pageSize,
        Status: params?.Status,
        SeverityLevel: params?.SeverityLevel,
      });
      if (Error) {
        setLoading(false);
        return message.error({ content: Error.Message });
      }
      setLoading(false);
      return {
        data: Bugs || Stories || [],
        success: true,
        total: Total,
      };
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <Drawer visible={isVisible} onClose={onClose} style={{ width: 1000 }} footer={false} destroyOnClose title="详情">
      <TcsTable
        // @ts-ignore
        request={fetchData}
        loading={loading}
        // @ts-ignore
        columns={getColumns({ type: workItemType?.IssueType })}
        pagination={{ defaultPageSize: 8 }}
        search={{ filterType: 'light' }}
        form={{
          initialValues: {
            SeverityLevel: workItemType?.SeverityLevel,
          },
        }}
      />
    </Drawer>
  );
};
export default ProductWorkItemList;
