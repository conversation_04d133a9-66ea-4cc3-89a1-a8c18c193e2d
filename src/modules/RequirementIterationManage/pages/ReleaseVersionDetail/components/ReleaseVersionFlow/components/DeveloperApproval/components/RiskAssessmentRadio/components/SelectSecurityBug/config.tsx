import { Tag } from '@tencent/tea-component';
import React from 'react';

export const getColumns = ({
  getLookupByCode,
  onDetail,
}: {
  getLookupByCode: (type, code) => any;
  onDetail?: (record) => void;
}) => [
  {
    title: 'ID',
    dataIndex: 'IssueID',
    width: '10%',
    search: false,
  },
  {
    title: '标题',
    dataIndex: 'Title',
    search: false,
    render(text, record: any) {
      return <a onClick={() => onDetail(record)}>{record.Title}</a>;
    },
  },

  {
    title: '缺陷类型',
    dataIndex: 'type',
    width: '15%',
    search: false,
    render(text, record) {
      const { Type } = record;
      const lookup = getLookupByCode('StoryType', Type);
      if (lookup) {
        return <Tag style={{ color: lookup.Extra?.Color }}>{lookup.Name}</Tag>;
      }
      return Type || '-';
    },
  },
  {
    title: '状态',
    dataIndex: 'Status',
    width: '10%',
    search: false,
    render(text, record) {
      const { Status } = record;
      const lookup = getLookupByCode('IssueStatus', Status);
      if (lookup) {
        return <Tag style={{ color: lookup.Extra?.Color }}>{lookup.Name}</Tag>;
      }
      return Status || '-';
    },
  },
];
