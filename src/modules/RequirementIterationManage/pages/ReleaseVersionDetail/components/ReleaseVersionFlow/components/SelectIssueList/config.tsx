import { IterationPathConfig } from '@/modules/IterationManage/config';
import { TcsButton, TcsPopConfirm } from '@tencent/tcs-component';
import { SelectOption } from '@tencent/tea-component';
import React from 'react';

export const getColumns = ({
  type,
  handleCancelWork,
  onDetail,
  productInfo,
}: {
  type: string;
  handleCancelWork: (record) => void;
  getLookupByCode: (type, code) => any;
  onDetail: (record) => void;
  lookups: any;
  productInfo: SelectOption[];
}) => [
  {
    title: 'ID',
    dataIndex: 'IssueID',
    width: '10%',
    copyable: true,
    search: false,
  },
  {
    title: '标题',
    dataIndex: 'Title',
    width: '20%',
    // search: false,
    render(text, record: any) {
      return <a onClick={() => onDetail(record)}>{record.Title}</a>;
    },
  },

  ...(type === IterationPathConfig.Story
    ? [
        {
          title: '需求类型',
          dataIndex: 'Type',
          width: '15%',
          search: false,
          valueType: 'dictSelect',
          fieldProps: {
            dictType: 'StoryType',
            showType: 'tag',
          },
        },
        {
          title: '需求分类',
          dataIndex: 'CategoryID',
          width: '15%',
          search: false,
          valueType: 'dictSelect',
          fieldProps: {
            dictType: 'StoryCategory',
            showType: 'tag',
          },
        },
      ]
    : [
        {
          title: '缺陷类型',
          dataIndex: 'type',
          width: '15%',
          search: false,
          valueType: 'dictSelect',

          fieldProps: {
            dictType: 'BugType',
            showType: 'tag',
          },
        },
      ]),
  {
    title: '关联产品',
    dataIndex: ['Product', 'Name'],
    valueType: 'select',
    fieldProps: {
      options:
        productInfo?.map((item) => ({
          value: item.value,
          label: item.text,
        })) || [],
    },
  },
  {
    title: '状态',
    dataIndex: 'Status',
    valueType: 'dictSelect',

    fieldProps: {
      dictType: 'IssueStatus',
      showType: 'tag',
    },
  },
  {
    title: '研发负责人',
    dataIndex: 'DevOwners',
    search: false,
    width: '10%',
  },

  {
    title: '操作',
    valueType: 'option',
    fixed: 'right',
    render: (text, record) => (
      <>
        <a href={record?.TapdUrl} target="_blank" rel="noreferrer">
          跳转至Tapd单
        </a>
        {type === IterationPathConfig.Story && (
          <TcsPopConfirm onConfirm={() => handleCancelWork(record)} title="确认取消该需求的发布？">
            <TcsButton type="link" style={{ marginLeft: 10 }}>
              取消发布
            </TcsButton>
          </TcsPopConfirm>
        )}
      </>
    ),
  },
];
