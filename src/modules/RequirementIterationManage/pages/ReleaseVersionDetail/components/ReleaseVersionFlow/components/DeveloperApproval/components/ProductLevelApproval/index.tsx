import {
  GetProductReleaseWorkItemsReport,
  GetVersionReleaseApprovalSheetWithDetail,
  GetVersionReleaseApprovalStageWithDetails,
} from '@/common/api/requirementIteration';
import { getUrlParams } from '@/common/utils';
import { TcsCard, TcsLayout, TcsSpin } from '@tencent/tcs-component';
import { Col, MetricsBoard, Row, message } from '@tencent/tea-component';
import React, { useEffect, useState } from 'react';
import { StageStatusType } from '../../../../../../constants';
import { checkRatio, checkRepotRatio } from '../../../../../../utils';
import DeveloperApprovalModal from '../DeveloperApprovalModal';
import ProductWorkItemList from './components/ProductWorkItemList';
export interface BugReportProps {
  Total: number;
  // FatalCount,
  SecurityCount: number;
  SeriousCount: number;
  TotalClosedCount: number;
  FatalCount: number;
  ClosedFatalCount: number;
  ClosedSecurityCount: number;
  ClosedSeriousCount: number;
  NotCloseFatalCount: number;
  NotClosedSecurityCount: number;
  NotTotalCount: number;
  NotClosedSeriousCount: number;
  TotalClosedRatio: number;
  SecurityClosedRatio: number;
  FatalClosedRatio?: number | string;
  SeriousClosedRatio?: number | string;
}
const ProductLevelApproval = () => {
  const urlParams = getUrlParams();
  const [stepInfo, setStepInfo] = useState<any>();
  const [spinning, setSpinning] = useState<boolean>(false);
  const [bugReport, setBugReport] = useState<BugReportProps>();
  const [storyReport, setStoryReport] = useState<Record<string, number>>();
  const [sheetBaseInfo, setSheetBaseInfo] = useState<Record<string, any>>();
  const [workItemVisible, setWorkItemVisible] = useState<boolean>(false);
  const [workItemType, setWorkItemType] = useState<any>();
  const [bugReportEqualNothing, setBugReportEqualNothing] = useState<boolean>(false);

  const handleViewProductWorkItems = async () => {
    try {
      const { BugReport, Error, StoryReport } = await GetProductReleaseWorkItemsReport({
        ProductUUID: urlParams?.ProductUUID,
        ApprovalSheetUUID: urlParams?.approval_id,
      });
      if (Error) {
        return message.error({ content: Error.Message });
      }
      const {
        Total,
        TotalClosedCount,
        FatalCount,
        ClosedFatalCount,
        SecurityCount,
        ClosedSecurityCount,
        SeriousCount,
        ClosedSeriousCount,
        TotalClosedRatio,
        SecurityClosedRatio,
      } = BugReport;
      setBugReport({
        Total,
        // FatalCount,
        SecurityCount,
        SeriousCount,
        TotalClosedCount,
        FatalCount,
        ClosedFatalCount,
        ClosedSecurityCount,
        ClosedSeriousCount,
        NotCloseFatalCount: FatalCount - ClosedFatalCount,
        NotClosedSecurityCount: SecurityCount - ClosedSecurityCount,
        NotTotalCount: Total - TotalClosedCount,
        NotClosedSeriousCount: SeriousCount - ClosedSeriousCount,
        TotalClosedRatio,
        SecurityClosedRatio,
        FatalClosedRatio: checkRepotRatio(FatalCount, ClosedFatalCount),
        SeriousClosedRatio: checkRepotRatio(SeriousCount, ClosedSeriousCount),
      });
      setStoryReport(StoryReport);
    } catch (error) {
      console.error('e:', error);
    }
  };
  const fetchStepData = async () => {
    setSpinning(true);
    try {
      const { Error, GetVersionReleaseApprovalStage } = await GetVersionReleaseApprovalStageWithDetails({
        UUID: urlParams?.approvalStageUUID,
      });

      if (Error) {
        return message.error({ content: Error.Message });
      }
      const findTaskInfo = GetVersionReleaseApprovalStage?.ApprovalTasks?.find(
        (item) => item.UUID === urlParams?.approvalTaskUUID,
      );
      // todo： 得优化逻辑
      const tableData = [findTaskInfo]?.map((item) => {
        // 优先找被驳回的，如果没有找到，再找处理中的
        const currentStepIndex: number =
          item?.ApprovalSteps?.findIndex(
            (step) => step?.Status === StageStatusType.beenRejected || step?.Status === StageStatusType.processing,
          ) || 0;
        // 如果找到了对应的步骤，添加到 currentStepData 中
        if (currentStepIndex !== -1) {
          return {
            ...item,
            currentStepData: item?.ApprovalSteps[currentStepIndex],
          };
        }
        // 如果没有找到任何匹配的步骤，返回原始的 item
        return item;
      });
      setStepInfo(tableData?.[0]);
    } catch (error) {
      console.error(error);
    } finally {
      setSpinning(false);
    }
  };
  const fetchSheetData = async () => {
    setSpinning(true);
    try {
      const { GetVersionReleaseApprovalSheet, Error } = await GetVersionReleaseApprovalSheetWithDetail({
        UUID: urlParams.approval_id,
      });
      if (Error) {
        return message.error({ content: Error.Message });
      }
      setSheetBaseInfo(GetVersionReleaseApprovalSheet);
      setSpinning(false);
    } catch (error) {
      console.error(error);
    }
  };
  useEffect(() => {
    fetchStepData();
    fetchSheetData();
    handleViewProductWorkItems();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const handleCheckItem = (value) => {
    setWorkItemVisible(true);
    setWorkItemType(value);
  };

  // const formItems = [
  //   { label: '总需求', count: storyReport?.Total, IssueType: 'story' },
  //   { label: '免测需求', count: storyReport?.TestFreeCount, IssueType: 'story' },
  //   { label: '总缺陷', count: bugReport?.Total, IssueType: 'bug' },
  //   { label: '未关闭缺陷', count: bugReport?.NotTotalCount, IssueType: 'bug', OpenedOnly: true },
  //   { label: '致命缺陷', count: bugReport?.FatalCount, IssueType: 'bug', SeverityLevel: 'fatal' },
  //   {
  //     label: '未关闭致命缺陷',
  //     count: bugReport?.NotCloseFatalCount,
  //     IssueType: 'bug',
  //     SeverityLevel: 'fatal',
  //     OpenedOnly: true,
  //   },

  //   { label: '严重缺陷', count: bugReport?.SeriousCount, IssueType: 'bug', SeverityLevel: 'serious' },
  //   {
  //     label: '未关闭严重缺陷',
  //     count: bugReport?.NotClosedSeriousCount,
  //     IssueType: 'bug',
  //     SeverityLevel: 'serious',
  //     OpenedOnly: true,
  //   },

  //   { label: '安全漏洞', count: bugReport?.SecurityCount, IssueType: 'bug', SecurityBug: true },
  //   {
  //     label: '未关闭安全漏洞',
  //     count: bugReport?.NotClosedSecurityCount,
  //     IssueType: 'bug',
  //     SecurityBug: true,
  //     OpenedOnly: true,
  //   },
  // ];
  // const FormItemWithLink = ({ label, count, IssueType, SecurityBug, SeverityLevel, OpenedOnly = false }) => (
  //   <Form.Item label={label}>
  //     <Form.Text>
  //       <a onClick={() => handleCheckItem({ IssueType, SecurityBug, SeverityLevel, OpenedOnly })}>{count || 0}</a>
  //     </Form.Text>
  //   </Form.Item>
  // );

  const handleCheckTotal = (value) => {
    const { IssueType, SecurityBug, SeverityLevel } = value;
    handleCheckItem({
      IssueType,
      SecurityBug: SecurityBug || false,
      SeverityLevel,
    });
  };
  const handleCheckClosed = (value) => {
    handleCheckItem(value);
  };
  const handleEditSuccess = () => {
    fetchStepData();
  };

  const test = [
    {
      id: 'story',
      title: <h3>总需求</h3>,
      value: storyReport?.Total,
      closedNum: storyReport?.TestFreeCount,
      standard: true,
      IssueType: 'story',
      ratio: checkRatio(storyReport?.Total, storyReport?.TestFreeRatio),
    },
    {
      id: 'bug',
      title: <h3>总缺陷</h3>,
      value: bugReport?.Total,
      closedNum: bugReport?.NotTotalCount,
      ratio: checkRatio(bugReport?.Total, bugReport?.TotalClosedRatio),
      standard: bugReport?.Total === 0 || (bugReport?.TotalClosedRatio && bugReport?.TotalClosedRatio >= 95),
      IssueType: 'bug',
      OpenedOnly: true,
    },
    {
      id: 'fatalBug',
      title: <h3>致命缺陷总数</h3>,
      value: bugReport?.FatalCount,
      closedNum: bugReport?.NotCloseFatalCount,
      ratio: checkRepotRatio(bugReport?.FatalCount, bugReport?.ClosedFatalCount),
      standard:
        bugReport?.FatalCount === 0 || checkRepotRatio(bugReport?.FatalCount, bugReport?.ClosedFatalCount) === 100,
      IssueType: 'bug',
      OpenedOnly: true,
      SeverityLevel: 'fatal',
    },
    {
      id: 'seriouBug',
      title: <h3>严重缺陷总数</h3>,
      value: bugReport?.SeriousCount,
      closedNum: bugReport?.NotClosedSeriousCount,
      ratio: checkRepotRatio(bugReport?.SeriousCount, bugReport?.ClosedSeriousCount),
      standard:
        bugReport?.SeriousCount === 0 ||
        checkRepotRatio(bugReport?.SeriousCount, bugReport?.ClosedSeriousCount) === 100,
      IssueType: 'bug',
      OpenedOnly: true,
      SeverityLevel: 'serious',
    },
    {
      id: 'securityBug',
      title: <h3>安全漏洞总数</h3>,
      value: bugReport?.SecurityCount,
      closedNum: bugReport?.NotClosedSecurityCount,
      ratio: checkRatio(bugReport?.SecurityCount, bugReport?.SecurityClosedRatio),
      standard:
        bugReport?.SecurityCount === 0 || (bugReport?.SecurityClosedRatio && bugReport?.SecurityClosedRatio === 100),
      IssueType: 'bug',
      OpenedOnly: true,
      SecurityBug: true,
    },
  ];
  // todo :得判断角色权限
  useEffect(() => {
    if (bugReport) {
      // 判断产品的缺陷关闭率是否为0
      setBugReportEqualNothing(bugReport?.NotTotalCount === 0);
    }
  }, [bugReport]);
  return (
    <TcsLayout title={`${stepInfo?.Product?.Name}产品负责人审批${urlParams?.ViewDetail ? '详情' : ''}`}>
      <TcsSpin spinning={spinning}>
        <TcsCard title="产品发布功能统计" collapsible={true}>
          {/* <TcsForm layout="inline" readonly>
            {formItems.map((item, index) => (
              <FormItemWithLink key={index} {...item} />
            ))}

          </TcsForm> */}
          <Row showSplitLine>
            {test?.map((item, index) => (
              <Col key={index}>
                <MetricsBoard
                  {...item}
                  infos={[
                    <>
                      <h3
                        onClick={(e) => {
                          e.stopPropagation();
                          handleCheckClosed(item);
                        }}
                      >
                        {index === 0 ? `免测需求数量 ` : `未关闭数量 `}
                        <span style={{ color: item.closedNum === 0 ? 'green' : 'red' }}>{`${
                          item.closedNum || 0
                        }`}</span>
                      </h3>
                      <h3>
                        {index === 0 ? `免测率 ` : `关闭率 `}
                        <span style={{ color: item.standard ? 'green' : 'red' }}>{`${item.ratio || 0}%`}</span>
                      </h3>
                    </>,
                  ]}
                  key={item.id}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCheckTotal(item);
                  }}
                />
              </Col>
            ))}
          </Row>
        </TcsCard>
        <DeveloperApprovalModal
          record={stepInfo}
          detailPage={!!urlParams.ViewDetail}
          sheetBaseInfo={sheetBaseInfo}
          workItemsList={bugReport}
          bugReportEqualNothing={bugReportEqualNothing}
          functionReport={{ ...bugReport, ...storyReport }}
          handleEditSuccess={handleEditSuccess}
        />
        <ProductWorkItemList
          isVisible={workItemVisible}
          onClose={() => setWorkItemVisible(false)}
          productStepInfo={stepInfo}
          sheetBaseInfo={sheetBaseInfo}
          workItemType={workItemType}
        />
      </TcsSpin>
    </TcsLayout>
  );
};
export default ProductLevelApproval;
