import {
  ApprovalResult,
  RiskAssessmentResultType,
} from '@/modules/RequirementIterationManage/pages/ReleaseVersionDetail/utils';
import { TcsFormRadio, TcsFormTextArea } from '@tencent/tcs-component';
import { Form, Icon, Text, Tooltip } from '@tencent/tea-component';
import React, { useEffect, useMemo, useState } from 'react';
export interface IProps {
  formData: Record<string, any>;
  developerApproval?: boolean;
  bugReportEqualZero?: boolean;
  functionReport?: any;
  readonly: boolean;
  editModel?: boolean;
}
const RiskAssessmentRadio = ({ formData, bugReportEqualZero, functionReport, readonly, editModel }: IProps) => {
  const [value, setValue] = useState<string>('');

  useEffect(() => {
    setValue(formData?.Conclusion);
  }, [formData, editModel]);

  const conclusionOptions = useMemo(() => {
    /**
     * 一般解决率>=95
     */

    const {
      TotalClosedRatio,
      SeriousClosedRatio,
      SecurityClosedRatio,
      FatalClosedRatio,
      SeriousCount,
      SecurityCount,
      FatalCount,
    } = functionReport || {};
    const options = [
      {
        label: (
          <Tooltip title="因为测试环境/机型缺失，导致无法开展相关测试">
            <span>
              已评估风险，受限发布
              <Icon type="help" style={{ marginBottom: 2 }} />
            </span>
          </Tooltip>
        ),
        value: ApprovalResult.RiskAssessmentReleaseWithLimitation,
      },
    ];
    const assessResult = {
      label: (
        <Tooltip title="功能不符合预期、产品bug或安全漏洞任何一种场景不达标（要求：功能符合预期，且致命bug&严重bug&安全漏洞 关闭率100%、一般及以下bug关闭率>=95%）但经过产品经理、研发leader、研发总监、TCE产品总监评估风险可控的情况下审批发布">
          <span>
            已评估风险，带伤发布
            <Icon type="help" style={{ marginBottom: 2 }} />
          </span>
        </Tooltip>
      ),
      value: ApprovalResult.assess,
    };
    const normalResult = {
      label: (
        <Tooltip title="各个环节均评估风险且功能符合要求，产品bug和安全漏洞修复已达标，且所有测试场景已测试通过。">
          <span>
            无风险，正常发布
            <Icon type="help" style={{ marginBottom: 2 }} />
          </span>
        </Tooltip>
      ),
      value: ApprovalResult.normal,
    };
    const isNormalRelease = () =>
      TotalClosedRatio >= 95 &&
      (SeriousCount === 0 || SeriousClosedRatio === 100) &&
      (SecurityCount === 0 || SecurityClosedRatio === 100) &&
      (FatalCount === 0 || FatalClosedRatio === 100);

    if (isNormalRelease() || bugReportEqualZero) {
      options.unshift(normalResult, assessResult);
    } else {
      options.unshift(assessResult);
    }
    if (!editModel) {
      options.push({
        label: (
          <Tooltip title="以下任何场景不达标，流程节点同学可拒绝发布，比如：功能不符合预期，产品bug或安全漏洞不达标（要求：致命bug&严重bug&安全漏洞 关闭率100%、一般及以下bug关闭率>=95%）">
            <span>
              存在风险，拒绝发布
              <Icon type="help" style={{ marginBottom: 2 }} />
            </span>
          </Tooltip>
        ),
        value: ApprovalResult.reject,
      });
    }

    return options;
  }, [functionReport, bugReportEqualZero, editModel]);

  return (
    <>
      {readonly ? (
        <Form.Item label="审批结论">
          <Form.Text>{RiskAssessmentResultType[value]}</Form.Text>
        </Form.Item>
      ) : (
        <TcsFormRadio
          name="Conclusion"
          label="审批结论"
          options={conclusionOptions}
          rules={[
            {
              required: true,
              message: '请选择风险是否可控',
            },
          ]}
          onChange={setValue}
          extra={
            editModel ? (
              <Text theme="warning">
                当前不允许更新结论为拒绝发布，若要更新为拒绝发布，请联系审批管理员进行流程驳回操作！
              </Text>
            ) : (
              ''
            )
          }
        />
      )}
      {value && value !== ApprovalResult.normal && (
        <TcsFormTextArea
          name="ScopeOfInfluence"
          label="主要影响"
          rules={[
            {
              required: true,
              message: '请填写主要影响',
            },
          ]}
          width="md"
          placeholder="请填写"
        />
      )}
    </>
  );
};

export default RiskAssessmentRadio;
