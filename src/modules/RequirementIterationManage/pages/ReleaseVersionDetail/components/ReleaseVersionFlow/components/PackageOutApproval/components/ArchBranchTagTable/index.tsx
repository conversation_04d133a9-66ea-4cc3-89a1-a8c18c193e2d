import useLookup from '@/common/hookups/useLookup';
import { TcsCard, TcsForm, TcsFormSelect, TcsTable } from '@tencent/tcs-component';
import React from 'react';

const ArchBranchTagTable = ({
  archType,
  form,
  loading,
  branchName,
  handleBranchName,
  fetchTagData,
  actionRef,
  branchOption,
  columns,
  selectRowKeys,
  onSelectRowChange,
}) => {
  const { getLookupByCode } = useLookup([]);
  return (
    <TcsCard style={{ marginBottom: 10 }} title={getLookupByCode('PackageArch', archType)?.Name || archType} bordered>
      <TcsForm form={form}>
        <TcsFormSelect
          label="制品分支"
          name={branchName}
          options={branchOption}
          fieldProps={{
            size: 'm',
            onChange: handleBranchName,
            showSearch: true,
          }}
        />
      </TcsForm>
      <div style={{ height: 500 }}>
        <TcsTable
          loading={loading}
          tableAlertRender={false}
          columns={columns}
          request={fetchTagData}
          actionRef={actionRef}
          rowSelection={{
            type: 'radio',
            selectedRowKeys: selectRowKeys,
            onChange: onSelectRowChange,
          }}
          scrollInTable
          search={{ filterType: 'light' }}
          pagination={{ pageSize: 5 }}
          style={{ marginTop: 10 }}
          rowKey="TagNum"
        />
      </div>
    </TcsCard>
  );
};

export default ArchBranchTagTable;
