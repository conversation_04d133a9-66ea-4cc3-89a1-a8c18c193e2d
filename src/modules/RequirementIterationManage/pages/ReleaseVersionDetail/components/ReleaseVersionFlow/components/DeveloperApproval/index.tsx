import ReleaseVersionContext from '@/common/context/release';
import { DefectManagementRoutePath } from '@/common/routePath';
import { TcsLayout, TcsTable } from '@tencent/tcs-component';
import React, { useContext, useEffect, useState } from 'react';
import { ProductLevelStepName, StageStatusType } from '../../../../constants';
import ChangeApprovalStageOwner from '../../../ChangeApprovalStageOwner';
import SelectProductIssue from './components/SelectProductIssue';
import getCloumns from './config';

const DeveloperApproval: React.FC = () => {
  const [changeVisible, setChangeVisible] = useState<boolean>(false);
  const { baseInfo, productLevelStepData, stageDetail, isManage } = useContext(ReleaseVersionContext);
  const [record, setRecord] = useState<any>();
  const { history } = TcsLayout.useHistory();
  const [dataSource, setDataSource] = useState<string[]>([]);
  const [issueVisible, setIssueVisible] = useState<boolean>(false);
  const fetchData = (params) => {
    const currentJiguangName = window.jiguang_username;
    const tableData = productLevelStepData?.map((item) => {
      // 优先找被驳回的，如果没有找到，再找处理中的
      const currentStepIndex = item?.ApprovalSteps?.findIndex(
        (step) => step?.Status === StageStatusType.beenRejected || step?.Status === StageStatusType.processing,
      );
      if (currentStepIndex !== -1) {
        return {
          ...item,
          currentStepData: item.ApprovalSteps[currentStepIndex],
        };
      }
      return item;
    });

    const filterUserName: any = [];
    const filterNotUserName: any = [];
    tableData?.forEach((item) => {
      if (item?.currentStepData?.Owners?.includes(currentJiguangName)) {
        filterUserName.push(item);
      } else {
        filterNotUserName.push(item);
      }
    });
    let currentDataSource: any = [...filterUserName, ...filterNotUserName];
    currentDataSource = currentDataSource?.filter((item) => {
      const matchesProductName = params.ProductName
        ? item.Product.Name?.toLowerCase()?.includes(params.ProductName?.toLowerCase())
        : true;
      const matchesStatus = params.Status ? item.Status?.includes(params.Status) : true;
      const matchesStage = params.Stage ? item?.currentStepData?.Name?.includes(params.Stage) : true;
      const matchesOwners = params.Owners ? item?.currentStepData?.Owners?.includes(params.Owners) : true;
      return matchesProductName && matchesStatus && matchesStage && matchesOwners;
    });

    setDataSource(currentDataSource);
  };

  const handleApproval = (value) => {
    if (value?.currentStepData?.Name === ProductLevelStepName.ProductConfirm) {
      setIssueVisible(true);
      setRecord(value);
    } else {
      history.push(
        `${
          !baseInfo?.IterationUUID
            ? DefectManagementRoutePath.RELEASE_PLAN_MANAGE_RELEASE_VERSION_PRODUCT_PAGE
            : DefectManagementRoutePath.REQUIREMENT_ITERATION_REAlEASE_VERSION_PRODUCT_PAGE
        }?approval_id=${baseInfo?.UUID}&iteration_id=${baseInfo?.IterationUUID}&approvalStepUUID=${
          value?.currentStepData?.UUID
        }&&approvalStageUUID=${stageDetail?.UUID}&approvalTaskUUID=${value.UUID}&ProductUUID=${value?.ProductUUID}`,
      );
    }
  };
  const handleChangeOwner = (value) => {
    setRecord(value?.currentStepData);
    setChangeVisible(true);
  };
  const handleViewDetail = (value) => {
    history.push(
      `${
        !baseInfo?.IterationUUID
          ? DefectManagementRoutePath.RELEASE_PLAN_MANAGE_RELEASE_VERSION_PRODUCT_PAGE
          : DefectManagementRoutePath.REQUIREMENT_ITERATION_REAlEASE_VERSION_PRODUCT_PAGE
      }?approval_id=${baseInfo?.UUID}&iteration_id=${baseInfo?.IterationUUID}&approvalStepUUID=${
        value?.currentStepData?.UUID
      }&&approvalStageUUID=${stageDetail?.UUID}&approvalTaskUUID=${value.UUID}&ProductUUID=${
        value?.ProductUUID
      }&ViewDetail=true`,
    );
  };
  const handleSelectProductIssue = (record) => {
    setIssueVisible(true);
    setRecord(record);
  };

  useEffect(() => {
    if (productLevelStepData) {
      fetchData({});
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [productLevelStepData]);
  if (!productLevelStepData) {
    return <div>Loading...</div>;
  }

  return (
    <>
      <div style={{ height: 500 }}>
        <TcsTable
          dataSource={dataSource}
          // @ts-ignore
          request={fetchData}
          // @ts-ignore
          columns={getCloumns({
            handleApproval,
            handleChangeOwner,
            baseInfo,
            handleViewDetail,
            isManage,
            handleSelectProductIssue,
          })}
          scrollInTable
          search={{
            filterType: 'light',
          }}
          options={{
            reload: false,
          }}
        />
      </div>

      {/* <DeveloperApprovalModal
        isVisible={isVisible}
        onClose={() => setIsVisible(false)}
        record={record}
        onSuccess={handleUpdateSuccess}
        disabled={disabled}
        stageBaseInfo={stageDetail}
      ></DeveloperApprovalModal> */}
      <ChangeApprovalStageOwner
        isVisible={changeVisible}
        onClose={() => setChangeVisible(false)}
        nowStageName="ProductDeveloper"
        taskId={record?.UUID}
        nowOwners={record?.Owners}
      />
      <SelectProductIssue
        isVisible={issueVisible}
        toggle={() => setIssueVisible(false)}
        iterationUUID={baseInfo.IterationUUID}
        approvalId={baseInfo.UUID}
        productUUID={record?.ProductUUID}
        stepUUID={record?.ApprovalSteps?.[0]?.UUID}
        record={record}
      />
    </>
  );
};
export default DeveloperApproval;
