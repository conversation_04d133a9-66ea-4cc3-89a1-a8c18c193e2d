import { Button, Tag } from '@tencent/tea-component';
import React from 'react';
import { ProductLevelStageNameOptions, StageStatusType } from '../../../../constants';
import { checkStageDisabled } from '../../../../utils';
export interface IProps {
  handleApproval: (record) => void;
  handleChangeOwner: (record) => void;
  baseInfo?: any;
  handleViewDetail: (record) => void;
  isManage: boolean;
  handleSelectProductIssue: (value) => void;
}

const getCloumns = ({
  handleApproval,
  handleChangeOwner,
  baseInfo,
  handleViewDetail,
  isManage,
}: // handleSelectProductIssue,
IProps) => [
  {
    title: '产品',
    dataIndex: 'ProductName',
    renderText: (text, record) => record?.Product?.Name,
    copyable: true,
  },

  {
    title: '当前审批阶段',
    dataIndex: 'Stage',
    valueType: 'select',
    fieldProps: {
      options: ProductLevelStageNameOptions,
    },
    render(text, record) {
      if (record?.Status === StageStatusType.rejected) {
        return <Tag theme="warning">步骤被驳回</Tag>;
      }
      if (record?.currentStepData?.Description) {
        return <Tag theme="primary">{record?.currentStepData?.Description}</Tag>;
      }
      return <Tag theme="primary">已完成审批</Tag>;
    },
  },
  {
    title: '开始时间',
    dataIndex: 'CreatedAt',
    valueType: 'dateTime',
    search: false,
  },
  {
    title: '完成时间',
    dataIndex: 'UpdateAt',
    valueType: 'dateTime',
    search: false,
  },

  {
    title: '审批状态',
    dataIndex: 'Status',
    width: '10%',
    valueType: 'dictSelect',
    fieldProps: {
      dictType: 'ReleaseVersionStatus',
      showType: 'tag',
    },
  },
  {
    title: '审批详情',
    dataIndex: 'Detail',
    search: false,
    render(text, record) {
      if (record?.currentStepData?.StepOrder === 0) {
        return '-';
      }

      return (
        <Button type="link" onClick={() => handleViewDetail(record)}>
          审批详情
        </Button>
      );
    },
  },
  {
    title: '当前审批人',
    dataIndex: 'Owners',
    // search: false,
    render(text, record) {
      return record?.currentStepData?.Owners || '-';
    },
  },
  {
    title: '操作',
    valueType: 'option',
    search: false,
    width: '15%',
    render(text, record) {
      const { currentStepData, Status: recordStatus } = record || {};
      const { Owners, Status: stepStatus } = currentStepData || {};
      const isOwner = Owners?.includes(window.jiguang_username);

      const changeOwnerDisabled = !(
        currentStepData && !checkStageDisabled(isOwner, isManage, stepStatus || recordStatus, baseInfo)
      );

      return (
        <>
          <Button type="link" onClick={() => handleApproval(record)} disabled={changeOwnerDisabled}>
            审批
          </Button>
          <Button type="link" onClick={() => handleChangeOwner(record)} disabled={changeOwnerDisabled}>
            转交审批人
          </Button>
          {/* {currentStepData?.Name !== ProductLevelStepName.ProductConfirm && (
            <Button type="link" onClick={() => handleSelectProductIssue(record)} disabled={selectIssueLimit}>
              添加发布需求
            </Button>
          )} */}
        </>
      );
    },
  },
];

export default getCloumns;
