import { withRouteBasename } from '@/common/routePath';
import { IssueTypeConfig, IterationPathConfig } from '@/modules/IterationManage/config';
import React from 'react';

export const getColumns = ({ type }: { type: string }) => [
  {
    title: '标题',
    dataIndex: 'Title',
    width: '40%',
    search: false,
    linkable: true,
    linkProps: {
      linkUrl: (text, record) =>
        `${withRouteBasename(
          `/iteration_manage/${type === 'bug' ? IterationPathConfig.Defect : IterationPathConfig.Story}/detail`,
        )}?issue_id=${record.IssueID}`,
    },
  },

  {
    title: '状态',
    dataIndex: 'Status',
    width: '20%',
    search: true,
    // valueType: 'select',
    valueType: 'dictSelect',
    fieldProps: {
      dictType: 'IssueStatus',
      showType: 'tag',
    },
  },

  {
    title: '严重程度',
    dataIndex: 'SeverityLevel',
    search: true,
    width: '20%',
    hideInTable: type !== IssueTypeConfig[IterationPathConfig.Defect],
    valueType: 'dictSelect',
    fieldProps: {
      dictType: 'Severity',
      showType: 'tag',
    },
  },
  {
    title: '负责人',
    dataIndex: 'Owner',
    width: '10%',
    search: false,
    valueType: 'staffSelect',
  },
  {
    title: '操作',
    valueType: 'option',
    width: '15%',
    search: false,
    render: (text, record) => {
      if (record?.TapdUrl) {
        return (
          <a href={record?.TapdUrl} target="_blank" rel="noreferrer">
            跳转至TAPD单
          </a>
        );
      }
      return '-';
    },
  },
];
