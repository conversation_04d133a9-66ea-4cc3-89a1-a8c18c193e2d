import { toListParamsCApi } from '@/common/api/api';
import {
  DeleteWorkItemsFromReleaseList,
  ListSolutionProductRelation,
  ListVersionReleaseWorkItems,
} from '@/common/api/requirementIteration';
import ReleaseVersionContext from '@/common/context/release';
import { withRouteBasename } from '@/common/routePath';
import { CreateButtonEnterMap, IssueTypeConfig, IterationPathConfig } from '@/modules/IterationManage/config';
import { TcsTable, TcsTabs } from '@tencent/tcs-component';
import { Button, Switch, message } from '@tencent/tea-component';
import React, { useContext, useEffect, useRef, useState } from 'react';
import AddIssueToRelease from './components/AddIssueToRelease';
import { getColumns } from './config';
// import { getColumns } from './config';
const { TabPane } = TcsTabs;
export interface IProps {
  approvalId: string;
  iterationUUID: string;
  productUUID?: string;
}
const SelectIssueList: React.FC<IProps> = ({ approvalId, iterationUUID, productUUID }) => {
  //   const columns: any = getColumns({});
  const [tabKey, setTabKey] = useState<string>(IterationPathConfig.Story);
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const actionRef = useRef<any>();
  const [tableDataSource, setTableDataSource] = useState<any>([]);
  const { handleUpdateSuccess, baseInfo } = useContext(ReleaseVersionContext);
  const [switchOnlyOpen, setSwitchOnlyOpen] = useState<boolean>(false);
  const storyDataRef = useRef<any>();
  const [productInfo, setProductInfo] = useState<any>([]);

  const tabItems = [
    {
      title: '需求',
      key: IterationPathConfig.Story,
    },
    {
      title: '缺陷',
      key: IterationPathConfig.Defect,
    },
  ];
  const handleTabChange = (value) => {
    setTabKey(value);
    setSwitchOnlyOpen(false);
  };

  const fetchData = async (params) => {
    try {
      const { Error, Total, Bugs, Stories } = await ListVersionReleaseWorkItems({
        IssueType: IssueTypeConfig[tabKey],
        ApprovalSheetUUID: approvalId,
        ProductUUID: productUUID,
        SolutionVersionUUID: baseInfo?.SolutionVersionUUID,
        PageNo: params.current,
        PageSize: params.pageSize,
        OpenedOnly: switchOnlyOpen,
        Status: params?.Status,
      });
      if (Error) {
        return message.error({ content: Error.Message });
      }
      storyDataRef.current = Stories;
      setTableDataSource(tabKey === IterationPathConfig.Story ? Stories || [] : Bugs || []);
      return {
        data: tabKey === IterationPathConfig.Story ? Stories || [] : Bugs || [],
        success: true,
        total: Total,
      };
    } catch (error) {
      console.error(error);
    }
  };

  const handleCancelWork = async (record) => {
    try {
      const { Error, Message } = await DeleteWorkItemsFromReleaseList({
        IssueIDList: [record.IssueID],
        IssueType: IssueTypeConfig[tabKey],
        ReleaseApprovalSheetUUID: approvalId,
      });
      if (Error) {
        return message.error({ content: Error.Message });
      }
      handleAddSuccess();
      return message.success({ content: Message });
    } catch (error) {
      console.error(error);
    }
  };

  const handleAddSuccess = () => {
    actionRef.current?.reload();
    handleUpdateSuccess();
  };

  function handleDetail(record: any) {
    const url = `${withRouteBasename(`/iteration_manage/${tabKey}/detail`)}?issue_id=${record.IssueID}`;
    window.open(url, '_blank');
  }
  const handleSwicthChange = (value) => {
    setSwitchOnlyOpen(value);
    actionRef?.current?.reload();
  };

  useEffect(() => {
    ListSolutionProductRelation({
      Query: toListParamsCApi(
        {
          SolutionVersionID: baseInfo?.SolutionVersionUUID,
          Branch: 'master',
          Tag: '',
        },
        {
          useEqFields: ['SolutionVersionID', 'Branch', 'Tag'],
          restParam: { _GroupBy: 'id' },
          page: false,
        },
      ),
      SolutionVersionID: baseInfo?.SolutionVersionUUID,
      Branch: 'master',
    }).then((res) => {
      if (res.Items) {
        setProductInfo(
          res.Items?.map((item) => ({
            text: item.Name,
            value: item.ProductUUID,
          })) || [],
        );
      } else {
        setProductInfo([]);
      }
    });
  }, [baseInfo]);

  return (
    <>
      <div style={{ height: 500, paddingBottom: 10 }}>
        <TcsTabs destroyInactiveTabPane onChange={handleTabChange} fullHeight={true}>
          {tabItems.map((tabItem) => (
            <TabPane tab={tabItem.title} key={tabItem.key}>
              {/* {tabKey === IterationPathConfig.Story && (
                <Alert type="warning">
                  请检查当前需求列表中是否存在未完成的需求！若存在，请联系需求负责人扭转需求状态；若不发布，可联系流程负责人取消发布。
                </Alert>
              )} */}

              <TcsTable
                actionRef={actionRef}
                // @ts-ignore
                key={tabKey}
                request={fetchData}
                scrollInTable={true}
                pagination={{ pageSize: 5 }}
                // @ts-ignore
                columns={getColumns({
                  type: tabKey,
                  handleCancelWork,
                  onDetail: handleDetail,
                  productInfo,
                })}
                search={{ filterType: 'light' }}
                toolBarRender={() => (
                  <>
                    <Switch onChange={handleSwicthChange}>
                      {tabKey === IterationPathConfig.Story ? '只查看未完成需求' : '只查看未关闭缺陷'}
                    </Switch>
                  </>
                )}
                cardBordered
                headerTitle={
                  tabKey === IterationPathConfig.Story && (
                    <Button type="primary" onClick={() => setIsVisible(true)}>
                      新增{CreateButtonEnterMap[tabKey]}
                    </Button>
                  )
                }
              />
            </TabPane>
          ))}
        </TcsTabs>

        <AddIssueToRelease
          isVisible={isVisible}
          onClose={() => setIsVisible(false)}
          type={tabKey}
          approvalId={approvalId}
          handleAddSuccess={handleAddSuccess}
          iterationUUID={iterationUUID}
          selectTableData={tableDataSource}
          productUUID={productUUID}
        />
      </div>
    </>
  );
};

export default SelectIssueList;
