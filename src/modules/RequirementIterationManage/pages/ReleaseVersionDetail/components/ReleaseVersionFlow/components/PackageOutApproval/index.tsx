import { RequirementIterationManage } from '@/common/api/requirementIteration';
import ReleaseVersionContext from '@/common/context/release';
import useLookup from '@/common/hookups/useLookup';
import { FormInstance, TcsActionType, TcsForm, TcsFormText } from '@tencent/tcs-component';
import { Button, Form, message, Text } from '@tencent/tea-component';
import React, { useContext, useEffect, useRef, useState } from 'react';
import { ArchType, BranchType, StageStatusType } from '../../../../constants';
import { checkStageDisabled } from '../../../../utils';
import NextStage from '../../../NextStage';
import UpdateStepContent from '../UpdateStepContent';
import ArchBranchTagTable from './components/ArchBranchTagTable';
export interface IProps {
  handleEditSuccess: (index) => void;
}
const PackageOutApproval = ({ handleEditSuccess }: IProps) => {
  const { stageInfo, stageOwner, baseInfo, isManage, stageDetail } = useContext(ReleaseVersionContext);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
  const [selectArmRowKeys, setSelectArmRowKeys] = useState<any>([]);
  const [currentStageData, setCurrentStageData] = useState<any>();
  const [selectArmRows, setSelectArmRows] = useState<any>();
  const [selectAmdRows, setSelectAmdRows] = useState<any>();
  const [loading, setLoading] = useState<boolean>(false);
  const { getLookupByCode } = useLookup(['PackageArch']);
  const [branchOption, setBranchOption] = useState<any>([]);
  const [branchName, setBranchName] = useState<string>('');
  const [armBranchName, setArmBranchName] = useState<string>('');
  const [xForm] = TcsForm.useForm();
  const [armForm] = TcsForm.useForm();
  const [form] = TcsForm.useForm();
  const formRef = useRef<any>();

  const [editContentState, setEditContentState] = useState<boolean>(false);
  const isDisabled = checkStageDisabled(stageOwner, isManage, stageDetail, baseInfo);
  const ownerLimit = stageOwner || isManage;

  const actionRef = useRef<TcsActionType>();
  const armActionRef = useRef<TcsActionType>();

  const [armLoading, setArmLoading] = useState<boolean>(false);

  const handleTarget = (value) => {
    const url = `${
      window.location.origin
    }/page/product_center/workspace/solution/edit_tag?&readonly=true&solutionVersionCode=${
      baseInfo?.SolutionVersion.Code
    }&solutionVersionUUID=${baseInfo?.SolutionVersionUUID}&tenantId=${(window as any).jiguang_currentNs}&dataBranch=${
      value.dataBranch
    }&artifactBranchUUID=${value.ArtifactBranchUUID}&defaultArch=${value.Arch}&tagId=${value.ID}`;
    window.open(url, '_blank');
  };
  const columns = [
    {
      title: '正式Tag',
      dataIndex: 'TagNum',
      search: false,
      render: (text, record) => (
        <Button type="link" onClick={() => handleTarget(record)}>
          {record?.TagNum}
        </Button>
      ),
    },
    {
      title: '发布状态',
      dataIndex: 'PublishStatus',
    },
    {
      title: '自定义标签',
      dateIndex: 'Label',
    },
    {
      title: '变更说明',
      dataIndex: 'TagDescription',
      width: '30%',
    },
    {
      title: '操作人',
      dataIndex: 'Modifier',
      search: false,
    },
    {
      title: '生成时间',
      dataIndex: 'CreatedAt',
      search: false,
      valueType: 'dateTime',
    },
  ];

  const onSelectChange = (newSelectedRowKeys: React.Key[], selectRows: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
    setSelectAmdRows(selectRows?.[0]);
  };
  const onSelectArmChange = (newSelectedRowKeys: React.Key[], selectRows) => {
    setSelectArmRowKeys(newSelectedRowKeys);
    setSelectArmRows(selectRows?.[0]);
  };

  const handleBranchName = (value) => {
    setBranchName(value);
  };
  const handleArmBranchName = (value) => {
    setArmBranchName(value);
  };

  const fetchTagData = async (archType, branchName, setLoadingFunc, params) => {
    setLoadingFunc(true);
    try {
      const res = await RequirementIterationManage.ListSolutionVersionArtifactTagsSimple({
        SolutionVersionID: baseInfo?.SolutionVersionUUID,
        ArtifactBranchUUID: branchName,
        Arch: archType,
        ...params,
      });
      setLoadingFunc(false);
      return {
        data: res.data || [],
        success: true,
        total: res.total,
      };
    } catch (error) {
      setLoadingFunc(false);
      console.error(error);
      return {
        data: [],
        success: false,
      };
    }
  };

  const fetchAmdTagData = (params) => fetchTagData(ArchType.amd, branchName, setLoading, params);
  const fetchArmTagData = (params) => fetchTagData(ArchType.arm, armBranchName, setArmLoading, params);

  const handleEditContent = (value) => {
    setEditContentState(value);
  };

  useEffect(() => {
    const content = stageInfo?.Content;
    // 这里是为了兼容之前的数据，以前是数据，现在是对象形式
    let contentArrayTag: any[] = [];
    let confirmConclusion = '';
    if (Array.isArray(content)) {
      contentArrayTag = content;
    } else if (content && Object.keys(content).length > 0) {
      contentArrayTag = content.ArtifactTag || [];
      confirmConclusion = content.ConfirmConclusion || '';
    }
    const baseArch = baseInfo?.Archs?.split(';') || [];

    const filterData = contentArrayTag.filter((item) => baseArch.includes(item.key));

    const getArchData = (archType: string) => filterData.find((item) => item.key === archType) || {};
    const { BranchName: amdData, TagNum: amdTag } = getArchData(ArchType.amd);
    const { BranchName: armData, TagNum: armTag } = getArchData(ArchType.arm);
    setCurrentStageData(filterData);
    const updateFormState = (
      value: string,
      setState: React.Dispatch<React.SetStateAction<string>>,
      form?: FormInstance,
      field?: string,
    ) => {
      setState(value || '');
      form?.setFieldsValue({ [field!]: value });
    };
    updateFormState(amdData, setBranchName, xForm, 'BranchName');
    updateFormState(armData, setArmBranchName, armForm, 'ArmBranchName');
    form.setFieldValue('ConfirmConclusion', confirmConclusion);
    const handleSelection = (tag: string | number, setKeys: React.Dispatch<React.SetStateAction<string[]>>) => {
      const shouldClear = stageDetail?.Status === StageStatusType.processing;
      setKeys(shouldClear ? [] : tag ? [`${tag}`] : []);
    };

    if (stageDetail) {
      handleSelection(amdTag, setSelectedRowKeys);
      handleSelection(armTag, setSelectArmRowKeys);
    } else {
      setSelectedRowKeys([]);
      setSelectArmRowKeys([]);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [stageInfo, stageDetail, baseInfo?.Archs]);

  useEffect(() => {
    if (baseInfo) {
      RequirementIterationManage.ListSolutionVersionArtifactBranches({
        SolutionVersionID: baseInfo?.SolutionVersionUUID,
      })
        .then((res) => {
          setBranchOption(
            res.ListSolutionVersionArtifactBranches?.map((item) => ({
              label: `${item.BranchName}-(${BranchType[item.BranchType]})`,
              value: item.UUID,
            })),
          );
          const fisrtUUID = res.ListSolutionVersionArtifactBranches?.[0].UUID;
          setBranchName(fisrtUUID);
          setArmBranchName(fisrtUUID);
          xForm?.setFieldsValue({ BranchName: fisrtUUID });
          armForm?.setFieldsValue({ ArmBranchName: fisrtUUID });
        })
        .catch((e) => {
          message.error(e?.message || '获取分支失败');
        });
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [baseInfo]);
  useEffect(() => {
    actionRef.current?.reload();
  }, [branchName]);
  useEffect(() => {
    armActionRef.current?.reload();
  }, [armBranchName]);

  return (
    <>
      {stageInfo?.Status === StageStatusType.approved && (
        <div style={{ float: 'right' }}>
          <UpdateStepContent
            handleConfirm={() => handleEditSuccess(stageDetail.StageOrder)}
            handleEdit={handleEditContent}
            stepInfo={stageInfo}
            stepContent={{
              ArtifactTag: [
                selectedRowKeys?.length
                  ? {
                      Arch: 'rhel.amd64(x86_64)',
                      key: 'rhel.amd64',
                      TagNum: selectedRowKeys?.[0],
                      ...selectAmdRows,
                      BranchName: branchName,
                    }
                  : null,
                selectArmRowKeys?.length
                  ? {
                      Arch: 'rhel.arm64(aarch64)',
                      key: 'rhel.arm64',
                      TagNum: selectArmRowKeys?.[0],
                      ...selectArmRows,
                      BranchName: armBranchName,
                    }
                  : null,
              ].filter(Boolean),
            }}
            form={formRef}
          />
        </div>
      )}
      <TcsForm formRef={formRef} form={form} readonly={isDisabled && !editContentState} style={{ marginBottom: 10 }}>
        <TcsFormText
          label="准出验证结论"
          name="ConfirmConclusion"
          rules={[
            {
              required: true,
              message: '请填写准出验证结论',
            },
          ]}
          width="md"
          placeholder="请填写准出验证结论"
        />
      </TcsForm>
      {!isDisabled || editContentState ? (
        <>
          {baseInfo?.Archs?.includes(ArchType.amd) && (
            <>
              <ArchBranchTagTable
                archType={ArchType.amd}
                form={xForm}
                loading={loading}
                branchName="BranchName"
                handleBranchName={handleBranchName}
                fetchTagData={fetchAmdTagData}
                actionRef={actionRef}
                branchOption={branchOption}
                columns={columns}
                selectRowKeys={selectedRowKeys}
                onSelectRowChange={onSelectChange}
              />
            </>
          )}
          {baseInfo?.Archs?.includes(ArchType.arm) && (
            <>
              <ArchBranchTagTable
                archType={ArchType.arm}
                form={armForm}
                loading={armLoading}
                branchName="ArmBranchName"
                handleBranchName={handleArmBranchName}
                fetchTagData={fetchArmTagData}
                actionRef={armActionRef}
                branchOption={branchOption}
                columns={columns}
                selectRowKeys={selectArmRowKeys}
                onSelectRowChange={onSelectArmChange}
              />
            </>
          )}
        </>
      ) : (
        <TcsForm>
          <TcsForm.Item label="解决方案">
            <Form.Text>{baseInfo?.SolutionVersion?.Solution?.Name || '-'}</Form.Text>
          </TcsForm.Item>
          <TcsForm.Item label="解决方案版本">
            <Form.Text>{baseInfo?.SolutionVersion?.Code || '-'}</Form.Text>
          </TcsForm.Item>

          {currentStageData?.map((item, index) => (
            <React.Fragment key={index}>
              <br />
              <Text theme="success">{getLookupByCode('PackageArch', item.Arch)?.Name || ArchType.arm}</Text>
              <TcsForm.Item label={`制品分支`} key={index}>
                <Form.Text>{branchOption?.find((branch) => branch.value === item?.BranchName)?.label || '-'}</Form.Text>
              </TcsForm.Item>
              <TcsForm.Item label={`制品Tag`}>
                <Form.Text>
                  <Button type="link" onClick={() => handleTarget(item)}>
                    {item?.TagNum || '-'}
                  </Button>
                </Form.Text>
              </TcsForm.Item>
            </React.Fragment>
          ))}
        </TcsForm>
      )}

      <NextStage
        NowStageName="CMO"
        /**
         * 由于数据来源于单选表格，所以所传数据需要自行拼接
         */
        extraInfo={{
          ArtifactTag: [
            selectedRowKeys?.length
              ? {
                  Arch: 'rhel.amd64(x86_64)',
                  key: 'rhel.amd64',
                  TagNum: selectedRowKeys?.[0],
                  ...selectAmdRows,
                  BranchName: branchName,
                }
              : null,
            selectArmRowKeys?.length
              ? {
                  Arch: 'rhel.arm64(aarch64)',
                  key: 'rhel.arm64',
                  TagNum: selectArmRowKeys?.[0],
                  ...selectArmRows,
                  BranchName: armBranchName,
                }
              : null,
          ].filter(Boolean),
        }}
        isApproval={ownerLimit}
        isReject={ownerLimit}
        formRef={formRef}
        changeOwner={ownerLimit}
      />
    </>
  );
};

export default PackageOutApproval;
