import { IterationPathConfig } from '@/modules/IterationManage/config';
import { Tag } from '@tencent/tea-component';
import React from 'react';

export const getColumns = ({ type, getLookupByCode, lookups }) => [
  {
    title: '标题',
    dataIndex: 'Title',
    width: '20%',
  },
  ...(type === IterationPathConfig.Story
    ? [
        {
          title: '需求类型',
          dataIndex: 'Type',
          width: '20%',
          search: false,
          render(text, record) {
            const { Type } = record;
            const lookup = getLookupByCode('StoryType', Type);
            if (lookup) {
              return <Tag style={{ color: lookup.Extra?.Color }}>{lookup.Name}</Tag>;
            }
            return Type || '-';
          },
        },
        {
          title: '需求分类',
          dataIndex: 'CategoryID',
          width: '20%',
          search: false,
          render(text, record) {
            const { CategoryID } = record;
            if (!CategoryID) {
              return '-';
            }
            const lookup = getLookupByCode('StoryCategory', CategoryID);
            if (lookup) {
              return lookup.Name;
            }
            return CategoryID || '-';
          },
        },
      ]
    : [
        {
          title: '缺陷类型',
          dataIndex: 'Type',
          width: '20%',
          search: false,
          render(text, record) {
            const { Type } = record;
            const lookup = getLookupByCode('BugType', Type);
            if (lookup) {
              return <Tag style={{ color: lookup.Extra?.Color }}>{lookup.Name}</Tag>;
            }
            return Type || '-';
          },
        },
      ]),
  {
    title: '关联产品',
    dataIndex: ['Product', 'Name'],
    valueType: 'select',
    search: false,
    width: '20%',
  },
  {
    title: '状态',
    dataIndex: 'Status',
    width: '15%',
    valueType: 'select',
    fieldProps: {
      options:
        lookups.IssueStatus?.map((item) => ({
          value: item.Code,
          label: item.Name,
        })) || [],
    },
    render(text, record) {
      const { Status } = record;
      const lookup = getLookupByCode('IssueStatus', Status);
      if (lookup) {
        return <Tag style={{ color: lookup.Extra?.Color }}>{lookup.Name}</Tag>;
      }
      return Status || '-';
    },
  },
];

export default getColumns;
