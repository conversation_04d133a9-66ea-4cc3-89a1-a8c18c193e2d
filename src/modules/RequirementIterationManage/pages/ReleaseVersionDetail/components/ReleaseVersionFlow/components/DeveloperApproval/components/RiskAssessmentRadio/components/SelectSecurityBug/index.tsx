import { Tcs<PERSON><PERSON>on, TcsModal, TcsSpace, TcsTable } from '@tencent/tcs-component';
import React, { useContext, useEffect, useState } from 'react';
import { getColumns } from './config';
import useLookup from '@/common/hookups/useLookup';
import { ListVersionReleaseWorkItems } from '@/common/api/requirementIteration';
import { message } from '@tencent/tea-component';
import ReleaseVersionContext from '@/common/context/release';
import { StageStatusType } from '../../../../constants';
export interface IProps {
  isVisible: boolean;
  onClose?: () => void;
  onSelectKeys: (value) => void;
  selectKeys: string[];
  record?: Record<string, any>;
  disabled: boolean;
}
const SelectSecurityBug = ({ isVisible, onClose, onSelectKeys, selectKeys, record, disabled }: IProps) => {
  const { getLookupByCode } = useLookup([]);
  const { baseInfo } = useContext(ReleaseVersionContext);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };
  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const handleCancel = () => {
    onClose?.();
    setSelectedRowKeys([]);
  };
  const fetchData = async (params) => {
    try {
      const { Error, Total, Bugs } = await ListVersionReleaseWorkItems({
        IssueType: 'bug',
        ApprovalSheetUUID: baseInfo.UUID,
        // 研发负责人需要ProductUUID
        ProductUUID: record?.ProductUUID,
        SecurityBugOnly: false,
        PageNo: params.current,
        PageSize: params.pageSize,
      });
      if (Error) {
        return message.error({ content: Error.Message });
      }
      return {
        data: Bugs || [],
        success: true,
        total: Total,
      };
    } catch (error) {
      console.error(error);
    }
  };
  const handleAddSelect = () => {
    onSelectKeys?.(selectedRowKeys);
    onClose?.();
  };
  useEffect(() => {
    if (isVisible) {
      setSelectedRowKeys(selectKeys);
    }
  }, [isVisible, selectKeys]);
  const isDisabled =
    [StageStatusType.approved, StageStatusType.doubleConfirmation]?.includes(record?.Status) || disabled;
  return (
    <TcsModal
      title="未关闭缺陷"
      visible={isVisible}
      onCancel={handleCancel}
      width={1000}
      destroyOnClose
      onOk={handleAddSelect}
      footer={
        isDisabled || (
          <>
            <TcsButton type="primary" onClick={handleAddSelect}>
              确认
            </TcsButton>
            <TcsButton onClick={handleCancel}>取消</TcsButton>
          </>
        )
      }
    >
      <TcsTable
        columns={getColumns({ getLookupByCode })}
        request={fetchData}
        rowKey="IssueID"
        rowSelection={rowSelection}
        pagination={{ defaultPageSize: 5 }}
        tableAlertRender={({ selectedRowKeys, onCleanSelected }) => {
          if (!isDisabled) {
            return (
              <TcsSpace>
                <span>
                  已选 {selectedRowKeys.length} 项
                  <a style={{ marginInlineStart: 8 }} onClick={onCleanSelected}>
                    取消选择
                  </a>
                </span>
              </TcsSpace>
            );
          }
          return false;
        }}
      />
    </TcsModal>
  );
};
export default SelectSecurityBug;
