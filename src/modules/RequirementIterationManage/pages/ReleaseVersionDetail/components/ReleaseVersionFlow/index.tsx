import {
  GetVersionReleaseApprovalStageWithDetails,
  IApprovalStageWithDetailsResponse,
} from '@/common/api/requirementIteration';
import ReleaseVersionContext from '@/common/context/release';
import { TcsSpin } from '@tencent/tcs-component';
import { Button, Form, FormItem, FormText, Stepper, message } from '@tencent/tea-component';
import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';
import { ApprovalLevelType, StageStatusType } from '../../constants';
import BeenRejectedList from './components/BeenRejectedList';
import DeploymentVerificationApproval from './components/DeploymentVerificationApproval';
import DeveloperApproval from './components/DeveloperApproval';
import DocumentApproval from './components/DocumentApproval';
import LeaderApproval from './components/LeaderApproval';
import PackageOutApproval from './components/PackageOutApproval';
import SelectIssueList from './components/SelectIssueList';
import styles from './index.module.scss';
export interface IProps {
  approvalId: string;
  approvalStage: any;
  iterationUUID: string;
}
export interface IStepProps {
  id: string;
  label?: string;
  content: React.ReactNode;
  UUID?: string;
  StageOrder?: number;
  Status?: string;
  ApprovalLevel?: string;
}
const ReleaseVersionFlow = ({ approvalStage, approvalId, iterationUUID }: IProps) => {
  const [current, setCurrent] = useState<string>('WorkItemRange');
  const [num, setNum] = useState<number>(-1);
  const orderRef = useRef<number>(-1);
  const { getStageInfo, stageInfo, updateData } = useContext(ReleaseVersionContext);
  const [spinning, setSpinning] = useState<boolean>(false);
  const stepBarRef = useRef<any>(null);
  const [isNumStable, setIsNumStable] = useState(false);
  const numStabilityTimer = useRef<any>(null);
  const [stageRejectInfo, setStageRejectInfo] = useState<IApprovalStageWithDetailsResponse.Response>();
  const [rejectDataVisible, setRejectDataVisible] = useState<boolean>(false);
  const [currentStageData, setCurrentStageData] = useState<IApprovalStageWithDetailsResponse.ApprovalSteps>();

  const steps: IStepProps[] = [
    {
      id: 'WorkItemRange',
      content: <SelectIssueList approvalId={approvalId} iterationUUID={iterationUUID} />,
    },
    {
      id: 'Document',
      content: <DocumentApproval handleEditSuccess={handleEditSuccess} />,
    },
    {
      id: 'Product',
      content: <DeveloperApproval />,
    },

    { id: 'CMO', content: <PackageOutApproval handleEditSuccess={handleEditSuccess} /> },
    {
      id: 'ReleaseTester',
      content: <DeploymentVerificationApproval handleEditSuccess={handleEditSuccess} />,
    },
    { id: 'Manager', content: <LeaderApproval nowStageName="Manager" /> },

    { id: 'GM', content: <LeaderApproval nowStageName="GM" /> },
  ];

  // 根据接口给的模版来展示
  const stepsResult = useMemo(() => {
    if (!approvalStage) {
      return [];
    }

    const approvalData = approvalStage
      .map((stage) => {
        const matchedStep = steps.find((step) => step.id === stage.Name);
        if (matchedStep) {
          let detailInfo: React.ReactNode = '';
          if (stage.Status === StageStatusType.beenRejected) {
            detailInfo = '被驳回';
          } else if (stage.Status === StageStatusType.rejected) {
            detailInfo = '已驳回';
          } else if (stage.Status === StageStatusType.approved) {
            detailInfo = '已完成';
          } else if (stage.Status === StageStatusType.processing) {
            detailInfo = '进行中';
          } else if (stage.Status === StageStatusType.waiting) {
            detailInfo = '待审批';
          } else if (stage.Status === StageStatusType.denied) {
            detailInfo = '拒绝带伤发布';
          }
          return {
            ...stage,
            content: matchedStep.content,
            id: matchedStep.id,
            label: stage.Description,
            detail: detailInfo,
          };
        }
        return stage;
      })
      ?.sort((a, b) => a.StageOrder - b.StageOrder);

    return approvalData;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [approvalStage]);

  const fetchStepData = async (stepUUID) => {
    try {
      setSpinning(true);
      const { Error, GetVersionReleaseApprovalStage } = await GetVersionReleaseApprovalStageWithDetails({
        UUID: stepUUID,
      });
      if (Error) {
        return message.error({ content: Error.Message });
      }

      setSpinning(false);
      getStageInfo(GetVersionReleaseApprovalStage);
      setStageRejectInfo(GetVersionReleaseApprovalStage);
      setCurrentStageData(GetVersionReleaseApprovalStage.ApprovalTasks[0]?.ApprovalSteps[0]);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    if (numStabilityTimer.current) {
      clearTimeout(numStabilityTimer.current);
    }
    // 设置一个新的计时器
    numStabilityTimer.current = setTimeout(() => {
      setIsNumStable(true); // num 稳定后更新状态
    }, 300);

    return () => {
      if (numStabilityTimer.current) {
        clearTimeout(numStabilityTimer.current);
      }
    };
  }, [num, updateData]);

  function updateCurrentStep(stepsResult, statusTypes) {
    for (const status of statusTypes) {
      let foundItem;
      for (let i = stepsResult.length - 1; i >= 0; i--) {
        if (stepsResult[i].Status === status) {
          foundItem = stepsResult[i];
          break;
        }
      }
      if (foundItem) {
        setCurrent(foundItem.id);
        setNum(foundItem.StageOrder);
        orderRef.current = foundItem.StageOrder;
        return;
      }
    }
  }

  // 使用数组按优先级顺序列出所有状态
  const statusPriority = [
    StageStatusType.processing,
    StageStatusType.beenRejected,
    StageStatusType.denied,
    StageStatusType.approved,
  ];
  useEffect(() => {
    if (stepsResult?.length !== 0) {
      updateCurrentStep(stepsResult, statusPriority);
    }
    const stepElements = stepBarRef.current.querySelectorAll('.tea-step__item');
    const stepNumElements = stepBarRef.current.querySelectorAll('.tea-step__item .tea-step__num');
    // 根据每一步的状态 添加新的classname 更改步骤条的颜色
    if (stepsResult) {
      for (let i = 0; i < stepsResult.length; ++i) {
        const currentClasses = stepNumElements[i].className.trim().split(/\s+/);
        const originalClasses = currentClasses.filter((c) => c.startsWith('tea-step__num'));
        stepNumElements[i].className = [...originalClasses, stepsResult[i].Status].join(' ');
      }
    }
    stepElements.forEach((stepElement, index) => {
      stepElement.addEventListener('click', () => handleStepClick(index));
    });

    return () => {
      stepElements.forEach((stepElement) => {
        stepElement.removeEventListener('click', handleStepClick);
      });
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [stepsResult]);

  useEffect(() => {
    // 当 num 稳定并且满足其他条件时，调用 fetchStepData
    if (isNumStable && stepsResult?.length !== 0) {
      if (stepsResult[orderRef.current]?.Status !== StageStatusType.waiting) {
        if (orderRef.current >= 0) {
          fetchStepData(stepsResult?.[orderRef.current]?.UUID);

          // stageInfo TODO: 研发负责人审批 ApprovalTasks 没有数据
          if (stageInfo) {
            setCurrent(stepsResult?.[orderRef.current]?.id);
          }
        }
        setIsNumStable(false); // 重置 num 稳定状态
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isNumStable, stepsResult, stageInfo]);

  const handleStepClick = (stepIndex) => {
    if (stepsResult[stepIndex]?.Status !== StageStatusType.waiting) {
      setNum(stepIndex);
      orderRef.current = stepIndex;
    }
  };
  function handleEditSuccess(index) {
    setNum(index);
    orderRef.current = index;
    fetchStepData(stepsResult?.[index]?.UUID);
  }

  return (
    <div>
      <div ref={stepBarRef} style={{ cursor: 'pointer' }}>
        <Stepper type="process" steps={stepsResult} current={current} className={styles.stepper} />
      </div>
      <hr />

      {(stageRejectInfo?.BeenRejectedHistories || stageRejectInfo?.RejectedHistories) && (
        <>
          <Button type="primary" onClick={() => setRejectDataVisible(true)}>
            驳回历史
          </Button>
        </>
      )}
      {stageRejectInfo?.ApprovalLevel === ApprovalLevelType.global && (
        <div>
          <Form layout="inline">
            <FormItem label="审批负责人">
              <FormText>{currentStageData?.Owners}</FormText>
            </FormItem>
          </Form>
        </div>
      )}

      <div style={{ marginTop: 10, marginBottom: 10, minHeight: 100 }}>
        <TcsSpin spinning={spinning}>
          {stepsResult[orderRef.current] ? stepsResult[orderRef.current].content : '已完成'}
        </TcsSpin>
      </div>

      <BeenRejectedList
        dataSource={stageRejectInfo}
        beenRejectVisible={rejectDataVisible}
        onClose={() => setRejectDataVisible(false)}
      />
    </div>
  );
};

export default ReleaseVersionFlow;
