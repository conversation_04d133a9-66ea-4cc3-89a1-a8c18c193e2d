import { FinishApprovalTask } from '@/common/api/requirementIteration';
import ReleaseVersionContext from '@/common/context/release';
import { DefectManagementRoutePath } from '@/common/routePath';
import { TcsForm, TcsFormSelect, TcsFormText, TcsFormTextArea, TcsLayout, TcsModal } from '@tencent/tcs-component';
import { SelectMultiple, message } from '@tencent/tea-component';
import React, { useContext, useEffect, useRef, useState } from 'react';
import { ApprovalLevelType, NowStageNameType, ProductLevelStepName, StageStatusType } from '../../constants';
export interface IProps {
  isVisible: boolean;
  onClose: () => void;
  stage?: number;
  NowStageName: string;
  nowStageContent: any;
  productApprovalStep?: any;
  sheetBaseInfo?: any;
}
const RejectModal: React.FC<IProps> = ({
  onClose,
  isVisible,
  NowStageName,
  nowStageContent,
  productApprovalStep,
  sheetBaseInfo,
}) => {
  const { handleUpdateSuccess, approvalSheetStage, stageInfo, baseInfo } = useContext(ReleaseVersionContext);
  const formRef = useRef<any>();
  const [form] = TcsForm.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const [options, setOptions] = useState<any>([]);
  const [approvalLevel, setApprovalLevel] = useState<boolean>(false);
  const [productOptions, setProductOptions] = useState<any>([]);
  const [productRejectStepOptions, setProductRejectStepOptions] = useState<any>([]);
  const [currentProducrLevel, setCurrentProductLevel] = useState<boolean>(false);
  const { history } = TcsLayout.useHistory();

  const handleReject = () => {
    formRef.current?.validateFields().then((value) => {
      setLoading(true);
      FinishApprovalTask({
        ApprovalStepUUID: stageInfo?.UUID || productApprovalStep?.currentStepData?.UUID,
        Status: StageStatusType.rejected,
        Comments: value?.Comments,
        RejectToStageUUID: value?.RejectToStageUUID,
        RejectToProductsUUID: value?.RejectToProductsUUID,
        RejectToStepUUID: value?.RejectToStepUUID,
        Content: nowStageContent,
      })
        .then(({ Error }) => {
          if (Error) {
            return message.error({ content: Error.Message });
          }
          handleCancel();
          handleUpdateSuccess();
          if (productApprovalStep) {
            history.push(
              `${DefectManagementRoutePath.REQUIREMENT_ITERATION_REAlEASE_VERSION_PAGE}?approval_id=${sheetBaseInfo?.UUID}&iteration_id=${sheetBaseInfo?.IterationUUID}`,
            );
          }
        })
        .catch((e) => {
          console.error(e);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };
  const handleChange = (values) => {
    handleProductLevelChange(values);
    const findLevel = approvalSheetStage?.find((item) => item.UUID === values);
    if (findLevel?.ApprovalLevel === ApprovalLevelType.product) {
      setApprovalLevel(true);
    } else {
      setApprovalLevel(false);
    }
  };
  const handleProductLevelChange = (values) => {
    if (values === productApprovalStep?.ApprovalStageUUID) {
      setCurrentProductLevel(true);
    } else {
      setCurrentProductLevel(false);
    }
  };
  const handleCancel = () => {
    setApprovalLevel(false);
    onClose?.();
    form?.resetFields();
  };

  const fetchStageOptions = (stageInfo) => {
    const orderApprovalSheetStage = stageInfo?.sort((a, b) => a.StageOrder - b.StageOrder);
    const selectOption = orderApprovalSheetStage?.map((item) => ({
      label: NowStageNameType[item.Name],
      value: item?.UUID,
      order: item.StageOrder,
    }));
    return selectOption;
  };

  useEffect(() => {
    if (isVisible) {
      if (approvalSheetStage?.length !== 0) {
        const selectOption = fetchStageOptions(approvalSheetStage);
        const findNowStageNum: any = approvalSheetStage?.find((item) => item.Name === NowStageName)?.StageOrder;
        const filterOptions = selectOption?.filter((item) => item.order < findNowStageNum);
        setOptions(filterOptions);
        const defaultData = filterOptions?.slice(-1)[0]?.value;
        form.setFieldsValue({
          RejectToStageUUID: defaultData,
        });
        handleChange(defaultData);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isVisible, approvalSheetStage, NowStageName]);

  useEffect(() => {
    if (isVisible) {
      if (productApprovalStep) {
        const filterProductOptions = productApprovalStep?.ApprovalSteps?.filter(
          (item) => item.UUID !== productApprovalStep.currentStepData?.UUID,
        );
        const ProductStepOptions = filterProductOptions?.map((item: any) => ({
          label: item?.Description,
          value: item?.UUID,
          order: item.StepOrder,
        }));
        const filterRejectStepOptions = ProductStepOptions?.filter(
          (item) => item.order < productApprovalStep?.currentStepData?.StepOrder,
        );
        setProductRejectStepOptions(filterRejectStepOptions);
      }
    }
  }, [isVisible, productApprovalStep]);
  useEffect(() => {
    if (isVisible) {
      if (sheetBaseInfo) {
        const selectOption = fetchStageOptions(sheetBaseInfo?.ApprovalStages);
        const findNowStageNum = sheetBaseInfo?.ApprovalStages?.find((item) => item.Name === NowStageName)?.StageOrder;
        const filterStageOptions = selectOption?.filter((item) => item.order < findNowStageNum);
        setOptions(filterStageOptions);
        const defaultData = filterStageOptions?.slice(-1)[0]?.value;
        form.setFieldsValue({
          RejectToStageUUID: defaultData,
        });
        if (![ProductLevelStepName.ProductConfirm]?.includes(productApprovalStep?.currentStepData?.Name)) {
          const filterStageOptions = selectOption?.filter((item) => item.order <= findNowStageNum);
          setOptions(filterStageOptions);
          const defaultData = filterStageOptions?.slice(-1)[0]?.value;
          form.setFieldsValue({
            RejectToStageUUID: defaultData,
          });
          handleProductLevelChange(defaultData);
        }
        // if (productApprovalStep?.currentStepData?.Name === ProductLevelStepName.ProductDirector) {
        //   // 只能驳回到选择功能列表阶段
        //   const filterStageOptions = selectOption?.[0];
        //   setOptions([filterStageOptions]);
        //   form.setFieldsValue({
        //     RejectToStageUUID: filterStageOptions.value,
        //   });
        // }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isVisible, sheetBaseInfo, NowStageName, productApprovalStep]);
  // 如果是产品阶段向本阶段驳回,需要选择驳回的步骤，只有产品级别的需要
  // 如果产品级别时产品经理审批，只能驳回到上层阶段

  useEffect(() => {
    if (isVisible) {
      if (baseInfo) {
        setProductOptions(
          baseInfo?.ReleaseProducts?.map((item) => ({
            text: item.Product?.Name,
            value: item.ProductUUID,
          })) || [],
        );
      }
    }
  }, [isVisible, baseInfo]);

  return (
    <TcsModal title="流程驳回" visible={isVisible} onCancel={handleCancel} onOk={handleReject} confirmLoading={loading}>
      <TcsForm initialValues={{ nowStage: NowStageNameType[NowStageName] }} formRef={formRef} form={form}>
        <TcsFormText readonly label="当前阶段" name="nowStage" fieldProps={{ size: 'full' }} />
        <TcsFormSelect
          label="驳回阶段"
          name="RejectToStageUUID"
          fieldProps={{ size: 'full' }}
          options={options}
          rules={[
            {
              required: true,
              message: '请选择驳回阶段',
            },
          ]}
          onChange={handleChange}
        />
        {approvalLevel && (
          <TcsForm.Item
            label="驳回产品"
            name="RejectToProductsUUID"
            rules={[
              {
                required: true,
                message: '请选择要驳回的产品',
              },
            ]}
            initialValue={[]}
          >
            <SelectMultiple
              options={productOptions}
              staging={false}
              appearance="button"
              searchable={true}
              matchButtonWidth={true}
              allOption={{
                value: 'all',
                text: '全选',
              }}
              placeholder="请选择要驳回的产品"
              size="full"
            />
          </TcsForm.Item>
        )}
        {currentProducrLevel && (
          <TcsFormSelect
            label="驳回步骤"
            name="RejectToStepUUID"
            fieldProps={{ size: 'full' }}
            options={productRejectStepOptions}
            rules={[
              {
                required: true,
                message: '请选择驳回阶段',
              },
            ]}
          />
        )}
        <TcsFormTextArea
          label="驳回原因"
          name="Comments"
          placeholder="请填写驳回原因"
          fieldProps={{ size: 'full' }}
          rules={[
            {
              required: true,
              message: '请输入驳回原因',
            },
          ]}
        />
      </TcsForm>
    </TcsModal>
  );
};
export default RejectModal;
