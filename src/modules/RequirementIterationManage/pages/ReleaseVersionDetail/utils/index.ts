import { StageStatusType } from '../constants';

export const RiskAssessmentResult = 'Conclusion';
export enum ApprovalResult {
  normal = 'RiskFree',
  reject = 'RefuseToRelease',
  assess = 'ReleaseWithInjuries',
  RiskAssessmentReleaseWithLimitation = 'ReleaseWithLimitation',
}
export const RiskAssessmentResultType = {
  RiskFree: '无风险，正常发布',
  RefuseToRelease: '存在风险，拒绝发布',
  ReleaseWithInjuries: '已评估风险，带伤发布',
  ReleaseWithLimitation: '已评估风险，受限发布',
};

export enum ConclusionType {
  ReleaseWithInjuries = 1,
  RefuseToRelease = 2,
  RiskFree = 3,
  ReleaseWithLimitation = 4,
}

export function checkFormData(values: Record<string, any>) {
  for (const key of Object.keys(values)) {
    if (key === RiskAssessmentResult) {
      if (values[key] === ApprovalResult.assess) {
        return ConclusionType.ReleaseWithInjuries;
      }
      if (values[key] === ApprovalResult.reject) {
        return ConclusionType.RefuseToRelease;
      }
      if (values[key] === ApprovalResult.normal) {
        return ConclusionType.RiskFree;
      }
      if (values[key] === ApprovalResult.RiskAssessmentReleaseWithLimitation) {
        return ConclusionType.ReleaseWithLimitation;
      }
    }
  }
}
export const checkRepotRatio = (totalCount, closedCount) => {
  if (totalCount === 0) {
    return 100;
  }
  const result = (closedCount / totalCount) * 100;
  if (result % 1 === 0) {
    return result;
  }
  if (totalCount === undefined || closedCount === undefined) {
    return 0;
  }
  return result.toFixed(2);
};
export const checkRatio = (total, value) => {
  if (total === 0) {
    return 100;
  }
  if (value) {
    if (value % 1 === 0) {
      return value;
    }
    return value.toFixed(2);
  }
  return value;
};

export const checkStageDisabled = (stageOwner, isManage, stageDetail, baseInfo) =>
  !(
    (stageOwner || isManage) &&
    stageDetail?.Status !== StageStatusType.approved &&
    stageDetail?.Status !== StageStatusType.rejected &&
    baseInfo?.Status !== StageStatusType?.canceled
  );

export function formatPercentage(part, total) {
  if (total === 0) {
    return '0%';
  }
  const percentage = (part / total) * 100;
  if (percentage % 1 === 0) {
    return percentage;
  }
  // 否则保留两位小数
  return percentage.toFixed(2);
}
