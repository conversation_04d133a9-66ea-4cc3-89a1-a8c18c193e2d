import { toListParamsCApi } from '@/common/api/api';
import { ListIteration } from '@/common/api/interationManage';
import { AddIssueListToIteration, ListIterationsWithDetail } from '@/common/api/requirementIteration';
import { ProForm } from '@/common/components';
import { withRouteBasename } from '@/common/routePath';
import { getUrlParams } from '@/common/utils';
import { CreateButtonEnterMap, IssueTypeConfig } from '@/modules/IterationManage/config';
import { TcsModal, TcsSpin, TcsTable } from '@tencent/tcs-component';
import { Select, SelectOptionWithGroup, message } from '@tencent/tea-component';
import React, { useEffect, useRef, useState } from 'react';

export interface IProps {
  addVisible: boolean;
  pagePath: string;
  onCancal: () => void;
  onConfirm: () => void;
  // 添加还是移除
  type: string;
  IterationSelectKey: string[];
  // 用来判断是发布计划还是迭代管理
  releaseType?: boolean;
}
const AddIssueToIteration = ({
  addVisible,
  pagePath,
  onCancal,
  onConfirm,
  type,
  IterationSelectKey,
  releaseType,
}: IProps) => {
  console.log('releaseType', releaseType);
  const urlParams = getUrlParams();
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  const [loading, setLoading] = useState<boolean>(false);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);

  const [options, setOptions] = useState<SelectOptionWithGroup[]>([]);
  const formRef = useRef<any>(null);
  const fetchData = async () => {
    try {
      setLoading(true);
      const res = await ListIterationsWithDetail({
        pageSize: 9999,
        current: 1,
      });
      if (res.Error) {
        return message.error({
          content: res.Error.Message,
        });
      }

      setOptions(
        res.ListIterations?.map((item) => ({
          value: item.UUID,
          text: item.Name,
        })),
      );
      setLoading(false);
    } catch (error) {
      console.error(error);
    }
  };
  useEffect(() => {
    if (addVisible) {
      if (type === 'remove') {
        fetchData();
      }
    }
  }, [addVisible, type]);
  const handleConfirm = async () => {
    const formData = formRef.current?.getFieldsValue();
    setConfirmLoading(true);
    try {
      const res = await AddIssueListToIteration({
        IterationUUID: type === 'remove' ? formData.IssueList || '' : urlParams.iteration_id,
        IssueType: IssueTypeConfig[pagePath],
        IssueIDList: type === 'remove' ? IterationSelectKey : selectedKeys,
      });
      if (res.Error) {
        setConfirmLoading(false);
        return message.error({
          content: res.Error.Message,
        });
      }
      setLoading(false);
      onConfirm?.();
      setSelectedKeys([]);
      setConfirmLoading(false);
      message.success({
        content: type === 'remove' ? '移出成功' : '添加成功',
      });
    } catch (error) {
      console.error(error);
    }
  };
  const handleCancel = () => {
    onCancal();
    setSelectedKeys([]);
  };

  // 当前选中的消息
  const fetchTableData = (params) => {
    setLoading(true);
    return ListIteration(
      {
        TAPD: params.TAPD,
        FilterExpr: toListParamsCApi(
          {
            IterationUUID: releaseType ? undefined : '',
            ReleaseID: releaseType ? '' : undefined,
            Title: params.Title || undefined,
            IssueID: params.IssueID || undefined,
          },
          {
            useEqFields: ['IssueID', 'IterationUUID', 'ReleaseID'],
            page: false,
          },
        )._Filter?.filter((item) => item.Value !== '%%'),
        IssueType: IssueTypeConfig[pagePath],
        PageNo: params.current,
        PageSize: params.pageSize,
        TenantUUID: (window as any).jiguang_currentNs,
      },
      pagePath,
    )
      .then((res) => {
        if (res.Error) {
          setLoading(false);
          return message.error({
            content: res.Error.Message,
          });
        }
        setLoading(false);
        return {
          success: true,
          data: res.Items || [],
          total: res.Total,
        };
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const columns = [
    {
      dataIndex: 'IssueID',
      title: 'ID',
      copyable: true,
    },
    {
      dataIndex: 'Title',
      title: '标题',
      valueType: 'text',
      linkable: true,
      linkProps: {
        linkUrl: (text, record) =>
          `${withRouteBasename(`/iteration_manage/${pagePath}/detail`)}?issue_id=${record.IssueID}`,
      },
    },
    { dataIndex: 'TAPD', title: '关联TAPD链接/ID', hideInTable: true },
  ];
  return (
    <TcsModal
      title={type === 'remove' ? '移出迭代' : `批量关联${CreateButtonEnterMap[pagePath]}`}
      visible={addVisible}
      onCancel={handleCancel}
      destroyOnClose={true}
      width={type === 'remove' ? 400 : 800}
      onOk={handleConfirm}
      confirmLoading={confirmLoading}
    >
      {type === 'remove' ? (
        <TcsSpin spinning={loading}>
          <ProForm formRef={formRef}>
            <ProForm.Item label="迭代" dataIndex="IssueList">
              <Select searchable size="m" appearance="button" options={options} placeholder="可选择关联到其他迭代" />
            </ProForm.Item>
          </ProForm>
        </TcsSpin>
      ) : (
        <div style={{ height: 500 }}>
          <TcsTable
            request={fetchTableData}
            loading={loading}
            columns={columns}
            cardBordered={true}
            scrollInTable={true}
            rowKey="IssueID"
            rowSelection={{
              type: 'checkbox',
              selectedRowKeys: selectedKeys,
              onChange: (selectedRowKeys: string[]) => {
                setSelectedKeys(selectedRowKeys);
              },
              rowSelectable: (record) => !record.IterationUUID,
            }}
            search={{ filterType: 'light' }}
            pagination={{ defaultPageSize: 10 }}
          />
        </div>
      )}
    </TcsModal>
  );
};
export default AddIssueToIteration;
