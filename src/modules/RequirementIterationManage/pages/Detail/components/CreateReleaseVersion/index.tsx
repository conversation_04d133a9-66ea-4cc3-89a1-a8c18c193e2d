import { toListParamsCApi } from '@/common/api/api';
import {
  CreateVersionReleaseApprovalSheet,
  ListSolutionProductRelation,
  UpdateVersionReleaseApprovalSheet,
} from '@/common/api/requirementIteration';
import ReleaseVersionContext from '@/common/context/release';
import useLookup from '@/common/hookups/useLookup';
import { withRouteBasename } from '@/common/routePath';
import { TcsForm, TcsFormSelect, TcsFormStaffSelect, TcsFormText, TcsLayout, TcsModal } from '@tencent/tcs-component';
import { SelectMultiple, message } from '@tencent/tea-component';
import React, { useContext, useEffect, useRef, useState } from 'react';
export interface IProps {
  isVisible: boolean;
  onClose: () => void;
  record?: Record<string, any>;
  iterationUUID?: string;
  releasePlanUUID?: string;

  // checkCurrentStageToProduct?: boolean;
  solutionVersionUUID?: string;
}
const CreateReleaseVersion = ({
  isVisible,
  onClose,
  record,
  iterationUUID,
  releasePlanUUID,
  // checkCurrentStageToProduct,
  solutionVersionUUID,
}: IProps) => {
  const [form] = TcsForm.useForm();
  const { lookups } = useLookup(['PackageArch', 'VersionReleaseApprovalTemplate', 'ReleaseVersionUsers']);
  const [productInfo, setProductInfo] = useState<any>([]);
  const { history } = TcsLayout.useHistory();
  const formRef = useRef<any>();
  const [loading, setLoading] = useState<boolean>(false);
  const { handleUpdateSuccess } = useContext(ReleaseVersionContext);

  const handleCancel = () => {
    onClose?.();
    formRef?.current?.resetFields();
    form?.resetFields();
  };

  const handleCreateVersion = () => {
    if (record) {
      formRef.current?.validateFields()?.then((values) => {
        setLoading(true);
        UpdateVersionReleaseApprovalSheet?.({
          ReleasePlanUUID: record?.releasePlanUUID,
          ApprovalSheetUUID: record?.UUID,
          Archs: values.Archs?.join(';'),
          IterationUUID: record?.IterationUUID,
          Description: values.Description,
          ProductUUIDs: values?.ProductUUIDs,
          TemplateName: values.TemplateName,
          WechatGroup: values?.WechatGroup,
          Owners: {
            CarbonCopyUsers: values.CarbonCopyUsers,
            DocumentOwner: values?.DocumentOwner,
            CMO: values.CMO,
            Owners: values.Owners,
            ReleaseTester: values.ReleaseTester,
            GM: values.GM,
            Manager: values.Manager,
          },
        })?.then((res) => {
          if (res.Error) {
            setLoading(false);
            return message.error({ content: res.Error.Message });
          }
          message.success({ content: '编辑成功' });
          setLoading(false);
          handleCancel();
          handleUpdateSuccess();
        });
      });
      // UpdateVersionReleaseApprovalSheet
    } else {
      formRef.current?.validateFields()?.then((values) => {
        setLoading(true);
        CreateVersionReleaseApprovalSheet?.({
          ReleasePlanUUID: releasePlanUUID || '',
          Archs: values.Archs?.join(';'),
          IterationUUID: iterationUUID || '',
          Description: values.Description,
          ProductUUIDs: values?.ProductUUIDs,
          TemplateName: values.TemplateName,
          WechatGroup: values?.WechatGroup,
          Owners: {
            CarbonCopyUsers: values.CarbonCopyUsers,
            DocumentOwner: values?.DocumentOwner,
            CMO: values.CMO,
            Owners: values.Owners,
            ReleaseTester: values.ReleaseTester,
            GM: values.GM,
            Manager: values.Manager,
            // ProjectManager: values.ProjectManager?.join(';'),
          },
        })?.then((res) => {
          if (res.Error) {
            setLoading(false);
            return message.error({ content: res.Error.Message });
          }
          setLoading(false);
          message.success({ content: '新建成功' });
          history.push(
            `${withRouteBasename(
              `/requirement_iteration_manage/detail/release?iteration_id=${res.VersionReleaseApprovalSheet?.IterationUUID}&approval_id=${res.VersionReleaseApprovalSheet?.UUID}`,
            )}`,
          );
        });
      });
    }
  };
  const handleFinish = () => {
    handleCreateVersion?.();
  };
  useEffect(() => {
    if (isVisible) {
      ListSolutionProductRelation({
        Query: toListParamsCApi(
          {
            SolutionVersionID: solutionVersionUUID || record?.SolutionVersionUUID,
            Branch: 'master',
            Tag: '',
          },
          {
            useEqFields: ['SolutionVersionID', 'Branch', 'Tag'],
            restParam: { _GroupBy: 'id' },
            page: false,
          },
        ),
        SolutionVersionID: solutionVersionUUID || record?.SolutionVersionUUID,
        Branch: 'master',
      }).then((res) => {
        if (res.Items) {
          setProductInfo(
            res.Items?.map((item) => ({
              text: item.Name,
              value: item.ProductUUID,
            })) || [],
          );
        } else {
          setProductInfo([]);
        }
      });
    }
  }, [isVisible]);

  useEffect(() => {
    if (isVisible) {
      if (record) {
        const { Archs, Description, Owners, ReleaseProducts, WechatGroup } = record;
        form.setFieldsValue({
          Description,
          ...Owners,
          ProjectManager: Owners?.ProjectManager?.split(';'),
          CarbonCopyUsers: Owners?.CarbonCopyUsers || [],
          ProductUUIDs: ReleaseProducts?.map((item) => item.ProductUUID),
          Archs: Archs?.split(';'),
          WechatGroup,
        });
      } else {
        const archInit = lookups?.PackageArch?.map((item) => {
          if (item.Code === 'rhel.amd64' || item.Code === 'rhel.arm64') {
            return item.Code;
          }
        })?.filter((item) => item);
        form.setFieldsValue({
          ...lookups?.ReleaseVersionUsers?.[0]?.Extra,
          Archs: archInit,
        });
      }
    }
  }, [isVisible, record]);

  return (
    <TcsModal
      visible={isVisible}
      title={record ? '编辑发布审批' : '创建发布审批'}
      onCancel={handleCancel}
      onOk={handleCreateVersion}
      confirmLoading={loading}
    >
      <TcsForm form={form} formRef={formRef} onFinish={handleFinish}>
        <TcsFormText
          label="发布说明"
          name="Description"
          fieldProps={{ size: 'full' }}
          rules={[
            {
              required: true,
              message: '请填写发布说明',
            },
          ]}
          placeholder="请填写发布说明"
        />

        <TcsForm.Item
          label="发布架构"
          name="Archs"
          rules={[
            {
              required: true,
              message: '请选择发布产品',
            },
          ]}
          initialValue={[]}
        >
          <SelectMultiple
            staging={false}
            options={
              lookups?.PackageArch?.map((item) => ({
                text: item.Name,
                value: item.Code,
                disabled: true,
              })) || []
            }
            appearance="button"
            searchable={true}
            matchButtonWidth={true}
            placeholder="请选择发布架构"
            size="full"
          />
        </TcsForm.Item>

        <TcsFormSelect
          label="发布产品"
          name="ProductUUIDs"
          rules={[
            {
              required: true,
              message: '请选择发布产品',
            },
          ]}
          initialValue={[]}
          // disabled={}
          fieldProps={{
            options: productInfo.map((item) => ({
              label: item.text,
              value: item.value,
            })),
            showSearch: true,
            size: 'full',
            showSelectAll: true,
            mode: 'multiple',
            placeholder: '请选择发布产品',
          }}
        />
        {!record && (
          <TcsFormSelect
            label="审批模版"
            name="TemplateName"
            fieldProps={{ size: 'full' }}
            options={
              lookups?.VersionReleaseApprovalTemplate?.map((item) => ({
                label: item.Name,
                value: item.Code,
              })) || []
            }
            rules={[
              {
                required: true,
                message: '请选择审批模版',
              },
            ]}
            disabled={!!record}
            placeholder="请选择审批模版"
          />
        )}

        <TcsFormStaffSelect
          label="审批管理员"
          name="Owners"
          rules={[
            {
              required: true,
              message: '请选择发布审批人',
            },
          ]}
          placeholder="请选择发布审批人"
          valueField="enName"
          fieldProps={{
            multiple: true,
            boxClassName: 'common-tag-select-lis-height',
          }}
        />

        <TcsFormStaffSelect
          label="文档负责人"
          name="DocumentOwner"
          rules={[
            {
              required: true,
              message: '请选择文档负责人',
            },
          ]}
          placeholder="请选择文档负责人"
          valueField="enName"
        />

        <TcsFormStaffSelect
          label="出包负责人"
          name="CMO"
          rules={[
            {
              required: true,
              message: '请选择出包负责人',
            },
          ]}
          placeholder="请选择出包负责人"
          valueField="enName"
          fieldProps={
            {
              // multiple: true,
            }
          }
        />

        <TcsFormStaffSelect
          label="部署验证负责人"
          name="ReleaseTester"
          rules={[
            {
              required: true,
              message: '请选择部署验证负责人',
            },
          ]}
          placeholder="请选择部署验证负责人"
          valueField="enName"
          fieldProps={
            {
              // multiple: true,
            }
          }
        />

        <TcsFormStaffSelect
          label="产品总监"
          name="Manager"
          rules={[
            {
              required: true,
              message: '请选择产品总监',
            },
          ]}
          placeholder="请选择产品总监"
          valueField="enName"
          fieldProps={
            {
              // multiple: true,
            }
          }
        />

        <TcsFormStaffSelect
          label="GM负责人"
          name="GM"
          rules={[
            {
              required: true,
              message: '请选择GM负责人',
            },
          ]}
          placeholder="请选择GM负责人"
          valueField="enName"
          fieldProps={
            {
              // multiple: true,
            }
          }
        />

        <TcsFormStaffSelect
          label="抄送人员"
          name="CarbonCopyUsers"
          fieldProps={{
            multiple: true,
            boxClassName: 'common-tag-select-lis-height',
          }}
          valueField="enName"
        />
        <TcsFormText
          label="企微群ID"
          name="WechatGroup"
          tooltip="1.请先将 TCSConnect Bot 机器人添加至要通知的群聊中! 2.添加 crid 机器人到群聊，@crid 查询会话id 即可获取"
          fieldProps={{ size: 'full' }}
          placeholder="请输入企微群ID"
        />
      </TcsForm>
    </TcsModal>
  );
};

export default CreateReleaseVersion;
