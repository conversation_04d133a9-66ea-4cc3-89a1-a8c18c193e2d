import {
  DeleteIteration,
  GetIterationDetail,
  GetIterationOverviewDetail,
  IOverviewDetailResponse,
  ListVersionReleaseApproval,
  UpdateIterationLockStatus,
  UpdateIterationStatus,
} from '@/common/api/requirementIteration';
import Loading from '@/common/components/Loading';
import useLookup from '@/common/hookups/useLookup';
import { withRouteBasename } from '@/common/routePath';
import { getUrlParams } from '@/common/utils';
import { IterationPathConfig } from '@/modules/IterationManage/config';
import IterationTable from '@/modules/IterationManage/pages/List/components/IterationTable';
import ReleaseVersionTable from '@/modules/ReleasePlanManage/pages/Detail/components/ReleaseVersionTable';
import { TcsCard, TcsLayout } from '@tencent/tcs-component';
import {
  Button,
  Card,
  Col,
  Form,
  Icon,
  MetricsBoard,
  Modal,
  PopConfirm,
  Row,
  Select,
  TabPanel,
  Tabs,
  Tag,
  Timeline,
  Tooltip,
  message,
} from '@tencent/tea-component';
import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';
import { IterationLockStatus, IterationStatusEnum, IterationStatusMap } from '../../config';
import CreateIterationModal from '../Create';
import { StageStatusType } from '../ReleaseVersionDetail/constants';
import CreateReleaseVersion from './components/CreateReleaseVersion';
import styles from './index.module.scss';
declare const window: any;
export interface IPlanProps {
  // 添加迭代
  addStoryLock?: boolean;
  // 添加缺陷
  addBugLock?: boolean;
  // 移除
  removeLock?: boolean;
}
const Detail = () => {
  const urlParams = getUrlParams();

  const { history } = TcsLayout.useHistory();

  const [loading, setLoading] = useState<boolean>(false);
  const [detailData, setDetailData] = useState<any>();
  const [overViewdata, setOverViewData] = useState<IOverviewDetailResponse>();
  const editIterationRef = useRef<any>();
  const [isManage, setIsManage] = useState<boolean>(false);
  const [editLockStatus, setEditLockStatus] = useState<boolean>(false);

  const [iterationPlanStatus, setIterationPlanStatus] = useState<IPlanProps>({
    addStoryLock: false,
    addBugLock: false,
    removeLock: false,
  });
  const [releaseVersionState, setReleaseVersionState] = useState<any>();
  const [createModalVisible, setCreateModalVisible] = useState<boolean>(false);

  const { getLookupByCode } = useLookup([]);
  const { lookups } = useLookup(['IterationLockStatus', 'IterationStatus']);
  const [iterationStatusMap, setIterationStatusMap] = useState<any>(IterationStatusMap);
  const [editStatus, setEditStatus] = useState<boolean>(false);
  // 只记录需求冻结、代码冻结和发布评审三个状态
  const [mrStatus, setMrStatus] = useState<boolean>(false);
  const [releaseListNumber, setReleaseListNumber] = useState<number>(0);

  const fetchData = async () => {
    setLoading(true);
    const res = await GetIterationDetail({
      UUID: urlParams.iteration_id,
    });
    if (res.Error) {
      message.error({
        content: res.Error.Message,
      });
    } else {
      setDetailData(res.GetIteration);
      setLoading(false);
      const currentStatus = res.GetIteration?.Status;
      if (
        [
          IterationStatusEnum.FeatureFrozen,
          IterationStatusEnum.CodeFrozen,
          IterationStatusEnum.ReleaseReviewing,
        ].includes(currentStatus as IterationStatusEnum)
      ) {
        setMrStatus(true);
      } else {
        setMrStatus(false);
      }

      // 里程碑节点是否高亮
      let nowIndex: number | undefined;
      const result = IterationStatusMap?.map((item, index) => {
        const newItem = { ...item };
        if (item.title === currentStatus) {
          if (currentStatus === IterationStatusEnum.Closed) {
            newItem.theme = 'success';
          } else {
            nowIndex = index;
            // 正在进行中的状态
            newItem.theme = 'doing';
          }
        } else {
          if (currentStatus === IterationStatusEnum.Open) {
            // 遇到这两个状态都为默认
            newItem.theme = 'default';
          } else {
            if (!nowIndex) {
              // 需求评审时第一个状态后面都是default
              newItem.theme = currentStatus === IterationStatusEnum.FeatureReviewing ? 'default' : 'success';
            } else if (index > nowIndex) {
              newItem.theme = 'default';
            }
          }
        }

        return newItem;
      });

      setIterationStatusMap(result);

      // 是否为管理员
      const isOwner = res.GetIteration?.Owners?.includes(window?.jiguang_username);
      const isCreator = res.GetIteration?.Creator?.includes(window?.jiguang_username);
      setIsManage(isOwner || isCreator || false);
    }
  };

  const fetchOverViewData = async () => {
    setLoading(true);
    const {
      Error,
      FixedBugNum,
      FinishedStoryNum,
      FixedFatalBugNum,
      FixedSeriousBugNum,
      TotalBugNum,
      TotalFatalBugNum,
      TotalSeriousBugNum,
      TotalStoryNum,
    } = await GetIterationOverviewDetail({
      IterationUUID: urlParams.iteration_id,
    });
    if (Error) {
      message.error({
        content: Error.Message,
      });
    } else {
      setOverViewData({
        FixedBugNum,
        FinishedStoryNum,
        FixedFatalBugNum,
        FixedSeriousBugNum,
        TotalBugNum,
        TotalFatalBugNum,
        TotalSeriousBugNum,
        TotalStoryNum,
      });

      setLoading(false);
    }
  };

  const handleDelete = async () => {
    const res = await DeleteIteration({
      IterationUUID: urlParams.iteration_id,
    });
    if (res.Error) {
      return message.error({
        content: res.Error.Message,
      });
    }
    message.success({
      content: '删除成功',
    });
    history.push(`${withRouteBasename(`/requirement_iteration_manage`)}`);
  };

  const handleEdit = () => {
    editIterationRef?.current?.show(detailData);
  };

  const handleSuccess = () => {
    fetchData();
  };
  const handleAddToIteration = () => {
    fetchOverViewData();
  };

  const handleChangeValue = (value) => {
    Modal.alert({
      type: 'warning',
      message: '更改锁定状态',
      description: '更改后可能对迭代规划需求/缺陷造成影响，确认是否更新？',
      buttons: [
        <Button type="primary" onClick={() => handleChangeLockStatus(value)} key="ok">
          确认
        </Button>,
        <Button onClick={() => oncancel} key="cancal">
          取消
        </Button>,
      ],
    });
  };

  const handleChangeLockStatus = async (value) => {
    try {
      const { Error } = await UpdateIterationLockStatus({
        IterationUUID: urlParams.iteration_id,
        LockStatus: value,
      });
      if (Error) {
        return message.error({
          content: Error.Message,
        });
      }
      message.success({
        content: '修改锁定状态成功',
      });
      handleSuccess();
      setEditLockStatus(false);
    } catch (error) {
      console.error(error);
    }
  };

  const handleChangeStatusModal = (value) => {
    Modal.alert({
      type: 'warning',
      message: '更改迭代状态',
      description: '更改后可能对迭代规划需求/缺陷造成影响，确认是否更新？',
      buttons: [
        <Button type="primary" onClick={() => handleStatusChangeValue(value)} key="ok">
          确认
        </Button>,
        <Button onClick={() => oncancel} key="cancal">
          取消
        </Button>,
      ],
    });
  };

  const handleStatusChangeValue = async (value) => {
    try {
      const { Error } = await UpdateIterationStatus({
        IterationUUID: urlParams.iteration_id,
        Status: value,
      });
      if (Error) {
        return message.error({
          content: Error.Message,
        });
      }
      message.success({
        content: '修改状态成功',
      });
      handleSuccess();
      setEditStatus(false);
      if (value === IterationStatusEnum.ReleaseReviewing && !releaseVersionState) {
        setCreateModalVisible(true);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const checkIterationStatus = () => {
    // 不管是管理员还是非管理员只要在这三种状态下都不可以进行规划
    if (
      [IterationStatusEnum.CodeFrozen, IterationStatusEnum.ReleaseReviewing, IterationStatusEnum.Closed]?.includes(
        detailData?.Status,
      )
    ) {
      setIterationPlanStatus({ addStoryLock: true, addBugLock: true });
    } else {
      // 是管理员其他情况下都可以操作
      if (isManage) {
        setIterationPlanStatus({ addStoryLock: false, addBugLock: false });
      } else {
        // 如果开启了锁定状态
        if (detailData?.LockStatus !== IterationLockStatus.Unlock) {
          // 只有在这两种状态下未开始和需求评审可以操作
          if ([IterationStatusEnum.Open, IterationStatusEnum.FeatureReviewing]?.includes(detailData?.Status)) {
            setIterationPlanStatus({ addStoryLock: false, addBugLock: false });
          }
          // 如果是研发中/需求冻结状态下要依赖锁定状态去判断
          if ([IterationStatusEnum.Processing, IterationStatusEnum.FeatureFrozen]?.includes(detailData?.Status)) {
            if (detailData?.LockStatus === IterationLockStatus.LockStory) {
              setIterationPlanStatus({ addStoryLock: true, addBugLock: false });
            }
            if (detailData?.LockStatus === IterationLockStatus.LockAll) {
              setIterationPlanStatus({ addStoryLock: true, addBugLock: true });
            }
          }
        } else {
          // 如果是迭代未锁定
          setIterationPlanStatus({ addStoryLock: false, addBugLock: false });
        }
      }
    }
  };

  const tabs = [
    {
      id: IterationPathConfig.Story,
      label: '需求',
      component: (
        <IterationTable
          pagePath={IterationPathConfig.Story}
          iterationManage={true}
          iterationUUID={urlParams.iteration_id}
          onAddToIteration={handleAddToIteration}
          isManage={isManage}
          iterationPlanStatus={iterationPlanStatus}
          mrStatus={mrStatus}
        />
      ),
    },
    {
      id: IterationPathConfig.Defect,
      label: '缺陷',
      component: (
        <IterationTable
          pagePath={IterationPathConfig.Defect}
          iterationManage={true}
          iterationUUID={urlParams.iteration_id}
          onAddToIteration={handleAddToIteration}
          isManage={isManage}
          iterationPlanStatus={iterationPlanStatus}
          mrStatus={mrStatus}
        />
      ),
    },
    {
      id: 'releaseVersion',
      label: '发布评审',
      component: <ReleaseVersionTable iterationUUID={urlParams.iteration_id} />,
    },
  ];

  const fetchReleaseData = async () => {
    try {
      const { ListVersionReleaseApprovalSheets, Error } = await ListVersionReleaseApproval({
        IterationUUID: urlParams.iteration_id,
        ReleasePlanUUID: '',
      });
      if (Error) {
        return message.error({ content: Error.Message });
      }
      setReleaseVersionState(ListVersionReleaseApprovalSheets?.[0]);
      setReleaseListNumber(ListVersionReleaseApprovalSheets?.length);
      // setApprovalData(ListVersionReleaseApprovalSheets);
    } catch (error) {
      console.error(error);
    }
  };

  const handleReleaseVersion = () => {
    setCreateModalVisible(true);
  };

  useEffect(() => {
    checkIterationStatus();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isManage, detailData]);

  useEffect(() => {
    if (urlParams.iteration_id) {
      fetchData();
      fetchOverViewData();
      fetchReleaseData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [urlParams.iteration_id]);

  // const buttonTitle = useMemo(() => {
  //   if (!releaseVersionState) {
  //     return '创建发布评审';
  //   }
  //   if (releaseVersionState) {
  //     if (releaseVersionState?.Status === 'approved') {
  //       return '查看发布审批';
  //     }
  //     return '进入发布审批';
  //   }
  // }, [releaseVersionState]);

  return (
    <>
      <TcsLayout history={history} title="迭代详情" customizeCard>
        <Loading loading={loading}>
          {/* <Card style={{ width: '100%' }}> */}
          <TcsCard
            collapsible
            title="基本信息"
            extra={
              <>
                <Tooltip
                  title={
                    detailData?.Status === IterationStatusEnum.ReleaseReviewing ||
                    detailData?.Status === IterationStatusEnum.Closed
                      ? ''
                      : '当前迭代状态不是发布评审，不能进行发布审批'
                  }
                >
                  <Button
                    type="primary"
                    onClick={handleReleaseVersion}
                    disabled={
                      // 限制最多只能创建5条
                      releaseListNumber >= 5 ||
                      (!isManage && releaseVersionState === StageStatusType.waiting) ||
                      (detailData?.Status !== IterationStatusEnum.ReleaseReviewing &&
                        detailData?.Status !== IterationStatusEnum.Closed)
                    }
                  >
                    创建发布评审
                  </Button>
                </Tooltip>

                <Tooltip title={isManage ? '' : '您不是迭代管理员，不可进行编辑操作'}>
                  <Button type="primary" style={{ marginRight: 10 }} onClick={handleEdit} disabled={!isManage}>
                    编辑
                  </Button>
                </Tooltip>
                <Tooltip title={isManage ? '' : '您不是迭代管理员，不可进行编辑操作'}>
                  <PopConfirm
                    title="确定要删除？"
                    disabled={!isManage}
                    footer={(close) => (
                      <>
                        <Button
                          type="link"
                          onClick={() => {
                            handleDelete();
                            close();
                          }}
                        >
                          删除
                        </Button>
                        <Button
                          type="text"
                          onClick={() => {
                            close();
                          }}
                        >
                          取消
                        </Button>
                      </>
                    )}
                    placement="top-start"
                  >
                    <Button type="error" style={{ marginRight: 10 }} disabled={!isManage}>
                      删除
                    </Button>
                  </PopConfirm>
                </Tooltip>
              </>
            }
          >
            <Form layout="fixed">
              <Row>
                <Col lg={8} md={24}>
                  <Form.Item label="迭代名称">
                    <Form.Text>{detailData?.Name}</Form.Text>
                  </Form.Item>
                </Col>
                <Col lg={8} md={24}>
                  <Form.Item label="创建人">
                    <Form.Text>{detailData?.Creator || '-'}</Form.Text>
                  </Form.Item>
                </Col>
                <Col lg={8} md={24}>
                  <Form.Item label="管理员">
                    <Form.Text>{detailData?.Owners || '-'}</Form.Text>
                  </Form.Item>
                </Col>

                <Col lg={8} md={24}>
                  <Form.Item label="创建时间">
                    <Form.Text>{dayjs(detailData?.CreatedAt).format('YYYY-MM-DD HH:mm:ss') || '-'}</Form.Text>
                  </Form.Item>
                </Col>
                <Col lg={8} md={24}>
                  <Form.Item label="完成时间">
                    <Form.Text>
                      {detailData?.CompletedAt ? dayjs(detailData?.CompletedAt).format('YYYY-MM-DD HH:mm:ss') : '-'}
                    </Form.Text>
                  </Form.Item>
                </Col>
                <Col lg={8} md={24}>
                  <Form.Item label="迭代类型">
                    <Form.Text>{getLookupByCode('IterationType', detailData?.Type)?.Name || '-'}</Form.Text>
                  </Form.Item>
                </Col>
                <Col lg={8} md={24}>
                  <Form.Item label="迭代状态">
                    <Form.Text>
                      {!editStatus ? (
                        <span>
                          <Tooltip title={getLookupByCode('IterationStatus', detailData?.Status)?.Extra?.tooltip}>
                            <Tag
                              style={{
                                color: getLookupByCode('IterationStatus', detailData?.Status)?.Extra?.Color,
                                marginTop: -3,
                              }}
                            >
                              {getLookupByCode('IterationStatus', detailData?.Status)?.Name || '-'}
                            </Tag>
                          </Tooltip>

                          {isManage && detailData?.Status !== IterationStatusEnum.Closed && (
                            <Tooltip title="修改迭代状态">
                              <Icon
                                type="pencil"
                                style={{ marginLeft: 5, marginTop: -3 }}
                                onClick={() => setEditStatus(true)}
                              />
                            </Tooltip>
                          )}
                        </span>
                      ) : (
                        <>
                          <Select
                            appearance="button"
                            options={
                              lookups.IterationStatus?.map((item) => ({
                                value: item.Code,
                                text: item.Name,
                                tooltip: item.Extra?.tooltip,
                              })) || []
                            }
                            // value={favorite}
                            value={detailData?.Status}
                            onChange={handleChangeStatusModal}
                          />
                          <Icon
                            type="dismiss"
                            onClick={() => setEditStatus(false)}
                            style={{ marginLeft: 5 }}
                            size="s"
                          />
                        </>
                      )}
                    </Form.Text>
                  </Form.Item>
                </Col>

                <Col lg={8} md={24}>
                  <Form.Item label="锁定状态">
                    <Form.Text>
                      {!editLockStatus ? (
                        <span>
                          <Tooltip
                            title={getLookupByCode('IterationLockStatus', detailData?.LockStatus)?.Extra?.tooltip}
                          >
                            <Tag
                              style={{
                                color: getLookupByCode('IterationLockStatus', detailData?.LockStatus)?.Extra?.Color,
                                marginTop: -3,
                              }}
                            >
                              {getLookupByCode('IterationLockStatus', detailData?.LockStatus)?.Name}
                            </Tag>
                          </Tooltip>
                          {isManage && detailData?.Status !== IterationStatusEnum.Closed && (
                            <Tooltip title="修改锁定状态">
                              <Icon
                                type="pencil"
                                style={{ marginLeft: 5, marginTop: -3 }}
                                onClick={() => setEditLockStatus(true)}
                              />
                            </Tooltip>
                          )}
                        </span>
                      ) : (
                        <>
                          <Select
                            appearance="button"
                            options={
                              lookups.IterationLockStatus?.map((item) => ({
                                value: item.Code,
                                text: item.Name,
                                tooltip: item.Extra?.tooltip,
                              })) || []
                            }
                            // value={favorite}
                            value={detailData?.LockStatus}
                            onChange={handleChangeValue}
                          />
                          <Icon
                            type="dismiss"
                            onClick={() => setEditLockStatus(false)}
                            style={{ marginLeft: 5 }}
                            size="s"
                          />
                        </>
                      )}
                    </Form.Text>
                  </Form.Item>
                </Col>
                <Col lg={8} md={24}>
                  <Form.Item label="解决方案名称">
                    <Form.Text>
                      {`${detailData?.SolutionVersion?.Solution.Name || '-'}(${
                        detailData?.SolutionVersion?.Solution.NameEN || '-'
                      })`}
                    </Form.Text>
                  </Form.Item>
                </Col>
                <Col lg={8} md={24}>
                  <Form.Item label="解决方案版本">
                    <Form.Text>{detailData?.SolutionVersion?.Code || '-'}</Form.Text>
                  </Form.Item>
                </Col>
                <Col lg={8} md={24}>
                  <Form.Item label="迭代描述">
                    <Form.Text>{detailData?.Description || '-'}</Form.Text>
                  </Form.Item>
                </Col>
                <Col lg={8} md={24}>
                  <Form.Item label="通知群ID">
                    <Form.Text>{detailData?.WechatGroup || '-'}</Form.Text>
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col lg={8} md={24}>
                  <Form.Item label="里程碑">
                    <Form.Text>
                      <Timeline mode="horizontal" className={styles.timeline}>
                        {iterationStatusMap?.map((item) => (
                          <Timeline.Item
                            label={detailData?.[item.label] || <span style={{ marginLeft: 30 }}>-</span>}
                            key={item.title}
                            title={
                              item.title === 'feature_reviewing'
                                ? '迭代开始'
                                : getLookupByCode('IterationStatus', item.title)?.Name
                            }
                            icon={item.theme === 'doing' && <Icon type="loading" />}
                            theme={item.theme}
                          />
                        ))}
                      </Timeline>
                    </Form.Text>
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </TcsCard>
          {/* </Card> */}
          <Card style={{ width: '100%' }}>
            <Card.Body title="信息面板">
              <Row showSplitLine>
                <Col>
                  <MetricsBoard
                    title="需求"
                    value={overViewdata ? `${overViewdata?.FinishedStoryNum}/${overViewdata?.TotalStoryNum}` : ''}
                  />
                </Col>
                <Col>
                  <MetricsBoard
                    title="缺陷"
                    value={overViewdata ? `${overViewdata?.FixedBugNum}/${overViewdata?.TotalBugNum}` : ''}
                  />
                </Col>
                <Col>
                  <MetricsBoard
                    title="致命缺陷"
                    value={overViewdata ? `${overViewdata?.FixedFatalBugNum}/${overViewdata?.TotalFatalBugNum}` : ''}
                  />
                </Col>
                <Col>
                  <MetricsBoard
                    title="严重缺陷"
                    value={
                      overViewdata ? `${overViewdata?.FixedSeriousBugNum}/${overViewdata?.TotalSeriousBugNum}` : ''
                    }
                  />
                </Col>
              </Row>
            </Card.Body>
          </Card>
          <Card>
            <Card.Body>
              <Tabs tabs={tabs} destroyInactiveTabPanel>
                {tabs.map((tab) => (
                  <TabPanel id={tab.id} key={tab.id}>
                    {tab.component}
                  </TabPanel>
                ))}
              </Tabs>
            </Card.Body>
          </Card>
        </Loading>
        <CreateIterationModal handleSuccess={handleSuccess} ref={editIterationRef} />
        <CreateReleaseVersion
          isVisible={createModalVisible}
          onClose={() => setCreateModalVisible(false)}
          iterationUUID={urlParams.iteration_id}
          solutionVersionUUID={detailData?.SolutionVersionUUID}
        />
      </TcsLayout>
    </>
  );
};

export default Detail;
