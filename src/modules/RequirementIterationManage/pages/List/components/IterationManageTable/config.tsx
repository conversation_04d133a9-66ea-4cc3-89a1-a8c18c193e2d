import { SolutionVersionSelect } from '@/common/components';
import { withRouteBasename } from '@/common/routePath';
import { TcsColumns } from '@tencent/tcs-component';
import { Button } from '@tencent/tea-component';
import React from 'react';

export const getTcsColumns = () =>
  [
    {
      dataIndex: 'Name',
      title: '标题',
      width: '20%',
      linkable: true,
      disable: true,
      fixed: 'left',
      linkProps: {
        linkUrl(text, record) {
          return `${withRouteBasename(`/requirement_iteration_manage/detail`)}?iteration_id=${record.UUID}`;
        },
      },
    },
    {
      dataIndex: 'Solution',
      title: '解决方案名称',
      width: '20%',
      search: false,
      render(text, record) {
        return `${record?.SolutionVersion?.Solution?.Name || '-'}(${record?.SolutionVersion?.Solution?.NameEN || '-'})`;
      },
    },
    {
      dataIndex: ['SolutionVersion', 'Code'],
      title: '解决方案版本',
      width: '10%',
      search: false,
    },
    {
      dataIndex: 'SolutionVersionUUID',
      title: '解决方案版本',
      hideInTable: true,
      renderFormItem: () => <SolutionVersionSelect />,
    },
    {
      dataIndex: 'Status',
      title: '迭代状态',
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'IterationStatus',
        showType: 'tag',
        mode: 'multiple',
        showSelectAll: true,
      },
    },

    {
      dataIndex: 'Type',
      title: '迭代类型',
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'IterationType',
        showType: 'tag',
      },
    },
    {
      dataIndex: 'Creator',
      title: '创建人',
      search: false,
    },
    {
      dataIndex: 'CreatedAt',
      title: '创建时间',
      valueType: 'dateTime',
      search: false,
    },
    {
      dataIndex: 'UpdateAt',
      title: '最后修改时间',
      valueType: 'dateTime',
      search: false,
    },
    {
      dataIndex: 'operation',
      title: '操作',
      fixed: 'right',
      valueType: 'option',
      render(text, record) {
        if (record.TapdURL) {
          return (
            <Button
              type="link"
              onClick={() => {
                window.open(record.TapdURL, '_blank');
              }}
            >
              跳转至Tapd单
            </Button>
          );
        }
        return '-';
      },
    },
  ] as TcsColumns[];

// export default getColumns;
