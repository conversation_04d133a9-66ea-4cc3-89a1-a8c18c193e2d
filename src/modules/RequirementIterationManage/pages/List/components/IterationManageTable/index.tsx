import { ListIterationsWithDetail } from '@/common/api/requirementIteration';
import { checkCurrentEnvState } from '@/common/utils/tools';
import SyncTapdModal from '@/modules/IterationManage/pages/List/components/SyncTapdModal';
import { TcsActionType, TcsTable } from '@tencent/tcs-component';
import { Button, Dropdown, Icon, List, message } from '@tencent/tea-component';
import React, { useEffect, useRef, useState } from 'react';
import CreateIterationModal from '../../../Create';
import { getTcsColumns } from './config';

const IterationManageTable = () => {
  const actionRef = useRef<TcsActionType>();

  const [update, setUpdate] = useState({});
  // const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);

  const createIterationRef = useRef<any>();

  async function handleRequest(params) {
    const res = await ListIterationsWithDetail({
      Name: params.Name || undefined,
      SolutionVersionUUID: params.SolutionVersionUUID?.[1] || undefined,
      Type: params.Type || undefined,
      Status: params.Status || undefined,
      CreatedAt: params.CreatedAt || undefined,
      pageSize: params.pageSize,
      current: params.current,
    });
    if (res.Error) {
      message.error({
        content: res.Error.Message,
      });
      return {
        data: [],
        total: 0,
      };
    }
    return {
      data: res.ListIterations || [],
      success: true,
      total: res.Total || 0,
    };
  }

  const handleToCreate = () => {
    createIterationRef.current.show();
  };
  const handleSyncTapd = () => {
    setVisible(true);
  };
  const handleSyncCancel = () => {
    setVisible(false);
  };
  const handleSyncConfirm = () => {
    setUpdate({});
  };

  const handleSuccess = () => {
    setUpdate({});
  };

  useEffect(() => {
    actionRef.current?.reload();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [update]);
  const columns = getTcsColumns();

  return (
    <>
      <TcsTable
        columns={columns}
        actionRef={actionRef}
        pagination={{}}
        search={{
          labelWidth: 80,
        }}
        request={handleRequest}
        rowKey="ID"
        syncToUrl
        scroll={{
          x: 1500,
        }}
        scrollInTable
        headerTitle={
          <>
            {checkCurrentEnvState() ? (
              <Dropdown
                button={
                  <span>
                    <Icon type="plus" />
                    创建迭代
                  </span>
                }
                appearance="button"
                trigger="hover"
              >
                <List type="option">
                  <List.Item onClick={() => handleToCreate()}>
                    <Icon type="and" />
                    <span style={{ marginLeft: 5 }}>新建迭代</span>
                  </List.Item>

                  <List.Item onClick={handleSyncTapd}>
                    <Icon type="and" />
                    <span style={{ marginLeft: 5 }}>从TAPD同步迭代</span>
                  </List.Item>
                </List>
              </Dropdown>
            ) : (
              <Button type="primary" onClick={handleSyncTapd}>
                从TAPD同步迭代
              </Button>
            )}
          </>
        }
        columnsState={{
          persistenceKey: 'defect_manage/requirement_iteration_manage/columns_state',
        }}
      />
      <SyncTapdModal
        visible={visible}
        onCancel={handleSyncCancel}
        onConfirm={handleSyncConfirm}
        iterationManage={true}
      />
      <CreateIterationModal handleSuccess={handleSuccess} ref={createIterationRef} />
    </>
  );
};

export default IterationManageTable;
