/*
 * @Author: lucyfang
 * @Date: 2024-12-16 11:10:45
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-12-17 19:15:26
 * @Description: 请输入注释信息
 */
import React, { useEffect, useRef, useState } from 'react';
import { TcsCard, TcsSpin, TcsTable, ActionType, TcsLayout } from '@tencent/tcs-component';
import { getPatchDetails } from '@/common/api/patchManage';
import { toListParamsCApi } from '@/common/api/api';
import moment from 'moment';
import { getUrlParams } from '@/common/utils';
import { getIssueColumns, getAppColumns, getVerificationColumns } from './config';

const Detail = () => {
  const issueActionRef = useRef<ActionType>();
  const appActionRef = useRef<ActionType>();
  const verificationsActionRef = useRef<ActionType>();
  const [loading, setLoading] = useState(false);
  const [productList, setProoductList] = useState([]);
  const [issueList, setIssueList] = useState([]);
  const [verifications, setVerifications] = useState([]);
  const { history } = TcsLayout.useHistory();
  const urlParams = getUrlParams();
  useEffect(() => {
    if (urlParams?.patchID) {
      setLoading(true);
      getPatchDetails({
        Patch: {
          _Filter: toListParamsCApi(
            { PatchID: urlParams.patchID },
            {
              useEqFields: ['PatchID'],
            },
          )._Filter,
        },
      })
        .then((res) => {
          if (res) {
            if (res?.GetPatch?.SelectedIssues?.length) {
              setIssueList(res.GetPatch.SelectedIssues);
            }
            if (res?.GetPatch?.PatchedApplications?.length) {
              setProoductList(res.GetPatch.PatchedApplications);
            }
            if (res?.GetPatch?.Verifications?.length) {
              setVerifications(res.GetPatch.Verifications);
            }
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [urlParams?.patchID]);
  useEffect(() => {
    issueActionRef.current?.reload();
  }, [issueList?.length]);
  useEffect(() => {
    appActionRef.current?.reload();
  }, [productList?.length]);
  useEffect(() => {
    issueActionRef.current?.reload();
  }, [issueList?.length]);
  useEffect(() => {
    verificationsActionRef.current?.reload();
  }, [verifications?.length]);

  const handleIssueRequest = (params, options) => {
    if (issueList?.length) {
      const data = issueList;
      const { pageSize, current, IssueID, IssueSolution, Title, Type, Priority, CreatedAt } = params;
      const { IssueAppRel, ProductInfo, Status } = IssueSolution;
      let createdAtRange = CreatedAt;
      if (CreatedAt && CreatedAt.length === 2) {
        createdAtRange = [
          moment(CreatedAt?.[0])?.format('YYYY-MM-DD 00:00:00'),
          moment(CreatedAt?.[1])?.format('YYYY-MM-DD 23:59:59'),
        ];
      }
      const filteredData = data?.filter((item) => {
        const issueData = item?.Issue || {};
        const storyData = item?.Story || {};
        const issueSolutionData = item?.IssueSolution || {};
        const issueAppRelData = issueSolutionData?.IssueAppRel || [];
        // 缺陷单 ID 或 需求 ID
        const matchesID =
          !IssueID ||
          issueData.IssueID?.toLowerCase().includes(IssueID.toLowerCase()) ||
          storyData.IssueID?.toLowerCase().includes(IssueID.toLowerCase());

        // 缺陷单/需求 标题
        const matchesTitle =
          !Title ||
          issueData.Title?.toLowerCase().includes(Title.toLowerCase()) ||
          storyData.Title?.toLowerCase().includes(Title.toLowerCase());

        // 类型（缺陷或需求）
        const matchesType = !Type || Type.length === 0 || Type.includes(item?.Story ? 'story' : 'bug');

        // 优先级
        const matchesPriority =
          !Priority ||
          issueData.Priority?.toLowerCase().includes(Priority.toLowerCase()) ||
          storyData.Priority?.toLowerCase().includes(Priority.toLowerCase());

        // 涉及产品
        const matchesProduct =
          !ProductInfo ||
          issueAppRelData.some((appRel) =>
            appRel.ProductVersionInfo?.ProductInfo?.Code.toLowerCase().includes(ProductInfo.toLowerCase()),
          );

        // 涉及应用
        const matchesApplication =
          !IssueAppRel ||
          issueAppRelData.some((appRel) => appRel.ApplicationName.toLowerCase().includes(IssueAppRel.toLowerCase()));

        // 状态
        const matchesStatus = !Status || issueSolutionData.Status?.toLowerCase().includes(Status.toLowerCase());

        // 创建时间
        const createdAtDate = new Date(item.CreatedAt);
        const matchesCreatedAt =
          !createdAtRange ||
          ((!createdAtRange[0] || createdAtDate >= new Date(createdAtRange[0])) &&
            (!createdAtRange[1] || createdAtDate <= new Date(createdAtRange[1])));

        return (
          matchesID &&
          matchesTitle &&
          matchesType &&
          matchesPriority &&
          matchesProduct &&
          matchesApplication &&
          matchesStatus &&
          matchesCreatedAt
        );
      });

      // 处理排序
      let sortedData = filteredData;
      const sorter = options?.sorter || [];
      if (sorter.length) {
        const { field, order } = sorter[0];
        sortedData = filteredData.sort((a, b) => {
          let aValue = a[field];
          let bValue = b[field];

          if (field === 'CreatedAt') {
            aValue = new Date(aValue).getTime();
            bValue = new Date(bValue).getTime();
          }
          if (order === 'ascend') {
            return aValue > bValue ? 1 : -1;
          }
          if (order === 'descend') {
            return aValue < bValue ? 1 : -1;
          }
          return 0;
        });
      } else {
        // 默认按 CreatedAt 倒序排列
        sortedData = filteredData.sort((a, b) => {
          const dateA = new Date(a.CreatedAt).getTime();
          const dateB = new Date(b.CreatedAt).getTime();
          return dateB - dateA; // 倒序排列，最晚时间排在前面
        });
      }

      const startIndex = (current - 1) * pageSize;
      const pagedData = sortedData.slice(startIndex, startIndex + pageSize);
      // 返回数据和分页信息
      return Promise.resolve({
        data: pagedData || [],
        success: true,
        total: filteredData.length,
      });
    }
  };

  const handleAppRequest = (params) => {
    if (productList?.length) {
      const {
        pageSize,
        current,
        ProductInfo: { Code },
        ProductVersion: { Name },
        ApplicationName,
      } = params;

      const filteredData = productList?.filter((item) => {
        // 产品code
        const matchesCode = !Code || item?.ProductInfo.Code?.toLowerCase().includes(Code.toLowerCase());
        // 应用名称
        const matchesApplicationName =
          !ApplicationName || item?.ApplicationName.toLowerCase().includes(ApplicationName.toLowerCase());
        // 产品版本
        const matchesProductVersion = !Name || item?.ProductVersion?.Name.toLowerCase().includes(Name.toLowerCase());

        return matchesCode && matchesApplicationName && matchesProductVersion;
      });
      const startIndex = (current - 1) * pageSize;

      const pagedData = filteredData.slice(startIndex, startIndex + pageSize);
      // 返回数据和分页信息
      return Promise.resolve({
        data: pagedData || [],
        success: true,
        total: filteredData.length,
      });
    }
  };

  const handleverificationsRequest = (params) => {
    const {
      pageSize,
      current,
      ArtifactBranch: { BranchName },
      Status,
      CreatedAt,
      VerificationTag: { TagNum },
    } = params;

    if (verifications?.length) {
      // 制品分支
      const filteredData = verifications?.filter((item) => {
        const matchesBranchName =
          !BranchName || item?.ArtifactBranch.BranchName?.toLowerCase().includes(BranchName.toLowerCase());
        // 测试制品TAG
        const matchesTagNum =
          !TagNum || item?.VerificationTag?.TagNum?.toString()?.toLowerCase().includes(TagNum.toLowerCase());
        // 状态
        const matchesStatus = !Status || item?.Status?.toLowerCase().includes(Status.toLowerCase());
        // 创建时间
        const createdAtDate = new Date(item.CreatedAt);
        const matchesCreatedAt =
          !CreatedAt ||
          ((!CreatedAt[0] || createdAtDate >= new Date(CreatedAt[0])) &&
            (!CreatedAt[1] || createdAtDate <= new Date(CreatedAt[1])));
        return matchesBranchName && matchesTagNum && matchesStatus && matchesCreatedAt;
      });
      const startIndex = (current - 1) * pageSize;
      const pagedData = filteredData.slice(startIndex, startIndex + pageSize);
      return Promise.resolve({
        data: pagedData || [],
        success: true,
        total: filteredData.length,
      });
    }
  };
  return (
    <TcsLayout title="Patch详情" customizeCard history={history}>
      <TcsSpin spinning={loading}>
        <TcsCard title="缺陷/需求单" collapsible>
          <TcsTable
            rowKey="IssueSolutionRelUUID"
            columns={getIssueColumns({})}
            cardBordered
            pagination={{ defaultPageSize: 10 }}
            // dataSource={issueList}
            actionRef={issueActionRef}
            request={handleIssueRequest}
            toolBarRender={() => <></>}
            scroll={{ x: 1800, y: 200 }}
            scrollInTable
            options={false}
            search={{}}
          />
        </TcsCard>
        <TcsCard title="应用及产品" collapsible>
          <TcsTable
            rowKey="ApplicationName"
            columns={getAppColumns({})}
            // dataSource={productList}
            actionRef={appActionRef}
            request={handleAppRequest}
            bordered={'all'}
            scroll={{ x: 1800, y: 200 }}
            scrollInTable
            pagination={{ defaultPageSize: 10 }}
            search={{}}
          />
        </TcsCard>
        <TcsCard title="测试包列表" collapsible>
          <TcsTable
            rowKey="VerificationTagUUID"
            columns={getVerificationColumns({})}
            actionRef={verificationsActionRef}
            request={handleverificationsRequest}
            bordered={'all'}
            scroll={{ x: 1800, y: 200 }}
            scrollInTable
            pagination={{ defaultPageSize: 10 }}
            search={{}}
          />
        </TcsCard>
      </TcsSpin>
    </TcsLayout>
  );
};

export default Detail;
