import React from 'react';
import { DropdownBox, List, Popover } from '@tencent/tea-component';
import { TcsColumns } from '@tencent/tcs-component';

export const getIssueColumns = ({}: {}) =>
  [
    {
      dataIndex: 'IssueID',
      title: '缺陷单ID',
      fixed: 'left',
      width: '10%',
    },
    {
      dataIndex: 'Title',
      title: '标题',
      width: '20%',
      renderText: (text, record) => (record?.Story ? record?.Story?.Title : record?.Issue?.Title),
      search: false,
    },
    {
      dataIndex: 'Title',
      title: '缺陷/需求标题',
      hideInTable: true,
    },
    {
      dataIndex: 'Type',
      title: '类型',
      width: '5%',
      renderText: (text, record) => (record?.Story ? 'story' : 'bug'),
      fieldProps: {
        mode: 'multiple',
        showSelectAll: true,
      },
      valueEnum: {
        story: {
          text: '需求',
          color: 'primary',
        },
        bug: {
          text: '缺陷',
          color: 'red',
        },
      },
    },
    {
      dataIndex: 'Priority',
      title: '优先级',
      width: '5%',
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'Priority',
        showType: 'tag',
      },
      renderText: (text, record) => (record?.Story ? record?.Story.Priority : record?.Issue.Priority),
    },
    {
      dataIndex: ['IssueSolution', 'ProductInfo'],
      title: '涉及产品',
      width: '10%',
      valueType: 'text',
      render: (text, record) => {
        const issueAppRel = record?.IssueSolution?.IssueAppRel;
        const productList = issueAppRel?.map((item) => item?.ProductVersionInfo?.ProductInfo?.Code) ?? [];
        return Array.from(new Set(productList)).join(',');
      },
    },
    {
      dataIndex: ['IssueSolution', 'IssueAppRel'],
      title: '涉及应用',
      width: '10%',
      valueType: 'text',
      render: (text, record) => {
        const issueAppRel = record?.IssueSolution?.IssueAppRel ?? [];
        return (
          <Popover
            placement="top"
            overlay={
              <DropdownBox>
                <List type="option">
                  {issueAppRel.map((item) => {
                    const version = `${item?.ReleaseApplicationPackage?.ApplicationVersion ?? ''}-${
                      item?.ReleaseApplicationPackage?.BuildTime ?? ''
                    }-${item?.ReleaseApplicationPackage?.CommitID?.substring(0, 8) ?? ''}`;
                    return (
                      <List.Item key={item?.ApplicationName}>
                        {item?.ApplicationName}-{version}
                      </List.Item>
                    );
                  })}
                </List>
              </DropdownBox>
            }
          >
            {issueAppRel.slice(0, 5).map((item) => (
              <div key={item?.ApplicationName}>{item?.ApplicationName};</div>
            ))}
            {issueAppRel?.length > 5 ? (
              <Popover
                trigger={'click'}
                placement="top"
                overlay={
                  <DropdownBox>
                    <List type="option">
                      {issueAppRel.map((item) => (
                        <List.Item key={item?.ApplicationName}>{item?.ApplicationName}</List.Item>
                      ))}
                    </List>
                  </DropdownBox>
                }
              >
                点击查看更多
              </Popover>
            ) : (
              ''
            )}
          </Popover>
        );
      },
    },
    {
      dataIndex: ['IssueSolution', 'Arch'],
      title: '架构',
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'PackageArch',
      },
      width: '10%',
      search: false,
    },
    {
      dataIndex: ['IssueSolution', 'Status'],
      title: '状态',
      width: '5%',
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'IssueStatus',
        showType: 'tag',
      },
    },
    {
      dataIndex: 'CreatedAt',
      title: '创建时间',
      width: '10%',
      valueType: 'dateTime',
      search: false,
    },
    {
      dataIndex: 'CreatedAt',
      title: '创建时间',
      width: '10%',
      valueType: 'dateRange',
      hideInTable: true,
    },
    { dataIndex: 'Warning', title: '备注', width: '5%', search: false },
  ] as TcsColumns[];
export const getAppColumns = ({}) =>
  [
    {
      dataIndex: ['ProductInfo', 'Code'],
      title: '产品Code',
      fixed: 'left',
      width: '10%',
      copyable: true,
    },
    {
      dataIndex: ['ProductVersion', 'Name'],
      title: '产品版本',
      width: '10%',
    },
    {
      dataIndex: 'ApplicationName',
      title: '应用名称',
      width: '10%',
    },
    {
      dataIndex: ['PatchPackage', 'Arch'],
      title: '架构',
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'PackageArch',
      },
      width: '10%',
      search: false,
    },
    {
      dataIndex: 'Details',
      title: '应用制品版本',
      children: [
        {
          dataIndex: ['Details', 'Baseline'],
          title: '基线版本',
          width: '10%',
        },
        {
          dataIndex: ['Details', 'Latest'],
          title: 'Patch版本',
          width: '10%',
        },
      ],
      search: false,
    },
    {
      dataIndex: 'Warning',
      title: '备注',
      width: '10%',
      search: false,
    },
  ] as TcsColumns[];
export const getVerificationColumns = ({}) =>
  [
    {
      dataIndex: 'ID',
      title: 'ID',
      fixed: 'left',
      width: '10%',
      copyable: true,
      search: false,
    },
    {
      dataIndex: ['ArtifactBranch', 'BranchName'],
      title: '制品分支',
      width: '10%',
    },
    {
      dataIndex: ['VerificationTag', 'TagNum'],
      title: '测试制品TAG',
      width: '10%',
    },
    {
      dataIndex: 'Status',
      title: '状态',
      width: '10%',
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'PatchVerificationStatus',
        showType: 'tag',
      },
    },
    {
      dataIndex: 'CreatedAt',
      title: '创建时间',
      width: '10%',
      valueType: 'dateTime',
      search: false,
    },
    {
      dataIndex: 'CreatedAt',
      title: '创建时间',
      width: '10%',
      valueType: 'dateRange',
      hideInTable: true,
    },
    {
      dataIndex: 'Description',
      title: '描述',
      width: '10%',
      search: false,
    },
  ] as TcsColumns[];
