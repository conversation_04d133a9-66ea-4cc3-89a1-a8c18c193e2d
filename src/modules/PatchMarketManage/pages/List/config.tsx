/*
 * @Author: lucyfang
 * @Date: 2024-12-10 17:39:40
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-12-17 16:32:33
 * @Description: 请输入注释信息
 */
import React from 'react';
import { DropdownBox, List, Popover } from '@tencent/tea-component';
import { TcsButton, TcsColumns, TcsTable } from '@tencent/tcs-component';

export const getColumns = ({
  onDetail,
  onChangeOrder,
  patchQueues,
}: {
  onDetail: (record: any) => void;
  onChangeOrder: (record: any) => void;
  patchQueues: any[];
}) => {
  const patchQueueValueEnum = patchQueues.reduce((acc, cur) => {
    acc[cur.UUID] = { text: cur.Name }; // 使用 UUID 作为 key，Name 作为显示文本
    return acc;
  }, {});
  return [
    {
      dataIndex: 'PatchID',
      title: 'PatchID',
      fixed: 'left',
      width: '9%',
      copyable: true,
    },
    {
      dataIndex: 'Name',
      title: 'Patch名称',
      width: '10%',
      search: false,
    },
    {
      dataIndex: 'PatchName',
      title: 'Patch名称',
      hideInTable: true,
    },
    {
      dataIndex: 'PatchQueue.Name',
      title: '所属队列',
      width: '10%',
      search: false,
      valueEnum: patchQueueValueEnum,
    },
    {
      dataIndex: 'PatchQueueUUID',
      title: 'Patch队列',
      valueEnum: patchQueueValueEnum,
      width: '10%',
      valueType: 'select',
      hideInTable: true,
    },
    {
      dataIndex: 'SolutionName',
      title: '解决方案',
      width: '10%',
      search: false,
    },
    {
      dataIndex: 'SolutionVersionCode',
      title: '解决方案版本',
      width: '6%',
      search: false,
    },

    {
      dataIndex: 'Arch',
      title: '架构',
      width: '7%',
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'PackageArch',
      },
    },
    {
      dataIndex: 'Status',
      title: '状态',
      width: '5%',
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'PatchStatus',
        showType: 'tag',
        mode: 'multiple',
        showSelectAll: true,
      },
    },
    {
      dataIndex: 'BranchName',
      title: '制品分支',
      width: '7%',
      search: false,
    },
    {
      dataIndex: 'BaselineTagNum',
      title: '初始制品Tag',
      width: '5%',
      search: false,
      render: (text, record) => (record?.BaselineTagNum > 0 ? record?.BaselineTagNum : '-'),
    },
    {
      dataIndex: 'PatchTagNum',
      title: 'Patch制品Tag',
      width: '5%',
      search: false,
      render: (text, record) => (record?.PatchTagNum > 0 ? record?.PatchTagNum : '-'),
    },
    {
      dataIndex: 'Issues',
      title: '缺陷个数',
      search: false,
      width: '4%',
      render: (text, record) => {
        const columns = [
          { dataIndex: 'IssueID', title: 'IssueID', width: 100 },
          {
            dataIndex: 'Title',
            title: '标题',
            width: 300,
            linkable: true,
            linkProps: {
              target: 'blank',
              linkUrl: (text, record) =>
                `/page/defect_manage/__develop_center_next/iteration_manage/defect/detail?issue_id=${record?.IssueID}`,
            },
          },
          {
            dataIndex: 'SeverityLevel',
            title: '严重程度',
            width: 100,
            valueType: 'dictSelect',
            fieldProps: {
              dictType: 'Severity',
              showType: 'tag',
            },
          },
        ];

        return (
          <Popover
            trigger={'click'}
            placement="bottom"
            overlayStyle={{ width: 600 }}
            overlay={
              <TcsTable
                columns={columns}
                dataSource={record?.Issues || []}
                options={false}
                scroll={{
                  x: 600,
                  y: 350,
                }}
              />
            }
          >
            <a>{`${record?.Issues?.length ?? 0}`}</a>
          </Popover>
        );
      },
    },
    {
      dataIndex: 'Stories',
      title: '需求个数',
      search: false,
      width: '4%',
      render: (text, record) => {
        const columns = [
          { dataIndex: 'IssueID', title: 'IssueID', width: 100 },
          {
            dataIndex: 'Title',
            title: '标题',
            width: 300,
            linkable: true,
            linkProps: {
              target: 'blank',
              linkUrl: (text, record) =>
                `/page/defect_manage/__develop_center_next/iteration_manage/story/detail?issue_id=${record?.IssueID}`,
            },
          },
          {
            dataIndex: 'Priority',
            title: '优先级',
            width: 100,
            valueType: 'dictSelect',
            fieldProps: {
              dictType: 'Priority',
              showType: 'tag',
            },
          },
        ];

        return (
          <Popover
            trigger={'click'}
            placement="bottom"
            overlayStyle={{ width: 600 }}
            overlay={
              <TcsTable
                columns={columns}
                dataSource={record?.Stories || []}
                options={false}
                scroll={{
                  x: 600,
                  y: 350,
                }}
              />
            }
          >
            <a>{`${record?.Stories?.length ?? 0}`}</a>
          </Popover>
        );
      },
    },
    {
      dataIndex: 'FuzzyProductCode',
      title: '涉及产品',
      width: '8%',
      render: (text, record) => (record?.Products || []).map((item) => item?.Code).join(','),
    },
    {
      dataIndex: 'APP',
      title: '涉及应用',
      width: '4%',
      search: false,
      render: (text, record) => (
        <Popover
          trigger={'click'}
          placement="bottom"
          overlay={
            <DropdownBox>
              <div style={{ maxHeight: 400 }}>
                <List type="option">
                  {(record?.Applications || []).map((item, index) => (
                    <List.Item key={index}>
                      {item?.Name}-{item?.ArtifactTag}
                    </List.Item>
                  ))}
                </List>
              </div>
            </DropdownBox>
          }
        >
          <a>{`${record?.Applications?.length ?? 0}`}</a>
        </Popover>
      ),
    },
    {
      dataIndex: 'Owner',
      title: '负责人',
      width: '8%',
      search: false,
      valueType: 'staffSelect',
    },
    {
      dataIndex: 'operation',
      title: '操作',
      fixed: 'right' as any,
      width: '10%',
      valueType: 'option',
      render(text, record) {
        return (
          <>
            <TcsButton type="link" onClick={() => onDetail(record)}>
              查看详细信息
            </TcsButton>
            <TcsButton type="link" onClick={() => onChangeOrder(record)}>
              查看交付单
            </TcsButton>
          </>
        );
      },
    },
  ] as TcsColumns[];
};
