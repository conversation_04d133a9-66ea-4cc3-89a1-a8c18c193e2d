/*
 * @Author: lucyfang
 * @Date: 2024-12-10 17:16:44
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-12-18 10:46:33
 * @Description: 请输入注释信息
 */
import React, { useState, useEffect } from 'react';
import { TcsButton, TcsLayout, TcsTable } from '@tencent/tcs-component';
import { PatchPublishMarketApi } from '@/common/api/patchPublishMarket';
import RevocationListModal from './components/RevocationListModal';
import { DefectManagementRoutePath } from '@/common/routePath';
import { getUrlParams } from '@/common/utils';
import { QueueManageApi } from '@/common/api/periodicQueueManage';
import { getColumns } from './config';

const List = () => {
  const [visibleRevocation, setVisibleRevocation] = useState(false);
  const { history } = TcsLayout.useHistory();
  const urlParams = getUrlParams();
  const [patchQueues, setPatchQueues] = useState([]);

  useEffect(() => {
    QueueManageApi.ListPatchQueues({
      PageNo: 1,
      PageSize: 999,
      Order: [{ Key: 'UpdatedAt', Sort: 'DESC' }],
    }).then((res) => {
      if (res.data.length) {
        setPatchQueues(res.data);
      }
    });
  }, []);

  const handleDetail = (record) => {
    history.push(`${DefectManagementRoutePath.PATCH_MARKET_DETAIL}?patchID=${record?.PatchID}`);
  };
  const handleChangeOrder = (record) => {
    PatchPublishMarketApi.GeneratePatchMarketDelivery({
      PatchID: record?.PatchID,
    }).then((res) => {
      if (res?.DeliveryDetailInfo?.length) {
        const uuid = res.DeliveryDetailInfo[0];
        const url = `/page/defect_manage/__flow-design/delivery_manage/detail?uuid=${uuid}`;
        window.open(url, '_blank');
      }
    });
  };
  const handleRevocationList = () => {
    setVisibleRevocation(true);
  };
  const columns = getColumns({ onDetail: handleDetail, onChangeOrder: handleChangeOrder, patchQueues });
  const handleRequest = (queryParams: any, options: any) => {
    const { Status, current, pageSize, PatchID, SolutionVersionCode, ...other } = queryParams;
    const { ProductVersionUUID } = urlParams;
    return PatchPublishMarketApi.ListMarketPatch(
      {
        ProductVersionUUID: ProductVersionUUID || undefined,
        ...other,
        Status,
        PageSize: pageSize,
        PageNo: current,
        PatchID: PatchID || undefined,
        SolutionUUID: SolutionVersionCode?.[0],
        SolutionVersionUUID: SolutionVersionCode?.[1],
      },
      options,
    );
  };
  return (
    <TcsLayout title="已发布Patch列表" customizeCard fullHeight history={history}>
      <TcsTable
        toolBarRender={() => <TcsButton onClick={handleRevocationList}>查看归档数据</TcsButton>}
        scrollInTable
        columns={columns}
        request={handleRequest}
        scroll={{
          x: 2000,
        }}
        pagination={{}}
        search={{}}
      />
      {visibleRevocation && (
        <RevocationListModal
          onSuccess={() => {
            setVisibleRevocation(false);
          }}
          onClose={() => {
            setVisibleRevocation(false);
          }}
        />
      )}
    </TcsLayout>
  );
};
export default List;
