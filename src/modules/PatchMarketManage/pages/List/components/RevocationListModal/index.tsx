/*
 * @Author: lucyfang
 * @Date: 2024-12-13 15:23:11
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-12-18 17:21:46
 * @Description: 请输入注释信息
 */
import React from 'react';
import { TcsModal, TcsTable, TcsColumns } from '@tencent/tcs-component';
import { PatchPublishMarketApi } from '@/common/api/patchPublishMarket';
import { Popover, DropdownBox, List } from '@tencent/tea-component';
interface IProps {
  onSuccess: () => void;
  onClose: () => void;
}

const RevocationListModal: React.FC<IProps> = ({ onClose }) => {
  const getColumns = ({}) =>
    [
      {
        dataIndex: 'PatchID',
        title: 'PatchID',
        fixed: 'left',
        width: '20%',
        copyable: true,
      },
      {
        dataIndex: 'Name',
        title: 'Patch名称',
        fixed: 'left',
        width: '20%',
      },
      {
        dataIndex: ['PatchMarket', 'Description'],
        title: '撤回原因',
        fixed: 'left',
        width: '20%',
      },
      {
        dataIndex: ['PatchMarket', 'Updater'],
        title: '撤回人',
        fixed: 'left',
        width: '10%',
      },
      {
        dataIndex: ['PatchMarket', 'UpdatedAt'],
        title: '最后更新时间',
        fixed: 'left',
        width: '15%',
        valueType: 'dateTime',
      },
      {
        dataIndex: ['PatchMarket', 'TenantList'],
        title: '发布空间',
        fixed: 'left',
        width: '20%',
        render: (text, record) => {
          if (record?.PatchMarket?.TenantList?.length) {
            const { TenantList } = record.PatchMarket;
            return (
              <Popover
                placement="top"
                overlay={
                  <DropdownBox>
                    <List type="option">
                      {TenantList.map((item) => (
                        <List.Item key={item?.Name}>{item?.Name}</List.Item>
                      ))}
                    </List>
                  </DropdownBox>
                }
              >
                {TenantList.slice(0, 3).map((item) => (
                  <div key={item?.Name}>{item?.Name};</div>
                ))}
                {TenantList?.length > 3 ? (
                  <Popover
                    trigger={'click'}
                    placement="top"
                    overlay={
                      <DropdownBox>
                        <List type="option">
                          {TenantList.map((item) => (
                            <List.Item key={item?.Name}>{item?.Name}</List.Item>
                          ))}
                        </List>
                      </DropdownBox>
                    }
                  >
                    点击查看更多
                  </Popover>
                ) : (
                  ''
                )}
              </Popover>
            );
          }
          return '所有空间';
        },
      },
    ] as TcsColumns[];
  const handleRequest = (queryParams: any, options: any) =>
    PatchPublishMarketApi.ListMarketPatch({ MarketStatus: 'revocation' }, options);
  return (
    <TcsModal title={'Patch撤回列表'} visible onCancel={onClose} footer={<></>} width={900}>
      <TcsTable request={handleRequest} columns={getColumns({})} scrollInTable pagination={{}} />
    </TcsModal>
  );
};
export default RevocationListModal;
