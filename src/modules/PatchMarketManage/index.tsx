/*
 * @Author: <PERSON><PERSON>
 * @Date: 2023-03-17 15:25:49
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-12-17 16:27:54
 * @Description: 请输入注释信息
 */
import React from 'react';
import { DefectManagementRoutePath } from '@/common/routePath';
import { Route } from 'react-router-dom';
import List from './pages/List';
import Detail from './pages/Detail';

const Index = () => {
  const routesConfig = [
    {
      path: DefectManagementRoutePath.PATCH_MARKET_MANAGE,
      component: List,
      exact: true,
    },
    {
      path: DefectManagementRoutePath.PATCH_MARKET_DETAIL,
      component: Detail,
      exact: true,
    },
  ];

  return (
    <>
      {routesConfig.map((item, index) => (
        <Route key={index} component={item.component} path={item.path} exact={item.exact} />
      ))}
    </>
  );
};
export default Index;
