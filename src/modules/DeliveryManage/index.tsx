/*
 * @Author: superfeng
 * @Date: 2023-03-17 15:25:49
 * @LastEditors: superfeng
 * @LastEditTime: 2023-04-10 16:59:46
 * @Description: 请输入注释信息
 */
import React from 'react';
import { Route } from 'react-router-dom';
import List from './pages/List';
import { DefectManagementRoutePath, withRouteBasename } from '@/common/routePath';
import Detail from './pages/Detail';
import AddDelivery from './pages/AddDelivery';

const Index = () => {
  const routesConfig = [
    {
      path: withRouteBasename('/delivery_manage'),
      component: List,
      exact: true,
    },
    {
      path: DefectManagementRoutePath.DELIVERY_DETAIL_PAGE,
      component: Detail,
      exact: true,
    },
    {
      path: DefectManagementRoutePath.DELIVERY_ADD_PAGE,
      component: AddDelivery,
      exact: true,
    },
    {
      path: DefectManagementRoutePath.DELIVERY_EDIT_PAGE,
      component: AddDelivery,
      exact: true,
    },
  ];

  return (
    <>
      {routesConfig.map((item, index) => (
        <Route key={index} component={item.component} path={item.path} exact={item.exact} />
      ))}
    </>
  );
};
export default Index;
