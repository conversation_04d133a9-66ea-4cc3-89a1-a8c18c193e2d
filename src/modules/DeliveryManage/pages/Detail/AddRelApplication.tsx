/*
 * @Author: superfeng
 * @Date: 2023-04-10 10:06:17
 * @LastEditors: superfeng
 * @LastEditTime: 2023-04-10 16:54:37
 * @Description: 请输入注释信息
 */
import {
  getApplicationVersions,
  IApplicationNameRelVersion,
  IDeliveryInfo,
  listApplicationNames,
  updateDeliveryInfo,
} from '@/common/api/deliveryManage';
import { Button, Card, Col, Form, message, Modal, Row, Select, Table, TagSelect } from '@tencent/tea-component';
import React, { useEffect, useState } from 'react';
import Loading from '@/common/components/Loading';

export interface IProps {
  visible: boolean;
  deliveryInfo?: IDeliveryInfo;
  onConfirm: () => void;
  onCancel: () => void;
}

const getColumns = ({
  appVersionList,
  onChangeVersion,
  onRemoveApp,
}: {
  appVersionList: IApplicationNameRelVersion[];
  onChangeVersion: (ApplicationName: string, ApplicationVersion: string) => void;
  onRemoveApp: (record: any) => void;
}) => [
  {
    header: '应用名称',
    key: 'ApplicationName',
    width: '30%',
  },
  {
    header: '应用版本',
    key: 'ApplicationVersion',
    render(record) {
      const find = appVersionList.find((item) => item.ApplicationName === record.ApplicationName);
      return (
        <Select
          appearance="button"
          style={{ width: '100%' }}
          value={record.ApplicationVersion}
          size="full"
          matchButtonWidth
          searchable
          options={
            find?.ApplicationVersions?.map((item) => ({
              text: item.ApplicationVersion,
              value: item.ApplicationVersion,
            })) || []
          }
          onChange={(value) => onChangeVersion(record.ApplicationName, value)}
        />
      );
    },
    width: '50%',
  },
  {
    header: '操作',
    key: 'action',
    width: '20%',
    render(record: any) {
      return (
        <Button type="link" onClick={() => onRemoveApp(record)}>
          移除
        </Button>
      );
    },
  },
];

const AddRelApplication: React.FC<IProps> = ({ visible, deliveryInfo, onConfirm, onCancel }) => {
  const [appList, setAppList] = useState<string[]>([]);
  const [selectedAppNames, setSelectedAppNames] = useState<string[]>([]);
  const [appVersionList, setAppVersionList] = useState<IApplicationNameRelVersion[]>([]);
  const [dataSource, setDataSource] = useState<Array<{ ApplicationName: string; ApplicationVersion: string }>>([]);
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    if (deliveryInfo?.SolutionVersionID && visible) {
      setLoading(true);
      listApplicationNames({
        SolutionVersionID: deliveryInfo.SolutionVersionID,
        Arch: deliveryInfo.Arch,
      })
        .then((res) => {
          if (res.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            setAppList(res.ApplicationsNames);
          }
        })
        .finally(() => {
          setLoading(false);
        });
      if (deliveryInfo.ApplicationInfos?.length) {
        setDataSource(deliveryInfo.ApplicationInfos);
        const selectedAppNames = deliveryInfo.ApplicationInfos.map((item) => item.ApplicationName);
        setSelectedAppNames(selectedAppNames);
        handleSelectedApp(selectedAppNames);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [deliveryInfo?.SolutionVersionID, deliveryInfo?.Arch, visible]);

  function handleSelectedApp(selectedAppNames: string[]) {
    setLoading(true);
    getApplicationVersions({
      SolutionVersionID: deliveryInfo!.SolutionVersionID,
      Arch: deliveryInfo!.Arch,
      ApplicationsNames: selectedAppNames,
    })
      .then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          setAppVersionList(res.ApplicationNameVersions || []);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }

  useEffect(() => {
    setDataSource((dataSource) => {
      const oldDataVersion = dataSource.reduce((pre: Record<string, string>, current) => {
        // eslint-disable-next-line no-param-reassign
        pre[current.ApplicationName] = current.ApplicationVersion;
        return pre;
      }, {});
      return (
        appVersionList.map((item) => {
          const version = oldDataVersion[item.ApplicationName];
          return {
            ApplicationName: item.ApplicationName,
            ApplicationVersion: version,
          };
        }) || []
      );
    });
  }, [appVersionList]);

  function handleChangeVersion(ApplicationName: string, ApplicationVersion: string) {
    setDataSource((dataSource) =>
      dataSource.map((item) => {
        if (item.ApplicationName === ApplicationName) {
          return {
            ...item,
            ApplicationVersion,
          };
        }
        return item;
      }),
    );
  }

  function handleRemoveApp(record: any) {
    setDataSource((dataSource) => dataSource.filter((item) => item.ApplicationName !== record.ApplicationName));
  }

  function handleConfirm() {
    if (dataSource.length === 0) {
      return message.warning({
        content: '请添加交付单关联的应用',
      });
    }
    const findEmpty = dataSource.find((item) => !item.ApplicationVersion);
    if (findEmpty) {
      return message.warning({
        content: `请选择应用【${findEmpty.ApplicationName}】对应的版本`,
      });
    }

    setLoading(true);
    updateDeliveryInfo({
      DeliveryID: deliveryInfo!.UUID,
      Applications: dataSource,
    })
      .then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          message.success({
            content: '更新成功',
          });
          onConfirm();
          setSelectedAppNames([]);
          setDataSource([]);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }

  function handleCancel() {
    onCancel();
    setSelectedAppNames([]);
    setDataSource([]);
  }

  return (
    <Modal visible={visible} caption="修改关联应用" size="l" onClose={handleCancel}>
      <Loading loading={loading}>
        <Card>
          <Card.Body>
            <Form style={{ width: '100%' }}>
              <Row style={{ width: '100%' }}>
                <Col span={20}>
                  <Form.Item label="选择关联应用">
                    <TagSelect
                      placeholder="请选择应用"
                      value={selectedAppNames}
                      options={appList.map((item) => ({
                        value: item,
                        text: item,
                      }))}
                      onChange={(values) => setSelectedAppNames(values)}
                    />
                  </Form.Item>
                </Col>
                <Col span={4}>
                  <Button type="primary" onClick={() => handleSelectedApp(selectedAppNames)}>
                    确认
                  </Button>
                </Col>
              </Row>
            </Form>
          </Card.Body>
        </Card>
        <Card>
          <Card.Body title="选择应用版本">
            <Table
              columns={getColumns({
                appVersionList,
                onChangeVersion: handleChangeVersion,
                onRemoveApp: handleRemoveApp,
              })}
              records={dataSource}
              recordKey="ApplicationName"
            />
          </Card.Body>
        </Card>
      </Loading>
      <Modal.Footer>
        <Button type="primary" onClick={handleConfirm}>
          确定
        </Button>
        <Button type="weak" onClick={handleCancel}>
          取消
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default AddRelApplication;
