/*
 * @Author: superfeng
 * @Date: 2023-05-22 14:39:58
 * @LastEditors: superfeng
 * @LastEditTime: 2023-05-22 19:12:48
 * @Description: 请输入注释信息
 */
import React from 'react';
import { IDeliverySiteRel } from '@/common/api/deliveryManage';
import { AntoolStatus } from '../../constant';
import { TcsTable } from '@tencent/tcs-component';

export interface IProps {
  record: IDeliverySiteRel;
  handleRecallSite: (record) => void;
  deliveryManage?: boolean;
}

const getColumns = ({
  handleRecallSite,
  // deliveryManage
}) => [
  {
    title: '局点信息库局点',
    dataIndex: ['AnDonSiteInfo', 'EnglishName'],
  },
  {
    title: '状态',
    dataIndex: 'Status',
  },
  {
    title: '所处阶段',
    dataIndex: 'Phase',
  },
  {
    title: 'Antool链接',
    dataIndex: 'AntoolProcessInstanceID',
    linkable: true,
    copyable: true,
    renderText: (text, record) => {
      if (!text) {
        return undefined;
      }
      let baseUrl = '';
      if (window.location.hostname.startsWith('dev.') || window.location.hostname.startsWith('pre.')) {
        baseUrl = `https://test-antool.woa.com/micro/antool-change/detail?appId=69&formKey=changeformnew&processInstanceId=${record.AntoolProcessInstanceID}`;
      } else {
        baseUrl = `https://antool.woa.com/micro/antool-change/detail?appId=69&formKey=changeformnew&processInstanceId=${record.AntoolProcessInstanceID}`;
      }
      return baseUrl;
    },
    linkProps: {
      linkText: (text) => (text ? '跳转至Antool' : undefined),
    },
  },
  {
    title: '物料链接',
    dataIndex: 'MaterialURL',
    copyable: true,
    linkable: true,
    linkProps: {
      linkText: () => '点击下载',
    },
  },
  {
    title: 'AnDonID',
    dataIndex: ['AnDonSiteInfo', 'AnDonID'],
    copyable: true,
  },
  {
    title: 'Antool 变更单ID',
    dataIndex: 'AntoolID',
    renderText: (text) => (text ? `C${text}` : undefined),
    copyable: true,
  },
  {
    title: '可用区ID',
    dataIndex: ['AnDonSiteInfo', 'ZoneID'],
  },
  {
    title: '可用区名称',
    dataIndex: ['AnDonSiteInfo', 'AndonSiteName'],
    width: '20%',
  },
  {
    title: '交付阶段',
    dataIndex: ['AnDonSiteInfo', 'Status'],
  },
  {
    title: '操作',
    dataIndex: 'option',
    valueType: 'option',
    render(text, record) {
      if (
        !Object.values(AntoolStatus)?.includes(record.Phase) &&
        record?.Status !== '已召回' &&
        // deliveryManage &&
        record?.AntoolID
      ) {
        return <a onClick={() => handleRecallSite(record)}>召回</a>;
      }
      return '-';
    },
  },
];

const AntoolSiteTable: React.FC<IProps> = ({ record, handleRecallSite }) => {
  const columns = getColumns({ handleRecallSite });

  return <TcsTable options={false} columns={columns} dataSource={record.DeliverySiteAntoolRel || []} />;
};

export default AntoolSiteTable;
