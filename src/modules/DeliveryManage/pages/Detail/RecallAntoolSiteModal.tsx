import { DeliveryManage, IDeliverySiteAntoolRel } from '@/common/api/deliveryManage';
import {
  TcsForm,
  TcsFormSelect,
  TcsFormTextArea,
  TcsModal,
  TcsFormRadio,
  TcsFormText,
  TcsFormStaffSelect,
  FormInstance,
  TcsButton,
} from '@tencent/tcs-component';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { AntoolStatus } from '../../constant';
import useLookup from '@/common/hookups/useLookup';
import RecallResultModal from './RecallResultModal';
export interface IProps {
  onClose: () => void;
  deliverySiteRel: IDeliverySiteAntoolRel[];
  deliveryUUID?: string;
  batchDeliverySiteRel: IDeliverySiteAntoolRel[];
  handleRecallSuccess: () => void;
}
const RecallAntoolSiteModal = ({
  onClose,
  deliverySiteRel,
  deliveryUUID,
  batchDeliverySiteRel,
  handleRecallSuccess,
}: IProps) => {
  const [form] = TcsForm.useForm();
  const formRef = useRef<FormInstance>();
  const [loading, setLoading] = useState<boolean>(false);
  const { lookups } = useLookup(['RecallSheetType']);
  const recallSheetType = lookups?.RecallSheetType;
  const [des, setDes] = useState('');
  const [switchOnlyOpen, setSwitchOnlyOpen] = useState<boolean>(false);
  const [updateTapd, setUpdateTapd] = useState<boolean>(false);
  const [targetStatusList, setTargetStatusList] = useState([]);
  const [needPackPackageAgain, setNeedPackPackageAgain] = useState(false);
  const [visibleRecallResultModal, setVisibleRecallResultModal] = useState(false);
  const handleCancel = () => {
    form.resetFields();
    onClose();
  };
  const [recallResults, setRecallResults] = useState([]);
  const antoolOptions = useMemo(
    () =>
      batchDeliverySiteRel
        ?.filter(
          (item) => !Object.values(AntoolStatus)?.includes(item.Phase) && item.Status !== '已召回' && item.AntoolID,
        )
        ?.map((antool) => ({
          label: `${antool.AnDonSiteInfo?.EnglishName || '-'}(C${antool.AntoolID})`,
          value: antool.AntoolID,
        })),
    [deliverySiteRel],
  );

  const handleConfirm = () => {
    formRef?.current?.validateFields().then((values) => {
      setLoading(true);
      const defaultParams = {
        DeliveryUUID: deliveryUUID || '',
        ChangeIDs: values.ChangeIDs,
        Comment: values.Comment,
        ReasonClass: values.ReasonClass,
        NeedPackPackageAgain: needPackPackageAgain,
        NeedUpdateTAPD: values?.NeedUpdateTAPD === '是' ? true : false,
      };
      if (values?.NeedUpdateTAPD === '是') {
        Object.assign(defaultParams, {
          TargetTAPDStatus: values.TargetStatus,
          TargetTAPDOwner: Array.isArray(values.TargetOwner) ? values.TargetOwner.join(';') : values.TargetOwner,
        });
      }
      DeliveryManage.RecallAntoolSheet(defaultParams)
        .then((res) => {
          if (res?.Results?.length) {
            setVisibleRecallResultModal(true);
            setRecallResults(res?.Results || []);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  useEffect(() => {
    form.setFieldsValue({
      ChangeIDs: antoolOptions?.map((item) => item.value),
    });
  }, [antoolOptions]);
  const handleReasonClass = (value) => {
    const reasonClassValue = recallSheetType?.filter((item) => item?.Name === value)?.[0];
    // 包类型描述
    const comment = reasonClassValue?.Extra?.Comment;
    // 是否更新tapd单
    const needUpdateTAPD = reasonClassValue?.Extra?.NeedUpdateTAPD ? '是' : '否';
    // 是否出包
    const needPackPackageAgain = reasonClassValue?.Extra?.NeedPackPackageAgain ? '是' : '否';
    if (reasonClassValue?.Extra?.NeedUpdateTAPD) {
      setUpdateTapd(true);
    } else {
      setUpdateTapd(false);
    }

    // 更新描述
    setDes(comment || '');
    // 更新是否重新出包
    setNeedPackPackageAgain(reasonClassValue?.Extra?.NeedPackPackageAgain);
    // 请求tapd状态
    // 更新表单默认值
    form.setFieldsValue({ NeedUpdateTAPD: needUpdateTAPD, NeedPackPackageAgain: needPackPackageAgain });
    handleGetRecallTAPDInfo(reasonClassValue?.Extra?.TapdRole || '');
  };
  const handleSwicthChange = () => {
    setSwitchOnlyOpen(!switchOnlyOpen);
  };
  const handleGetRecallTAPDInfo = (value) => {
    DeliveryManage.GetRecallTAPDInfo({ DeliveryUUID: deliveryUUID || '', TapdRole: value || '' }).then((res) => {
      const statusName = res?.StatusName;
      const currentOwner = res?.CurrentOwner;
      const targetStatusList = res?.TargetStatusList;
      setTargetStatusList(targetStatusList);
      form.setFieldsValue({ CurrentOwner: currentOwner, StatusName: statusName });
    });
  };
  const handleCloseModal = () => {
    setVisibleRecallResultModal(false);
    handleRecallSuccess();
    handleCancel();
  };
  return (
    <>
      <TcsModal visible title="Antool局点召回" onCancel={handleCancel} onOk={handleConfirm} confirmLoading={loading}>
        <TcsForm form={form} formRef={formRef}>
          {batchDeliverySiteRel?.length !== 0 && (
            <TcsFormSelect
              label="Antool变更单"
              name="ChangeIDs"
              options={antoolOptions || []}
              rules={[
                {
                  required: true,
                  message: '请选择要发布的antool变更单',
                },
              ]}
              placeholder="请选择要发布的antool变更单"
              fieldProps={{ mode: 'multiple', showSelectAll: true, size: 'full' }}
            />
          )}
          <TcsFormSelect
            extra={
              des ? (
                <>
                  {des}
                  <TcsButton onClick={handleSwicthChange} type="link" style={{ marginLeft: '10px' }}>
                    {switchOnlyOpen ? '关闭高级设置' : '打开高级设置'}
                  </TcsButton>
                </>
              ) : (
                <></>
              )
            }
            label="召回类型"
            name="ReasonClass"
            fieldProps={{ size: 'full' }}
            options={recallSheetType?.map((item) => ({
              label: item?.Name,
              value: item?.Name,
            }))}
            rules={[
              {
                required: true,
                message: '请选择召回类型',
              },
            ]}
            onChange={handleReasonClass}
          />
          {switchOnlyOpen && (
            <TcsFormRadio
              label="是否需要重新出包"
              name="NeedPackPackageAgain"
              options={[
                { label: '是', value: '是' },
                { label: '否', value: '否' },
              ]}
              onChange={(value) => {
                if (value === '是') {
                  return setNeedPackPackageAgain(true);
                }
                return setNeedPackPackageAgain(false);
              }}
            />
          )}

          <TcsFormRadio
            label="是否更新TAPD单"
            name="NeedUpdateTAPD"
            options={[
              { label: '是', value: '是' },
              { label: '否', value: '否' },
            ]}
            onChange={(value) => {
              if (value === '是') {
                return setUpdateTapd(true);
              }
              setUpdateTapd(false);
            }}
          />
          {updateTapd && (
            <>
              <TcsFormText readonly name="StatusName" label="当前TAPD状态" />
              <TcsFormText readonly name="CurrentOwner" label="当前TAPD处理人" />
              <TcsFormSelect
                name="TargetStatus"
                label="目标状态"
                rules={[
                  {
                    required: true,
                    message: '请选择目标状态',
                  },
                ]}
                fieldProps={{
                  size: 'full',
                  options: targetStatusList?.map((item) => {
                    return {
                      value: item?.Code,
                      label: item?.Name,
                    };
                  }),
                }}
              />
              <TcsFormStaffSelect
                fieldProps={{
                  multiple: true,
                }}
                rules={[
                  {
                    required: true,
                    message: '请选择目标处理人',
                  },
                ]}
                name="TargetOwner"
                label="目标处理人"
              />
            </>
          )}
          <TcsFormTextArea
            label="召回原因"
            name="Comment"
            fieldProps={{ size: 'full' }}
            rules={[
              {
                required: true,
                message: '请选择召回原因',
              },
            ]}
          />
        </TcsForm>
      </TcsModal>
      {visibleRecallResultModal && <RecallResultModal recallResults={recallResults} onClose={handleCloseModal} />}
    </>
  );
};
export default RecallAntoolSiteModal;
