import { IDeliveryInfo } from '@/common/api/deliveryManage';
import useLookup from '@/common/hookups/useLookup';
import { SearchBox, Table, Tag } from '@tencent/tea-component';
import { rowtooltip, scrollable, selectable, sortable } from '@tencent/tea-component/lib/table/addons';
import React, { ForwardRefRenderFunction, forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';

import { EXPORT_PACKAGE_STATUS } from '@/common/config';
import { TcsButton, TcsSpace } from '@tencent/tcs-component';

interface ExportFlowProps {
  deliveryInfo?: IDeliveryInfo;
  // onShowMore?: () => void;
  showMore: boolean;
  onRemoveMore?: (site: any) => void;
  selectedKeys: string[];
  setSelectedKeys: React.Dispatch<any>;
  initialSelectAll: boolean;
}

type NewType =
  // onRemoveMore,
  {
    getLookupByCode: (type, value) => any;
    // showMore: boolean;
    // onRemoveMore?: (site: any) => void;
  };

const getColumns = ({
  getLookupByCode,
}: // showMore,
// onRemoveMore,
NewType) => {
  const columns = [
    { key: 'ClientName', header: '客户名称', width: '20%' },
    { key: 'SiteName', header: '局点名称', width: '20%' },
    {
      key: 'SiteVariable.SiteArch',
      header: '局点架构',
      width: '25%',
      render(record) {
        const SiteArch = record?.SiteVariable?.SiteArch;
        return SiteArch || '-';
      },
    },
    {
      key: 'Status',
      header: '出包状态',
      width: '15%',
      render(record) {
        const { Status } = record;
        const lookup = getLookupByCode('DeliveryStatus', Status);
        if (lookup) {
          return <Tag style={{ color: lookup.Extra?.Color }}>{lookup.Name}</Tag>;
        }
        return Status || '-';
      },
    },
    {
      key: 'SiteVariable.BugfixType',
      header: '缺陷修复方式',
      width: '14%',
    },
    {
      width: '16%',
      key: 'SiteVariable.OperationTool',
      header: '变更工具',
    },
  ];

  return columns;
};

const ExportSelectSite: ForwardRefRenderFunction<any, ExportFlowProps> = (props, ref) => {
  const {
    deliveryInfo,
    showMore,
    // onRemoveMore,
    selectedKeys,
    setSelectedKeys,
    initialSelectAll,
  } = props;

  const { getLookupByCode } = useLookup([]);
  // const [moreSites, setMoreSites] = useState<any[]>([]);

  const [searchValue, setSearchValue] = useState('');
  const [sorts, setSorts] = useState<any>([]);

  useEffect(() => {
    if (initialSelectAll && deliveryInfo) {
      setSelectedKeys(() =>
        deliveryInfo?.DeliverySiteRel?.filter((item) =>
          ([EXPORT_PACKAGE_STATUS.NEED_PACK_PACKAGE] as any).includes(item?.Status),
        )?.map((item) => item?.SiteUUID as string),
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [deliveryInfo, initialSelectAll]);

  const allSites = useMemo(() => deliveryInfo?.DeliverySiteRel || [], [deliveryInfo?.DeliverySiteRel]);

  // useMemo(
  //   () => [
  //     ...(moreSites || []).map((item) => ({
  //       ...item,
  //       Status: 'New',
  //       SiteName: item.SiteName,
  //       SiteUUID: item.SiteUUID,
  //       isNewSite: true,
  //     })),
  //     ...(deliveryInfo?.DeliverySiteRel || []),
  //   ],
  //   [deliveryInfo?.DeliverySiteRel, moreSites],
  // );
  const getSelectableSiteUUIDs = () =>
    allSites
      .filter(
        (item) =>
          item.Status !== EXPORT_PACKAGE_STATUS.NEW &&
          item.Status !== EXPORT_PACKAGE_STATUS.CHECK_NEED_PACK_PACKAGE_FAILED,
      )
      .map((item) => item.SiteUUID);

  useImperativeHandle(
    ref,
    () => ({
      getSelectedKeys: () => selectedKeys,
      // setMoreSites: (sites: any[]) => {
      //   setMoreSites(sites);
      //   setSelectedKeys((keys) => Array.from(new Set([...keys, ...sites.map((item) => item.SiteUUID)])));
      // },
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [selectedKeys],
  );

  const handleSearch = (value) => {
    setSearchValue(value);
  };

  const handleInvertSelection = () => {
    // 获取所有可选择的状态的 SiteUUID
    const selectableSiteUUIDs = getSelectableSiteUUIDs();

    // 过滤出当前已选择的且可选择的状态的 SiteUUID
    const currentlySelectedSelectable = selectedKeys.filter((uuid) => selectableSiteUUIDs.includes(uuid));

    // 反选：选择所有可选择的但未被当前选择的 SiteUUID
    const newSelectedKeys = selectableSiteUUIDs.filter((uuid) => !currentlySelectedSelectable.includes(uuid));

    // 更新选择状态
    setSelectedKeys(newSelectedKeys);
  };
  const handleAllSelection = () => {
    const selectableSiteUUIDs = getSelectableSiteUUIDs();
    setSelectedKeys(selectableSiteUUIDs);
  };

  const filteredRecords = useMemo(() => {
    if (searchValue) {
      return allSites.filter((item: any) => item?.ClientName.includes(searchValue));
    }
    return allSites;
  }, [searchValue, allSites]);

  return (
    <>
      <div style={{ marginBottom: 5 }}>
        <SearchBox placeholder="请输入客户名称进行搜索" onSearch={handleSearch} />
      </div>
      {!showMore && (
        <>
          <div style={{ marginBottom: 5 }}>
            <TcsSpace>
              <TcsButton type="weak" onClick={handleAllSelection}>
                全选
              </TcsButton>
              <TcsButton type="weak" onClick={handleInvertSelection}>
                反选
              </TcsButton>
            </TcsSpace>
          </div>
        </>
      )}

      <Table
        bordered
        recordKey="SiteUUID"
        columns={getColumns({
          getLookupByCode,
          // showMore,
          // onRemoveMore,
        })}
        addons={[
          selectable({
            all: false,
            value: selectedKeys,
            onChange: (keys) => {
              setSelectedKeys(keys);
            },
            rowSelect: true,
            rowSelectable: (rowKey, context) =>
              context.record?.Status !== EXPORT_PACKAGE_STATUS.NEW &&
              context.record.Status !== EXPORT_PACKAGE_STATUS.CHECK_NEED_PACK_PACKAGE_FAILED,
          }),
          rowtooltip({
            tooltip(record) {
              if (record?.Status === EXPORT_PACKAGE_STATUS.NEW) {
                return '当前局点为【新建】状态，无法选择';
              }
              if (record?.Status === EXPORT_PACKAGE_STATUS.CHECK_NEED_PACK_PACKAGE_FAILED) {
                return '当前局点为【出包前置检查失败】状态，无法选择';
              }
              if (record?.Status === EXPORT_PACKAGE_STATUS.APPROVAL_PENDING) {
                return '当前局点为【待审批】状态，请勾选后点击提交审批流程进行审批';
              }
            },
          }),
          scrollable({
            maxHeight: 300,
          }),
          sortable({
            columns: [
              {
                key: 'Status',
                prefer: 'desc',
              },
              {
                key: 'SiteVariable.BugfixType',
                prefer: 'desc',
              },
            ],
            value: sorts,
            onChange: (value) => setSorts(value),
          }),
        ]}
        records={filteredRecords?.sort(sortable.comparer(sorts))}
      />
    </>
  );
};

export default forwardRef(ExportSelectSite);
