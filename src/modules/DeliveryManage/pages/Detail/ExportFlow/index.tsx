import { generateWorkflowInstance, IDeliveryInfo } from '@/common/api/deliveryManage';
import { Alert, message, Modal } from '@tencent/tea-component';
import React, { FC, useMemo, useRef, useState } from 'react';
import { startWorkflow } from '../../../utils';
import { TcsButton, TcsModal, TcsPopConfirm, TcsSpace } from '@tencent/tcs-component';
// import AddRelRegionContent from '../AddRelRegion/AddRelRegionContent';
import ExportSelectSite from './SelectedSite';
import { DELIVERY_FROM_TYPE, EXPORT_PACKAGE_STATUS } from '@/common/config';
// import { DeliveryManageApi } from '@/common/api/deliveryManage.api';
import { copyText } from '@/common/utils';
// import { IListProjectClientSiteApi } from '@/common/api/iterationManage.api';

interface ExportFlowProps {
  setVisible: (visible: boolean) => void;
  deliveryInfo?: IDeliveryInfo;
}

const ExportFlow: FC<ExportFlowProps> = (props) => {
  const { setVisible, deliveryInfo } = props;
  // const [newSites, setNewSites] = useState<IListProjectClientSiteApi.ISiteBaseInfo[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const exportSelectSiteRef = useRef<any>();

  // 选中的待审批局点
  const selectedApprovalPendingSites = useMemo(
    () =>
      deliveryInfo?.DeliverySiteRel?.filter(
        (item) => selectedKeys.includes(item.SiteUUID) && item.Status === EXPORT_PACKAGE_STATUS.APPROVAL_PENDING,
      ) || [],
    [selectedKeys, deliveryInfo?.DeliverySiteRel],
  );

  // 是否显示审批按钮
  const showApprovalBtn = useMemo(
    () => (deliveryInfo?.FromOriginKey || deliveryInfo?.FromType) !== DELIVERY_FROM_TYPE.patch,
    [deliveryInfo],
  );

  const handleConfirm = async () => {
    if (!selectedKeys?.length) {
      message.warning({
        content: '请至少选择一个局点',
      });
      return;
    }
    if (showApprovalBtn && selectedApprovalPendingSites?.length > 0) {
      Modal.alert({
        message: '提示',
        description: '您勾选的局点中包含需审批的局点，请点击下方提交审批流程按钮进行审批',
      });
      return;
    }
    // const findNewSite = newSites.filter((item) => selectedKeys.includes(item.SiteUUID));
    setLoading(true);
    const allSiteUUIDs = deliveryInfo?.DeliverySiteRel.map((item) => item.SiteUUID) || [];
    // 如果添加了新局点，则需要更新局点列表
    // if (findNewSite?.length) {
    //   allSiteUUIDs.push(...findNewSite.map((item) => item.SiteUUID));
    //   await updateDeliveryInfo({
    //     DeliveryID: deliveryInfo?.UUID,
    //     Sites: [
    //       ...findNewSite.map((item) => ({
    //         SiteName: item.SiteName,
    //         SiteUUID: item.SiteUUID,
    //         SiteType: item.SiteType,
    //       })),
    //       ...(deliveryInfo?.DeliverySiteRel || []),
    //     ],
    //   });
    // }
    // 可能有新的局点先新增，后移除，所以需要过滤一下
    const keys = selectedKeys.filter((item) => allSiteUUIDs.includes(item));
    const checkedAll = keys.length === allSiteUUIDs.length;
    const params = checkedAll ? { record: deliveryInfo as any } : { record: deliveryInfo as any, SiteUUIDList: keys };
    startWorkflow(params).finally(() => {
      setLoading(false);
      setVisible(false);
    });
  };

  // function handleChangeCheckedSites(uuids, sites: IListProjectClientSiteApi.ISiteBaseInfo[]) {
  //   // 只保留本次要新增的局点，已经加到局点列表里面的排除掉
  //   const selectedUUIDs = deliveryInfo?.DeliverySiteRel.map((item) => item.SiteUUID);
  //   setNewSites(sites.filter((item) => !selectedUUIDs?.includes(item.SiteUUID)));
  // }

  function handleCancel() {
    setVisible(false);
  }

  // 启动审批流程
  function handleStartApprovalFlow() {
    generateWorkflowInstance({
      GlobalValues: {
        DeliveryUUID: deliveryInfo!.UUID,
        SiteUUIDList: selectedApprovalPendingSites.map((item) => item.SiteUUID),
      },
      WorkflowUUID: '40697eb7e8d746f1b5ccf2d102d8c2c4',
    }).then((res) => {
      if (res.Error) {
        message.error({
          content: res.Error.Message,
        });
      } else {
        Modal.success({
          message: '提示',
          description:
            '流程启动成功,请联系熊欢(huanxiong)，李琪(qsli)或李先峰(nikexfli)进行审批，审批通过后，将针对审批的局点自动启动出包流程进行出包',
          buttonText: '复制并打开审批流',
          onClose() {
            copyText(
              `window.location.origin/page/flow-design/flow-publish/exec?instance_id=${res.WorkflowInstanceID}&workflow_id=${res.WorkflowID}`,
            );
            window.open(
              `/page/flow-design/flow-publish/exec?instance_id=${res.WorkflowInstanceID}&workflow_id=${res.WorkflowID}`,
              '_blank',
            );
          },
        });
      }
    });
  }

  return (
    <TcsModal
      title="选择出包局点"
      visible
      onCancel={handleCancel}
      destroyOnClose
      onOk={handleConfirm}
      confirmLoading={loading}
      // width={more ? 1300 : 800}
      width={800}
      footer={
        <TcsSpace>
          <TcsButton type="primary" onClick={handleConfirm}>
            启动出包流程
          </TcsButton>
          {showApprovalBtn && (
            <TcsPopConfirm
              title="提示"
              message={
                <>
                  流程启动成功,请联系熊欢(huanxiong)或李琪(qsli)进行审批，审批通过后，将
                  <span style={{ color: 'red', fontWeight: 600 }}>针对审批的局点</span>
                  自动启动出包流程进行出包，是否继续？
                </>
              }
              onConfirm={handleStartApprovalFlow}
              disabled={selectedApprovalPendingSites?.length === 0}
            >
              <TcsButton
                disabled={selectedApprovalPendingSites?.length === 0}
                tooltip={
                  selectedApprovalPendingSites?.length === 0
                    ? '如您需要出包的局点状态为待审批，勾选此局点点击提交审批局点按钮，即可启动审批流程'
                    : '点击启动审批流程'
                }
              >
                提交审批流程
              </TcsButton>
            </TcsPopConfirm>
          )}
          <TcsButton onClick={handleCancel}>取消</TcsButton>
        </TcsSpace>
      }
    >
      {selectedApprovalPendingSites.length > 0 && showApprovalBtn && (
        <Alert type="warning">您勾选的局点中包含需审批的局点，请点击下方提交审批流程按钮进行审批</Alert>
      )}
      <ExportSelectSite
        ref={exportSelectSiteRef}
        deliveryInfo={deliveryInfo}
        // onShowMore={() => {
        //   setMore((more) => !more);
        // }}
        showMore={false}
        selectedKeys={selectedKeys}
        setSelectedKeys={setSelectedKeys}
        initialSelectAll={true} // 初始默认全选
      />
      {/* {more ? (
        <Transfer
          operations={[
            {
              onClick() {
                exportSelectSiteRef.current.setMoreSites(newSites);
              },
              bubble: '添加新局点出包',
            },
            false,
          ]}
          leftCell={
            <Transfer.Cell title="选择更多局点" style={{ width: '54%' }}>
              <AddRelRegionContent
                deliveryInfo={deliveryInfo}
                onChangeCheckedClientSites={handleChangeCheckedSites}
                disabledAllBeforeSelected
                maxHeight={235}
              />
            </Transfer.Cell>
          }
          rightCell={
            <Transfer.Cell title="可出包局点" style={{ width: '46%' }}>
              <ExportSelectSite
                deliveryInfo={deliveryInfo}
                onShowMore={() => {
                  setMore((more) => !more);
                }}
                showMore={more}
                onRemoveMore={(site) => {
                  console.log(site);
                }}
                selectedKeys={selectedKeys}
                setSelectedKeys={setSelectedKeys}
                ref={exportSelectSiteRef}
                initialSelectAll={false} // 左右结构时不全选
              />
            </Transfer.Cell>
          }
        />
      ) : */}

      {/* } */}
    </TcsModal>
  );
};

export default ExportFlow;
