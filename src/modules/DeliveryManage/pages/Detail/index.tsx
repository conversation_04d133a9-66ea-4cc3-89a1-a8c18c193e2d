/*
 * @Author: superfeng
 * @Date: 2023-03-22 16:20:16
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-05-08 14:36:27
 * @Description: 请输入注释信息
 */
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Button, Card, Justify, TabPanel, Table, Tabs, Tag, message } from '@tencent/tea-component';
import { getAppColumns, getRegionColumns } from './config';
import ExEditor from '@/common/components/ExEditor';
import { getUrlParams } from '@/common/utils';
import { IDeliveryInfo, IDeliverySiteAntoolRel, getDeliveryInfo, DeliveryManage } from '@/common/api/deliveryManage';
import Loading from '@/common/components/Loading';
import { deliveryIsFinished, enableShowExportButton } from '../../utils';
import { DefectManagementRoutePath } from '@/common/routePath';
// import useLookup from '@/common/hookups/useLookup';
import AddRelApplication from './AddRelApplication';
import AddRelRegion from './AddRelRegion';
import { EXPORT_PACKAGE_STATUS, FROM_TYPE } from '@/common/config';
import AntoolSiteTable from './AntoolSiteTable';
import WorkflowListModal from '../List/components/WorkflowListModal';
import ExportFlow from './ExportFlow';
import { TcsActionType, TcsDescriptions, TcsLayout, TcsSpace, TcsTable } from '@tencent/tcs-component';
import RecallAntoolSiteModal from './RecallAntoolSiteModal';
import { AntoolStatus } from '../../constant';
import { DeliveryManageApi } from '@/common/api/deliveryManage.api';
import CompareModal from './CompareModal';

const Detail = () => {
  const [data, setData] = useState<IDeliveryInfo>();
  const [visibleAddRelApp, setVisibleAddRelApp] = useState(false);
  const [visibleAddRelRegion, setVisibleAddRelRegion] = useState(false);
  const workflowListRef = useRef<any>();
  const urlParams = getUrlParams();
  const [loading, setLoading] = useState(false);
  const { history } = TcsLayout.useHistory();
  // const { getLookupByCode, lookups } = useLookup(['DeliveryStatus']);
  const [update, setUpdate] = useState({});
  const actionRef = useRef<TcsActionType>();
  const [exportFlowVisible, setExportFLowVisible] = useState(false);
  const callbackRef = useRef<Function[]>([]);
  const [selectedRowKeys, setSelectRowKeys] = useState<string[]>([]);
  const [recallAntoolSiteVisible, setRecallAntoolSiteVisible] = useState<boolean>(false);
  const [deliverySiteRel, setDeliverySiteRel] = useState<IDeliverySiteAntoolRel[]>([]);
  const [batchDeliverySiteRel, setBatchDeliverySiteRel] = useState<IDeliverySiteAntoolRel[]>([]);
  const [deliveryManage, setDeliveryManage] = useState<boolean>(false);
  const [isDelete, setIsDelete] = useState(false);
  const [compareVision, setCompareVision] = useState(false);
  const selectedRecordRef = useRef<{ DeliveryUUID: string; SiteUUID: string }>({
    DeliveryUUID: '',
    SiteUUID: '',
  });

  useEffect(() => {
    if (urlParams.uuid) {
      setLoading(true);
      getDeliveryInfo({
        DeliveryID: urlParams.uuid,
      })
        .then((res) => {
          if (res.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            setData(res.DeliveryDetailInfo);
            callbackRef.current.forEach((fn) => {
              if (typeof fn === 'function') {
                fn(res.DeliveryDetailInfo);
              }
            });
            callbackRef.current = [];
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [urlParams.uuid, update]);

  function handleGoExportFlow(record: IDeliveryInfo) {
    if (!record.CurrentOperator) {
      return message.warning({
        content: '请编辑交付单添加交付责任人之后再行出包',
      });
    }
    if (!record.DeliverySiteRel?.length) {
      return message.warning({
        content: '需要关联出包局点后再行出包',
      });
    }
    setExportFLowVisible(true);
  }

  function handleWorkflowInstance(record: IDeliveryInfo) {
    DeliveryManageApi.GenerateWorkflowInstance({
      WorkflowUUID: '1c73117c5cc644a99986f1dc252f4c30', // 固定，启动的是 Patch 出制品包 流程
      WorkflowTagNum: 0,
      GlobalValues: {
        DeliveryUUID: record.UUID,
      },
    }).then((res) => {
      window.open(
        `/page/flow-design/flow-publish/exec?instance_id=${res?.WorkflowInstanceID}&workflow_id=${res?.WorkflowID}`,
        '_blank',
      );
      message.success({
        content: '启动成功',
      });
      setUpdate({});
    });
  }

  function handleEdit() {
    history.push(`${DefectManagementRoutePath.DELIVERY_EDIT_PAGE}?uuid=${data!.UUID}`);
  }

  // function handleViewCtrl() {
  //   window.open(
  //     `/page/product-market-jiguang/ops-sheet-manager/parent-sheet-detail?parentSheetId=${data!.OperationSheetUUID}`,
  //     '_blank',
  //   );
  // }

  function handleAddRelApp() {
    setVisibleAddRelApp(true);
  }

  function handleAddRelAppConfirm() {
    setVisibleAddRelApp(false);
    actionRef.current?.reload();
  }

  function handleAddRelAppCancel() {
    setVisibleAddRelApp(false);
  }

  function handleAddRelRegion() {
    if (!data?.CurrentOperator) {
      return message.warning({
        content: '当前交付单缺少交付责任人，请先完善交付责任人后再行修改关联局点',
      });
    }
    setVisibleAddRelRegion(true);
  }

  function handleAddRelRegionConfirm() {
    setVisibleAddRelRegion(false);
    actionRef.current?.reload();
  }

  function handleAddRelRegionCancel() {
    setVisibleAddRelRegion(false);
  }
  function handledDeleteRelRegion() {
    setIsDelete(true);
  }
  function handleConfirmDelete() {
    if (selectedRowKeys?.length) {
      DeliveryManage.DeleteDeliverySiteRel({
        DeliveryID: urlParams.uuid,
        SiteUUIDs: selectedRowKeys,
      })
        .then(() => {
          setSelectRowKeys([]);
          actionRef.current?.reload();
        })
        .finally(() => {
          setIsDelete(false);
        });
    } else {
      message.error({ content: '请选择局点' });
    }
  }
  function handleCancelDelete() {
    setSelectRowKeys([]);
    setIsDelete(false);
  }
  function handleRecallSuccess() {
    setRecallAntoolSiteVisible(false);
    setSelectRowKeys([]);
    actionRef.current?.reload();
  }

  // const status = useMemo(() => {
  //   if (!data?.Status) {
  //     return '-';
  //   }
  //   const lookup = getLookupByCode('DeliveryStatus', data.Status);
  //   if (lookup) {
  //     return <Tag style={{ color: lookup.Extra?.Color }}>{lookup.Name}</Tag>;
  //   }
  //   return data.Status || '-';
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [data?.Status, lookups.DeliveryStatus]);

  function handleViewExportFlowList(record: IDeliveryInfo) {
    workflowListRef.current.show(record);
  }

  // function handleRequest(params) {

  // }

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const enableExport = useMemo(() => enableShowExportButton(data), [data?.DeliverySiteRel, data?.Status]);

  // 是否已关闭或者失败的单子
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const isFinished = useMemo(() => deliveryIsFinished(data), [data?.Status]);

  const relatedOrderViewText = useMemo(() => {
    if (data?.FromType === FROM_TYPE.IssueSolution) {
      return data?.FromOriginType === 'story' ? '查看关联需求单' : '查看关联缺陷单';
    }
    return '查看关联Patch单';
  }, [data?.FromType, data?.FromOriginType]);

  function handleRelatedOrderView() {
    const fromType = data?.FromType;
    const formOriginType = data?.FromOriginType;
    const fromOriginKey = data?.FromOriginKey || data?.FromKey;

    if (data?.FromType === FROM_TYPE.IssueSolution) {
      const type = formOriginType === 'story' ? 'story' : 'defect';
      window.open(
        `/page/defect_manage/__develop_center_next/iteration_manage/${type}/detail?issue_id=${fromOriginKey}`,
        '_blank',
      );
    } else if (fromType === FROM_TYPE.Patch) {
      window.open(`/page/defect_manage/__flow-design/patch_manage/detail?patchId=${fromOriginKey}`, '_blank');
    }
  }
  function handleRecallAntool(record) {
    setRecallAntoolSiteVisible(true);
    setDeliverySiteRel(record?.DeliverySiteAntoolRel);
  }
  function handleBatchRecall(record) {
    setRecallAntoolSiteVisible(true);
    if (record) {
      setBatchDeliverySiteRel([record]);
    } else {
      const filterData = data?.DeliverySiteRel?.filter((item) => selectedRowKeys?.includes(item.SiteUUID));
      const antoolStatusValues = Object.values(AntoolStatus);
      const filterAntoolPhase: IDeliverySiteAntoolRel[] =
        filterData?.flatMap(
          (item) => item.DeliverySiteAntoolRel?.filter((antool) => !antoolStatusValues.includes(antool.Phase)) || [],
        ) || [];
      const filterAntoolStatus = filterAntoolPhase?.filter((item) => item.Status !== '已召回' && item.AntoolID);
      setBatchDeliverySiteRel(filterAntoolStatus);
    }
    setDeliverySiteRel([]);
  }
  useEffect(() => {
    const deliveryOperator = data?.CurrentOperator?.split(';')?.includes(window.jiguang_username);
    const jiguangRoleManager = ['jiguang2_manager', 'cmo'].includes((window as any)?.jiguang_currentRole);
    setDeliveryManage(deliveryOperator || jiguangRoleManager);
  }, [data]);

  function handleForceDawnPlan(record, status: boolean) {
    setLoading(true);
    DeliveryManageApi.UpdateDeliverySiteRel({
      DeliveryID: record.DeliveryID,
      SiteUUID: record.SiteUUID,
      Variable: JSON.stringify(
        Object.assign({}, record?.Variable ? JSON.parse(record.Variable) : {}, {
          ForceDawnPlan: status,
        }),
      ),
    }).finally(() => {
      setLoading(false);
      setUpdate({});
      actionRef.current?.reload();
    });
  }
  function handleDelete(record) {
    DeliveryManage.DeleteDeliverySiteRel({
      DeliveryID: urlParams.uuid,
      SiteUUIDs: [record?.SiteUUID],
    })
      .then(() => {
        actionRef.current?.reload();
      })
      .finally(() => {});
  }
  function handleCompare(record) {
    setCompareVision(true);
    selectedRecordRef.current = {
      DeliveryUUID: record.DeliveryID,
      SiteUUID: record.SiteUUID,
    };
  }
  function handleNeedTailor(record) {
    setLoading(true);
    DeliveryManageApi.UpdateDeliverySiteRel({
      ID: record?.ID,
      Variable: '{"NeedTailor":true}',
    })
      .then((res) => {
        if (res?.Error) {
          return message.error({
            content: res?.Error?.Message,
          });
        }
        message.success({ content: '裁剪成功' });
      })
      .finally(() => {
        setLoading(false);
        setUpdate({});
        actionRef.current?.reload();
      });
  }

  return (
    <TcsLayout title="交付单详情" customizeCard history={history}>
      <Loading loading={loading}>
        <TcsDescriptions
          layout="fixed"
          fixedLabelWidth={80}
          dataSource={data}
          column={2}
          collapsible
          collapseState={{
            persistenceKey: 'defect_manage/delivery_manage/detail/base_info/card',
          }}
          title="基本信息"
          extra={
            <>
              {!isFinished && (
                <Button type="link" onClick={handleEdit}>
                  编辑
                </Button>
              )}
              {(enableExport || isFinished) && (
                <Button type="link" onClick={() => handleGoExportFlow(data as IDeliveryInfo)}>
                  启动局点出包
                </Button>
              )}
              {data?.FromType === 'Patch' && (
                <Button type="link" onClick={() => handleWorkflowInstance(data as IDeliveryInfo)}>
                  启动制品包
                </Button>
              )}

              {/* {data?.OperationSheetUUID && (
                <Button type="link" onClick={handleViewCtrl}>
                  查看变更单
                </Button>
              )} */}
              {data?.FromOriginKey || data?.FromKey ? (
                <Button type="link" onClick={handleRelatedOrderView}>
                  {relatedOrderViewText}
                </Button>
              ) : undefined}
              {data?.WorkflowInstanceID?.length && (
                <Button type="link" onClick={() => handleViewExportFlowList(data)}>
                  查看出包记录
                </Button>
              )}
            </>
          }
        >
          <TcsDescriptions.Item label="交付单名称" dataIndex="Name" />
          <TcsDescriptions.Item label="解决方案版本" dataIndex="SolutionVersion" />
          <TcsDescriptions.Item
            label="架构"
            valueType="dictSelect"
            dataIndex="Arch"
            fieldProps={{
              dictType: 'PackageArch',
            }}
          />
          <TcsDescriptions.Item
            label="状态"
            dataIndex="Status"
            valueType="dictSelect"
            fieldProps={{
              dictType: 'DeliveryStatus',
              showType: 'tag',
            }}
          />
          <TcsDescriptions.Item
            label="交付责任人"
            renderText={(text) => (text ? text.split(';') : [])}
            valueType="staffSelect"
            dataIndex="CurrentOperator"
          />
          <TcsDescriptions.Item label="局点交付状态" dataIndex="DeliverySiteStatusCount" />
          <TcsDescriptions.Item label="交付单标签">
            {data?.Tags?.length ? (
              <Tag.Group>
                {data.Tags?.map((tag) => (
                  <Tag key={tag}>{tag}</Tag>
                ))}
              </Tag.Group>
            ) : (
              '-'
            )}
          </TcsDescriptions.Item>
          <TcsDescriptions.Item
            label="交付单来源"
            valueType="dictSelect"
            dataIndex={data?.FromOriginType ? 'FromOriginType' : 'FromType'}
            fieldProps={{
              dictType: 'DeliveryFromType',
            }}
          />
        </TcsDescriptions>

        <Card>
          <Card.Body>
            <Tabs
              destroyInactiveTabPanel={true}
              tabs={[
                {
                  id: 'description',
                  label: '交付单描述',
                },
                {
                  id: 'app_info',
                  label: '产品应用信息',
                },
                {
                  id: 'region_delivery',
                  label: '局点列表交付状态',
                },
              ]}
              defaultActiveId="region_delivery"
            >
              <TabPanel id="description">
                <ExEditor readonly placeholder="" value={data?.Description || ''} />
              </TabPanel>
              <TabPanel id="app_info">
                <Table.ActionPanel>
                  <Justify
                    left={
                      <>
                        {data?.Status &&
                          [
                            EXPORT_PACKAGE_STATUS.NEW,
                            EXPORT_PACKAGE_STATUS.NEED_PACK_PACKAGE,
                            EXPORT_PACKAGE_STATUS.PACKING,
                          ].includes(data.Status as any) && (
                            <Button type="primary" onClick={handleAddRelApp}>
                              修改关联应用
                            </Button>
                          )}
                      </>
                    }
                  />
                </Table.ActionPanel>
                <Table
                  columns={getAppColumns()}
                  records={data?.ApplicationInfos || []}
                  addons={[Table.addons.autotip({})]}
                />
                <AddRelApplication
                  onConfirm={handleAddRelAppConfirm}
                  onCancel={handleAddRelAppCancel}
                  visible={visibleAddRelApp}
                  deliveryInfo={data}
                />
              </TabPanel>
              <TabPanel id="region_delivery">
                <TcsTable
                  scroll={{
                    x: 1500,
                  }}
                  actionRef={actionRef}
                  headerTitle={
                    <TcsSpace>
                      {!isDelete && (
                        <Button type="primary" onClick={handleAddRelRegion}>
                          新增关联局点
                        </Button>
                      )}
                      {!isDelete && (
                        <Button type="primary" onClick={handledDeleteRelRegion}>
                          批量删除局点
                        </Button>
                      )}
                      {isDelete && (
                        <Button type="primary" onClick={handleConfirmDelete}>
                          确定
                        </Button>
                      )}
                      {isDelete && <Button onClick={handleCancelDelete}>取消</Button>}

                      {
                        // deliveryManage &&
                        !isDelete && (
                          <Button
                            type="primary"
                            disabled={selectedRowKeys?.length === 0}
                            tooltip={selectedRowKeys?.length === 0 ? '请选择局点' : ''}
                            onClick={() => handleBatchRecall(undefined)}
                          >
                            批量召回
                          </Button>
                        )
                      }
                    </TcsSpace>
                  }
                  columns={getRegionColumns({
                    handleViewExportFlowList,
                    handleRecallAntool,
                    deliveryManage,
                    handleForceDawnPlan,
                    handleDelete,
                    handleCompare,
                    fromType: data?.FromType,
                    handleNeedTailor,
                  })}
                  columnsState={{
                    persistenceKey: 'defect_manage/delivery_manage/edit/region_delivery',
                  }}
                  loading={false}
                  expandable={{
                    rowExpandable: (record) => !!record.DeliverySiteAntoolRel?.length,
                    expandedRowRender(record) {
                      return (
                        <AntoolSiteTable
                          record={record}
                          handleRecallSite={handleBatchRecall}
                          deliveryManage={deliveryManage}
                        />
                      );
                    },
                  }}
                  request={() => {
                    if (!loading) {
                      setUpdate({});
                    }
                    return new Promise((resolve) => {
                      callbackRef.current.push((data) => {
                        resolve({
                          data: data?.DeliverySiteRel || [],
                          success: true,
                        });
                      });
                    });
                  }}
                  rowKey="SiteUUID"
                  rowSelection={
                    // deliveryManage
                    //   ?

                    {
                      type: 'checkbox',
                      rowSelectable: (record: any) => {
                        if (isDelete) {
                          return (
                            record?.Status === EXPORT_PACKAGE_STATUS.NEED_PACK_PACKAGE ||
                            record?.Status === EXPORT_PACKAGE_STATUS.CHECK_NEED_PACK_PACKAGE_FAILED ||
                            record?.Status === EXPORT_PACKAGE_STATUS.NEW
                          );
                        }
                        if (!record?.DeliverySiteAntoolRel) {
                          return false;
                        }
                        const antoolRel = record?.DeliverySiteAntoolRel;
                        const findAntool = antoolRel?.find(
                          (item) =>
                            !Object.values(AntoolStatus)?.includes(item.Phase) &&
                            item.Status !== '已召回' &&
                            item.AntoolID,
                        );
                        if (findAntool) {
                          return true;
                        }
                        return false;
                      },
                      selectedRowKeys,
                      onChange: (selectedRowKeys: string[]) => {
                        setSelectRowKeys(selectedRowKeys);
                      },
                    }
                    // : false
                  }
                  onRow={(record: any) => {
                    const message = record?.CheckMessage?.trim();
                    if (message) {
                      return {
                        tooltip: message,
                      };
                    }
                    return {
                      tooltip:
                        record?.Status === 'Closed' || record?.Status === 'Failed'
                          ? '已关闭或失败状态可启动出包流程补充出包'
                          : '',
                    };
                  }}
                />
                <AddRelRegion
                  visible={visibleAddRelRegion}
                  deliveryInfo={data}
                  onConfirm={handleAddRelRegionConfirm}
                  onCancel={handleAddRelRegionCancel}
                />
                {recallAntoolSiteVisible && (
                  <RecallAntoolSiteModal
                    onClose={() => setRecallAntoolSiteVisible(false)}
                    deliverySiteRel={deliverySiteRel}
                    deliveryUUID={data?.UUID}
                    batchDeliverySiteRel={batchDeliverySiteRel}
                    handleRecallSuccess={handleRecallSuccess}
                  />
                )}
              </TabPanel>
            </Tabs>
          </Card.Body>
        </Card>
        <WorkflowListModal ref={workflowListRef} />
      </Loading>
      {exportFlowVisible && (
        <ExportFlow
          setVisible={(value) => {
            if (!value) {
              actionRef.current?.reload();
            }
            setExportFLowVisible(value);
          }}
          deliveryInfo={data}
        />
      )}
      {compareVision && (
        <CompareModal
          onUpdate={() => {
            setCompareVision(false);
          }}
          onClose={() => {
            setCompareVision(false);
          }}
          detail={selectedRecordRef.current}
        />
      )}
    </TcsLayout>
  );
};

export default Detail;
