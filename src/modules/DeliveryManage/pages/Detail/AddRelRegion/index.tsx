/*
 * @Author: superfeng
 * @Date: 2023-04-10 15:28:44
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-11-12 16:00:40
 * @Description: 请输入注释信息
 */
import { message } from '@tencent/tea-component';
import React, { useState } from 'react';
import { IDeliveryInfo, DeliveryManage } from '@/common/api/deliveryManage';

import { TcsModal } from '@tencent/tcs-component';
import AddRelRegionOnlyContent from './AddRelRegionOnlyContent';
import { IListProjectClientSiteApi } from '@/common/api/iterationManage.api';

export interface IProps {
  visible: boolean;
  deliveryInfo?: IDeliveryInfo;
  onConfirm: () => void;
  onCancel: () => void;
}

const AddRelRegion: React.FC<IProps> = ({ visible, deliveryInfo, onConfirm, onCancel }) => {
  const [loading, setLoading] = useState(false);

  const [sites, setSites] = useState<IListProjectClientSiteApi.ISiteBaseInfo[]>([]);
  function handleConfirm() {
    const deliverySiteUUID = deliveryInfo?.DeliverySiteRel?.map((item) => {
      return item?.SiteUUID || [];
    });
    const filteredSites = sites.filter((site) => !deliverySiteUUID?.includes(site.SiteUUID));
    if (filteredSites?.length) {
      setLoading(true);
      DeliveryManage.AddDeliverySiteRel({
        DeliveryID: deliveryInfo?.UUID || '',
        SiteInfos: filteredSites.map((item) => ({
          SiteUUID: item.SiteUUID,
        })),
      })
        .then((res) => {
          if (res.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            message.success({
              content: '更新成功',
            });
            onConfirm();
          }
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      message.error({ content: '请选择局点' });
    }
  }

  function handleCancel() {
    onCancel();
  }

  function handleChangeSites(ids, sites: IListProjectClientSiteApi.ISiteBaseInfo[]) {
    setSites(sites);
  }

  return (
    <TcsModal
      title="修改关联局点"
      width={950}
      visible={visible}
      onCancel={handleCancel}
      onOk={handleConfirm}
      destroyOnClose
      confirmLoading={loading}
    >
      {visible && (
        <AddRelRegionOnlyContent deliveryInfo={deliveryInfo} onChangeCheckedClientSites={handleChangeSites} />
      )}
    </TcsModal>
  );
};

export default AddRelRegion;
