import React, { useEffect, useMemo, useState } from 'react';
import {
  Alert,
  Bubble,
  Checkbox,
  DropdownBox,
  List,
  Popover,
  SearchBox,
  Switch,
  Text,
  message,
  Button,
} from '@tencent/tea-component';
import { DefectManagementRoutePath } from '@/common/routePath';
import { IDeliveryInfo, getDeliveryReleatedSites } from '@/common/api/deliveryManage';
import styles from './AddRelRegion.module.less';
import { EXPORT_PACKAGE_STATUS } from '@/common/config';
import { TcsSpin } from '@tencent/tcs-component';
import { IListProjectClientSiteApi, IterationManage } from '@/common/api/iterationManage.api';

export interface IProps {
  deliveryInfo?: IDeliveryInfo;
  // 是否禁用到之前已经加到交付单局点列表的所有局点，用于在启动出包时选择出包局点
  disabledAllBeforeSelected?: boolean;

  onChangeCheckedClientSites: (siteUUIDs: string[], sites: IListProjectClientSiteApi.ISiteBaseInfo[]) => void;

  maxHeight?: number;
}

const AddRelRegionContent: React.FC<IProps> = ({
  deliveryInfo,
  onChangeCheckedClientSites,
  disabledAllBeforeSelected = false,
  maxHeight = 550,
}) => {
  const [loading, setLoading] = useState(false);
  const [clientList, setClientList] = useState<IListProjectClientSiteApi.IClientSites[]>([]);
  const [checkedClientSites, setCheckedClientSites] = useState<Record<string, string[]>>({});
  const [releatedSites, setReleatedSites] = useState<any[]>([]);
  const [searchValue, setSearchValue] = useState<{ clientName?: string; siteName?: string }>({});
  const [isFullSites, setIsFullSites] = useState(false);
  useEffect(() => {
    if (deliveryInfo?.SolutionVersionID) {
      setLoading(true);
      Promise.all([
        IterationManage.ListProjectClientSite({
          SolutionVersionID: deliveryInfo.SolutionVersionID,
          Arch: deliveryInfo.Arch,
          Applications: deliveryInfo.ApplicationInfos?.map((item) => item.ApplicationName),
          IsFullSites: isFullSites,
          PatchID: deliveryInfo.FromType === 'Patch' ? deliveryInfo.FromKey : '',
          ShowPatchSites: deliveryInfo.FromType !== 'Patch',
        }).then((res) => res.ClientInfos),
        getDeliveryReleatedSites({
          DeliveryID: deliveryInfo?.UUID,
        }).then((res) => {
          if (res.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            return res?.ReleatedSites;
          }
        }),
      ])
        .then((res) => {
          const clientInfos = res[0] || [];
          const releatedSites = res[1] || [];
          const releatedSitesIds: string[] = [];
          releatedSites.forEach((item) => releatedSitesIds.push(item?.SiteUUID));
          clientInfos.forEach(
            (item) =>
              // eslint-disable-next-line no-param-reassign
              (item.ProjectSite = item.ProjectSite?.filter((item) => !releatedSitesIds.includes(item.SiteUUID))),
          );
          const data = clientInfos.filter((item) => item.ProjectSite?.length);

          const siteClientMap = data.reduce((mapping: Record<string, string> = {}, item) => {
            if (item.ProjectSite) {
              item.ProjectSite.forEach((site) => {
                // eslint-disable-next-line no-param-reassign
                mapping[site.SiteUUID] = item.ClientUUID;
              });
            }
            return mapping;
          }, {});
          setClientList(data);
          setReleatedSites(releatedSites);

          if (deliveryInfo.DeliverySiteRel?.length) {
            const checkedClientSites: Record<string, string[]> = {};
            deliveryInfo.DeliverySiteRel.forEach((item) => {
              const clientUUID = siteClientMap[item.SiteUUID];
              if (clientUUID) {
                if (!checkedClientSites[clientUUID]) {
                  checkedClientSites[clientUUID] = [];
                }
                checkedClientSites[clientUUID].push(item.SiteUUID);
              }
            });
            setCheckedClientSites(checkedClientSites);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    deliveryInfo?.SolutionVersionID,
    deliveryInfo?.Arch,
    deliveryInfo?.UUID,
    deliveryInfo?.DeliverySiteRel,
    isFullSites,
  ]);

  useEffect(() => {
    const allSiteUUIDs = Object.values(checkedClientSites).flat();
    const allSite: any[] = [];
    clientList.forEach((client) => {
      if (client.ProjectSite) {
        client.ProjectSite.forEach((item) => {
          if (allSiteUUIDs.includes(item.SiteUUID)) {
            allSite.push({
              ...item,
              ClientName: client.ClientName,
            });
          }
        });
      }
    });
    onChangeCheckedClientSites(allSiteUUIDs, allSite);
    // onChangeOnlyCheckedClientSites();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [checkedClientSites, clientList]);

  function handleChangeSiteStatus(
    client: IListProjectClientSiteApi.IClientSites,
    site: IListProjectClientSiteApi.ISiteBaseInfo,
    checked: boolean,
  ) {
    setCheckedClientSites((sites) => {
      let arr = sites[client.ClientUUID];
      if (arr) {
        if (checked) {
          arr.push(site.SiteUUID);
        } else {
          arr = arr.filter((item) => item !== site.SiteUUID);
        }
        return {
          ...sites,
          [client.ClientUUID]: arr,
        };
      }
      if (checked) {
        return {
          ...sites,
          [client.ClientUUID]: [site.SiteUUID],
        };
      }
      return sites;
    });
  }
  function handleSelectAll() {
    const newCheckedClientSites = filterClientList.reduce((acc, client) => {
      acc[client.ClientUUID] = client.ProjectSite.map((site) => site.SiteUUID);
      return acc;
    }, {});
    setCheckedClientSites(newCheckedClientSites);
  }

  function handleInverseSelect() {
    const newCheckedClientSites = { ...checkedClientSites };
    filterClientList.forEach((client) => {
      const currentChecked = newCheckedClientSites[client.ClientUUID] || [];
      const newChecked = client.ProjectSite.map((site) => site.SiteUUID)
        .filter((siteUUID) => !currentChecked.includes(siteUUID))
        .concat(
          currentChecked.filter((siteUUID) => !client.ProjectSite.map((site) => site.SiteUUID).includes(siteUUID)),
        );
      newCheckedClientSites[client.ClientUUID] = newChecked;
    });
    setCheckedClientSites(newCheckedClientSites);
  }

  function handleChangeGroup(client: IListProjectClientSiteApi.IClientSites, oldChecked: boolean) {
    // 如果之前是全选，则改为全不选
    if (oldChecked) {
      setCheckedClientSites((sites) => {
        const selectedSites = sites[client.ClientUUID] || [];
        const unSelectedSites = client?.ProjectSite?.map((item) => item.SiteUUID) || [];
        return {
          ...sites,
          [client.ClientUUID]: selectedSites.filter((item) => !unSelectedSites.includes(item)),
        };
      });
    } else {
      // 如果之前是半选或者未选，则改为全选
      setCheckedClientSites((sites) => {
        const selectedSites = sites[client.ClientUUID] || [];
        const list = Array.from(
          new Set([...(client.ProjectSite?.map((item) => item.SiteUUID) || []), ...selectedSites]),
        );
        return {
          ...sites,
          [client.ClientUUID]: list,
        };
      });
    }
  }

  const siteStatus = useMemo(
    () =>
      deliveryInfo?.DeliverySiteRel?.reduce((res, item) => {
        res[item.SiteUUID] = item.Status;
        return res;
      }, {}) || {},
    [deliveryInfo],
  );

  const isDisableSite = (site: IListProjectClientSiteApi.ISiteBaseInfo) => {
    const status = siteStatus[site.SiteUUID];
    if (!status) {
      return false;
    }
    if (disabledAllBeforeSelected) {
      const siteIds = deliveryInfo?.DeliverySiteRel.map((item) => item.SiteUUID);
      if (siteIds?.includes(site.SiteUUID)) {
        return true;
      }
    }
    return status !== EXPORT_PACKAGE_STATUS.NEED_PACK_PACKAGE && status !== EXPORT_PACKAGE_STATUS.NEW;
  };

  const isDisableClient = (client: IListProjectClientSiteApi.IClientSites) => {
    const result = client.ProjectSite?.filter((item) => !isDisableSite(item)) || [];
    return result.length === 0;
  };

  function handleSearch(value?: string) {
    setSearchValue({
      clientName: value,
      siteName: value,
    });
  }

  const filterClientList = useMemo(() => {
    if (Object.keys(searchValue).length) {
      const params: { clientName?: string; siteName?: string } = searchValue;
      return clientList.filter((item) => {
        if (item.ClientName.includes(params.clientName!)) {
          return true;
        }
        const includeSite = item.ProjectSite?.filter?.((site) => site.SiteName.includes(params.siteName!)) || [];
        return includeSite.length > 0;
      });
    }
    return clientList;
  }, [searchValue, clientList]);

  return (
    <TcsSpin spinning={loading}>
      <Alert
        type="warning"
        extra={
          releatedSites.length > 0 && (
            <Popover
              trigger={'click'}
              placement="bottom"
              overlay={
                <DropdownBox style={{ maxHeight: 200, overflowY: 'auto' }}>
                  <List type="option">
                    {(releatedSites || []).map((item) => (
                      <List.Item
                        key={item?.SiteUUID}
                        onClick={() => {
                          window.open(
                            `${DefectManagementRoutePath.DELIVERY_DETAIL_PAGE}?uuid=${item?.DeliveryID}`,
                            '_blank',
                          );
                        }}
                      >
                        {item?.SiteName}
                      </List.Item>
                    ))}
                  </List>
                </DropdownBox>
              }
            >
              <a target="_blank" style={{ textDecoration: 'underline', color: '#006eff' }}>
                查看关联交付单已出包局点
              </a>
            </Popover>
          )
        }
      >
        <div>可用局点状态：此处仅支持处于交付中和售后的局点</div>
        <div>
          迁移局点说明：当前极光Next的局点分为两类，一类是Next局点，另一类是从2.0迁移至Next的局点。对于后者，我们会在局点名称后添加【迁移】标识以示区分。
        </div>
        <div>
          缺陷修复方式说明：当前极光Next的缺陷修复方式分为两类，分别是bugfix和patch,
          我们会在局点名称后面添加【bugfix】或 【patch】来以示区分。
        </div>
        <div />
      </Alert>

      <div style={{ marginBottom: 5, display: 'flex', justifyContent: 'space-between' }}>
        <Bubble title="打开表示查看全量可交付局点，关闭表示进行精准过滤，只查询出过指定应用架构组合的可交付局点">
          <Switch value={isFullSites} onChange={(value) => setIsFullSites(value)}>
            查看全部局点
          </Switch>
          <div>
            {isFullSites && <Text theme="danger">请确认好选择的局点的出包架构与局点现场的架构信息是否匹配</Text>}
          </div>
        </Bubble>
        <SearchBox size="l" onSearch={handleSearch} placeholder="可输入客户名称/局点名称进行搜索" />
      </div>
      <div style={{ maxHeight, overflow: 'auto' }}>
        <>
          <Button onClick={handleSelectAll} type="weak" style={{ marginRight: 5, marginBottom: 5 }}>
            全选
          </Button>
          <Button onClick={handleInverseSelect} type="weak" style={{ marginRight: 5, marginBottom: 5 }}>
            反选
          </Button>
        </>
        {filterClientList.map((item) => {
          let value = false;
          let indeterminate = false;
          const checkedList = checkedClientSites[item.ClientUUID] || [];
          if (checkedList.length === item.ProjectSite?.length) {
            value = true;
          } else if (checkedList.length > 0) {
            indeterminate = true;
          }

          return (
            <div key={item.ClientUUID} className={styles.add_rel_region__group}>
              <div className={styles.add_rel_region__group_title}>
                <Checkbox
                  indeterminate={indeterminate}
                  value={value}
                  disabled={isDisableClient(item)}
                  onChange={() => {
                    handleChangeGroup(item, value);
                  }}
                >
                  {item.ClientName}
                </Checkbox>
              </div>
              <Checkbox.Group value={checkedList}>
                {item.ProjectSite?.map((site) => (
                  <Checkbox
                    name={site.SiteUUID}
                    key={site.SiteUUID}
                    disabled={isDisableSite(site)}
                    onChange={(checked) => handleChangeSiteStatus(item, site, checked)}
                  >
                    {site.SiteName}
                    <Text style={{ fontWeight: 600 }}> {site.SiteType === 'gray' ? '【迁移】' : ''}</Text>
                    <Text style={{ fontWeight: 600 }}>【{site?.BugfixType}】</Text>
                  </Checkbox>
                ))}
              </Checkbox.Group>
            </div>
          );
        })}
      </div>
    </TcsSpin>
  );
};

export default AddRelRegionContent;
