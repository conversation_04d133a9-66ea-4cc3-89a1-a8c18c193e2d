/*
 * @Author: superfeng
 * @Date: 2023-03-22 16:36:35
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-03-19 16:55:15
 * @Description: 请输入注释信息
 */
import { TcsButton, TcsColumns, TcsPopConfirm, TcsSpace } from '@tencent/tcs-component';
import React from 'react';
import { AntoolStatus } from '../../constant';
import { EXPORT_PACKAGE_STATUS } from '@/common/config';
export const getAppColumns = () => [
  {
    header: '产品版本',
    key: 'ProductVersion',
  },
  {
    header: '应用名称',
    key: 'ApplicationName',
  },
  {
    header: '应用版本',
    key: 'ApplicationVersion',
  },
];

export const getRegionColumns = ({
  handleViewExportFlowList,
  handleRecallAntool,
  // deliveryManage,
  handleForceDawnPlan,
  handleDelete,
  handleCompare,
  fromType,
  handleNeedTailor,
}: {
  handleViewExportFlowList: (record) => void;
  handleRecallAntool: (record) => void;
  handleForceDawnPlan: (record, status: boolean) => void;
  deliveryManage?: boolean;
  handleDelete: (record) => void;
  handleCompare: (record) => void;
  fromType: string;
  handleNeedTailor: (record) => void;
}) =>
  [
    {
      title: '客户名称',
      dataIndex: 'ClientName',
      disable: true,
      width: '15%',
    },
    {
      title: '局点名称',
      dataIndex: 'SiteName',
      width: '15%',
    },
    {
      title: '局点架构',
      dataIndex: ['SiteVariable', 'SiteArch'],
      width: '15%',
    },
    {
      title: '局点类型',
      dataIndex: 'SiteType',
      width: '10%',
    },
    {
      title: '出包状态',
      dataIndex: 'Status',
      valueType: 'dictSelect',
      width: '10%',
      fieldProps: {
        dictType: 'DeliveryStatus',
        showType: 'tag',
      },
    },
    {
      title: '最近出包时间',
      dataIndex: 'LatestPackPackageTime',
      width: '10%',
      valueType: 'dateTime',
    },
    {
      title: '缺陷修复方式',
      width: '10%',
      dataIndex: ['SiteVariable', 'BugfixType'],
    },
    {
      title: '变更工具',
      width: '15%',
      dataIndex: ['SiteVariable', 'OperationTool'],
    },
    {
      title: '推包类型',
      width: '15%',
      dataIndex: ['SiteVariable', 'PushPackageType'],
    },
    {
      title: '物料链接',
      width: '15%',
      dataIndex: 'MaterialURL',
      linkable: true,
      copyable: true,
      linkProps: {
        linkText() {
          return '点击下载';
        },
        linkUrl: (text) => text,
      },
    },
    {
      title: '操作',
      width: '20%',
      dataIndex: 'operation',
      disable: true,
      valueType: 'option',
      render: (text, record) => {
        const optionButton: any = [];
        const isDelete =
          record?.Status !== EXPORT_PACKAGE_STATUS.NEED_PACK_PACKAGE &&
          record?.Status !== EXPORT_PACKAGE_STATUS.CHECK_NEED_PACK_PACKAGE_FAILED &&
          record?.Status !== EXPORT_PACKAGE_STATUS.NEW;
        optionButton.push(
          <TcsPopConfirm
            key={'delete'}
            title="确定要删除？"
            onConfirm={() => handleDelete(record)}
            // onCancel={cancel}
            okText="确定"
            cancelText="取消"
          >
            <TcsButton type="link" disabled={isDelete} tooltip={isDelete ? '当前状态不可删除' : ''}>
              删除
            </TcsButton>
          </TcsPopConfirm>,
        );

        if (fromType === 'Patch' && record?.SiteType === 'next') {
          optionButton.push(
            <TcsButton key={'compare'} type="link" onClick={() => handleCompare(record)}>
              规划对比
            </TcsButton>,
          );
        }

        if (record?.WorkflowInstanceID) {
          optionButton.push(
            <TcsButton key={'view'} type="link" onClick={() => handleViewExportFlowList(record)}>
              查看出包记录
            </TcsButton>,
          );
        }
        if (record?.DeliverySiteAntoolRel?.length > 0) {
          const deliverySiteAntool = record?.DeliverySiteAntoolRel;
          const filterStatus = deliverySiteAntool?.filter(
            (item) => !Object.values(AntoolStatus)?.includes(item.Phase) && item.Status !== '已召回' && item.AntoolID,
          );
          if (
            filterStatus?.length !== 0
            //  && deliveryManage
          ) {
            optionButton.push(
              <TcsButton key={'recall'} type="link" onClick={() => handleRecallAntool(record)}>
                召回
              </TcsButton>,
            );
          }
        }
        if (record?.SiteType === 'gray' && record?.Status === 'Failed') {
          const variable = JSON.parse(record?.Variable || '{}');
          if (variable?.ForceDawnPlan) {
            optionButton.push(
              <TcsButton key={'cancelDawnPlan'} type="link" onClick={() => handleForceDawnPlan(record, false)}>
                取消强制出规划包
              </TcsButton>,
            );
          } else {
            optionButton.push(
              <TcsButton key={'dawnPlan'} type="link" onClick={() => handleForceDawnPlan(record, true)}>
                强制出规划包
              </TcsButton>,
            );
          }
        }
        if (record?.Status === EXPORT_PACKAGE_STATUS.FAILED) {
          optionButton.push(
            <TcsPopConfirm
              key={'needTailor'}
              title="确定要裁剪应用？"
              onConfirm={() => handleNeedTailor(record)}
              okText="确定"
              cancelText="取消"
            >
              <TcsButton type="link">裁剪应用</TcsButton>
            </TcsPopConfirm>,
          );
        }
        return optionButton?.length === 0 ? '-' : <TcsSpace wrap={true}>{optionButton}</TcsSpace>;
      },
    },
  ] as TcsColumns[];
