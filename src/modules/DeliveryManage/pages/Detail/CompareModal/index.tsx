/*
 * @Author: lucyfang
 * @Date: 2024-12-11 15:32:11
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-12-25 16:36:14
 * @Description: 请输入注释信息
 */

import React, { useState } from 'react';
import { TcsButton, TcsModal, TcsTable, TcsColumns, TcsLayout } from '@tencent/tcs-component';
import { Alert, message } from '@tencent/tea-component';
import { PatchPublishMarketApi } from '@/common/api/patchPublishMarket';
import { AlertStatus } from '../../../constant';

interface IProps {
  onUpdate: () => void;
  onClose: () => void;
  detail: {
    SiteUUID: string;
    DeliveryUUID: string;
  };
}

const CompareModal: React.FC<IProps> = ({ onUpdate, onClose, detail }) => {
  const [loading, setLoading] = useState(false);
  const [isAlert, setIsAlert] = useState(false);
  const [alertMsg, setAlertMsg] = useState<{ level: string; message: string }>({ level: '', message: '' });
  const { history } = TcsLayout.useHistory();
  const getColumns = ({}) =>
    [
      {
        dataIndex: ['NewProductVersion', 'ProductCode'],
        title: '产品',
        width: '10%',
      },
      {
        dataIndex: ['NewProductVersion', 'ProductVersionName'],
        title: '产品版本',
        width: '10%',
      },
      {
        title: '局点数据快照',
        dataIndex: ['PlanProductVersion', 'VersionInfo', 'TagNum'],
        width: '10%',
      },

      {
        title: 'Patch数据快照',
        dataIndex: ['NewProductVersion', 'ProductDataTagNum'],
        width: '10%',
        render: (text, record) => {
          if (record?.NewProductVersion?.ProductDataTagNum === record?.PlanProductVersion?.VersionInfo?.TagNum) {
            return record.NewProductVersion.ProductDataTagNum;
          }
          return (
            <span title={record.NewProductVersion.ProductDataTagNum} style={{ color: 'red' }}>
              {record.NewProductVersion.ProductDataTagNum}
            </span>
          );
        },
      },
      {
        dataIndex: 'operation',
        title: '操作',
        width: '10%',
        render: (text, record) => {
          const disable =
            record?.NewProductVersion?.ProductDataTagNum && record?.PlanProductVersion?.VersionInfo?.TagNum;
          return (
            <TcsButton
              type="link"
              disabled={!disable}
              tooltip={!disable && '当前不支持对比'}
              onClick={() => {
                const params = {
                  productVersionUUID: record?.PlanProductVersion?.ProductVersionUUID,
                  baseBranchName: record?.PlanProductVersion?.ProductVersionDataBranchName,
                  baseBranch: record?.PlanProductVersion?.ProductVersionDataBranchCode,
                  baseTag: record?.PlanProductVersion?.VersionInfo?.Tag,
                  baseTagName: record?.PlanProductVersion?.VersionInfo?.TagNum,

                  diffBranch: record?.NewProductVersion?.ProductDataBranch,
                  diffBranchName: record?.NewProductVersion?.ProductDataBranch,
                  diffTag: record?.NewProductVersion?.ProductVersionTag,
                  diffTagName: record?.NewProductVersion?.ProductDataTagNum,
                };
                const queryString = new URLSearchParams(params).toString();

                const url = `/page/product_center/workspace/product/version_diff?${queryString}`;
                history.push(url, {
                  breadcrumbTitle: `${record?.PlanProductVersion?.Name}（${record?.PlanProductVersion?.Code}）${record?.PlanProductVersion?.ProductVersionName}-快照差异对比
                  `,
                });
              }}
            >
              对比快照
            </TcsButton>
          );
        },
      },
    ] as TcsColumns[];
  const handleUpdate = () => {
    setLoading(true);
    PatchPublishMarketApi.GenerateWorkflowInstance({
      WorkflowUUID: 'e5fe09bde9404c0aad69c867840ebd72', // 固定，启动的是 交付单自动更新规划流程 流程
      WorkflowTagNum: 0,
      GlobalValues: {
        ...detail,
      },
    })
      .then((res) => {
        window.open(
          `/page/flow-design/flow-publish/exec?instance_id=${res?.WorkflowInstanceID}&workflow_id=${res?.WorkflowID}`,
          '_blank',
        );
        message.success({ content: '启动成功' });
        onUpdate();
      })
      .finally(() => {
        setLoading(false);
      });
  };
  const handleCancelUpdate = () => {
    onClose();
  };
  const handleRequest = async () => {
    if (detail) {
      const res = await PatchPublishMarketApi.DeliveryDiffPlanInfo({
        ...detail,
      });

      const { ProductList = [], MessageInfo } = res;
      if (MessageInfo) {
        setIsAlert(true);
        setAlertMsg({ level: MessageInfo?.Level, message: MessageInfo?.Message });
      }
      return {
        success: true,
        data: ProductList,
        total: ProductList.length,
      };
    }
  };

  return (
    <TcsModal
      title="对比规划是否有变动"
      visible
      onCancel={onClose}
      width={900}
      footer={
        <>
          <TcsButton onClick={handleUpdate} type="primary" loading={loading}>
            启动自动更新规划流程
          </TcsButton>
          <TcsButton onClick={handleCancelUpdate}>无需更新规划</TcsButton>
        </>
      }
    >
      {isAlert && <Alert type={AlertStatus[alertMsg?.level]}>{alertMsg?.message}</Alert>}
      <TcsTable scrollInTable columns={getColumns({})} request={handleRequest} pagination={{ defaultPageSize: 10 }} />
    </TcsModal>
  );
};
export default CompareModal;
