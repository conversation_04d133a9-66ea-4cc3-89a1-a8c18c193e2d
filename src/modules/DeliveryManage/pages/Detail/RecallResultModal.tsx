/*
 * @Author: lucyfang
 * @Date: 2025-05-08 11:28:34
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-05-08 17:20:50
 * @Description: 请输入注释信息
 */
import React, { useRef, useEffect } from 'react';
import { TcsButton, TcsModal, TcsTable, TcsActionType } from '@tencent/tcs-component';

export interface IProps {
  onClose: () => void;
  recallResults: any;
}
const getColumns = () => {
  return [
    {
      dataIndex: 'AntoolChangeID',
      title: '变更单ID',
    },
    {
      dataIndex: 'Successful',
      title: '是否成功',
      render: (_, record) => {
        return record?.Successful ? '是' : '否';
      },
    },
    {
      dataIndex: 'Message',
      title: '信息',
    },
  ];
};
const RecallResultModal: React.FC<IProps> = ({ onClose, recallResults }) => {
  const actionRef = useRef<TcsActionType>();
  useEffect(() => {
    actionRef.current?.reload();
  }, [recallResults?.length]);
  return (
    <TcsModal width={600} visible onCancel={onClose} footer={<TcsButton onClick={onClose}>关闭</TcsButton>}>
      <TcsTable
        actionRef={actionRef}
        rowKey="AntoolChangeID"
        columns={getColumns()}
        dataSource={recallResults}
        pagination={{}}
        options={false}
      />
    </TcsModal>
  );
};

export default RecallResultModal;
