/*
 * @Author: superfeng
 * @Date: 2023-03-22 15:20:03
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-03-05 15:33:12
 * @Description: 请输入注释信息
 */
import React from 'react';
import { FROM_TYPE } from '@/common/config';
import { Tag } from '@tencent/tea-component';
import { deliveryIsFinished, enableShowExportButton } from '../../utils';
import { DefectManagementRoutePath } from '@/common/routePath';
import { TcsButtonGroup, TcsColumns } from '@tencent/tcs-component';
import { SolutionVersionSelect } from '@/common/components';

export const getTcsColumns = ({
  onEdit,
  onGoExportFlow,
  onViewExportFlowList,
  // onCopy,
  siteList,
}: {
  onEdit: (record) => void;
  onGoExportFlow: (record) => void;
  onViewExportFlowList: (record) => void;
  // onCopy: (record) => void;
  siteList: any[];
}) =>
  [
    {
      title: '交付单名称',
      dataIndex: 'Name',
      width: '17%',
      fixed: 'left',
      linkable: true,
      disable: true,
      linkProps: {
        linkUrl: (text, record) =>
          `${DefectManagementRoutePath.DELIVERY_DETAIL_PAGE}?uuid=${record.UUID}&id=${record.ID}`,
      },
    },
    {
      dataIndex: 'tapdUrls',
      title: 'TAPD链接',
      width: 110,
      formItemProps: {
        tooltip: '不同TAPD链接用;分割',
      },
      hideInTable: true,
    },
    {
      title: '状态',
      dataIndex: 'Status',
      width: 100,
      valueType: 'dictSelect',
      disable: true,
      fieldProps: {
        dictType: 'DeliveryStatus',
        showType: 'tag',
        mode: 'multiple',
        showSelectAll: true,
      },
    },
    {
      title: '局点交付状态',
      dataIndex: 'DeliverySiteStatusCount',
      width: '12%',
      search: false,
    },
    {
      title: '解决方案',
      dataIndex: 'Solution',
      width: 220,
      search: false,
      render(text, record) {
        if (record) {
          return `${record?.Solution?.Name}(${record?.Solution?.Code})`;
        }
        return '-';
      },
    },
    {
      title: '解决方案版本',
      dataIndex: 'SolutionVersion',
      width: 150,
      renderFormItem: () => <SolutionVersionSelect />,
    },
    {
      title: '架构',
      dataIndex: 'Arch',
      width: 140,
      valueType: 'dictSelect',
      fieldProps: {
        dictType: 'PackageArch',
      },
    },
    {
      title: '产品版本',
      dataIndex: 'ProductVersion',
      width: '6%',
      search: false,
    },
    {
      title: '局点',
      dataIndex: 'SiteUUID',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        options: siteList || [],
        showSearch: true,
      },
    },
    {
      title: '关联应用个数',
      dataIndex: 'ApplicationInfos',
      width: 100,
      search: false,
      render(text, record) {
        const { ApplicationInfos } = record;
        return ApplicationInfos?.length || 0;
      },
    },
    {
      title: '关联局点个数',
      dataIndex: 'DeliverySiteRel',
      width: 100,
      search: false,
      render(text, record) {
        const { DeliverySiteRel } = record;
        return DeliverySiteRel?.length || 0;
      },
    },
    {
      title: '出包标签',
      dataIndex: 'Tags',
      width: '12%',
      search: false,
      render(text, record) {
        const { Tags } = record;
        return Tags?.length ? (
          <Tag.Group>
            {Tags.map((tag) => (
              <Tag key={tag}>{tag}</Tag>
            ))}
          </Tag.Group>
        ) : (
          '-'
        );
      },
    },
    {
      dataIndex: 'Creator',
      title: '创建人',
      width: 140,
      search: false,
      valueType: 'staffSelect',
    },
    {
      dataIndex: 'CreatedAt',
      title: '创建时间',
      width: 110,
      valueType: 'dateTime',
      search: false,
    },
    {
      dataIndex: 'CreatedAt',
      title: '创建时间',
      width: 110,
      valueType: 'dateRange',
      hideInTable: true,
      fieldProps: {
        clearable: true,
      },
    },
    {
      dataIndex: 'UpdatedAt',
      title: '最后修改时间',
      width: 110,
      valueType: 'dateTime',
      search: false,
      fieldProps: {
        clearable: true,
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 260,
      valueType: 'option',
      disable: true,
      render(text, record) {
        // const showCopyBtn = [EXPORT_PACKAGE_STATUS.CLOSED, EXPORT_PACKAGE_STATUS.PACKING].includes(record.Status);
        return (
          <TcsButtonGroup
            wrap={true}
            items={[
              {
                text: '编辑',
                onClick: () => onEdit(record),
                hidden: deliveryIsFinished(record),
              },
              {
                text: '启动出包流程',
                onClick: () => onGoExportFlow(record),
                hidden: !enableShowExportButton(record) && !deliveryIsFinished(record),
              },
              {
                hidden: !record?.FromOriginKey,
                text: record?.FromType === FROM_TYPE.IssueSolution ? '查看关联缺陷单' : '查看关联Patch单',
                onClick: () => {
                  const fromType = record?.FromType;
                  const fromOriginKey = record?.FromOriginKey;
                  if (record?.FromType === FROM_TYPE.IssueSolution) {
                    window.open(
                      `/page/defect_manage/__develop_center_next/iteration_manage/defect/detail?issue_id=${fromOriginKey}`,
                      '_blank',
                    );
                  } else if (fromType === FROM_TYPE.Patch) {
                    window.open(
                      `/page/defect_manage/__flow-design/patch_manage/detail?patchId=${fromOriginKey}`,
                      '_blank',
                    );
                  }
                },
              },
              // {
              //   text: '复制',
              //   onClick: () => onCopy(record),
              //   hidden: !showCopyBtn,
              // },
              {
                text: '出包记录',
                onClick: () => onViewExportFlowList(record),
                hidden: !record.WorkflowInstanceID?.length,
              },
            ]}
          />
        );
      },
    },
  ] as TcsColumns[];
