/*
 * @Author: superfeng
 * @Date: 2023-03-21 20:17:29
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-04-01 15:46:35
 * @Description: 请输入注释信息
 */
import React, { useEffect, useRef, useState } from 'react';

import { getTcsColumns } from './config';
import { Switch, message } from '@tencent/tea-component';

import { DefectManagementRoutePath } from '@/common/routePath';
import { DeliveryManage, IDeliveryInfo, ListDeliverySiteRels } from '@/common/api/deliveryManage';
import WorkflowListModal from './components/WorkflowListModal';
import ExportFlow from '../Detail/ExportFlow';
import { TcsActionType, TcsLayout, TcsTable } from '@tencent/tcs-component';

import useLookup from '@/common/hookups/useLookup';
const List = () => {
  const relatedCache = localStorage.getItem('defect_manage/delivery_manage/RelatedToMe');
  const [related, setRelated] = useState(relatedCache ? relatedCache === 'true' : false);
  const workflowListRef = useRef<any>();

  const actionRef = useRef<TcsActionType>();

  const [siteList, setSiteList] = useState<any[]>([]);

  const { history } = TcsLayout.useHistory();

  const [exportFlowVisible, setExportFLowVisible] = useState(false);
  const [deliveryInfo, setDeliveryInfo] = useState<any>();
  const { getLookupByCode } = useLookup([]);
  const columns = getTcsColumns({
    onEdit: handleEdit,
    onGoExportFlow: handleGoExportFlow,
    onViewExportFlowList: handleViewExportFlowList,
    // onCopy: handleCopy,
    siteList,
  });

  useEffect(() => {
    ListDeliverySiteRels().then((res) => {
      const siteRels = res?.ListDeliverySiteRels || [];
      setSiteList(
        siteRels.map((item) => ({
          value: item.SiteUUID,
          label: item.SiteName,
        })),
      );
    });
  }, []);

  function handleRequest(queryParams: any, options: any) {
    const { SolutionVersion, SiteUUID, current: pageIndex, pageSize, tapdUrls, ...otherParams } = queryParams;
    const tapdUrlList = tapdUrls ? tapdUrls.split(';') : [];
    return DeliveryManage.ListDeliveryInfo(
      {
        DeliveryInfo: {
          ...otherParams,
          SolutionVersionID: SolutionVersion ? SolutionVersion[1] : undefined,
        },
        PageSize: pageSize,
        PageNo: pageIndex,
        RelatedToMe: related,
        SiteUUID: SiteUUID || '',
        TAPDUrls: tapdUrlList,
      },
      options,
    );
  }

  function handleEdit(record: IDeliveryInfo) {
    history.push(`${DefectManagementRoutePath.DELIVERY_EDIT_PAGE}?uuid=${record.UUID}`);
  }
  function handleGoExportFlow(record: IDeliveryInfo) {
    if (!record.CurrentOperator) {
      return message.warning({
        content: '请编辑交付单添加交付责任人之后再行出包',
      });
    }
    if (!record.DeliverySiteRel?.length) {
      return message.warning({
        content: '需要关联出包局点后再行出包',
      });
    }
    setExportFLowVisible(true);
    setDeliveryInfo(record);
  }

  // function handleCreateDelivery() {
  //   history.push(`${DefectManagementRoutePath.DELIVERY_ADD_PAGE}`);
  // }

  function handleViewExportFlowList(record: IDeliveryInfo) {
    workflowListRef.current.show(record);
  }

  // function handleCopy(record) {
  //   copyDeliveryInfo({ DeliveryID: record.UUID }).then((res) => {
  //     if (res.Error) {
  //       message.error({
  //         content: res.Error.Message,
  //       });
  //     } else {
  //       history.push(`${DefectManagementRoutePath.DELIVERY_DETAIL_PAGE}?uuid=${res.UUID}`);
  //     }
  //   });
  // }

  return (
    <TcsLayout title="交付单管理" customizeCard history={history} fullHeight>
      <TcsTable
        columns={columns}
        pagination={{}}
        scroll={{
          x: 2200,
        }}
        actionRef={actionRef}
        search={{
          labelWidth: 80,
        }}
        scrollInTable
        syncToUrl
        request={handleRequest}
        columnsState={{
          persistenceKey: 'defect_manage/delivery_manage/columns_state',
        }}
        // headerTitle={
        //   <Button type="primary" onClick={handleCreateDelivery}>
        //     新建交付单
        //   </Button>
        // }
        onRow={(record) => {
          const { Status } = record;
          const lookup = getLookupByCode('DeliveryStatus', Status);
          return {
            tooltip: lookup?.Extra?.HitMessage || lookup?.Name,
          };
        }}
        toolBarRender={() => (
          <Switch
            defaultChecked
            value={related}
            onChange={(value) => {
              localStorage.setItem('defect_manage/delivery_manage/RelatedToMe', String(value));
              setRelated(value);
              setTimeout(() => {
                actionRef.current?.reload(true);
              });
            }}
          >
            与我相关
          </Switch>
        )}
      />
      <WorkflowListModal ref={workflowListRef} />
      {exportFlowVisible && <ExportFlow setVisible={setExportFLowVisible} deliveryInfo={deliveryInfo} />}
    </TcsLayout>
  );
};
export default List;
