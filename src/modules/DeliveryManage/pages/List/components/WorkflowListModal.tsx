/*
 * @Author: superfeng
 * @Date: 2023-05-29 19:07:42
 * @LastEditors: superfeng
 * @LastEditTime: 2023-05-29 19:42:10
 * @Description: 请输入注释信息
 */
import { Button, Modal, Table, message } from '@tencent/tea-component';
import { IDeliveryInfo, IWorkflowInstanceList, getWorkflowInstanceList } from '@/common/api/deliveryManage';
import React, { useEffect, useImperativeHandle, useState } from 'react';
import dayjs from 'dayjs';
import Loading from '@/common/components/Loading';
import { WorkflowStatusList } from '@/common/constants';
import { scrollable } from '@tencent/tea-component/lib/table/addons';

const getColumns = () => [
  {
    header: '流程ID',
    key: 'WorkflowID',
  },
  {
    header: '实例ID',
    key: 'ID',
  },
  {
    header: '流程状态',
    key: 'Status',
    render(record) {
      return WorkflowStatusList[record.Status] || '-';
    },
  },
  {
    header: '启动人',
    key: 'Creator',
  },
  {
    header: '启动时间',
    key: 'CreatedAt',
    width: '30%',
    render(record) {
      return dayjs(record.CreatedAt).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    header: '操作',
    key: 'operation',
    render(record) {
      return (
        <>
          <Button
            type="link"
            onClick={() => {
              window.open(
                `/page/flow-design/flow-publish/exec?instance_id=${record.ID}&workflow_id=${record.WorkflowID}`,
                '_blank',
              );
            }}
          >
            查看执行日志
          </Button>
        </>
      );
    },
  },
];

const WorkflowListModal: React.ForwardRefRenderFunction<any, any> = (props, ref) => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [record, setRecord] = useState<IDeliveryInfo>(null);
  const [dataSource, setDataSource] = useState<IWorkflowInstanceList[]>([]);
  useEffect(() => {
    if (visible && record) {
      setLoading(true);
      getWorkflowInstanceList({
        WorkflowInstanceIDs: record.WorkflowInstanceID!,
      })
        .then((res) => {
          if (res.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            setDataSource(res.WorkflowInstanceList);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [record, visible]);

  useImperativeHandle(
    ref,
    () => ({
      show(record: IDeliveryInfo) {
        setRecord(record);
        setVisible(true);
      },
    }),
    [],
  );

  function handleCancel() {
    setVisible(false);
    setDataSource([]);
  }

  return (
    <Modal caption="查看出包记录" visible={visible} onClose={handleCancel} size="l">
      <Loading loading={loading}>
        <Table columns={getColumns()} records={dataSource} addons={[scrollable({ maxHeight: 480 })]} />
      </Loading>
    </Modal>
  );
};

export default React.forwardRef(WorkflowListModal);
