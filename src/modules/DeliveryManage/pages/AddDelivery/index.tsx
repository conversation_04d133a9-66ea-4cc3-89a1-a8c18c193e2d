/*
 * @Author: superfeng
 * @Date: 2023-03-22 16:51:59
 * @LastEditors: superfeng
 * @LastEditTime: 2023-05-23 11:38:43
 * @Description: 请输入注释信息
 */
import { IDeliveryInfo, createDeliveryInfo, getDeliveryInfo, updateDeliveryInfo } from '@/common/api/deliveryManage';
import ExEditor from '@/common/components/ExEditor';
import { DefectManagementRoutePath } from '@/common/routePath';
import { getUrlParams } from '@/common/utils';
import {
  TcsButton,
  TcsCard,
  TcsForm,
  TcsFormDictSelect,
  TcsFormSelect,
  TcsFormStaffSelect,
  TcsFormTextArea,
  TcsLayout,
} from '@tencent/tcs-component';
import { Select, message } from '@tencent/tea-component';
import React, { useEffect, useMemo, useRef, useState } from 'react';

import styles from './index.module.less';
import { ISolution, ISolutionVersion } from '@/common/api/common';
import { CommonApi } from '@/common/api/common.api';

const AddDelivery = () => {
  const [loading, setLoading] = useState(false);
  const { history } = TcsLayout.useHistory();
  const formRef = useRef<any>(null);

  const urlParams = getUrlParams();
  const [deliveryInfo, setDeliveryInfo] = useState<IDeliveryInfo>();

  const [solutionList, setSolutionList] = useState<ISolution[]>([]);
  const [currentSolutionUUID, setCurrentSolutionUUID] = useState<string>();
  const [solutionVersionList, setSolutionVersionList] = useState<ISolutionVersion[]>([]);

  const isEdit = useMemo(() => !!urlParams.uuid, [urlParams.uuid]);

  useEffect(() => {
    if (urlParams.uuid) {
      getDeliveryInfo({
        DeliveryID: urlParams.uuid,
      }).then((res) => {
        if (res.Error) {
          message.error({
            content: res.Error.Message,
          });
        } else {
          setDeliveryInfo(res.DeliveryDetailInfo);
          formRef.current?.setFieldsValue({
            Name: res.DeliveryDetailInfo?.Name,
            Description: res.DeliveryDetailInfo?.Description,
            CurrentOperator: res.DeliveryDetailInfo?.CurrentOperator
              ? res.DeliveryDetailInfo.CurrentOperator.split(';')
              : [],
          });
        }
      });
    }
  }, [urlParams.uuid]);

  useEffect(() => {
    // listSolution().then((res) => {
    //   if (res.Error) {
    //     message.error({
    //       content: res.Error.Message,
    //     });
    //   } else {
    //     setSolutionList(res.ListSolutions || []);
    //   }
    // });
    CommonApi.ListSolutions().then((res) => {
      setSolutionList(res.ListSolutions || []);
    });
  }, []);

  useEffect(() => {
    if (currentSolutionUUID) {
      // listSolutionVersion({
      //   SolutionUUID: currentSolutionUUID!,
      // }).then((res) => {
      //   if (res.Error) {
      //     message.error({
      //       content: res.Error.Message,
      //     });
      //   } else {
      //     setSolutionVersionList(res.ListSolutionVersions || []);
      //   }
      // });
      CommonApi.ListSolutionVersionWithMarketFlag({
        SolutionID: currentSolutionUUID!,
      }).then((res) => {
        setSolutionVersionList(res.ListSolutionVersions || []);
      });
    }
  }, [currentSolutionUUID]);

  function handleChangeSolution(value: string) {
    setCurrentSolutionUUID(value);
    formRef.current.setFieldValue('SolutionVersionID', undefined);
  }

  const handleConfirm = () => {
    formRef?.current?.validateFields()?.then((values) => {
      setLoading(true);
      const method = isEdit ? updateDeliveryInfo : createDeliveryInfo;
      const params = {
        ...values,
        SolutionVersion: solutionVersionList.find((item) => item.UUID === values.SolutionVersionID)?.Code,
        CurrentOperator: values.CurrentOperator.join(';'),
      };
      if (isEdit) {
        Object.assign(params, {
          DeliveryID: urlParams.uuid,
        });
      }
      method(params)
        .then((res) => {
          if (res.Error) {
            message.error({
              content: res.Error.Message,
            });
          } else {
            message.success({
              content: isEdit ? '编辑成功' : '创建成功',
            });
            // 跳转至详情页
            history.replace(
              `${DefectManagementRoutePath.DELIVERY_DETAIL_PAGE}?uuid=${isEdit ? urlParams.uuid : res.UUID}`,
            );
          }
        })
        .finally(() => {
          setLoading(false);
        });
      // history.go(-1);
    });
  };
  const handleCancel = () => {
    history.go(-1);
  };

  return (
    <TcsLayout title={isEdit ? '编辑交付单' : '新建交付单'} customizeCard fullHeight history={history}>
      <div className={styles.delivery_edit}>
        <TcsCard className={styles.delivery_edit__form}>
          <TcsForm formRef={formRef}>
            <TcsFormTextArea
              label="交付单名称"
              name="Name"
              rules={[
                {
                  required: true,
                  message: '请输入交付单名称',
                },
              ]}
              fieldProps={{
                style: {
                  width: 420,
                },
              }}
            />

            {!deliveryInfo?.IsFromIssue && (
              <TcsForm.Item label="交付单标签" name="Tags">
                <Select size="l" appearance="button" options={[]} searchable />
              </TcsForm.Item>
            )}
            <TcsFormStaffSelect
              label="交付责任人"
              name="CurrentOperator"
              rules={[
                {
                  required: true,
                  message: '请选择交付责任人',
                },
              ]}
              fieldProps={{
                multiple: true,
                style: { width: 420 },
              }}
            />
            {!isEdit && (
              <TcsForm.Item
                label="解决方案"
                name="SolutionUUID"
                rules={[
                  {
                    required: true,
                    message: '请选择解决方案',
                  },
                ]}
              >
                <Select
                  size="l"
                  searchable
                  appearance="button"
                  matchButtonWidth
                  options={solutionList.map((item) => ({
                    text: `${item.Name}(${item.NameEN || ''})`,
                    value: item.UUID,
                  }))}
                  onChange={handleChangeSolution}
                />
              </TcsForm.Item>
            )}

            {!isEdit && (
              <TcsFormSelect
                label="解决方案版本"
                name="SolutionVersionID"
                rules={[
                  {
                    required: true,
                    message: '请选择解决方案版本',
                  },
                ]}
                fieldProps={{
                  options: solutionVersionList.map((item) => ({
                    label: item.Code,
                    value: item.UUID,
                  })),
                  size: 'l',
                }}
              />
            )}
            {!isEdit && (
              <TcsFormDictSelect
                label="架构"
                name="Arch"
                rules={[
                  {
                    required: true,
                    message: '请选择架构',
                  },
                ]}
                fieldProps={{
                  dictType: 'PackageArch',
                  size: 'l',
                }}
              />
            )}

            <TcsForm.Item label="交付单描述" name="Description">
              <ExEditor minHeight={400} />
            </TcsForm.Item>
          </TcsForm>
        </TcsCard>
        <div style={{ textAlign: 'center', paddingTop: 10 }}>
          <TcsButton type="primary" loading={loading} onClick={handleConfirm} style={{ marginRight: 10 }}>
            确定
          </TcsButton>
          <TcsButton type="weak" onClick={handleCancel}>
            取消
          </TcsButton>
        </div>
      </div>
    </TcsLayout>
  );
};

export default AddDelivery;
