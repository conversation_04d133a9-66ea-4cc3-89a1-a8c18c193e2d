/*
 * @Author: super<PERSON>
 * @Date: 2023-04-04 11:22:45
 * @LastEditors: superfeng
 * @LastEditTime: 2023-07-11 14:56:00
 * @Description: 请输入注释信息
 */
import { EXPORT_PACKAGE_STATUS, EXPORT_WORKFLOW_UUID } from '@/common/config';
import { IDeliveryInfo, generateWorkflowInstance } from '@/common/api/deliveryManage';
import { message } from '@tencent/tea-component';

export const startWorkflow = (params: { record: IDeliveryInfo; SiteUUIDList?: string[] }) => {
  const { record, SiteUUIDList } = params;
  return generateWorkflowInstance({
    WorkflowUUID: EXPORT_WORKFLOW_UUID[record?.FromType],
    GlobalValues: {
      DeliveryUUID: record.UUID,
      SiteUUIDList,
    },
  }).then((res) => {
    if (res.Error) {
      message.error({
        content: res.Error.Message,
      });
    } else {
      message.success({
        content: '流程启动成功',
      });

      window.open(
        `/page/flow-design/flow-publish/exec?instance_id=${res.WorkflowInstanceID}&workflow_id=${res.WorkflowID}`,
        '_blank',
      );
    }
  });
};

// 判断是否显示出包按钮
export const enableShowExportButton = (data?: IDeliveryInfo) => {
  if (
    (
      [EXPORT_PACKAGE_STATUS.NEED_PACK_PACKAGE, EXPORT_PACKAGE_STATUS.PACKING, EXPORT_PACKAGE_STATUS.PACKED] as any
    ).includes(data?.Status)
  ) {
    return true;
  }

  return false;
};

// 是否已关闭或者失败的单子
export const deliveryIsFinished = (data?: IDeliveryInfo) => {
  if (data?.Status) {
    return [EXPORT_PACKAGE_STATUS.CLOSED, EXPORT_PACKAGE_STATUS.FAILED].includes(data.Status as any);
  }
  return false;
};
