import { DefectManageProvider } from '@/common/context';
import { TcscPlatform, tapi } from '@tencent/tcsc-base';
import { CURRENT_TENANT_LOOKUP_GROUPS, LOOKUP_GROUPS } from './common/constants';
import './index.less';
import DeliveryManage from './modules/DeliveryManage';
import IterationManage from './modules/IterationManage';
import KaleidoManage from './modules/KaleidoManage';
import MaterialManage from './modules/MaterialManage/pages/Material';
import SiteList from './modules/MaterialManage/pages/SiteList';
import PublishManage from './modules/PackageManage';
import PatchManage from './modules/PatchManage';
import PatchMarketManage from './modules/PatchMarketManage';
import ReleasePlanManage from './modules/ReleasePlanManage';
import RequirementIterationMange from './modules/RequirementIterationManage';

const menuCompMapping = [
  {
    path: '/iteration_manage',
    component: IterationManage,
    exact: false,
  },
  {
    path: '/delivery_manage',
    component: DeliveryManage,
    exact: false,
  },
  {
    path: '/patch_manage',
    component: PatchManage,
    exact: false,
  },
  {
    path: '/patch_market',
    component: PatchMarketManage,
    exact: false,
  },
  {
    path: '/defect_history',
    component: PublishManage,
    exact: false,
  },
  {
    path: '/defect_board',
    component: PublishManage,
    exact: false,
  },
  {
    path: '/requirement_iteration_manage',
    component: RequirementIterationMange,
    exact: false,
  },
  {
    path: '/kaleido_manage',
    component: KaleidoManage,
    exact: false,
  },
  {
    path: '/release_plan_manage',
    component: ReleasePlanManage,
    exact: false,
  },
  {
    path: '/__:parentCode/iteration_manage',
    component: IterationManage,
    exact: false,
  },
  {
    path: '/__:parentCode/delivery_manage',
    component: DeliveryManage,
    exact: false,
  },
  {
    path: '/__:parentCode/patch_manage',
    component: PatchManage,
    exact: false,
  },
  {
    path: '/__:parentCode/patch_market_manage',
    component: PatchMarketManage,
    exact: false,
  },
  {
    path: '/__:parentCode/defect_history',
    component: PublishManage,
    exact: false,
  },
  {
    path: '/__:parentCode/defect_board',
    component: PublishManage,
    exact: false,
  },
  {
    path: '/__:parentCode/material_manage',
    component: MaterialManage,
    exact: false,
  },
  {
    path: '/__:parentCode/site_list',
    component: SiteList,
    exact: false,
  },
  // 迭代管理
  {
    path: '/__:parentCode/requirement_iteration_manage',
    component: RequirementIterationMange,
    exact: false,
  },
  // kaleido
  {
    path: '/__:parentCode/kaleido_manage',
    component: KaleidoManage,
    exact: false,
  },
  // 发布计划
  {
    path: '/__:parentCode/release_plan_manage',
    component: ReleasePlanManage,
    exact: false,
  },
];

tapi.config({
  appCode: 'defect_manage',
});

TcscPlatform.register(
  'defect_manage',
  menuCompMapping,
  TcscPlatform.createTcscContext(
    {
      lookupCodes: LOOKUP_GROUPS,
      tenantLookupCodes: CURRENT_TENANT_LOOKUP_GROUPS,
      transformDict(globalStatus = [], tenantStatus = []) {
        const globalIssueStatus = globalStatus.filter((item) => item.Type === 'IssueStatus');
        const tenantIssueStatus = tenantStatus.filter((item) => item.Type === 'IssueStatus');
        const globalIssueStatusCode = globalIssueStatus.map((item) => item.Code);
        tenantIssueStatus.forEach((item) => {
          if (!globalIssueStatusCode.includes(item.Code)) {
            globalIssueStatus.push(item);
          }
        });

        return [
          ...globalStatus.filter((item) => item.Type !== 'IssueStatus'),
          ...tenantStatus.filter((item) => item.Type !== 'IssueStatus'),
          ...globalIssueStatus,
        ];
      },
    },
    DefectManageProvider,
  ),
);
function loadScripts(urls, callback) {
  let loadedCount = 0;

  urls.forEach((url) => {
    const script = document.createElement('script');
    script.src = url;
    script.async = true;

    script.onload = () => {
      loadedCount += 1;
      if (loadedCount === urls.length) {
        callback();
      }
    };

    document.head.appendChild(script);
  });
}
if (window.location.hostname.startsWith('pre.')) {
  const scriptUrls = [
    'http://************/browser_plugins/dom/dist/dom.umd.js',
    'http://************/browser_plugins/history/dist/history.umd.js',
    'http://************/browser_plugins/hash/dist/hash.umd.js',
    'http://************/browser_plugins/customer/dist/customer.umd.js',
    'http://************/browser_plugins/fetch/dist/fetch.umd.js',
  ];
  loadScripts(scriptUrls, () => {
    console.log('所有脚本已加载完成');
    // 在这里执行您的代码

    const script = document.createElement('script');
    script.src = 'http://************/browser/dist/browser.iife.js';
    script.async = true;
    // const rrwebConfig = {
    //   sampling: {
    //     media: 5000,
    //     input: 'last',
    //   },
    // };

    // 脚本加载完成后设置HEIMDALLYR_OPTIONS
    window.__HEIMDALLR_OPTIONS__ = {
      dsn: {
        host: '************/manager',
        init: '/project/init',
        report: '/log/report',
        upload: '/log/upload',
      },
      app: {
        name: 'patchManage',
        leader: 'lucyfang',
        desc: 'test',
      },
      userIdentify: {
        name: 'jiguang_username', // 确保 window.state 存在
        position: 'global',
      },
      plugins: [HEIMDALLR_DOM(), HEIMDALLR_HISTORY(), HEIMDALLR_HASH(), HEIMDALLR_CUSTOMER(), HEIMDALLR_FETCH()],
    };
    document.body.appendChild(script);
  });
}
