/*
 * @Author: lucyfang
 * @Date: 2024-08-05 10:21:06
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-02-28 19:40:40
 * @Description: 请输入注释信息
 */
// / <reference types="react-scripts" />
declare global {
  interface Window {
    jiguang_api_domain?: string;
    jiguang_domain?: string;
    menus: any;
    allMemberList: [
      {
        combineName: string;
        enName: string;
      },
    ];
    // eslint-disable-next-line camelcase
    jiguang_username: string;
  }
}

declare module '*.less' {
  const classes: { [key: string]: string };
  export default classes;
}

declare module '*.module.less' {
  const classes: { [className: string]: string };
  export default classes;
}
declare module '*.css' {
  const content: { [className: string]: string };
  export = content;
}

declare namespace NodeJS {
  interface ProcessEnv {
    readonly NODE_ENV: 'development' | 'production' | 'test';
    readonly PUBLIC_URL: string;
  }
}

declare module '*.bmp' {
  const src: string;
  export default src;
}

declare module '*.gif' {
  const src: string;
  export default src;
}

declare module '*.jpg' {
  const src: string;
  export default src;
}

declare module '*.jpeg' {
  const src: string;
  export default src;
}

declare module '*.png' {
  const src: string;
  export default src;
}

declare module '*.webp' {
  const src: string;
  export default src;
}

declare module '*.svg' {
  import * as React from 'react';

  export const ReactComponent: React.FunctionComponent<React.SVGProps<SVGSVGElement> & { title?: string }>;

  const src: string;
  export default src;
}

declare module '*.module.css' {
  const classes: { readonly [key: string]: string };
  export default classes;
}

declare module '*.module.scss' {
  const classes: { readonly [key: string]: string };
  export default classes;
}

declare module '*.module.sass' {
  const classes: { readonly [key: string]: string };
  export default classes;
}

declare module '*.scss';
declare module '*.less';

// export {};
