/*
 * @Author: superfeng
 * @Date: 2023-05-29 19:39:41
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-05-06 15:01:41
 * @Description: 请输入注释信息
 */

// 流程状态
export enum WorkFlowInstanceStatus {
  // 运行中
  RUNNING = 'running',
  // 成功
  SUCCESS = 'success',
  // 失败
  FAIL = 'failed',
  // 等待
  WAITING = 'waiting',
  // 暂停
  PAUSE = 'paused',
  // 终止
  TERMINATED = 'terminated',
}

export const WorkflowStatusList = {
  [WorkFlowInstanceStatus.RUNNING]: '运行中',
  [WorkFlowInstanceStatus.SUCCESS]: '成功',
  [WorkFlowInstanceStatus.FAIL]: '失败',
  [WorkFlowInstanceStatus.TERMINATED]: '终止',
  [WorkFlowInstanceStatus.PAUSE]: '暂停',
  [WorkFlowInstanceStatus.WAITING]: '等待输入',
};

// 缺陷单已合流版本验证状态
export enum PackageTestStatus {
  //	待验证
  WAITING_FOR_TESTING = 'waiting_for_testing',
  // 验证中
  TESTING = 'testing',
  // 验证失败
  TEST_FAILED = 'test_failed',
  // 验证成功
  TEST_SUCCEED = 'test_succeed',
  NO_MERGE = 'no_merge',
}

export const LOOKUP_GROUPS = [
  'OSArch',
  'PackageArch',
  'Severity',
  'Priority',
  'IssueStatus',
  'IssueSolutionArchStatus',
  'IssueAppStatus',
  'DeliveryStatus',
  'DeliverType',
  'IssueUpdateType',
  'IssueUpdateMethod',
  'IssueUpdateEntity',
  'BugType',
  'PatchStatus',
  'DeliveryFromType',
  'PatchVerificationStatus',
  'IssueMetadataStatus',
  'IssueMetadataKey',
  'PackageOutRecordStatus',
  'ProductSource',
  'ProductCategory',
  'MaterialDeployStatus',
  'MaterialPublishStatus',
  'PackageStatus',
  'MaterialDeployStatus',
  'SiteType',
  'IterationType',
  'IterationStatus',
  'IterationLockStatus',
  'StoryType',
  'MRStatus',
  // 缺陷单验证出包状态
  'VerificationStatus',
  //
  'PackageTestStatus',
  'VersionReleaseApprovalTemplate',
  'ReleaseVersionStatus',
  'ReleaseVersionUsers',
  'ProjectsSiteStatus',
  'PushPackageType',
  'OperationTool',
  'PatchType',
  'OperationSiteVariable',
  'DeliverySiteVariable',
  'PatchAttachmentSource',
  'DefectEvaluateDependencies',
  'TestConclusion',
  'SheetConclusion',
  'AndonSiteStatus',
  'branchRelatedSolutionVersion',
  'AppRulesConfigLimitUsers',
  'RecallSheetType',
];

export const CURRENT_TENANT_LOOKUP_GROUPS = ['StoryCategory', 'IssueStatus', 'SolutionVersionInfo'];

// 出包类型
export enum DELIVER_TYPE {
  // 仅后续新增客户出包
  NO_NEED = 'NoNeed',
  // 存量及新增客户出包
  ALL_PROJECTS = 'AllProjects',
  // 指定客户出包
  SPECIFIED_PROJECTS = 'SpecifiedProjects',
}

// 缺陷单/需求单状态
export enum IssueStatus {
  // 新建
  NEW = 'new',
  //  处理中
  OPEN = 'open',
  // 已完成
  CLOSED = 'closed',
  // 已拒绝
  REJECTED = 'rejected',
}
