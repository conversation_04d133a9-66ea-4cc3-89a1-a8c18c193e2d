/*
 * @Author: super<PERSON>
 * @Date: 2023-03-15 10:20:19
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-12-17 16:26:28
 * @Description: 请输入注释信息
 */
export function withRouteBasename(routeUrl: string) {
  const parentCode =
    routeUrl.startsWith('/iteration_manage') ||
    routeUrl.startsWith('/requirement_iteration_manage') ||
    routeUrl.startsWith('/kaleido_manage') ||
    routeUrl.startsWith('/release_plan_manage')
      ? 'develop_center_next'
      : 'flow-design';
  if (!parentCode) {
    return `/page/defect_manage${routeUrl}`;
  }
  return `/page/defect_manage/__${parentCode}${routeUrl}`;
}
export const DefectManagementRoutePath = {
  ITERATION_INDEX_PAGE: withRouteBasename('/iteration_manage/:pagePath'),
  ITERATION_EDIT_PAGE: withRouteBasename('/iteration_manage/:pagePath/edit'),
  ITERATION_DETAIL_PAGE: withRouteBasename('/iteration_manage/:pagePath/detail'),
  // 嵌套到 tapd 里面的页面
  ITERATION_TAPD_PAGE: withRouteBasename('/iteration_manage/:pagePath/tapd_page'),
  DELIVERY_DETAIL_PAGE: withRouteBasename('/delivery_manage/detail'),
  DELIVERY_ADD_PAGE: withRouteBasename('/delivery_manage/add'),
  DELIVERY_EDIT_PAGE: withRouteBasename('/delivery_manage/edit'),
  PATCH_DETAIL_PAGE: withRouteBasename('/patch_manage/detail'),
  PATCH_QUEUE_MANAGE_PAGE: withRouteBasename('/patch_manage/queue_manage'),
  PATCH_QUEUE_DEYTAIL_PAGE: withRouteBasename('/patch_manage/queue_detail'),
  PACKAGE_HISTORY_PAGE: withRouteBasename('/defect_history'),
  PACKAGE_HISTORY_DETAIL_PAGE: withRouteBasename('/defect_history/detail'),
  ISSUE_BOARD_DETAIL_PAGE: withRouteBasename('/defect_board/issue/detail'),
  SITE_BOARD_DETAIL_PAGE: withRouteBasename('/defect_board/site/detail'),
  PATCH_EXPORT_FROM_HISTORY_PAGE: withRouteBasename('/defect_history/export'),
  PATCH_EXPORT_FROM_BOARD_PAGE: withRouteBasename('/defect_board/export'),
  // KALEIDO
  KALEIDO_MANAGE_PAGE: withRouteBasename('/kaleido_manage'),
  // 迭代管理
  REQUIREMENT_ITERATION_LIST_PAGE: withRouteBasename('/requirement_iteration_manage'),
  REQUIREMENT_ITERATION_DETAIL_PAGE: withRouteBasename('/requirement_iteration_manage/detail'),
  REQUIREMENT_ITERATION_REAlEASE_VERSION_PAGE: withRouteBasename('/requirement_iteration_manage/detail/release'),
  REQUIREMENT_ITERATION_REAlEASE_VERSION_PRODUCT_PAGE: withRouteBasename(
    '/requirement_iteration_manage/detail/release/product',
  ),
  // 发布计划
  RELEASE_PLAN_MANAGE_LIST_PAGE: withRouteBasename('/release_plan_manage'),
  RELEASE_PLAN_MANAGE_DETAIL_PAGE: withRouteBasename('/release_plan_manage/detail'),
  RELEASE_PLAN_MANAGE_RELEASE_VERSION_PAGE: withRouteBasename('/release_plan_manage/detail/release'),
  RELEASE_PLAN_MANAGE_RELEASE_VERSION_PRODUCT_PAGE: withRouteBasename('/release_plan_manage/detail/release/product'),

  // patch公共市场
  PATCH_MARKET_MANAGE: withRouteBasename('/patch_market_manage'),
  PATCH_MARKET_DETAIL: withRouteBasename('/patch_market_manage/detail'),
};

export const MaterialManageRoutePath = {
  MATERIAL_INDEX_PAGE: withRouteBasename('/material_manage'),
  SITE_LIST_PAGE: withRouteBasename('/site_list'),
  SITE_DETAIL_PAGE: withRouteBasename('/site_list/detail'),
  EDIT_SITE_TAG: withRouteBasename('/site_list/detail/edit_site_tag'),
};
