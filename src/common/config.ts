/*
 * @Author: superfeng
 * @Date: 2023-03-15 10:20:19
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-12-13 10:28:45
 * @Description: 请输入注释信息
 */

// 出包流程使用的工作流UUID
// export const EXPORT_WORKFLOW_UUID = 'c9dbb29317524ae9b7af3c5e0c866dde';

export const EXPORT_WORKFLOW_UUID = {
  IssueSolution: 'c9dbb29317524ae9b7af3c5e0c866dde',
  Patch: '1a6d57cf032341cbbe34b14754cc680c',
};

// 验证出包流程使用的工作流UUID
export const VERIFY_EXPORT_WORKFLOW_UUID = {
  // 产品市场
  ProductMarket: 'e4aefa13f8a84ff499e0f2afbe2abc92',
  // 手动填写的产品运维中心变更单
  Tops: 'e6dc65fe431d464393df53fa9f9fda66',
  Kaleido: 'b9a1ba08a0994a9bad9df007e52ce151',
};

// 缺陷单关联的解决方案版本架构的处理状态
export enum SOLUTION_ARCH_STATUS {
  // NEW 新建状态
  NEW = 'new',
  // REJECTED 已拒绝
  REJECTED = 'rejected',
  // CLOSED 已关闭
  CLOSED = 'closed',
  // RESOLVED 已解决
  RESOLVED = 'resolved',
  // DEVELOPING 研发中
  DEVELOPING = 'developing',
  // PRODUCT_EXPERIENCE 产品体验
  PRODUCT_EXPERIENCE = 'product_experience',
  // TEST_FREE 免测
  TEST_FREE = 'test_free',
  // FOR_TEST 产品转测
  FOR_TEST = 'for_testing',
  // WAITING_FOR_REGISTRATION 已测试待合流
  WAITING_FOR_REGISTRATION = 'waiting_for_registration',
  // REGISTERED 已合流
  REGISTERED = 'registered',
  // INTEGRATION_TESTING 集成验收中
  INTEGRATION_TESTING = 'integration_testing',
  // INTEGRATION_TEST_PASS 集成测试已完成
  INTEGRATION_TEST_PASS = 'integration_test_pass',
  // WAITING_COMPLETE_OPERATION_SHEET 待完善变更控制表
  WAITING_COMPLETE_OPERATION_SHEET = 'waiting_complete_operation_sheet',
  // WAITING_CHECK_OPERATION_SHEET 待变更评估
  WAITING_CHECK_OPERATION_SHEET = 'waiting_check_operation_sheet',
  // OPERATION_TEST 变更验证
  OPERATION_TEST = 'operation_testing',
  // WAITING_FOR_DELIVERING 待出包
  WAITING_FOR_DELIVERING = 'waiting_for_delivering',
  // RELEASED 已发布
  RELEASED = 'released',
}

// 研发阶段
export const DevelopmentStage: SOLUTION_ARCH_STATUS[] = [
  SOLUTION_ARCH_STATUS.NEW,
  SOLUTION_ARCH_STATUS.DEVELOPING,
  SOLUTION_ARCH_STATUS.PRODUCT_EXPERIENCE,
  SOLUTION_ARCH_STATUS.TEST_FREE,
  SOLUTION_ARCH_STATUS.FOR_TEST,
  SOLUTION_ARCH_STATUS.WAITING_FOR_REGISTRATION,
  SOLUTION_ARCH_STATUS.REGISTERED,
  SOLUTION_ARCH_STATUS.WAITING_COMPLETE_OPERATION_SHEET,
  SOLUTION_ARCH_STATUS.WAITING_CHECK_OPERATION_SHEET,
];

// 测试阶段
export const TestingStage: SOLUTION_ARCH_STATUS[] = [
  SOLUTION_ARCH_STATUS.INTEGRATION_TEST_PASS,
  SOLUTION_ARCH_STATUS.OPERATION_TEST,
  SOLUTION_ARCH_STATUS.INTEGRATION_TESTING,
];

// 交付阶段
export const DeliveryStage: SOLUTION_ARCH_STATUS[] = [
  SOLUTION_ARCH_STATUS.WAITING_FOR_DELIVERING,
  SOLUTION_ARCH_STATUS.RELEASED,
  SOLUTION_ARCH_STATUS.CLOSED,
];

// 交付单出包状态
export enum EXPORT_PACKAGE_STATUS {
  // New 新建
  NEW = 'New',
  // NeedPackPackage 待出包
  NEED_PACK_PACKAGE = 'NeedPackPackage',
  // Packing 出包中
  PACKING = 'Packing',
  // Packed 出包完成
  PACKED = 'Packed',
  // Closed 已关闭
  CLOSED = 'Closed',
  // 推包中
  PUSHING = 'Pushing',
  // 失败
  FAILED = 'Failed',
  // 出包前置检查失败
  CHECK_NEED_PACK_PACKAGE_FAILED = 'CheckNeedPackPackageFailed',
  // 待审批
  APPROVAL_PENDING = 'ApprovalPending',
}

export enum PACKAGE_STATUS {
  Published = 'published',
  Draft = 'draft',
}

export enum FROM_TYPE {
  Patch = 'Patch',
  IssueSolution = 'IssueSolution',
}

export enum SORT_TYPE {
  asc = 'ASC',
  desc = 'DESC',
}

export enum PUBLIC_PATCH_TYPE {
  Publish = 1,
  No_Publish = 2,
}

// 交付单出包类型
export enum DELIVERY_FROM_TYPE {
  story = 'story',
  bug = 'bug',
  patch = 'Patch',
}
