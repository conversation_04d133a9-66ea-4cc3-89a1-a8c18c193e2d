/*
 * @Author: superfeng
 * @Date: 2023-03-16 11:42:58
 * @LastEditors: superfeng
 * @LastEditTime: 2023-06-25 18:22:30
 * @Description: 请输入注释信息
 */
import { Form, ColProps, Col } from '@tencent/tea-component';
import React from 'react';
import { ControllerProps, Controller } from 'react-hook-form';

export interface IProps extends Partial<ControllerProps> {
  label?: string | React.ReactNode;
  dataIndex?: string;
  initialValue?: any;
  grid?: boolean;
  colProps?: ColProps;
  hidden?: boolean;
  tooltip?: string | React.ReactNode;
}

function getStatus(meta, formState) {
  if (meta?.invalid) {
    return 'error';
  }
  if (!meta?.isDirty && !formState?.isSubmitted) {
    return null;
  }
  return 'success';
}

const FormItem: React.FC<IProps> = ({
  label,
  dataIndex,
  initialValue,
  children,
  errors,
  rules,
  colProps = { span: 6 },
  grid,
  hidden,
  tooltip,
  ...rest
}) => {
  const item = (
    <Controller
      name={dataIndex!}
      {...rest}
      defaultValue={initialValue}
      rules={rules}
      render={({ field, fieldState, formState }) => (
        <Form.Item
          style={{
            display: hidden ? 'none' : undefined,
          }}
          label={label}
          tips={tooltip}
          status={rules ? getStatus(fieldState, formState) : undefined}
          message={fieldState?.error?.message}
        >
          {React.Children.only(children)
            ? React.cloneElement(children, {
                ...field,
                onChange: (...args) => {
                  field?.onChange?.(...args);
                  children?.props?.onChange?.(...args);
                },
              })
            : undefined}
        </Form.Item>
      )}
    />
  );
  if (grid) {
    return (
      <Col
        style={{
          display: hidden ? 'none' : undefined,
        }}
        {...colProps}
      >
        {item}
      </Col>
    );
  }
  return item;
};

export default FormItem;
