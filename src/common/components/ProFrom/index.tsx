/*
 * @Author: superfeng
 * @Date: 2023-03-16 11:30:32
 * @LastEditors: superfeng
 * @LastEditTime: 2023-07-03 15:45:13
 * @Description: 请输入注释信息
 */
import { Button, ButtonProps, Form, FormProps, Row } from '@tencent/tea-component';
import React, { useImperativeHandle } from 'react';

import { useForm } from 'react-hook-form';
import FormItem from './components/FormItem';

export interface IProps extends FormProps {
  onFinish?: (values: any) => Promise<void>;
  onReset?: () => void;
  formRef?: React.MutableRefObject<any>;
  grid?: boolean;
  initialValues?: any;
  submitter?:
    | false
    | {
        submitButtonProps?: ButtonProps & { text?: string };
        resetButtonProps?: ButtonProps & { text?: string };
      };
}
const ProForm = ({
  onFinish,
  onReset,
  formRef,
  grid,
  children,
  submitter = false,
  initialValues = {},
  ...rest
}: IProps) => {
  const form = useForm({
    mode: 'all',
    defaultValues: initialValues,
  });
  const { control, handleSubmit, formState, reset, getValues, setValue, trigger } = form;

  async function onSubmit(values) {
    if (onFinish) {
      await onFinish(values);
    }
  }

  function handleReset() {
    reset({});
    onReset?.();
  }

  useImperativeHandle(
    formRef,
    () => ({
      getFieldsValue: () => getValues(),
      setFieldsValue: (values: object) => {
        Object.entries(values).forEach(([key, value]) => setValue(key, value));
      },
      getFieldValue: (name: string) => getValues(name),
      setFieldValue: (name: string, value: any) => setValue(name, value),
      validateFields: () =>
        trigger().then((success) => {
          if (success) {
            return getValues();
          }
          throw new Error('表单校验失败');
        }),
      resetFields: () => {
        const values = getValues();
        Object.keys(values).forEach((key) => {
          if (typeof values[key] === 'string') {
            values[key] = '';
          } else if (Array.isArray(values[key])) {
            values[key] = [];
          } else {
            values[key] = undefined;
          }
        });
        reset(values);
      },
    }),
    [trigger, getValues, setValue, reset],
  );

  const content = React.Children.map(children, (child) =>
    React.isValidElement(child)
      ? React.cloneElement(child, {
          control,
          grid,
        })
      : child,
  );

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Form style={{ width: '100%' }} {...rest}>
        {grid ? <Row>{content}</Row> : content}
      </Form>
      {submitter ? (
        <Form.Action>
          <Button {...submitter?.resetButtonProps} htmlType="reset" onClick={handleReset}>
            {submitter?.resetButtonProps?.text || '重置'}
          </Button>
          <Button
            {...submitter?.submitButtonProps}
            type="primary"
            loading={formState.isSubmitting}
            disabled={!formState.isValid}
          >
            {submitter?.submitButtonProps?.text || '确定'}
          </Button>
        </Form.Action>
      ) : undefined}
    </form>
  );
};

ProForm.Item = FormItem;

export default ProForm;
