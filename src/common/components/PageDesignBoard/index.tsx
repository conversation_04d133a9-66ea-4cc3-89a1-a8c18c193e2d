/*
 * @Author: super<PERSON>
 * @Date: 2023-03-20 19:30:38
 * @LastEditors: superfeng
 * @LastEditTime: 2023-03-30 16:49:56
 * @Description: 请输入注释信息
 */
import React from 'react';
import { RenderBoard } from '@tencent/jpdesign-components';

export interface IProps {
  inputParams: any;
  list: any;
}
const PageDesignBoard: React.ForwardRefRenderFunction<any, IProps> = ({ inputParams, list }, ref) => (
  <RenderBoard ref={ref} inputParams={inputParams} data={list} />
);

export default React.forwardRef(PageDesignBoard);
