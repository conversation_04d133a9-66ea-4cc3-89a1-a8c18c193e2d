import useLookup from '@/common/hookups/useLookup';
import { Cascader } from '@tencent/tea-component';
import React, { CSSProperties, useEffect, useState } from 'react';

export interface IProps {
  value?: string[];
  onChange?: (value: string[]) => void;
  style?: CSSProperties;
  disabled?: boolean;
}

const CategorySelect: React.FC<IProps> = ({ value = [], onChange, style, disabled = false }) => {
  const { lookups } = useLookup(['StoryCategory']);
  const [data, setData] = useState<any[]>([]);
  // 有父元素ID的默认在父元素

  useEffect(() => {
    const filterNotParent = lookups?.StoryCategory?.filter((item) => !item.ParentID);
    const filterHaveParent = lookups?.StoryCategory?.filter((item) => item.ParentID);
    const storyCategoryData = filterNotParent?.map((item) => {
      const childrens = filterHaveParent?.filter((value) => value.ParentID === item.UUID);
      return {
        ...item,
        children: childrens,
      };
    });
    setData(
      storyCategoryData?.map((item) => ({
        label: item.Name,
        value: item.Code,
        isLeaf: false,
        children: item?.children?.map((item) => ({
          label: item.Name,
          value: item.Code,
        })),
      })) || [],
    );
  }, [lookups?.StoryCategory]);

  const handleChange = (value) => {
    onChange?.(value);
  };
  return (
    <Cascader
      data={data}
      type="menu"
      onChange={handleChange}
      multiple={false}
      searchable
      clearable
      disabled={disabled}
      value={value || []}
      changeOnSelect={true}
      style={{ maxWidth: '100%', ...style }}
    />
  );
};

export default CategorySelect;
