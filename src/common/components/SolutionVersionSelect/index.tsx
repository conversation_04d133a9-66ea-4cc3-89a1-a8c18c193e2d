import { listSolution, listSolutionVersion } from '@/common/api/common';
import { Cascader, message } from '@tencent/tea-component';
import React, { CSSProperties, useEffect, useState } from 'react';

export interface IProps {
  value?: string[];
  onChange?: (value: string[]) => void;
  style?: CSSProperties;
  disabled?: boolean;
}

const SolutionVersionSelect: React.FC<IProps> = ({ value = [], onChange, style, disabled = false }) => {
  const [data, setData] = useState<any[]>([]);
  useEffect(() => {
    Promise.all([listSolution(), listSolutionVersion({})]).then(([solutionRes, solutionVersionRes]) => {
      if (solutionRes.Error || solutionVersionRes.Error) {
        message.error({
          content: solutionRes.Error?.Message || solutionVersionRes.Error?.Message,
        });
      } else {
        setData(
          (solutionRes.ListSolutions || []).map((item) => ({
            label: `${item.Name}(${item.NameEN})`,
            value: item.UUID,
            isLeaf: false,
            children: (solutionVersionRes.ListSolutionVersions || [])
              .filter((version) => version.SolutionID === item.UUID)
              .map((version) => ({
                label: version.Code,
                value: version.UUID,
                isLeaf: true,
              })),
          })),
        );
      }
    });
  }, []);

  function handleChange(value) {
    onChange?.(value);
  }

  return (
    <Cascader
      data={data || []}
      type="menu"
      onChange={handleChange}
      multiple={false}
      searchable
      clearable
      valueRender={(options) => {
        const leaf = options[options.length - 1];
        return leaf?.label || leaf?.value;
      }}
      disabled={disabled}
      value={value || []}
      style={{ maxWidth: '100%', ...style }}
    />
  );
};

export default SolutionVersionSelect;
