/*
 * @Author: super<PERSON>
 * @Date: 2023-03-28 16:11:45
 * @LastEditors: superfeng
 * @LastEditTime: 2023-04-10 16:50:44
 * @Description: 请输入注释信息
 */
import React from 'react';
import styles from './index.module.less';
import { StatusTip } from '@tencent/tea-component';

export interface IProps {
  tip?: React.ReactNode;
  loading: boolean;
}

const Loading: React.FC<IProps> = ({ tip = '加载中', loading, children }) => (
  <div className={styles.loading}>
    {loading && (
      <div className={styles.loading__mask}>
        <StatusTip status="loading" loadingText={tip} className={styles.loading__tip} />
      </div>
    )}
    <div className={loading ? styles.loading__container : ''}>{children}</div>
  </div>
);
export default Loading;
