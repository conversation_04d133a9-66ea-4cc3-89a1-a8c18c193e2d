import {
  ListSolutionVersionArtifactBranches,
  ListSolutionVersionArtifactTagsSimple,
} from '@/common/api/requirementIteration';
import { Cascader, message } from '@tencent/tea-component';
import React, { CSSProperties, useEffect, useState } from 'react';

export interface IProps {
  value?: string[];
  onChange?: (value: string[]) => void;
  style?: CSSProperties;
  disabled?: boolean;
  solutionVersionID: string;
  arch: string;
}

const SolutionVersionArtifactTag: React.FC<IProps> = ({
  value = [],
  onChange,
  style,
  disabled = false,
  solutionVersionID,
  arch,
}) => {
  const [data, setData] = useState<any[]>([]);
  useEffect(() => {
    Promise.all([
      ListSolutionVersionArtifactBranches({ SolutionVersionID: solutionVersionID }),
      ListSolutionVersionArtifactTagsSimple({
        SolutionVersionID: solutionVersionID,
        Arch: arch,
      }),
    ]).then(([solutionArtifactBranchRes, solutionVersionArtifactTagsRes]) => {
      if (solutionArtifactBranchRes.Error || solutionVersionArtifactTagsRes.Error) {
        message.error({
          content: solutionArtifactBranchRes.Error?.Message || solutionVersionArtifactTagsRes.Error?.Message,
        });
      } else {
        setData(
          (solutionArtifactBranchRes.ListSolutionVersionArtifactBranches || []).map((item) => ({
            label: item.BranchName,
            value: item.UUID,
            isLeaf: false,
            children: (solutionVersionArtifactTagsRes.ListProductVersionArtifactTags || [])
              .filter((version) => version.ArtifactBranchUUID === item.UUID)
              .map((version) => ({
                label: version.TagNum,
                value: version.TagNum,
                isLeaf: true,
              })),
          })),
        );
      }
    });
  }, []);

  function handleChange(value) {
    onChange?.(value);
  }

  return (
    <Cascader
      data={data || []}
      type="menu"
      onChange={handleChange}
      multiple={false}
      searchable
      clearable
      valueRender={(options) => {
        const leaf = options[options.length - 1];
        return leaf?.label || leaf?.value;
      }}
      disabled={disabled}
      value={value || []}
      style={{ maxWidth: '100%', ...style }}
    />
  );
};

export default SolutionVersionArtifactTag;
