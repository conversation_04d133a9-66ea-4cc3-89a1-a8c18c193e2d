import { Button, Modal, ModalProps } from '@tencent/tea-component';
import React from 'react';

export interface IProModal extends ModalProps {
  onOk?: () => void;
  onCancel?: () => void;
  title: string;
  confirmLoading?: boolean;
  footer?: boolean | React.ReactNode;
}

const ProModal: React.FC<React.PropsWithChildren<IProModal>> = ({
  children,
  onOk,
  onCancel,
  title,
  confirmLoading,
  footer = true,
  ...props
}) => (
  <Modal {...props} onClose={onCancel} caption={title}>
    <Modal.Body>{children}</Modal.Body>

    {
      <Modal.Footer>
        {typeof footer !== 'boolean' && footer}
        {footer === true && (
          <>
            <Button type="primary" onClick={onOk} loading={confirmLoading}>
              确定
            </Button>
            <Button type="weak" onClick={onCancel}>
              取消
            </Button>
          </>
        )}
      </Modal.Footer>
    }
  </Modal>
);

export default ProModal;
