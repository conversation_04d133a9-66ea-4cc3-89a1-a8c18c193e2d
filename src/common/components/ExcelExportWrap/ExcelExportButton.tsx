import { TcsButton } from '@tencent/tcs-component';
import React, { useState } from 'react';
import { ExcelExportWrapRef } from '.';
import { ButtonProps, Tooltip } from '@tencent/tea-component';
import { useExcelExport } from './ExcelExportContext';

export interface ExcelExportButtonProps extends ButtonProps {
  fieldName?: string;
  text?: string;
  exportRef?: React.MutableRefObject<ExcelExportWrapRef>;
}

const ExcelExportButton: React.FC<ExcelExportButtonProps> = ({
  fieldName = 'export.xlsx',
  exportRef,
  text = '导出Excel',
  ...rest
}) => {
  const [loading, setLoading] = useState(false);
  const innerExportRef = useExcelExport();

  return (
    <Tooltip title="一次最多导出5000条数据">
      <TcsButton
        {...rest}
        loading={loading}
        onClick={() => {
          setLoading(true);
          const ref = exportRef?.current || innerExportRef;
          ref?.downloadExcel?.(fieldName).finally(() => {
            setLoading(false);
          });
        }}
      >
        {text}
      </TcsButton>
    </Tooltip>
  );
};

export default ExcelExportButton;
