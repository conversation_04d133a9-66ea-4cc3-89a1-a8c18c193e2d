import React, { useRef, useCallback, useImperativeHandle, forwardRef, useContext, useMemo } from 'react';
import { generateExcel } from '../../utils/excelUtils'; // 假设这是之前封装的导出Excel的函数
import { TcsContext } from '@tencent/tcs-component';
import { ExcelExportContext } from './ExcelExportContext';

export interface ExcelExportWrapRef {
  downloadExcel: (fieldName?: string) => Promise<void>;
}

const ExcelExportWrap = forwardRef<ExcelExportWrapRef, { children: React.ReactElement }>(({ children }, ref) => {
  const { dictList = [] } = useContext(TcsContext);
  // const [loading, setLoading] = useState(false);
  const lastRequestParams = useRef<any>();
  // 从TcsTable获取columns和request
  const { columns, request } = children.props;

  // 拦截请求函数以记录参数
  const handleRequest = useCallback(
    (params) => {
      lastRequestParams.current = params;
      return request(params);
    },
    [request],
  );

  // 导出Excel的方法
  const downloadExcel = useCallback(
    async (fieldName = 'export') => {
      // 使用上次请求的参数，但将分页参数设置为导出所需的值
      const params = {
        ...lastRequestParams.current,
        current: 1,
        pageSize: 5000,
      };
      const response = await request(params);
      if (response.success !== false) {
        await generateExcel({
          columns,
          tableData: response.data,
          fileName: `${fieldName}.xlsx`,
          dataDictionary: dictList,
        });
      }
    },
    [columns, dictList, request],
  );

  // 使用useImperativeHandle来暴露downloadExcel方法
  useImperativeHandle(ref, () => ({
    downloadExcel,
  }));

  // 克隆TcsTable并替换request属性
  const clonedChildren = React.cloneElement(children, {
    request: handleRequest,
  });

  // 使用useMemo来确保context值的稳定性
  const contextValue = useMemo(() => ({ downloadExcel }), [downloadExcel]);

  return <ExcelExportContext.Provider value={contextValue}>{clonedChildren}</ExcelExportContext.Provider>;
});

export default ExcelExportWrap;
