/* eslint-disable no-param-reassign */
/*
 * @Author: superfeng
 * @Date: 2023-03-15 17:45:17
 * @LastEditors: superfeng
 * @LastEditTime: 2023-07-03 17:22:28
 * @Description: 请输入注释信息
 */
import React, { PropsWithChildren, useState, useMemo, useRef, useEffect } from 'react';
import RcResizeObserver from 'rc-resize-observer';
import ProForm from '../ProFrom';
import { Button, Col, Icon } from '@tencent/tea-component';
import styles from './index.module.less';

export interface IProps {
  initialValues?: any;
  onSearch: (values: any) => void;
  defaultSpread?: boolean;
  proFormRef?: any;
}

const BREAKPOINTS = {
  defaults: [
    [0, 1, 'xs'],
    [768, 2, 'sm'],
    [1220, 3, 'md'],
    [Infinity, 4, 'lg'],
  ],
};

// const COL_SPANS = BREAKPOINTS.defaults.reduce((res, item) => {
//   res[item[2]] = 24 / item[1];
//   return res;
// }, {} as any);

const QueryFilter: React.FC<PropsWithChildren<IProps>> = ({
  initialValues,
  onSearch,
  children,
  defaultSpread = false,
  proFormRef,
}) => {
  const [width, setWidth] = useState(0);
  const [isSpread, setIsSpread] = useState(defaultSpread);
  const formRef = useRef(null);

  const span = useMemo(() => {
    for (const item of BREAKPOINTS.defaults) {
      if ((item[0] as number) >= width) {
        return 24 / (item[1] as number);
      }
    }
    return 1;
  }, [width]);

  const [showItems, hiddenItems] = useMemo(() => {
    const showItems: any[] = [];
    const hiddenItems: any[] = [];
    if (!isSpread) {
      let totalSpan = 0;
      React.Children.forEach(children, (child) => {
        if (React.isValidElement(child)) {
          if (totalSpan < 24 - span) {
            showItems.push(
              React.cloneElement(child, {
                colProps: {
                  span,
                },
              } as any),
            );
            totalSpan += span;
          } else {
            hiddenItems.push(
              React.cloneElement(child, {
                hidden: true,
              } as any),
            );
          }
        }
      });
    } else {
      React.Children.forEach(children, (child) => {
        if (React.isValidElement(child)) {
          showItems.push(
            React.cloneElement(child, {
              colProps: {
                span,
              },
            } as any),
          );
        }
      });
    }
    return [showItems, hiddenItems];
  }, [children, span, isSpread]);

  const childrenSize = React.Children.count(
    React.Children.toArray(children).filter((item) => React.isValidElement(item)),
  );
  const len = showItems.length;
  const isShowSpread = childrenSize * span >= 24;
  const buttonEl = useMemo(() => {
    // 只有一行显示，
    let buttonSpan = 24;
    if (len * span < 24) {
      buttonSpan = 24 - len * span;
    } else {
      const lastSpan = (len * span) % 24;
      if (lastSpan === 0) {
        buttonSpan = 24;
      } else {
        buttonSpan = 24 - lastSpan;
      }
    }

    function handleSpread() {
      setIsSpread((spread) => !spread);
    }

    function handleFinish() {
      formRef.current?.validateFields()?.then((values) => {
        onSearch?.(values);
      });
    }

    function handleReset() {
      formRef.current?.resetFields();
      onSearch?.(formRef.current.getFieldsValue());
    }

    return (
      <Col span={buttonSpan}>
        <div className={styles.query_filter__buttons}>
          <Button type="primary" htmlType="submit" className={styles.query_filter__button} onClick={handleFinish}>
            搜索
          </Button>
          <Button className={styles.query_filter__button} htmlType="button" onClick={handleReset}>
            重置
          </Button>
          {isShowSpread && (
            <Button type="link" onClick={handleSpread} htmlType="button" className={styles.query_filter__button}>
              {isSpread ? <Icon type="arrowup" /> : <Icon type="arrowdown" />}
              {isSpread ? '收起' : '展开'}
            </Button>
          )}
        </div>
      </Col>
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [len, span, isSpread, isShowSpread]);

  useEffect(() => {
    if (proFormRef) {
      proFormRef.current = formRef.current;
    }
  }, [proFormRef]);

  return (
    <RcResizeObserver
      key="queryFilter"
      onResize={(offset) => {
        if (width !== offset.width && offset.width > 17) {
          setWidth(offset.width);
        }
      }}
    >
      <ProForm submitter={false} initialValues={initialValues} grid={true} formRef={formRef}>
        {showItems}
        {hiddenItems}
        {buttonEl}
      </ProForm>
    </RcResizeObserver>
  );
};

export default QueryFilter;
