/*
 * @Author: superfeng
 * @Date: 2023-03-17 17:00:15
 * @LastEditors: superfeng <EMAIL>
 * @LastEditTime: 2023-07-17 20:06:24
 * @Description: 请输入注释信息
 */
import React, { useEffect, useRef } from 'react';
import * as ReactDOM from 'react-dom';
import { createEditor, defaultConfig } from '@tencent/exeditor3-preset-editor';
import { PicturePlugin } from '@tencent/exeditor3-plugin-picture';
import { uploadFile } from '@/common/api/uploadFile';

import styles from './index.module.less';
import { ImagePreview } from '@tencent/tea-component';

export interface IProps {
  value?: string;
  placeholder?: string;
  onChange?: (value) => void;
  readonly?: boolean;
  height?: number | string;
  minHeight?: number | string;
}

const ExEditor: React.FC<IProps> = ({
  value,
  placeholder = '请输入',
  onChange,
  readonly,
  height = 'auto',
  minHeight = 100,
}) => {
  const editorDomRef = React.useRef<HTMLDivElement>(null);
  const toolbarDomRef = React.useRef<HTMLDivElement>(null);
  const editor = useRef<any>(null);
  // const isTriggerChange = useRef(false);

  useEffect(() => {
    const imagePlugin = new PicturePlugin({
      accept: 'image/*',
      editable: !readonly,
      inline: false,
      hasTitle: false,
      upload: (file) =>
        uploadFile({
          fileName: (file as File).name,
          file,
        }),
    });

    imagePlugin.editView = function (node, getPos) {
      if (node.attrs.uploading) {
        return this.editPlaceholderView(node, getPos);
      }
      if (readonly) {
        const dom = document.createElement('div');
        dom.style.outline = 'none';
        ReactDOM.render(
          <ImagePreview previewSrc={node.attrs.info.src} maskClosable>
            {(open) => (
              <img
                src={node.attrs.info.src}
                style={{ maxWidth: '100%', width: node.attrs.info.width || '100%' }}
                onClick={open}
              />
            )}
          </ImagePreview>,
          dom,
        );
        return {
          dom,
        };
      }
      const image = document.createElement('img');
      image.style.width = node.attrs.info.width || '100%';
      image.style.maxWidth = '100%';
      image.src = node.attrs.info.src;
      const dom = document.createElement('div');
      dom.appendChild(image);
      dom.style.outline = 'none';
      return {
        dom,
      };
    };
    editor.current = createEditor({
      root: editorDomRef.current,
      toolbarRoot: toolbarDomRef.current,
      toolbar: [
        'clearFormat paintFormat heading headingNumbering fontFamily fontSize bold italic underline strikethrough superScript subScript color backgroundColor link quote bulletList orderList hr codeBlock picture table textAlign textIndent marginTop marginBottom marginSide lineSpace wordSpace paragraphCheck undo redo marginLeftSide',
      ],
      placeholder,
      plugins: [...defaultConfig.plugins, imagePlugin],
    });

    editor.current.on(
      'idlechange',
      (e) => {
        if (e.docChanged) {
          // isTriggerChange.current = true;
          onChange?.(editor.current.getHTML());
        }
      },
      [],
    );
    // 如果只读状态，需要手动给a标签监听onClick事件，用于跳转
    if (readonly) {
      editorDomRef.current?.addEventListener('click', (e) => {
        if (e.target.nodeName === 'A') {
          const href = e.target.getAttribute('href');
          if (href) {
            window.open(href, '_blank');
          }
        }
      });
    }

    return () => {
      editor.current.destroy();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [placeholder, readonly]);

  useEffect(() => {
    if (editor.current) {
      editor.current.setReadOnly(!!readonly);
      toolbarDomRef.current!.style.display = !!readonly ? 'none' : 'block';
    }
  }, [readonly, editor]);

  useEffect(() => {
    if (editor.current && value && editor.current.getHTML() !== value) {
      editor.current.setHTML(value);
    }
  }, [value, readonly]);

  return (
    <>
      <div style={{ border: '1px solid #cfd5de' }}>
        <div ref={toolbarDomRef} />
        <div className={styles.custom_ex_editor}>
          <div className={styles.custom_ex_editor__container} style={{ height, minHeight }} ref={editorDomRef} />
        </div>
      </div>
    </>
  );
};

export default ExEditor;
