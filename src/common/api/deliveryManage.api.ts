/*
 * @Author: lucyfang
 * @Date: 2025-02-13 17:24:01
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-03-11 15:26:50
 * @Description: 请输入注释信息
 */
import { TApiAuroraServices, TApiDataEngine } from '@tencent/tcsc-base';
import { IDeliveryInfo } from './deliveryManage';

export interface UpdateDeliverySiteRelParams {
  DeliveryID?: string;
  SiteUUID?: string;
  Variable?: string;
  ID?: number;
}

export interface GenerateWorkflowInstanceParams {
  WorkflowUUID: string;
  WorkflowTagNum?: number;
  GlobalValues: {
    DeliveryUUID: string;
    [Key: string]: any;
  };
}

export interface GenerateWorkflowInstanceResponse {
  WorkflowInstanceID: number;
  WorkflowID: number;
  RequestId: string;
}

export class DeliveryManageApi {
  @TApiAuroraServices()
  static UpdateDeliveryInfo(
    _params: Partial<IDeliveryInfo & { DeliveryID: string }>,
    _res: any,
  ): Promise<{ UUID: string }> {
    return _res;
  }

  @TApiDataEngine({
    paramModelName: 'DeliverySiteRel',
    paramConversion: {
      useEqualFields: ['DeliveryID', 'SiteUUID', 'ID'],
      unConversionFields: ['Variable'],
    },
  })
  // 更新交付单关联局点信息
  static UpdateDeliverySiteRel(_params: UpdateDeliverySiteRelParams, _res?: Promise<any>): Promise<any> {
    return _res!;
  }

  @TApiAuroraServices({})
  static GenerateWorkflowInstance(
    _params: GenerateWorkflowInstanceParams,
    _res?: Promise<GenerateWorkflowInstanceResponse>,
  ): Promise<GenerateWorkflowInstanceResponse> {
    return _res!;
  }
}
