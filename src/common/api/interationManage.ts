/*
 * @Author: superfeng
 * @Date: 2023-03-17 15:07:19
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-05-29 15:03:01
 * @Description: 请输入注释信息
 */
import { PackageTestStatus } from '../constants';
import { requestAurora<PERSON>pi, requestCloud<PERSON>pi, toListParamsCApi } from './api';
import { TApiAuroraServices } from '@tencent/tcsc-base';

// 缺陷单关联应用
export interface IPropsDevApplicationPackage {
  ApplicationVersion: string;
  Arch: string;
  BuildTime: string;
  CommitID: string;
  UUID: string;
}

export interface IPropsReleaseApplicationPackage {
  ApplicationVersion: string;
  Arch: string;
  BuildTime: string;
  CommitID: string;
  UUID: string;
}

export interface IPropsIssueAppRel {
  ApplicationBranchUUID: string;
  ApplicationName: string;
  ApplicationType: string;
  Arch: string;
  DevApplicationPackage: IPropsDevApplicationPackage;
  DevPackageUUID: string;
  IssueSolutionRelUUID: string;
  ProductVersion: string;
  ProductVersionUUID: string;
  ReleaseApplicationPackage: IPropsReleaseApplicationPackage;
  ReleasePackageUUID: string;
  Status: string;
  UUID: string;
}

// 缺陷单关联解决方案
export interface IPropsIssueSolutionRel {
  Arch: string;
  CreatedAt: string;
  Creator: string;
  IssueAppRel: IPropsIssueAppRel[];
  IssueID: string;
  IssueType: string;
  OperationSheetUUID: string;
  SolutionVersion: string;
  SolutionVersionUUID: string;
  Status: string;
  TapdUrl: string;
  UUID: string;
  DeliveryUUID: string;
  DeliverType: string;
  SolutionVersionDetail: SolutionVersionDetail;
  DevApplicationPackage?: {
    ApplicationVersion: string;
    BuildTime: string;
    CommitID: string;
    Arch: string;
  };
  ReleaseApplicationPackage?: {
    ApplicationVersion: string;
    BuildTime: string;
    CommitID: string;
    Arch: string;
  };
}
// 解决方案
export interface SolutionVersionDetail {
  Code: string;
  ID: number;
  Solution: Solution;
  UUID: string;
}
export interface Solution {
  Code: string;
  ID: number;
  Name: string;
  UUID: string;
  NameEN: string;
}

// 缺陷单
export interface IPropsBug {
  Arch: string;
  Cause: string;
  CreatedAt: string;
  Creator: string;
  DeliverOwners: string;
  DeliverType: string;
  Description: string;
  DevOwners: string;
  Disabled: boolean;
  ID: string;
  IssueID: string;
  IsTestFree: boolean;
  IterationUUID: string;
  IssueSolutionRel: IPropsIssueSolutionRel[];
  Owner: string;
  ParentStoryUUID: string;
  Priority: string;
  ProcedureUUID: string;
  SeverityLevel: string;
  SolutionVersion: string;
  SolutionVersionUUID?: string;
  Source: string;
  Status: string;
  TapdUrl: string;
  TestOwners: string;
  Title: string;
  UUID: string;
  UpdateAt: string;
  IssueTemplateUUID?: string;
}

export interface ICreateBugParams {
  // 发现架构
  Arch: string;
  // 交付同学
  DeliverOwners: string;
  DeliverType: '仅后续新增出包' | '指定客户出包' | '存量及新增客户出包';
  // 内容描述
  Description: string;
  // 研发负责人
  DevOwners: string;
  // 是否免测
  IsTestFree: false;
  // 处理人 分号分割
  Owner: string;
  // 优先级
  Priority: string;
  // 缺陷单模板，定义缺陷单类型
  ProcedureUUID: string;
  // 严重程度
  SeverityLevel: string;
  // 发现版本
  SolutionVersion: string;
  // 问题来源 内部发现或者客户名称
  Source: string;
  // 测试同学
  TestOwners: string;
  // 标题
  Title: string;
}

// 应用所属产品及解决方案版本;
export interface ISolutionProductAppRels {
  // 解决方案
  SolutionName: string;
  // 解决方案版本名
  SolutionVersionName: string;
  // 解决方案版本uuid，对应表solution_version表中的uuid
  SolutionVersionUUID: string;
  // 子产品名，对应表product_info表中的code字段
  ProductCode: string;
  // 子产品版本名，对应表product_version表中的name字段
  ProductVersionName: string;
  // 子产品版本记录，对应product_version表的uuid
  ProductVersionUUID: string;
  // 应用版本名，对应application_branch表的branch_name字段
  ApplicationBranchName: string;
  // 应用版本记录，对应application_branch表中的uuid
  ApplicationBranchUUID: string;
  // 应用名称，对应application表中的applicaiton_name字段
  ApplicationName: string;
  // 应用版本支持的架构，对应arch表中的arch_name字段
  Archs: string[];
  SolutionVersion: any;
}

// 变更历史
export interface IIssueChangeHistories {
  CreateAt: string;
  Creator: string;
  // 变更方式，手动或者系统自动变更对应数据字典 IssueUpdateMethod
  UpdateMethod: string;
  // 变更类型, UpdateType对应数据字典 IssueUpdateType
  OperationType: string;
  // 变更内容
  UpdateEntity: string;
  UpdateColumns: string[];
  // 富文本对应字段
  RichTextColumns: string[];
  IsRichText: boolean;
  OriginValue: string[];
  NewValue: string[];
}

export interface IIssueAttachment {
  CreatedAt: string;
  Creator: string;
  DeletedAt: number;
  DownloadUrl: string;
  ID: number;
  IssueID: string;
  Name: string;
}

export interface IIssueCommentReplies {
  ID: number;
  // 一级评论的UUID
  MainCommentUUID: string;
  // 回复信息的UUID
  ReplyCommentUUID: string;
}

export interface IIssueComment {
  ID: number;
  UUID: string;
  //  标记是否为一级评论，true 一级评论｜ false 评论信息的回复
  IsTop: boolean;
  // 如果 IsTop=false，则此字段表示当前回复的评论UUID
  ParentCommentUUID: string;
  IssueID: string;
  // 评论详情，富文本内容
  Description: string;
  Creator: string;
  CreatedAt: string;
  UpdateAt: string;
  DeletedAt: number;
  // 回复信息关联表
  Replies: IIssueCommentReplies[];

  // 前端独有参数
  children?: IIssueComment[];
}

export interface GetComponentsNextParams {
  TapdWorkspaceID: string;
}

export interface ApplicationItem {
  AppName: string;
  ComponentName: string;
}

export interface GetComponentsNextResponse {
  Applications: ApplicationItem[];
}

export interface ListSolutionVersionAndArchParams {
  ApplicationList: Array<{ ComponentName?: string; ApplicationName: string }>;
  TAPDWorkspaceID: string;
}

export interface SolutionVersionArchItem {
  // 根据实际返回数据结构补充具体字段
  [key: string]: any;
}

export interface ListSolutionVersionAndArchResponse {
  SolutionVersionArchs: SolutionVersionArchItem[];
}

// 获取缺陷单模板
export const listTemplate = (params: { IssueType: string }) =>
  requestCloudApi<{ ListIssueTemplates: any }>('ListIssueTemplates', {
    IssueTemplate: toListParamsCApi(params, {
      useEqFields: ['IssueType'],
    }),
  });

// 同步tapd单
export const syncTapdWithBug = (params: { TapdUrl: string }) => requestAuroraApi('CreateBug', { Bug: params });
export const syncTapdWithStory = (params: { TapdUrl: string }) => requestAuroraApi('CreateStory', { Story: params });

// 手动创建缺陷单
export const createBug = (params: ICreateBugParams) => requestAuroraApi<any>('CreateBug', { Bug: params });
// 手动创建需求单
export const createStory = (params: ICreateBugParams) => requestAuroraApi<any>('CreateStory', { Story: params });

// 修改缺陷单
export const updateBug = (params: ICreateBugParams) => requestAuroraApi<any>('UpdateBug', { Bug: params });
// 修改需求单
export const updateStory = (params: ICreateBugParams) => requestAuroraApi<any>('UpdateStory', { Story: params });

// 获取缺陷单详情
export const getBugWithDetail = (params: { IssueID: string }) =>
  requestCloudApi<{ GetBug: IPropsBug }>('GetBugWithDetail', {
    Bug: toListParamsCApi(params, {
      useEqFields: ['IssueID'],
    }),
  });

// 获取需求单详情
export const getStoryWithDetail = (params: { IssueID: string }) =>
  requestCloudApi<{ GetStory: IPropsBug }>('GetStoryWithDetail', {
    Story: toListParamsCApi(params, {
      useEqFields: ['IssueID'],
    }),
  });

// 查询缺陷列表
export const listBugs = (params: {
  IssueType: string;
  RelatedToMe?: boolean;
  PageNo: number;
  PageSize: number;
  RelatedSolutionVersion?: string;
  FilterExpr?: any[];
}) => requestAuroraApi<{ Items: any[] }>('ListBugsWithDetail', params);

// 查询需求或缺陷
export const ListIteration = (
  params: {
    TAPD?: string;
    IssueType: string;
    RelatedToMe?: boolean;
    PageNo: number;
    PageSize: number;
    RelatedSolutionVersion?: string;
    CategoryID?: string;
    RelatedApplications?: string[];
    FilterExpr?: any[];
    TenantUUID: string;
    IterationUUID?: string;
    ReleaseID?: string;
  },
  path,
) => {
  if (path === 'defect') return requestAuroraApi<{ Items: any[] }>('ListBugsWithDetail', params);
  return requestAuroraApi<{ Items: any[] }>('ListStoriesWithDetail', params);
};

// 获取当前租户下的所有应用列表
export const listAllApplicationNames = () =>
  requestAuroraApi<{ ApplicationNames: string[] }>('ListAllApplicationNames');

// 查询应用所属产品及解决方案版本;
export const getAppSolutionProductRel = (params: { ApplicationNames: string[]; SolutionVersionUUID?: string }) =>
  requestAuroraApi<{ SolutionProductAppRels: ISolutionProductAppRels[] }>('GetAppSolutionProductRel', params);

// 创建缺陷评估组件
export const createIssueRelatedApps = (params: {
  IssueApps: any[];
  Sites: Array<{
    SolutionVersionUUID: string;
    Arch: string;
    SiteUUIDs: string[];
    DeliverType: string;
  }>;
}) => requestAuroraApi('CreateIssueRelatedApps', params);

// 解除评估组件关联
export const deleteIssueRelatedApp = (params: {
  RelatedApplicationUUIDs: string[];
  IssueID: string;
  IssueType: string;
}) => requestAuroraApi('DeleteIssueRelatedApp', params);

// 获取评估组件列表
export const listIssueSolutionRelsWithDetail = (params: { IssueID: string; IssueType: string }) =>
  requestCloudApi<{ ListIssueSolutionRels: IPropsIssueSolutionRel[] }>('ListIssueSolutionRelsWithDetail', {
    IssueSolutionRel: toListParamsCApi(params, {
      useEqFields: ['IssueID', 'IssueType'],
    }),
  });

// 召回交付单
export const recallDeliveryInfo = (params: { DeliveryID: string }) => requestAuroraApi('RecallDeliveryInfo', params);

// 查询缺陷单变更历史
export const listIssueChangeHistories = (params: { IssueID: string }) =>
  requestCloudApi<{ ListIssueChangeHistories: IIssueChangeHistories[] }>('ListIssueChangeHistories', {
    IssueChangeHistory: toListParamsCApi(params, {
      useEqFields: ['IssueID'],
      page: false,
      order: [{ Key: 'CreatedAt', Sort: 'DESC' }],
    }),
  });

// 创建缺陷单附件
export const createIssueAttachment = (params: { DownloadUrl: string; IssueID: string; Name: string }) =>
  requestAuroraApi<{ Attachment: IIssueAttachment }>('CreateIssueAttachment', {
    Attachment: params,
  });

// 删除缺陷单附件
export const deleteIssueAttachment = (params: { AttachmentID: number }) =>
  requestAuroraApi('DeleteIssueAttachment', params);

// 查询缺陷单附件列表
export const listIssueAttachments = (params: { IssueID: string; DeletedAt: number }) =>
  requestCloudApi<{ ListIssueAttachments: IArguments[] }>('ListIssueAttachments', {
    IssueAttachment: toListParamsCApi(params, {
      useEqFields: ['IssueID', 'DeletedAt'],
    }),
  });

// 查询缺陷单评论列表
export const listIssueComments = (params: { IssueID: string }) =>
  requestCloudApi<{ ListIssueComments: IIssueComment[] }>('ListIssueComments', {
    IssueComment: toListParamsCApi(
      {
        ...params,
        DeletedAt: 0,
      },
      {
        useEqFields: ['IssueID', 'DeletedAt'],
        order: [
          {
            Key: 'CreatedAt',
            Sort: 'DESC',
          },
        ],
      },
    ),
  });

// 添加缺陷单评论
export const createIssueComment = (params: Partial<IIssueComment>) =>
  requestAuroraApi('CreateIssueComment', {
    Comment: params,
  });

// 更新缺陷单评论
export const updateIssueComment = (params: { CommentID: number; Description: string }) =>
  requestAuroraApi('UpdateIssueComment', params);

// 珊瑚缺陷单评论
export const deleteIssueComment = (params: { CommentID: number }) => requestAuroraApi('DeleteIssueComment', params);

// 修改修改缺陷单出包类型
export const updateIssueSolutionArchDeliveryType = (params: { UUID: string; DeliverType: string }) =>
  requestAuroraApi('UpdateIssueSolutionArchDeliveryType', params);

// 获取需求单下所有的缺陷单

// 查询缺陷单评论列表
export const listStoryToBugs = (params: { ParentStoryIssueID: string }) =>
  requestCloudApi<{ ListBugs: any[] }>('ListBugs', {
    Bug: toListParamsCApi(params, {
      useEqFields: ['ParentStoryIssueID'],
    }),
  });

//	获取需求/缺陷单元数据列表

export const ListIssueMetadatasWithDetail = (params: { IssueSolutionRelUUID: string }) =>
  requestCloudApi<{ ListIssueMetadatas: any[] }>('ListIssueMetadatas', {
    IssueMetadata: toListParamsCApi(params, {
      useEqFields: ['IssueSolutionRelUUID'],
    }),
  });

// 废弃元数据

export const DeleteIssueMetadata = (params: { IssueMetadataUUID: string }) =>
  requestAuroraApi('DeleteIssueMetadata', params);
// CreateIssueMetadata
export const CreateIssueMetadata = (params: {
  IssueID: string;
  MetadataKey: string;
  IssueSolutionRelUUID: string;
  MetadataValue: any;
}) => requestAuroraApi('CreateIssueMetadata', { IssueMetadata: params });

// UpdateIssueMetadata

export const UpdateIssueMetadata = (params: { IssueMetadataUUID: string; MetadataValue: any }) =>
  requestAuroraApi('UpdateIssueMetadata', params);

// 获取应用与问题单关联的合并请求列表(GetIssueAPPRelatedMRList)
export const getIssueAPPRelatedMRList = (params: { IssueID: string; ApplicationBranchUUID: string }) =>
  requestAuroraApi<{ MRList: any[] }>('GetIssueAPPRelatedMRList', params);
// 获取制品包
export const ListApplicationPackages = (params: {
  ApplicationBranch: string;
  ApplicationUUID: string;
  CodeBranch?: string;
  CodeCommitter?: string;
  Arch: string;
  PackageType: number;
  pageNo?: number;
  pageSize?: number;
  // 验证状态，不传的话获取待验证的数据
  Status?: PackageTestStatus;
}) => requestAuroraApi<{ ApplicationPackages: any }>('ListApplicationPackages', params);

// 获取单关联的所有制品
export const ListApplicationPackageIssueRels = (params: { IssueID: string; ApplicationBranchUUID: string }) =>
  requestCloudApi<{ ListApplicationPackageIssueRels: any[] }>('ListApplicationPackageIssueRelsWithDetail', {
    ApplicationPackageIssueRel: {
      _Filter: [{ Key: 'IssueID', Op: '=', Value: params.IssueID }],
      ApplicationPackage: {
        _Filter: [
          {
            Key: 'ApplicationBranchUUID',
            Op: '=',
            Value: params.ApplicationBranchUUID,
          },
        ],
      },
    },
  });

// 单子关联制品
export const LinkApplicationPackageToIssues = (params: {
  IssueIDs: string[];
  IssueType: string;
  PackageUUID: string;
  Status?: PackageTestStatus;
}) => requestAuroraApi<{ Msg: string }>('LinkApplicationPackageToIssues', params);

// 解除关联

export const UnlinkApplicationPackageFromIssues = (params: { IssueIDs: string[]; PackageUUID: string }) =>
  requestAuroraApi<{ Msg: string }>('UnlinkApplicationPackageFromIssues', params);

// 查询指定工单上和应用关联的所有制品列表
export const listApplicationPackageIssueRels = (params: {
  IssueID: string;
  ApplicationBranchUUID: string;
  Arch: string;
}) => requestAuroraApi<{ Items: any[] }>('ListApplicationPackageIssueRels', params);

// 修改关联到指定工单下的制品验收状态
export const updateApplicationPackageIssueStatus = (params: {
  PackageUUID: string;
  IssueID: string;
  Status: string;
  Comments: string;
}) => requestAuroraApi('UpdateApplicationPackageIssueStatus', params);

// 查询出包验证记录
export namespace ListPackageVerifications {
  export interface IResponseData {
    ClientName: string;
    CreatedAt: string;
    Creator: string;
    DeletedAt: number;
    Description: string;
    ID: number;
    MaterialURL: string;
    PackedAt: string;
    SiteName: string;
    SiteUUID: string;
    SourceKey: string;
    SourceType: string;
    Status: string;
    UpdatedAt: string;
    WorkflowID: number;
    WorkflowInstanceID: number;
  }

  export const requestData = (params: { SourceKey: string }) =>
    requestCloudApi<{ ListPackageVerifications: IResponseData[] }>('ListPackageVerifications', {
      PackageVerification: toListParamsCApi(params, {
        useEqFields: ['SourceKey'],
        page: false,
      }),
    });
}

export class GeneratedApi {
  @TApiAuroraServices({})
  static ListBugfixApplications(
    _params: GetComponentsNextParams,
    _res?: Promise<GetComponentsNextResponse>,
  ): Promise<GetComponentsNextResponse> {
    return _res!;
  }
  @TApiAuroraServices({})
  static ListSolutionVersionArchsForApp(
    _params: ListSolutionVersionAndArchParams,
    _res?: Promise<ListSolutionVersionAndArchResponse>,
  ): Promise<ListSolutionVersionAndArchResponse> {
    return _res!;
  }
}
