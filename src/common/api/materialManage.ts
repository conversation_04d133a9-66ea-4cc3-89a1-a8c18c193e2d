import { request<PERSON>urora<PERSON><PERSON>, requestCloud<PERSON>pi, toListParamsCApi } from './api';
import { TApiAuroraServices, TApiDataEngine } from '@tencent/tcsc-base';
export interface SetSiteVariableParams {
  SiteUUID: string;
  SiteVariableList: {
    Key: string;
    Value: string;
  }[];
}
export interface GetSiteArtifactAvailableArchRequest {
  SiteUUID?: string;
}
export interface GetSolutionVersionArtifactTagParams {
  ID: string;
}

export interface GetSiteArtifactAvailableArchResponse {
  WorkspaceActive: boolean;
  IsEnableCommitExist: boolean;
  IsLatestEnableCommitChanged: boolean;
  ArchList: string[];
  LastBackfillUUID: string;
  SolutionVersionUUID: string;
  SiteBackfillList: {
    TagDescription: string;
    UUID: string;
    Time: string;
  }[];
}

export interface AppArtifactModify {
  ApplicationWorkspaceID: number;
  Arch: string;
  ApplicationPackageID: number;
}

export interface ModifyAppArtifactVersionRequest {
  SiteUUID?: string;
  AppArtifactModifyList: AppArtifactModify[];
}

export interface GenerateSiteAppArtifactInfoRequest {
  SiteUUID: string;
  ArtifactVersionBackfillUUID: string;
  UsePackageHistory?: boolean;
  Arch: string[];
  KeepManualModification?: boolean;
}
export interface ListApplicationPackagesParams {
  ApplicationUUID?: string[];
  Arch?: string;
}
export interface ListOperationSiteAppArtifactWorkspacesParams {
  SiteUUID?: string;
  Arch?: string;
  IsContained?: string;
}
export interface ProjectSiteArtifactVersionBackfill {
  SiteUUID?: string;
}
export interface ListSiteAppArtifactHistoryTagsParams {
  SiteUUID: string;
  Arch: string;
}
export interface ListSiteAppArtifactHistoryTagsResponse {
  CreatedAt: string;
  ID: number;
  TagNum: number;
  TagDescription: string;
  UUID: string;
  Arch: string;
}

export interface GetSiteAppArtifactHistoryTagParams {
  ID?: string;
}
// 获取产品列表树
export const getProductList = (params: { Name?: string }) =>
  requestAuroraApi<{ ProductList: any[] }>('GetProductList', params);

export const getSiteProductList = (params: { Name?: string; SiteUUID?: string }) =>
  requestAuroraApi<{ ProductList: any[] }>('GetSiteProductList', params);

// 获取解决方案版本树
export const getSolutionList = (params: { Name?: string; SiteUUID?: string }) =>
  requestAuroraApi<{ SolutionList: any[] }>('GetSolutionList', params);
// 获取解决方案版本树
export const getSiteSolutionList = (params: { Name?: string; SiteUUID?: string }) =>
  requestAuroraApi<{ SolutionList: any[] }>('GetSiteSolutionList', params);
// 获取物料列表
export const getMaterialList = (params: {
  Status?: string;
  ProductVersionUUID?: string;
  SolutionVersionUUID?: string;
  Type: string;
  Limit: number;
  Offset: number;
  Name?: string;
  Arch?: string;
  LabelName?: string;
  Version?: string;
}) => requestAuroraApi<{ MaterialList: any[] }>('GetMaterialList', params);

// 查询物料标签列表
export const listMaterialLabels = () =>
  requestCloudApi<{ ListMaterialLabels: any[] }>('ListMaterialLabels', {
    MaterialLabel: toListParamsCApi(
      {
        TenantUUID: (window as any).jiguang_currentNs,
      },
      {
        useEqFields: ['TenantUUID'],
        order: [
          {
            Sort: 'DESC',
            Key: 'CreatedAt',
          },
        ],
      },
    ),
  });

// 下载物料包
export const downloadMaterialPackage = (params: {
  MaterialInfoUUID: string;
  ProductVersionUUID?: string;
  SolutionVersionUUID?: string;
}) => requestAuroraApi<{ DownloadURL: string }>('DownloadMaterialPackage', params);

// 废弃物料包
export const abandonedPackage = (params: { MaterialInfoUUID: string }) => requestAuroraApi('AbandonedPackage', params);

// 发布物料包
export const publishMaterial = (params: { MaterialInfoUUID: string }) => requestAuroraApi('PublishMaterial', params);

// 发布物料包
export const unpublishedMaterial = (params: { MaterialInfoUUID: string }) =>
  requestAuroraApi('UnpublishedMaterial', params);

// 获取原始物料下载地址
export const downloadOriginalPackage = (params: { MaterialInfoUUID: string }) =>
  requestAuroraApi<{ DownloadUrl: string }>('DownloadOriginalPackage', params);

// 更新物料
export const updateMaterialPackageInfo = (params: any) =>
  requestAuroraApi<{ MaterialUUID: string }>('UpdateMaterialPackageInfo', params);

// 获取可推送的局点列表
export const getPushableSite = (params: { Status?: string; MaterialInfoUUIDList: string[] }) =>
  requestAuroraApi<{ SiteInfoList: any }>('GetPushableSite', params);

// 推送
export const releasePackage = (params: any) => requestAuroraApi('ReleasePackage', params);

// 查询局点列表
export const listOperationSiteList = (params: any) =>
  requestAuroraApi<{ SiteList: any[] }>('ListOperationSiteList', params);

// 获取局点详情
export const getOperationSiteDetail = (params: any) =>
  requestAuroraApi<{ Site: any }>('GetOperationSiteDetail', params);

// 查询局点出包记录
export const listOperationHistories = (params: {
  SiteUUID: string;
  pageSize: number;
  current: number;
  [key: string]: any;
}) =>
  requestCloudApi<{ ListOperationHistories: any[] }>('ListOperationHistories', {
    OperationHistory: toListParamsCApi(params, {
      useEqFields: ['SiteUUID'],
      order: [
        {
          Key: 'CreatedAt',
          Sort: 'DESC',
        },
      ],
      retainEmptyString: false,
    }),
  });

export const getOperationPushedPackageDetail = (params: { SiteUUID: string; TagType: string }) =>
  requestCloudApi<{ GetOperationPushedPackage: any }>('GetOperationPushedPackageDetail', {
    OperationPushedPackage: toListParamsCApi(params, {
      useEqFields: ['SiteUUID', 'TagType'],
      order: [
        {
          Key: 'CreatedAt',
          Sort: 'DESC',
        },
      ],
    }),
  });

// 获取局点对应物料清单
export const getSiteMaterialList = (params: {
  Offset: number;
  Limit: number;
  Name?: string;
  Arch?: string;
  Type: string;
  SiteUUID: string;
}) => requestAuroraApi<{ MaterialList: any[] }>('GetSiteMaterialList', params);

export const updateSiteMaterialStatus = (params: {
  SiteUUID: string;
  MaterialInfoUUID: string;
  Status: string;
  ProductVersionUUID?: string;
  SolutionVersionUUID?: string;
}) => requestAuroraApi<any>('UpdateSiteMaterialStatus', params);

export const getOTACertificate = (params: { SiteUUID: string }) =>
  requestAuroraApi<{ Certificate: any; Exist: boolean }>('GetOTACertificate', params);

export const signOTACertificate = (params: { SiteUUID: string }) => requestAuroraApi('SignOTACertificate', params);

export const listMaterialSolutionVersionRelDetails = (params: { MaterialInfoUUID: string }) =>
  requestCloudApi<{ ListMaterialSolutionVersionRels: any[] }>('ListMaterialSolutionVersionRelDetails', {
    MaterialSolutionVersionRel: toListParamsCApi(params, {
      useEqFields: ['MaterialInfoUUID'],
    }),
  });

export const listMaterialProductVersionRelDetails = (params: { MaterialInfoUUID: string }) =>
  requestCloudApi<{ ListMaterialProductVersionRels: any[] }>('ListMaterialProductVersionRelDetails', {
    MaterialProductVersionRel: toListParamsCApi(params, {
      useEqFields: ['MaterialInfoUUID'],
    }),
  });

// 查询局点未修复缺陷列表
export const listSiteUnresolvedIssues = (params: {
  PageSize: number;
  PageNo: number;
  Filters: any[];
  StartTime?: string;
  EndTime?: string;
  SiteUUID: string;
  IssueType?: 'bug' | 'story';
  IsSameSolutionVersion: boolean;
}) => requestAuroraApi<{ Issues: any[] }>('ListSiteUnresolvedIssues', params);

export class GeneratedApi {
  @TApiAuroraServices({})
  static SetSiteVariableInfo(_params: SetSiteVariableParams, _res?: Promise<any>): Promise<any> {
    return _res!;
  }
}

export class GenerateSiteApi {
  @TApiAuroraServices({})
  static GetSiteGeneralInfoForApplicationArtifact(
    _params: GetSiteArtifactAvailableArchRequest,
    _res?: Promise<GetSiteArtifactAvailableArchResponse>,
  ): Promise<GetSiteArtifactAvailableArchResponse> {
    return _res!;
  }

  @TApiAuroraServices({})
  static GenerateSiteAppArtifactInfo(_params: GenerateSiteAppArtifactInfoRequest, _res?: Promise<any>): Promise<any> {
    return _res!;
  }

  @TApiAuroraServices({})
  static ModifyAppArtifactVersion(_params: ModifyAppArtifactVersionRequest, _res?: Promise<any>): Promise<any> {
    return _res!;
  }

  @TApiAuroraServices({})
  static PackSiteAppArtifactTag(
    _params: { SiteUUID?: string; Arch: string[]; Comment: string },
    _res?: Promise<any>,
  ): Promise<any> {
    return _res!;
  }

  @TApiDataEngine({
    paramModelName: 'OperationSiteAppArtifactWorkspace',
    paramConversion: {
      useEqualFields: ['SiteUUID', 'Arch', 'IsContained'],
      order: [{ Key: 'UpdatedAt', Sort: 'DESC' }],
      page: false,
    },
    defaultParams: {
      IsContained: 1,
    },
  })
  static ListOperationSiteAppArtifactWorkspaces(
    _params: ListOperationSiteAppArtifactWorkspacesParams,
    _res?: Promise<any>,
  ): Promise<any> {
    return _res!;
  }

  @TApiDataEngine({
    paramModelName: 'SolutionVersionArtifactBranch',
    paramConversion: {
      useEqualFields: ['SolutionVersionID'],
      order: [{ Key: 'ID', Sort: 'ASC' }],
      page: false,
    },
  })
  static ListSolutionVersionArtifactBranches(_params: any, _res?: Promise<any>): Promise<any> {
    return _res!;
  }

  @TApiDataEngine({
    paramModelName: 'SolutionVersionArtifactTag',
    paramConversion: {
      page: false,
      useEqualFields: ['SolutionVersionID'],
      order: [{ Key: 'TagNum', Sort: 'DESC' }],
      selectFields: ['id', 'tag_num', 'tag_description', 'artifact_branch_uuid', 'arch', 'publish_status'],
    },
  })
  static ListSolutionVersionArtifactTagsSimple(_params: any, _res?: Promise<any>): Promise<any> {
    return _res!;
  }

  @TApiDataEngine({
    paramModelName: 'ApplicationPackage',
    paramConversion: {
      useEqualFields: ['Arch'],
      page: false,
      order: [
        { Key: 'BuildTime', Sort: 'DESC' },
        { Key: 'ID', Sort: 'DESC' },
      ],
      selectFields: ['application_version', 'build_time', 'commit_id', 'id', 'application_uuid'],
    },
    defaultParams: {
      ApplicationPackageType: [1, 2],
    },
  })
  static ListApplicationPackages(_params: ListApplicationPackagesParams, _res?: Promise<any[]>): Promise<any[]> {
    return _res!;
  }

  @TApiDataEngine({
    paramModelName: 'SolutionVersionArtifactTag',
    paramConversion: {
      useEqualFields: ['ID'],
      page: false,
    },
  })
  static GetSolutionVersionArtifactTag(
    _params: GetSolutionVersionArtifactTagParams,
    _res?: Promise<any>,
  ): Promise<any> {
    return _res!;
  }

  @TApiDataEngine({
    paramModelName: 'ProjectSiteArtifactVersionBackfill',
    paramConversion: {
      useEqualFields: ['SiteUUID'],
      order: [{ Key: 'UpdatedAt', Sort: 'DESC' }],
      selectFields: ['package_source', 'tag_description', 'uuid', 'id', 'created_at', 'updated_at'],
      page: false,
    },
  })
  static ListProjectSiteArtifactVersionBackfills(
    _params: ProjectSiteArtifactVersionBackfill,
    _res?: Promise<any>,
  ): Promise<any> {
    return _res!;
  }

  @TApiDataEngine({
    paramModelName: 'SiteAppArtifactHistoryTag',
    paramConversion: {
      useEqualFields: ['SiteUUID', 'Arch'],
      selectFields: ['id', 'tag_num', 'tag_description', 'arch', 'uuid', 'created_at'],
      order: [{ Key: 'ID', Sort: 'DESC' }],
      page: false,
    },
  })
  static ListSiteAppArtifactHistoryTags(
    _params: ListSiteAppArtifactHistoryTagsParams,
    _res?: Promise<{ ListSiteAppArtifactHistoryTags: ListSiteAppArtifactHistoryTagsResponse[]; Total: number }>,
  ): Promise<{ ListSiteAppArtifactHistoryTags: ListSiteAppArtifactHistoryTagsResponse[]; Total: number }> {
    return _res!;
  }

  @TApiDataEngine({
    paramModelName: 'SiteAppArtifactHistoryTag',
    paramConversion: {
      useEqualFields: ['ID'],
      page: false,
    },
  })
  static GetSiteAppArtifactHistoryTag(_params: GetSiteAppArtifactHistoryTagParams, _res?: Promise<any>): Promise<any> {
    return _res!;
  }
}
