/*
 * @Author: lucyfang
 * @Date: 2024-08-28 16:02:18
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-10-14 14:29:30
 * @Description: 请输入注释信息
 */
import { AxiosRequestConfig } from 'axios';
import { tapi } from '@tencent/tcsc-base';
// 定义通用请求
const baseURL = window.location.origin;

const convertToNum = (str, defaultValue) => {
  try {
    return parseInt(str);
  } catch (error) {
    return defaultValue;
  }
};

const buildRandString = (charset, length) => {
  length = convertToNum(length, 0) || 16;
  if (!(length > 0)) {
    throw 'the parameter "length" must be bigger than 0';
  }
  const temp = [];
  for (let i = 0; i < length; i++) {
    const value = Math.ceil(Math.random() * 100);
    const index = value % charset.length;
    temp.push(charset[index]);
  }
  return temp.join('');
};
const requestId = () => {
  return `${new Date().getTime()}${buildRandString('012345789', 7)}`;
};
const AuthorizationKey = 'change_control';

const requestPostApi = async (endpoint: string, data: any, method: string) => {
  const config: AxiosRequestConfig = {
    baseURL,
    url: endpoint,
    method: method,
    data,
    headers: {
      'Request-Id': requestId(),
      'Content-Type': 'application/json;charset=UTF-8',
      Authorization: localStorage.getItem(AuthorizationKey) as string,
      staffname: (window as any).jiguang_username as string,
      appcode: AuthorizationKey,
    },
  };

  if (method.toUpperCase() === 'GET' && data) {
    config.params = data;
  } else {
    config.data = data;
  }
  try {
    const response = await tapi.requestNormalApi(config);
    return response?.data;
  } catch (error) {
    throw error;
  }
};

// 获取 tapd 信息
export const getTapdInfo = async (states: { tapd_id: string; workspace_id: string }) => {
  const endpoint = `/delivery/py/bugfix/tapd_info/?tapd_id=${states.tapd_id}&workspace_id=${states.workspace_id}`;
  return requestPostApi(endpoint, null, 'GET');
};

// 通过变更工具读取 Tapd 信息
export const getTapdInfoByTools = async (states: { tapd_id: string; workspace_id: string; tool: 'kaleido' }) => {
  const endpoint = `/deliveryx/chgctrl/tool/${states.workspace_id}/${states.tapd_id}/${states.tool}`;
  return requestPostApi(endpoint, null, 'GET');
};

// 获取变更单的相关的变更单
export const getChangeOrders = async (states: { workspace_id: string; bug_id: string }) => {
  const endpoint = `/deliveryx/tapd/relevant/${states.workspace_id}/${states.bug_id}`;
  return requestPostApi(endpoint, states, 'GET');
};

// 查询变更控制表子项列表
export const listCtrlSubItem = async (states: { sub_ctrl_id: number }) => {
  const endpoint = `/deliveryx/chgctrl/comp/${states.sub_ctrl_id}`;
  return requestPostApi(endpoint, null, 'GET');
};

// 获取模板的数据
export const getTempData = async (states: { ctrlId: number; tempType: string; key: string }) => {
  const endpoint = `/deliveryx/chgctrl/template/compV2/${states.ctrlId}/${states.tempType}/${states.key}`;
  return requestPostApi(endpoint, null, 'GET');
};

// 通过此接口，可以删除变更控制表子项，同时系统自动解除合流和规划信息
export const removeTapdRel = async (states: { sub_ctrl_id: string }) => {
  const endpoint = `/deliveryx/chgctrl/comp/${states.sub_ctrl_id}`;
  return requestPostApi(endpoint, null, 'DELETE');
};

// 获取变更控制表基本信息
export const getCtrlBaseInfo = async (states: { sub_ctrl_id: number }) => {
  const endpoint = `/deliveryx/chgctrl/base/${states.sub_ctrl_id}/kaleido`;
  return requestPostApi(endpoint, null, 'GET');
};

// 查询主产品
export const queryMainProject = async (states: { ctrlId: string }) => {
  const endpoint = `/deliveryx/chgctrl/product/${states.ctrlId}`;
  return requestPostApi(endpoint, null, 'GET');
};

// 保存变更控制表基本信息
export const saveCtrlBaseInfo = async (states: {}) => {
  const endpoint = `/deliveryx/chgctrl/base`;
  return requestPostApi(endpoint, states, 'POST');
};

// 查询变更控制表模板列表
export const queryTempList = async (states: { solution_ver: string; comp_id: string }) => {
  const endpoint = `/deliveryx/chgctrl/template/list/${states.solution_ver}/${states.comp_id}`;
  return requestPostApi(endpoint, null, 'GET');
};

// 保存变更控制表模板
export const saveTemp = async (data) => {
  const endpoint = '/deliveryx/chgctrl/template/comp';
  return requestPostApi(endpoint, data, 'POST');
};

// 变更控制表子项保存
export const saveCtrlSubItem = async (params = {}) => {
  const endpoint = '/deliveryx/chgctrl/comp';
  return requestPostApi(endpoint, params, 'POST');
};

// 变更控制表子项顺序调整
export const orderCtrlSeq = async (params = {}) => {
  const endpoint = '/deliveryx/chgctrl/seq';
  return requestPostApi(endpoint, params, 'POST');
};

// 提交撤回
export const submitCtrl = (params = {}) => {
  const endpoint = '/deliveryx/chgctrl/status';
  return requestPostApi(endpoint, params, 'POST');
};

// 变更控制表基础信息同步
export const SyncBase = (params: {}) => {
  const endpoint = '/deliveryx/chgctrl/base/sync';
  return requestPostApi(endpoint, params, 'POST');
};

// 变更控制表子表同步
export const SyncSubCtrl = (params: {}) => {
  const endpoint = '/deliveryx/chgctrl/sub/sync';
  return requestPostApi(endpoint, params, 'POST');
};
