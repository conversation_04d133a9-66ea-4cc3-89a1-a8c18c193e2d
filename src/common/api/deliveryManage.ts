/*
 * @Author: superfeng
 * @Date: 2023-03-23 14:29:50
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-05-07 15:16:05
 * @Description: 请输入注释信息
 */
import { TApiAuroraServices, TApiAuroraServicesForTable } from '@tencent/tcsc-base';
import { requestAuroraApi } from './api';
import type { TAapiDecoratorMoreOptions } from '@tencent/tcsc-base';

export interface IPropsIssueUUIDList {
  IssueType: string;
  IssueUUID: string;
}

export type IProjectClientSite = any;

// 交付单
export interface IDeliveryInfo {
  // 交付单关联的应用列表
  ApplicationInfos: any[];
  // 应用架构arch/x86
  Arch: string;
  // 关单时间
  CloseTime: string;
  // 创建时间
  CreatedAt: string;
  // 创建人
  Creator: string;
  // 责任人
  CurrentOperator: string;
  // 删除时间
  DeletedAt: number;
  // 交付单关联的局点列表
  DeliverySiteRel: IDeliverySiteRel[];
  // 交付单关联的标签列表
  Tags: string[];
  // 描述信息
  Description: string;
  // 是否从缺陷单同步过来的数据
  IsFromIssue?: boolean;
  // ( primary key ) 主键
  FromKey: string;
  FromOriginKey: string;
  // IssueSolution / Patch 来源
  FromType: string;
  // story bug
  FromOriginType: string;
  ID: number;
  IssueID: string;
  // 交付单关联的缺陷单列表
  IssueUUIDList: object;
  // 修改人
  Modifier: string;
  // 名称
  Name: string;
  // 交付单问题修复主产品
  ProductVersion: string;
  // 解决方案版本
  SolutionVersion: string;
  // 解决方案版本ID
  SolutionVersionID: string;
  // 交付单状态
  Status: string;
  // 租户UUID
  TenantUUID: string;
  // UUID
  UUID: string;
  // 更新时间
  UpdatedAt: string;
  // 交付单对应的出包流程实例ID
  WorkflowInstanceID?: number[];
  // 变更单ID
  OperationSheetUUID?: string;
  // 局点交付状态
  DeliverySiteStatusCount?: string;
}

export interface IDeliverySiteAntoolRel {
  AnDonSiteInfo: {
    AuroraData: string;
    CreatedAt: string;
    DeletedAt: string;
    EnglishName: string;
    ID: number & string;
    ProductList: string;
    SiteUUID: string;
    Status: string;
    UUID: string;
    UpdatedAt: string;
    ZoneID: string;
    [k: string]: unknown;
  };
  /**
   * 推动的antool单的ID
   */
  AntoolID: string;
  AntoolSiteUUID: string;
  Phase: string;
  /**
   * antool单状态
   */
  Status: string;
  AntoolStatus: string;
  /**
   * 关单类型
   */
  CloseType: string;
  CreatedAt: string;
  Creator: string;
  DeletedAt: string;
  DeliverySiteRelUUID: string;
  ID: string;
  Modifier: string;
  /**
   * 扩展信息
   */

  RichMessage: string;
  UUID: string;
  UpdatedAt: string;
  /**
   * Antool 实例ID 用于跳转Antool单
   */
  AntoolProcessInstanceID: string;
  [k: string]: unknown;
}

// 交付单局点关联表
export interface IDeliverySiteRel {
  SiteUUID: string;
  // Antool 单链接
  AntoolID: string;
  // 关单时间
  CloseTime: string;
  // 创建时间
  CreatedAt: string;
  // 创建人
  Creator: string;
  // 局点变更责任人
  CurrentOperator: string;
  // 删除时间
  DeletedAt: number;
  // 交付单ID
  DeliveryID: string;
  // ( primary key ) 主键
  ID: number;
  // 交付物料链接
  MaterialURL: string;
  // 修改人
  Modifier: string;
  // 局点名称
  SiteName: string;
  // 交付单状态
  Status: string;
  // 更新时间
  UpdatedAt: string;
  /**
   * 局点对应antool局点列表
   */
  DeliverySiteAntoolRel?: IDeliverySiteAntoolRel[];
  // 关联问题单ID
  IssueID?: string;
}

// 交付单Tag关联表
export interface IDeliveryTagRel {
  // 创建时间
  CreatedAt: string;
  // 创建人
  Creator: string;
  // 删除时间
  DeletedAt: number;
  // 交付单ID
  DeliveryID: string;
  // ( primary key ) 主键
  ID: number;
  // 修改人
  Modifier: string;
  // 标签信息
  Tag: ITag;
  // 标签ID
  TagID: number;
  // 更新时间
  UpdatedAt: string;
  Value: number;
}

export interface ISolutionProductCapability {
  AfterSales: {
    Detail: any[];
    Value: string[];
  };
  InSales: {
    Detail: any[];
    Value: string[];
  };
  PreSales: {
    Detail: any[];
    Value: string[];
  };
  RD: {
    Detail: any[];
    Value: string[];
  };
}

export interface ISolution {
  Code: string;
  CreatedAt: string;
  Creator: string;
  DeletedAt: number;
  Description: string;
  ID: number;
  Modifier: string;
  Name: string;
  NameEN: string;
  Owner: string;
  ProductCapability: ISolutionProductCapability;
  TenantID: string;
  UUID: string;
  UpdatedAt: string;
}

export interface ISolutionVersion {
  ApplicationConfig: string;
  AuditDescription: string;
  AuditStatus: string;
  BaselineID: string;
  Branch: string;
  Code: string;
  CreatedAt: string;
  Creator: string;
  DeletedAt: number;
  Description: string;
  Etag: string;
  ID: number;
  InheritID: string;
  Modifier: string;
  NetworkTemplateTagList: number[];
  ProductCapability: any;
  ProductVersionTagList: number[];
  ServiceBaselineTagID: number;
  SolutionID: string;
  SolutionMarketFlag: null;
  Status: string;
  Tag: string;
  TagNum: number;
  TagStatus: string;
  UUID: string;
  UpdatedAt: string;
}

export interface IApplicationVersions {
  ApplicationName: string;
  ApplicationVersion: string;
  ReleasePackageUUID: string;
  ProductVersion: string;
  ProductVersionUUID: string;
}

export interface IApplicationNameRelVersion {
  ApplicationName: string;
  ApplicationVersions: IApplicationVersions[];
}

export interface IPropsProjectSite {
  Code: string;
  ID: number;
  Name: string;
  Status: string;
  Tenant: string;
  Type: string;
  UUID: string;
  SiteType: string;
}

export interface IProjectClientSiteInfos {
  Code: string;
  ID: number;
  Name: string;
  ProjectSite: IPropsProjectSite[];
  Tenant: string;
  UUID: string;
}

export interface IWorkflowInstanceList {
  ContextValues: string;
  CreatedAt: string;
  Creator: string;
  DeletedAt: number & string;
  ExtraAttributes: string;
  GlobalValues: {
    [k: string]: unknown;
  };
  ID: number & string;
  Modifier: string;
  OrchestrationJobID: string;
  Status: string;
  TenantUUID: string;
  UpdatedAt: string;
  WorkflowID: number & string;
  [k: string]: unknown;
}

export interface IListDeliverySiteRels {
  ClientName?: string;
  SiteUUID: string;
  SiteName: string;
}

export interface IReleatedSites {
  ClientName: string;
  DeliveryID: string;
  ID: number;
  Modifier: string;
  SiteName: string;
  SiteUUID: string;
  Status: string;
}

export interface GetRecallTAPDInfoParams {
  DeliveryUUID: string;
  TapdURL?: string;
  TapdRole?: string;
}

export interface TargetStatus {
  Code: string;
  Name: string;
}

export interface GetRecallTAPDInfoResponse {
  StatusCode: string;
  StatusName: string;
  CurrentOwner: string;
  TargetStatusList: TargetStatus[];
  TargetOwner: string;
}

export interface RecallAntoolSheetParams {
  DeliveryUUID: string;
  ChangeIDs: string[];
  Comment: string;
  ReasonClass: string;
  NeedUpdateTAPD: boolean;
  TargetTAPDStatus?: string;
  TargetTAPDOwner?: string;
  NeedPackPackageAgain?: boolean;
}

export interface RecallResult {
  AntoolChangeID: string;
  Successful: boolean;
  Message: string;
}
export interface RecallAntoolSheetResponse {
  Results: RecallResult[];
}

// 根据缺陷单的架构组合创建交付单
export const createDeliveryInfoByIssue = (params: { IssueSolutionVersionArchID: string | number }) =>
  requestAuroraApi<{ UUID: string }>('CreateDeliveryInfoByIssue', params);

// 创建交付单信息
export const createDeliveryInfo = (params: { Name: string; Description?: string; IssueUUIDList: string[] }) =>
  requestAuroraApi<{ UUID: string }>('CreateDeliveryInfo', params);

// 更新交付单信息
export const updateDeliveryInfo = (
  params: Partial<IDeliveryInfo & { DeliveryID: string; Sites: any[]; Applications: any[] }>,
) => requestAuroraApi<{ UUID: string }>('UpdateDeliveryInfo', params);

// 获取交付单列表
export const listDeliveryInfos = (params: {
  PageNo: number;
  PageSize: number;
  DeliveryInfo?: {
    _Filter?: any[];
  };
  RelatedToMe: boolean;
  SiteUUID: string;
}) => requestAuroraApi<{ DeliveryDetailInfos: IDeliveryInfo[] }>('ListDeliveryInfo', params);

// 获取交付单详情
export const getDeliveryInfo = (params: { DeliveryID: string }) =>
  requestAuroraApi<{ DeliveryDetailInfo: IDeliveryInfo }>('GetDeliveryInfo', params);

export const copyDeliveryInfo = (params: { DeliveryID: string }) =>
  requestAuroraApi<{ UUID: string }>('CreateDeliveryInfoByDeliverySheet', params);

// 废弃Bug
export const deleteBug = (params: { IssueID: string }) => requestAuroraApi('DeleteBug', params);
export const deleteStory = (params: { IssueID: string }) => requestAuroraApi('DeleteStory', params);

// 启动运维出包流程
export const generateWorkflowInstance = (params: { WorkflowUUID: string; GlobalValues?: any }) =>
  requestAuroraApi<{ WorkflowInstanceID: number; WorkflowID: number }>('GenerateWorkflowInstance', params);

// 获取创建交付单应用列表
export const listApplicationNames = (params: { SolutionVersionID: string; Arch: string }) =>
  requestAuroraApi<{ ApplicationsNames: string[] }>('ListApplicationNames', params);

// 根据应用名称列表获取应用版本信息列表
export const getApplicationVersions = (params: {
  ApplicationsNames: string[];
  SolutionVersionID: string;
  Arch: string;
}) => requestAuroraApi<{ ApplicationNameVersions: IApplicationNameRelVersion[] }>('GetApplicationVersions', params);

// 获取某个交付单已关联的局点列表
export const getDeliveryReleatedSites = (params: { DeliveryID: string }) =>
  requestAuroraApi<{ ReleatedSites: IReleatedSites[] }>('GetDeliveryReleatedSites', params);

// 获取获取交付单关联的局点列表
export const ListDeliverySiteRels = () =>
  requestAuroraApi<{ ListDeliverySiteRels: IListDeliverySiteRels[] }>('ListDeliverySiteRels');

// // 根据缺陷单获取交付单信息
// export const getDeliveryInfoByIssueSolutionArch = (params: { IssueSolutionArchUUID: string }) =>
//   requestAuroraApi<{ DeliveryDetailInfo: any[] }>('GetDeliveryInfoByIssueSolutionArch', params);

// 获取交付出包流程执行记录
export const getWorkflowInstanceList = (params: { WorkflowInstanceIDs: number[] }) =>
  requestAuroraApi<{ WorkflowInstanceList: IWorkflowInstanceList[] }>('GetWorkflowInstanceList', params);

export class DeliveryManage {
  @TApiAuroraServicesForTable({
    returnModelName: 'DeliveryDetailInfos',
    specifiedFieldConvertion: [
      {
        field: 'DeliveryInfo',
        convertType: 'all',
        paramConversion: {
          page: false,
          useEqualFields: ['SolutionVersionID', 'Arch'],
          retainEmptyString: false,
          dateFields: ['CreatedAt'],
        },
      },
    ],
  })
  static ListDeliveryInfo(
    _params: {
      PageNo: number;
      PageSize: number;
      DeliveryInfo?: any;
      RelatedToMe: boolean;
      SiteUUID: string;
      TAPDUrls?: string[];
    },
    _options: TAapiDecoratorMoreOptions,
    _res?: any,
  ): Promise<{ DeliveryDetailInfos: IDeliveryInfo[] }> {
    return _res;
  }

  @TApiAuroraServices({})
  static RecallAntoolSheet(
    _params: RecallAntoolSheetParams,
    _res?: Promise<RecallAntoolSheetResponse>,
  ): Promise<RecallAntoolSheetResponse> {
    return _res!;
  }
  @TApiAuroraServices()
  static AddDeliverySiteRel(_params: { DeliveryID: string; SiteInfos: any[] }, apiResult?: any): Promise<any> {
    return apiResult;
  }
  @TApiAuroraServices()
  static DeleteDeliverySiteRel(_params: { DeliveryID: string; SiteUUIDs: string[] }, apiResult?: any): Promise<any> {
    return apiResult;
  }

  @TApiAuroraServices({})
  static GetRecallTAPDInfo(
    _params: GetRecallTAPDInfoParams,
    _res?: Promise<GetRecallTAPDInfoResponse>,
  ): Promise<GetRecallTAPDInfoResponse> {
    return _res!;
  }
}

export interface IRecallAntoolSheetResponse {
  Results: {
    AntoolChangeID: string;
    Message: string;
    Successful: string;
  }[];
}
