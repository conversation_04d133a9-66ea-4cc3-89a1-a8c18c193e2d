import { TApiDataEngine } from '@tencent/tcsc-base';

export interface ISolution {
  Code: string;
  CreatedAt: string;
  Creator: string;
  DeletedAt: number;
  Description: string;
  ID: number;
  Modifier: string;
  Name: string;
  NameEN: string;
  Owner: string;
  TenantID: string;
  UUID: string;
  UpdatedAt: string;
}

export interface ISolutionVersion {
  ApplicationConfig: string;
  AuditDescription: string;
  AuditStatus: string;
  BaselineID: string;
  Branch: string;
  Code: string;
  CreatedAt: string;
  Creator: string;
  DeletedAt: number;
  Description: string;
  Etag: string;
  ID: number;
  InheritID: string;
  Modifier: string;
  ServiceBaselineTagID: string;
  SolutionID: string;
  Status: string;
  Tag: string;
  TagNum: number;
  TagStatus: string;
  UUID: string;
  UpdatedAt: string;
}

export class CommonApi {
  @TApiDataEngine({
    paramModelName: 'SolutionVersion',
    paramConversion: {
      useEqualFields: ['SolutionID', 'Tag', 'Branch', 'SolutionID'],
      page: false,
      order: [{ Key: 'CreatedAt', Sort: 'DESC' }],
    },
    defaultParams: {
      Status: ['01', '02', '50', '60'],
      Tag: '',
      Branch: 'master',
    },
  })
  static ListSolutionVersionWithMarketFlag(
    _params: {
      SolutionID?: string;
    },
    _result?: any,
  ): Promise<{ ListSolutionVersions: ISolutionVersion[] }> {
    return _result;
  }

  @TApiDataEngine({
    defaultParams: {
      TenantID: (window as any).jiguang_currentNs,
    },
    paramModelName: 'Solution',
    paramConversion: {
      useEqualFields: ['TenantID'],
      page: false,
    },
  })
  static ListSolutions(_result?: any): Promise<{ ListSolutions: ISolution[] }> {
    return _result;
  }
}
