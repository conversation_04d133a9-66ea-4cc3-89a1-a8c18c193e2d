import { Filter, request<PERSON><PERSON>ra<PERSON>pi } from './api';

export interface IRequestParams {
  PageSize: number & string;
  PageNo: number & string;
  _Filter?: Filter[];
}

export interface IRecordsItem {
  ID: string;
  Name: string;
  Date: string;
  IssueList: string[];
  Tapd: string;
  SolutionVersionArchList: {
    SolutionVersion: string;
    Arch: string;
  }[];
  PackageSiteStatusCount: {
    TotalCount: number & string;
    PackingCount: number & string;
    PackSuccessCount: number & string;
    PackFailedCount: number & string;
  };
}

export interface IPackageIssue {
  IssueID: string;
  IssueType: string;
  SolutionVersion: string;
  Arch: string;
  SolutionVersionUUID: string;
  Title: string;
  IssueSolutionArchRelUUID: string;
  IssueApplications: {
    Name: string;
    Version: string;
  }[];
  Status: string;
  DeliverySiteStatusCount: string;
  DeliverySiteInfos: {
    ClientName: string;
    SiteName: string;
    SiteType: string;
    SiteUUID: string;
    Status: string;
    WorkflowInstances: number[];
  }[];
}

export interface IPackageSite {
  ClientName: string;
  SiteName: string;
  SiteType: string;
  SiteUUID: string;
  Status: string;
  WorkflowInstances: number[];
  SolutionVersion: string;
  Arch: string;
  SolutionVersionUUID: string;
  DeliverySiteStatusCount: string;
  IssueInfos: {
    IssueID: string;
    IssueType: string;
    SolutionVersion: string;
    Arch: string;
    SolutionVersionUUID: string;
    Title: string;
    IssueSolutionArchRelUUID: string;
    IssueApplications: {
      Name: string;
      Version: string;
    }[];
  }[];
}

export interface IRequestPackageSiteDetail {
  SolutionVersionUUID: string;
  Arch: string;
  SiteUUID: string;
  Filters?: Filter[];
}

export interface PageParams {
  PageSize: number | string;
  PageNo: number | string;
}

// 出包历史列表
export const listPackageOutRecords = (params: IRequestParams) =>
  requestAuroraApi<{ Items: IRecordsItem[] }>('ListPackageOutRecords', params);

// 出包历史对应的详情
export const getPackageOutRecordDetail = (params: { RecordID: string }) =>
  requestAuroraApi('GetPackageOutRecordDetail', params);

// 出包历史中缺陷单视角对应的详情
export const getPackageOutRecordDetailIssue = (params: { RecordID: string }) =>
  requestAuroraApi('GetPackageOutRecordDetailIssue', params);

// 出包历史中局点视角对应的详情
export const getPackageOutRecordDetailSite = (params: { RecordID: string; Filters?: Filter[] } & PageParams) =>
  requestAuroraApi('GetPackageOutRecordDetailSite', params);

// 缺陷单视角：出包缺陷列表
export const listPackageIssueList = (params: IRequestParams) =>
  requestAuroraApi<{ PackageIssueList: IPackageIssue[] }>('ListPackageIssueList', params);

// 缺陷单视角：出包缺陷详情
export const getPackageIssueDetailInfo = (params: { IssueSolutionArchRelUUID: string }) =>
  requestAuroraApi<{ PackageIssueInfo: IPackageIssue }>('GetPackageIssueDetailInfo', params);

// 局点视角：出包缺陷列表
export const listPackageSiteIssueList = (params: IRecordsItem) =>
  requestAuroraApi<{ PackageSiteList: IPackageSite[] }>('ListPackageSiteIssueList', params);

// 局点视角：出包缺陷详情
export const getPackageSiteIssueDetailInfo = (params: IRequestPackageSiteDetail & PageParams) =>
  requestAuroraApi<{ PackageSiteInfo: IPackageSite }>('GetPackageSiteIssueDetailInfo', params);
