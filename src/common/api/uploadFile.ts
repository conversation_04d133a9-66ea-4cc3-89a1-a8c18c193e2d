/*
 * @Author: superfeng
 * @Date: 2023-03-23 10:24:39
 * @LastEditors: superfeng
 * @LastEditTime: 2023-05-12 10:27:15
 * @Description: 请输入注释信息
 */
import { requestNormalApi } from './api';

export const getUploadCosUrl = ({ fileName }: { fileName: string }) =>
  requestNormalApi({
    url: `/cgw/packages-api/`,
    method: 'POST',
    mode: 'cors',
    // 请求参数
    data: {
      Action: 'CreateCSPUploadURL',
      FileName: fileName,
      FileVersion: '00',
      PkgName: '0',
    },
  });

export const uploadFile = (options: { fileName: string; file: Blob }) => {
  const reader = new FileReader();
  reader.readAsArrayBuffer(options.file);
  return new Promise((resolve, reject) => {
    reader.onloadend = function () {
      getUploadCosUrl({ fileName: options.fileName })
        .then((result) => {
          if (result.Response.Url) {
            const url = new URL(result.Response.Url);
            fetch(`${window.location.protocol}//${url.hostname}${url.pathname}${url.search}`, {
              method: 'PUT',
              headers: {
                'content-type': 'application/octet-stream',
              },

              mode: 'cors',
              // 请求参数
              body: reader.result,
            })
              .then(() => {
                resolve(`//registry.jiguang.woa.com/${result.Response.Key}`);
              })
              .catch((error) => reject(error));
          }
        })
        .catch((error) => reject(error));
    };
  });
};

export const getDownloadUrl = (url: string) => `//registry.jiguang.woa.com/${url}`;
