import { TApiAuroraServices, TApiAuroraServicesForTable, TApiDataEngine } from '@tencent/tcsc-base';
import type { TAapiDecoratorMoreOptions } from '@tencent/tcsc-base';
interface ProductInfo {
  ProductUUID: string;
  ProductCode: string;
}

export class QueueManageApi {
  // 查询patch队列列表
  @TApiAuroraServicesForTable({
    returnModelName: 'PatchQueueList',
  })
  static ListPatchQueues(
    _params?: {
      PageNo?: number;
      PageSize?: number;
      Name?: string;
      Arch?: string;
      SolutionUUID?: string;
      SolutionVersionUUID?: string;
      Order?: [{ Key: 'UpdatedAt'; Sort: 'DESC' }];
    },
    _options?: TAapiDecoratorMoreOptions,
    _res?: any,
  ): Promise<any> {
    return _res;
  }
  // 删除

  // 创建patch队列
  @TApiAuroraServices({})
  static CreatePatchQueue(_params: {
    Name: string;
    Arch: string;
    SolutionVersionUUID: string;
    ArtifactBranchUUID: string;
    Description?: string;
    NamingRule?: string;
    Owners: string;
  }): Promise<any> {}

  // 队列详细信息
  @TApiAuroraServices({})
  static GetPatchQueueDetail(_params: { PatchQueueUUID: string }): Promise<any> {}

  // 查询 Patch 队列局点列表
  @TApiAuroraServicesForTable({
    returnModelName: 'SiteList',
  })
  static GetPatchQueueSiteList(
    _params: { PageNo: number; PageSize: number; PatchQueueUUID: string },
    _options: TAapiDecoratorMoreOptions,
    _res?: any,
  ): Promise<any> {
    return _res;
  }

  // 队列局点操作
  @TApiAuroraServices({})
  static PatchQueueSiteOperation(_params: {
    PatchQueueUUID: string;
    Operation: string;
    SiteUUIDList: string[];
  }): Promise<any> {}

  // 队列局点替换
  @TApiAuroraServices({})
  static UpdatePatchQueueSiteRel(_params: { PatchQueueUUID: string; SiteUUIDList: string[] }): Promise<any> {}

  // 查询 Patch 队列可添加的局点
  @TApiAuroraServices({})
  static QueryAddableSite(_params: { PatchQueueUUID: string }): Promise<any> {}

  // 查询产品列表
  @TApiAuroraServicesForTable({
    returnModelName: 'ProductList',
  })
  static QueryAddableProduct(
    _params: {
      PageNo?: number;
      PageSize?: number;
      PatchQueueUUID: string;
      SiteUUID?: string;
      Name?: string;
      Code?: string;
    },
    _options?: TAapiDecoratorMoreOptions,
    _res?: any,
  ): Promise<any> {
    return _res;
  }

  // 修改队列下产品

  @TApiDataEngine({
    paramModelName: 'PatchQueue',
    paramConversion: {
      page: false, // 指定不进行分页处理
      unConversionFields: ['ProductInfo', 'NamingRule'],
      useEqualFields: ['UUID'],
    },
  })
  static UpdatePatchQueue(
    _params: {
      ProductInfo?: ProductInfo[];
      UUID: string;
      NamingRule?: string;
    },
    _result?: any,
  ): Promise<any> {
    return _result;
  }

  // 修改队列下局点产品
  @TApiDataEngine({
    paramModelName: 'PatchQueueSiteInfo',
    paramConversion: {
      page: false, // 指定不进行分页处理
      unConversionFields: ['ProductInfo'],
      useEqualFields: ['SiteUUID', 'PatchQueueUUID', 'DeletedAt'],
    },
  })
  static UpdatePatchQueueSiteInfo(
    _params: {
      ProductInfo: ProductInfo[];
      PatchQueueUUID: string;
      SiteUUID: string;
      DeletedAt: 0;
    },
    _result?: any,
  ): Promise<any> {
    return _result;
  }

  // 预生成patch名称
  @TApiAuroraServices({})
  static PreRenderPatchName(_params: {
    SolutionVersionUUID?: string;
    Arch?: string;
    PatchQueueUUID: string;
    MainProductCode?: string;
    PatchType: string;
    UpdateItem?: string;
  }): Promise<any> {}
}
