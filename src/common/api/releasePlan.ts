import {
  TApiAuroraServices,
  TApiAuroraServicesForTable,
  TApiDataEngine,
  TApiDataEngineForTable,
} from '@tencent/tcsc-base';
import { Filter, requestAuroraApi, requestCloudApi, toListParamsCApi } from './api';

// const { pageSize = 10, current = 1, ...rest } = params;
export const ListReleasePlanWithDetail = (params: {
  ReleaseName?: string;
  SolutionVersionUUID?: string;
  Type?: string;
  pageSize: number;
  current: number;
  Status?: string[];
  CreatedAt?: string;
}) =>
  requestCloudApi<{ ListReleasePlans: any[] }>('ListReleasePlansWithDetail', {
    ReleasePlan: toListParamsCApi(
      { ...params, DeletedAt: 0 },
      {
        useEqFields: ['SolutionVersionUUID', 'Type', 'DeletedAt'],
        order: [{ Key: 'CreatedAt', Sort: 'DESC' }],
      },
    ),
  });
export interface ListSolutions {
  Name: string;
  UUID: string;
  Code: string;
}
// 获取解决方案
export const ListSolutions = (params: { TenantID: string }) =>
  requestCloudApi<{ ListSolutions: ListSolutions[] }>('ListSolutions', {
    Solution: toListParamsCApi(params, {
      useEqFields: ['TenantID'],
    }),
  });

// 获取解决方案版本

export const ListSolutionVersionWithMarketFlag = (params: {
  SolutionID?: string;
  Tag: string;
  Branch: string;
  Status: string[];
  Code?: string;
}) =>
  requestCloudApi<{ ListSolutionVersions: ListSolutions[] }>('ListSolutionVersionWithMarketFlag', {
    SolutionVersion: toListParamsCApi(params, {
      useEqFields: ['SolutionID', 'Tag', 'Branch', 'Status'],
      order: [{ Key: 'CreatedAt', Sort: 'DESC' }],
    }),
  });
export interface IIterationDetailResponse {
  CodeFreezeDate: string;
  Creator?: string;
  DeletedAt: number;
  Description: string;
  EndDate: string;
  FeatureFreezeDate: string;
  ID: number;
  LockStatus?: string;
  Name: string;
  Owners: string;
  StartDate: string;
  Status: string;
  TenantUUID: string;
  Type: string;
  UUID: string;
  UpdateAt: string;
  ReleaseReviewDate: string;
  SolutionVersion: {
    Code: string;
    ID: number;
    Solution: { Name: string; UUID: string };
    Name: string;
    UUID: string;
    SolutionID: string;
  };
  TapdCreator: string;
  IterationReviewDate: string;
}

// GetIterationDetail
export const GetReleasePlanDetail = (params: { UUID?: string }) =>
  requestCloudApi<{ GetReleasePlan: IIterationDetailResponse }>('GetReleasePlanDetail', {
    ReleasePlan: toListParamsCApi(params, {
      useEqFields: ['UUID'],
    }),
  });
export interface ICreateIterationRequestParams {
  CodeFreezeDate: string;
  CompletedAt: string;
  Description: string;
  FeatureFreezeDate: string;
  EndDate: string;
  IterationReviewDate: string;
  ReleaseReviewDate: string;
  SolutionVersionUUID: string;
  StartDate: string;
  Type: string;
  Owners: string;
  ReleaseName: string;
  TapdURL: string;
  UUID?: string;
  WechatGroup: string;
  // Locked: boolean;
}

// CreateIteration
export const CreateIteration = (params: ICreateIterationRequestParams) =>
  requestAuroraApi('CreateIteration', { Iteration: params });
export interface ISyncTapdWithIterationReponse {
  Iteration: { UUID: string };
}
export const syncTapdWithIteration = (params: { TapdUrl: string }) =>
  requestAuroraApi<ISyncTapdWithIterationReponse>('CreateIteration', { Iteration: params });
// UpdateIteration
export const UpdateReleasePlan = (params: ICreateIterationRequestParams) =>
  requestAuroraApi('UpdateReleasePlan', { ReleasePlan: params });
// GetIterationOverviewDetail
export interface IOverviewDetailResponse {
  TotalStoryNum?: number;
  FinishedStoryNum?: number;
  TotalBugNum?: number;
  FixedBugNum?: number;
  TotalFatalBugNum?: number;
  FixedFatalBugNum?: number;
  TotalSeriousBugNum?: number;
  FixedSeriousBugNum?: number;
}
export const GetReleasePlanOverviewDetail = (params: { ReleaseID?: string }) =>
  requestAuroraApi<IOverviewDetailResponse>('GetReleasePlanOverviewDetail', params);

// DeleteIteration
export const DeleteReleasePlan = (params: { ReleasePlanUUID?: string }) =>
  requestAuroraApi<any>('DeleteReleasePlan', params);

// UpdateIterationLockStatus

export const UpdateReleasePlanLockStatus = (params: { ReleasePlanUUID: string; LockStatus: string }) =>
  requestAuroraApi<any>('UpdateReleasePlanLockStatus', params);
// AddIssueListToIteration
export const AddIssueListToReleasePlan = (params: { ReleaseUUID: string; IssueType: string; IssueIDList: string[] }) =>
  requestAuroraApi<any>('AddIssueListToReleasePlan', params);

// UpdateIterationStatus 更新迭代状态
export const UpdateReleasePlanStatus = (params: { ReleasePlanUUID: string; Status: string }) =>
  requestAuroraApi<any>('UpdateReleasePlanStatus', params);
export interface ListProductInfos {
  Name: string;
  Code: string;
  UUID: string;
}
// 获取所有产品
export const ListProductInfos = (params) =>
  requestCloudApi<{ ListProductInfos: ListProductInfos[] }>('ListProductInfos', {
    ProductInfo: toListParamsCApi(params, {
      page: false,
    }),
  });

export const ListVersionReleaseApproval = (params: { IterationUUID: string }) =>
  requestCloudApi<{ ListVersionReleaseApprovalSheets: any }>('ListVersionReleaseApprovalSheets', {
    VersionReleaseApprovalSheet: toListParamsCApi(params, {
      useEqFields: ['IterationUUID'],
      page: false,
    }),
  });
export namespace GetVersionReleaseApprovalSheetIProps {
  export interface ApprovalStage {
    ApprovalLevel: string;
    ApprovalSheetUUID: string;
    CorrelatedStages: string;
    CreatedAt: string;
    DeletedAt: number;
    Description: string;
    ID: number;
    IsSummaryStage: boolean;
    Name: string;
    NeedToAssessRisk: boolean;
    StageOrder: number;
    Status: string;
    UUID: string;
    UpdateAt: string;
  }

  export interface Iteration {
    ID: number;
    Name: string;
    UUID: string;
  }

  export interface Owners {
    CMO: string;
    CarbonCopyUsers: any[];
    DeployTester: string;
    DocumentOwner: string;
    GM: string;
    IntegrationTestOwner: string;
    Manager: string;
    Owners: string[];
    ProductManager: string;
    ProjectManager: string;
    ReleaseTester: string;
  }

  export interface Product {
    Code: string;
    ID: number;
    Name: string;
    NameEN: string;
    UUID: string;
  }

  export interface ReleaseProduct {
    ApprovalSheetUUID: string;
    ID: number;
    Product: Product;
    ProductUUID: string;
  }

  export interface Solution {
    AndonProductName: string;
    Code: string;
    CreatedAt: string;
    Creator: string;
    DeletedAt: number;
    Description: string;
    ID: number;
    Modifier: string;
    Name: string;
    NameEN: string;
    OperationsArchitect: string;
    Owner: string;
    ProductCapability: any;
    ProductManager: string;
    TenantID: string;
    UUID: string;
    UpdatedAt: string;
  }

  export interface SolutionVersion {
    Code: string;
    ID: number;
    Solution: Solution;
    SolutionID: string;
    UUID: string;
  }

  export interface BugReport {
    ClosedFatalCount: number;
    ClosedSecurityCount: number;
    ClosedSeriousCount: number;
    FatalCount: number;
    SecurityClosedRatio: number;
    SecurityCount: number;
    SeriousCount: number;
    Total: number;
    TotalClosedCount: number;
    TotalClosedRatio: number;
  }

  export interface StoryReport {
    ClosedCount: number;
    TestFreeCount: number;
    TestFreeRatio: number;
    Total: number;
  }

  export interface StatisticalReport {
    BugReport: BugReport;
    StoryReport: StoryReport;
  }

  export interface ApprovalStep {
    Content: any;
    Description: string;
    Name: string;
    StepOrder?: number;
    NeedToAssessRisk?: boolean;
  }

  export interface Stage {
    ApprovalLevel: string;
    ApprovalSteps: ApprovalStep[];
    CorrelatedStages: string;
    Description: string;
    IsSummaryStage: boolean;
    Name: string;
    NeedToApproveIfRiskFree: boolean;
    NeedToAssessRisk: boolean;
    StageOrder: number;
  }

  export interface VersionReleaseTemplate {
    Stages: {
      CMO: Stage;
      Document: Stage;
      GM: Stage;
      Manager: Stage;
      Product: Stage;
      ReleaseTester: Stage;
      WorkItemRange: Stage;
    };
  }

  export interface IResponse {
    ApprovalStages: ApprovalStage[];
    Archs: string;
    CompletedAt: string;
    CreatedAt: string;
    Creator: string;
    DeletedAt: number;
    Description: string;
    ID: number;
    Iteration: Iteration;
    IterationUUID: string;
    Modifier: string;
    Owners: Owners;
    ReleaseProducts: ReleaseProduct[];
    SolutionVersion: SolutionVersion;
    SolutionVersionUUID: string;
    StatisticalReport: StatisticalReport;
    Status: string;
    UUID: string;
    UpdateAt: string;
    VersionReleaseTemplate: VersionReleaseTemplate;
    WechatGroup: string;
  }
}
export const GetVersionReleaseApprovalSheetWithDetail = (params: { UUID: string }) =>
  requestCloudApi<{ GetVersionReleaseApprovalSheet: GetVersionReleaseApprovalSheetIProps.IResponse }>(
    'GetVersionReleaseApprovalSheetWithDetails',
    {
      VersionReleaseApprovalSheet: toListParamsCApi(params, {
        useEqFields: ['UUID'],
        page: false,
      }),
    },
  );

// 创建审批流程

export interface ICreateVersionReleaseApprovalSheetParams {
  IterationUUID?: string;
  ReleasePlanUUID: string;
  Description: string;
  Archs: string;
  ProductUUIDs: string;
  TemplateName: string;
  WechatGroup: string;
  Owners: {
    Owners: string;
    CarbonCopyUsers: string;
    DocumentOwner: string;
    CMO: string;
    Manager: String;
    GM: string;
    ProjectManager: string;
    ReleaseTester: string;
  };
}
export interface VersionReleaseApprovalSheetRespones {
  VersionReleaseApprovalSheet: {
    UUID: string;
    IterationUUID: string;
  };
}
export const CreateVersionReleaseApprovalSheet = (params: ICreateVersionReleaseApprovalSheetParams) =>
  requestAuroraApi<VersionReleaseApprovalSheetRespones>('CreateVersionReleaseApprovalSheet', params);
export const UpdateVersionReleaseApprovalSheet = (params: ICreateVersionReleaseApprovalSheetParams) =>
  requestAuroraApi<VersionReleaseApprovalSheetRespones>('UpdateVersionReleaseApprovalSheet', params);

// 获取审批单关联的工单列表
export interface IListVersionReleaseWorkItemsParams {
  ApprovalSheetUUID: string;
  ProductUUID?: string;
  IssueType?: string;
  SecurityBugOnly?: boolean;
  SolutionVersionUUID?: string;
  OpenedOnly?: boolean;
  Status?: string;
  SeverityLevel?: string;
  PageNo: number;
  PageSize: number;
}
export const ListVersionReleaseWorkItems = (params: IListVersionReleaseWorkItemsParams) =>
  requestAuroraApi<{ Bugs: any; Stories: any; Total: number }>('ListVersionReleaseWorkItems', params);
// 取消工单发布
export const DeleteWorkItemsFromReleaseList = (params: {
  IssueIDList: string[];
  IssueType: string;
  ReleaseApprovalSheetUUID: string;
}) => requestAuroraApi<{ Message: string }>('DeleteWorkItemsFromReleaseList', params);

// 添加工单发布

export const AddWorkItemsToReleaseList = (params: {
  IssueIDList: string[];
  IssueType: string;
  ReleaseApprovalSheetUUID: string;
}) => requestAuroraApi<{ Message: string }>('AddWorkItemsToReleaseList', params);

// 获取审批任务详情GetVersionReleaseApprovalStageWithDetails

export namespace IApprovalStageWithDetailsResponse {
  export interface Response {
    ApprovalLevel: string;
    ApprovalSheet: {
      Archs: string;
      CompletedAt: string;
      CreatedAt: string;
      Creator: string;
      DeletedAt: number;
      Description: string;
      ID: number;
      Iteration: {
        ID: number;
        Name: string;
        UUID: string;
      };
      IterationUUID: string;
      Owners: {
        CMO: string;
        CarbonCopyUsers: string[];
        DeployTester: string;
        DocumentOwner: string;
        GM: string;
        IntegrationTestOwner: string;
        Manager: string;
        Owners: string[];
        ProductManager: string;
        ProjectManager: string;
        ReleaseTester: string;
      };
      SolutionVersion: {
        Code: string;
        ID: number;
        Solution: {
          AndonProductName: string;
          Code: string;
          CreatedAt: string;
          Creator: string;
          DeletedAt: number;
          Description: string;
          ID: number;
          Modifier: string;
          Name: string;
          NameEN: string;
          OperationsArchitect: string;
          Owner: string;
          ProductCapability: {
            AfterSales: {
              Detail: string[];
              Value: string[];
            };
            InSales: {
              Detail: string[];
              Value: string[];
            };
            PreSales: {
              Detail: string[];
              Value: string[];
            };
            RD: {
              Detail: string[];
              Value: string[];
            };
          };
          ProductManager: string;
          TenantID: string;
          UUID: string;
          UpdatedAt: string;
        };
        SolutionID: string;
        UUID: string;
      };
      SolutionVersionUUID: string;
      StatisticalReport: {
        BugReport: {
          ClosedFatalCount: number;
          ClosedSecurityCount: number;
          ClosedSeriousCount: number;
          FatalCount: number;
          SecurityClosedRatio: number;
          SecurityCount: number;
          SeriousCount: number;
          Total: number;
          TotalClosedCount: number;
          TotalClosedRatio: number;
        };
        StoryReport: {
          ClosedCount: number;
          TestFreeCount: number;
          TestFreeRatio: number;
          Total: number;
        };
      };
      Status: string;
      UUID: string;
      UpdateAt: string;
      VersionReleaseTemplate: {
        Stages: {
          [key: string]: {
            ApprovalLevel: string;
            ApprovalSteps: {
              Content: any;
              Description: string;
              Name: string;
              StepOrder?: number;
              NeedToAssessRisk?: boolean;
            }[];
            CorrelatedStages: string;
            Description: string;
            IsSummaryStage: boolean;
            Name: string;
            NeedToApproveIfRiskFree: boolean;
            NeedToAssessRisk: boolean;
            StageOrder: number;
          };
        };
      };
      WechatGroup: string;
    };
    ApprovalSheetUUID: string;
    ApprovalTasks: ApprovalTasks[];
    BeenRejectedHistories: {
      ApprovalSheetUUID: string;
      BeenRejectedStageUUID: string;
      Comments: string;
      CreatedAt: string;
      Creator: string;
      DeletedAt: number;
      ID: number;
      OriginStage: {
        ApprovalSheetUUID: string;
        CreatedAt: string;
        DeletedAt: number;
        ID: number;
        Name: string;
        StageOrder: number;
        Status: string;
        UUID: string;
        UpdateAt: string;
      };
      OriginStageUUID: string;
    };
    RejectedHistories: any;
  }
  export interface ApprovalSteps {
    ApprovalSheetUUID: string;
    ApprovalStageUUID: string;
    ApprovalTaskUUID: string;
    Comments: string;
    CompletedAt: string;
    Content: {
      DocumentURL: string;
    };
    CreatedAt: string;
    DeletedAt: number;
    Description: string;
    ID: number;
    Modifier: string;
    Name: string;
    NeedToAssessRisk: boolean;
    Owners: string;
    ProductUUID: string;
    Status: string;
    StepOrder: number;
    UUID: string;
    UpdateAt: string;
  }
  export interface ApprovalTasks {
    ApprovalStageUUID: string;
    ApprovalSteps: ApprovalSteps[];
    CompletedAt: string;
    CreatedAt: string;
    DeletedAt: number;
    ID: number;
    Modifier: string;
    ProductUUID: string;
    Status: string;
    UUID: string;
    UpdateAt: string;
  }
}

export const GetVersionReleaseApprovalStageWithDetails = (params: { UUID: string }) =>
  requestCloudApi<{
    GetVersionReleaseApprovalStage: IApprovalStageWithDetailsResponse.Response;
  }>('GetVersionReleaseApprovalStageWithDetails', {
    VersionReleaseApprovalStage: toListParamsCApi(params, {
      useEqFields: ['UUID'],
      page: false,
    }),
  });

export const ChangeVersionApprovalTaskOwner = (params: {
  // ApprovalTaskUUID: string;
  ApprovalStepUUID: string;
  Owners: any;
}) => requestAuroraApi<{ Message: string }>('ChangeVersionApprovalStepOwner', params);

export const FinishApprovalTask = (params: {
  ApprovalStepUUID?: string;
  Status: string;
  Comments?: string;
  RejectToStageUUID?: string;
  Content?: any;
  DoubleConfirmationOwner?: string;
  RejectToProductsUUID?: string[];
  // 产品级别时才给这个
  RejectToStepUUID?: string;
}) => requestAuroraApi<{ Message: string }>('FinishApprovalStep', params);

// UpdateVersionReleaseApprovalStatus取消发布审批

export const UpdateVersionReleaseApprovalStatus = (params: { ReleaseApprovalSheetUUID: string; Status: string }) =>
  requestAuroraApi<{ Message: string }>('UpdateVersionReleaseApprovalStatus', params);

// 查看驳回记录ListVersionReleaseRejectionHistoriesWithDetail
export const ListVersionReleaseRejectionHistoriesWithDetail = (params: { BeenRejectedStageUUID: string }) =>
  requestCloudApi<{ ListVersionReleaseRejectionHistories: any }>('ListVersionReleaseRejectionHistoriesWithDetail', {
    VersionReleaseRejectionHistory: toListParamsCApi(params, {
      useEqFields: ['BeenRejectedStageUUID'],
      page: false,
    }),
  });

// 获取本产品下的发布的需求以及缺陷统计信息

export interface BugReportItems {
  ClosedFatalCount: number;
  ClosedSecurityCount: number;
  ClosedSeriousCount: number;
  FatalCount: number;
  SecurityClosedRatio: number;
  SecurityCount: number;
  SeriousCount: number;
  Total: number;
  TotalClosedCount: number;
  TotalClosedRatio: number;
}
export const GetProductReleaseWorkItemsReport = (params: { ProductUUID: string; ApprovalSheetUUID: string }) =>
  requestAuroraApi<{ Message: string; StoryReport: any; BugReport: BugReportItems }>(
    'GetProductReleaseWorkItemsReport',
    params,
  );

// ListMRWorkItemWhitelists 白名单列表
export const ListMRWorkItemWhitelists = (params: { IterationUUID?: string }) =>
  requestCloudApi<{ ListMRWorkItemWhitelists: any }>('ListMRWorkItemWhitelists', {
    MRWorkItemWhitelist: toListParamsCApi(params, {
      useEqFields: ['IterationUUID'],
    }),
  });
// 删除合流白名单
export const DeleteMRWorkItemCheckWhitelist = (params: {
  IterationUUID: string;
  IssueIDs: string[];
  TAPDUrl: string;
}) => requestAuroraApi<any>('DeleteMRWorkItemCheckWhitelist', params);

// AddMRWorkItemCheckWhitelist
export const AddMRWorkItemCheckWhitelist = (params: {
  IterationUUID: string;
  TAPDUrl: string;
  IssueIDs: string[];
  IssueType: string;
}) => requestAuroraApi<any>('AddMRWorkItemCheckWhitelist', params);

export interface SolutionVersionArtifactBranch {
  BranchName?: string;
  BranchCode?: string;
  BranchType?: string;
  CreatedAt?: string;
  Creator?: string;
  DeletedAt?: string;
  Description?: string;
  ID?: number;
  Label?: string;
  Modifier?: string;
  SolutionVersionID?: string;
  UUID?: string;
  UpdatedAt?: string;
}

// ListMRWorkItemWhitelists 白名单列表
export const ListSolutionVersionArtifactBranches = (params: { SolutionVersionID?: string }) =>
  requestCloudApi<{ ListSolutionVersionArtifactBranches: any }>('ListSolutionVersionArtifactBranches', {
    SolutionVersionArtifactBranch: toListParamsCApi(params, {
      useEqFields: ['SolutionVersionID'],
    }),
  });
// ListSolutionVersionArtifactTagsSimple
export const ListSolutionVersionArtifactTagsSimple = (params: {
  SolutionVersionID?: string;
  ArtifactBranchUUID?: string;
  Arch: string;
}) =>
  requestCloudApi<{ ListProductVersionArtifactTags: any }>('ListSolutionVersionArtifactTagsSimple', {
    ProductVersionArtifactTag: toListParamsCApi(params, {
      useEqFields: ['SolutionVersionID', 'ArtifactBranchUUID', 'Arch'],
    }),
  });

// ListSolutionVersionWithMarket

// export const ListSolutionVersionWithMarket = (params: { Tag?: string; Branch?: string; UUID: string }) =>
//   requestCloudApi<{ ListSolutionVersions: any }>('ListSolutionVersionWithMarket', {
//     SolutionVersion: toListParamsCApi(
//       { ...params },
//       {
//         useEqFields: ['Branch', 'UUID'],
//         order: [{ Key: 'TagNum', Sort: 'DESC' }],
//       },
//     ),
//   });
export interface IDataSolutionVersionsParams {
  SolutionVersion: {
    _Filter?: Filter[];
    _Order: { Key: string; Sort: string }[];
    _Limit: number;
    _Offset: number;
  };
}
export const ListSolutionVersionWithMarket = (params: IDataSolutionVersionsParams) =>
  requestCloudApi<{ ListSolutionVersions: any[] }>('ListSolutionVersionWithMarket', params);
export interface ListSolutionProductRelationRes {
  Items: IListSolutionProductRelation[];
}

export interface IListSolutionProductRelation {
  BpsVisible?: number;
  Branch?: string;
  Category?: string;
  Code?: string;
  FromSpace?: string;
  ID?: number;
  Name?: string;
  Owner?: string;
  ProductUUID?: string;
  SolutionVersionID?: string;
}
export const ListSolutionProductRelation = (params): Promise<ListSolutionProductRelationRes> =>
  requestAuroraApi('ListSolutionProductRelation', params);
export namespace IUpdateVersionReleaseStatisticalReportApi {
  export interface IResponse {
    StatisticalReport: {
      StoryReport: any;
      BugReport: any;
    };
  }
}

export interface ListSolutionVersionArtifactBranchesParams {
  BranchName: string;
  UUID: string;
  BranchType: string;
}

export interface ListSolutionVersionArtifactBranchesResponse {
  BranchCode: string;
  BranchName: string;
  BranchType: string;
  CreatedAt: string;
  Creator: string;
  DataBranch: string;
  DeletedAt: number;
  Description: string;
  EnablePermission: number;
  ID: number;
  Label: string;
  Modifier: string;
  Operator: string | null;
  Owner: string | null;
  SolutionVersionID: string;
  UUID: string;
  UpdatedAt: string;
}

export interface ListSolutionVersionArtifactTags {
  Arch: string;
  TagNum: string;
  PublishStatus: string;
  TagDescription: string;
}
export interface ListSolutionVersionArtifactTagsSimpleResponse {
  SolutionVersionID: string;
  ArtifactBranchUUID: string;
  Arch: string;
  pageSize: number;
  current: number;
}

export interface ListSolutionVersionArtifactTagsSimpleParams {
  SolutionVersionID: string;
  ArtifactBranchUUID: string;
  Arch: string;
  PublishStatus?: string;
  TagDescription?: string;
  Label?: string;
  pageSize: number;
  current: number;
}

export class RequirementIterationManage {
  @TApiAuroraServices()
  static UpdateVersionReleaseStatisticalReport(
    _params: { ReleaseApprovalSheetUUID: string },
    apiResult?: any,
  ): Promise<IUpdateVersionReleaseStatisticalReportApi.IResponse> {
    return apiResult;
  }

  // 更新审批内容

  @TApiAuroraServices()
  static UpdateVersionApprovalStepMessage(
    _params: { StepUUID: string; Comments: string; Content: any },
    apiResult?: any,
  ): Promise<any> {
    return apiResult;
  }

  @TApiDataEngine({
    paramModelName: 'SolutionVersionArtifactBranch',
    paramConversion: {
      useEqualFields: ['SolutionVersionID'],
      page: false,
      order: [{ Key: 'ID', Sort: 'ASC' }],
    },
  })
  static ListSolutionVersionArtifactBranches(
    _params: {
      SolutionVersionID: string;
    },
    _result?: any,
  ): Promise<{ ListSolutionVersionArtifactBranches: ListSolutionVersionArtifactBranchesParams[] }> {
    return _result;
  }

  // @TApiDataEngine({
  //   paramModelName: 'SolutionVersionArtifactTag',
  //   paramConversion: {
  //     useEqualFields: ['SolutionVersionID', 'Arch', 'ArtifactBranchUUID'],
  //     page: true,
  //     order: [{ Key: 'ID', Sort: 'ASC' }],
  //   },
  // })
  // static ListSolutionVersionArtifactTagsSimple(
  //   _params: {
  //     SolutionVersionID: string;
  //     Arch: string;
  //     ArtifactBranchUUID: string;
  //   },
  //   _result?: any,
  // ): Promise<{ ListSolutionVersionArtifactTags: ListSolutionVersionArtifactTags[] }> {
  //   return _result;
  // }

  @TApiAuroraServicesForTable({
    returnModelName: 'Items',
  })
  static ListVersionApprovalStepMessage(
    _params: {
      PageNo?: number;
      PageSize?: number;
      StepUUID: string;
    },
    _options?: {},
    _res?: any,
  ): Promise<any> {
    return _res;
  }

  @TApiDataEngineForTable({
    paramModelName: 'SolutionVersionArtifactTag',
    paramConversion: {
      useEqualFields: ['SolutionVersionID', 'ArtifactBranchUUID', 'Arch', 'PublishStatus'],
      page: true,
      order: [{ Key: 'TagNum', Sort: 'DESC' }],
    },
    returnModelName: 'ListSolutionVersionArtifactTags',
  })
  static ListSolutionVersionArtifactTagsSimple(
    _params: ListSolutionVersionArtifactTagsSimpleParams,
    _res?: Promise<{ success: boolean; total?: number; data: ListSolutionVersionArtifactTagsSimpleResponse[] }>,
  ): Promise<{ success: boolean; total?: number; data: ListSolutionVersionArtifactTagsSimpleResponse[] }> {
    return _res!;
  }
}
