import moment from 'moment';
import { requestAuroraApi, requestCloudApi, toListParamsCApi } from './api';
import { IPropsIssueAppRel } from './interationManage';

// 获取局点出包缺陷序列传递的参数 同时 创建批量出包记录也使用此类型
export interface ISiteIssueSequenceParams {
  /**
   * 解决方案架构组合UUID
   */
  IssueSolutionArchUUID: string;
  /**
   * 是否是全部局点
   */
  IsAllSites: boolean;
  /**
   * 局点选择列表, 如果不是全部局点，需要传选中的局点
   */
  SiteSelections?: IIssueSelectionsResponse[];
}
// 获取局点出包缺陷序列响应数据  同时 创建批量出包记录也使用此类型
export interface IIssueSelectionsResponse {
  ClientName: string;
  SiteUUID: string;
  SiteName: string;
  IssueID: string;
  IssueSolutionArchUUID: string;
  Sequence: number;
}

// 查询可出包的缺陷列表(区分解决方案架构)
export interface IAvailableIssuesResponse {
  /**
   * 缺陷解决方案架构UUID
   */
  IssueSolutionArchUUID: string;
  /**
   * 缺陷ID
   */
  IssueID: string;
  /**
   * 类型
   */
  IssueType: string;
  /**
   * 解决方案版本
   */
  SolutionVersion: string;
  /**
   * 架构
   */
  Arch: string;
  /**
   * 严重程度
   */
  SeverityLevel: string;
  /**
   * 优先级
   */
  Priority: string;
  Title: string;
}

export interface ISiteInfo {
  /**
   * 客户名称
   */
  ClientName: string;
  /**
   * 局点UUID
   */
  SiteUUID: string;
  /**
   * 局点名称
   */
  SiteName: string;
  /**
   * 局点类型 next or 2.0
   */
  SiteType: string;
  /**
   * 是否可出包
   */
  Available: boolean & string;
  ReasonType: string;
  /**
   * 不可出包原因
   */
  Message: string;
  /**
   * 出包状态
   */
  Status: string;
}

export interface ICreatePackageOutRecordParams {
  Sequences?: IIssueSelectionsResponse[];
  IssueSelections: ISiteIssueSequenceParams[];
}

export interface IIssueProblems {
  IssueSolutionArchUUID: string;
  Problems: Array<{
    /**
     * 问题类型，局点：Site, 关联应用：Application
     */
    Type: string;
    /**
     * 问题描述
     */
    Message: string;
  }>;
}

// 获取缺陷关联应用清单
export const listIssueAppRels = (params: { IssueSolutionRelUUID: string; current: number; pageSize: number }) =>
  requestCloudApi<{ ListIssueAppRels: IPropsIssueAppRel[] }>('ListIssueAppRelsWithDetail', {
    IssueAppRel: toListParamsCApi(params, {
      useEqFields: ['IssueSolutionRelUUID'],
    }),
  });

// 获取局点出包缺陷序列请求
export const getSiteIssueSequence = (params: { IssueSelections: ISiteIssueSequenceParams[] }) =>
  requestAuroraApi<{ Sequences: IIssueSelectionsResponse[] }>('GetSiteIssueSequence', params);

// 创建批量出包记录
export const createPackageOutRecord = (params: ICreatePackageOutRecordParams) =>
  requestAuroraApi('CreatePackageOutRecord', params);

// 查询缺陷待出包局点列表
export const listIssueRelatedSites = (params: { IssueSolutionArchUUID: string }) =>
  requestAuroraApi<{ SiteInfos: ISiteInfo[] }>('ListIssueRelatedSites', params);

// 查询可出包的缺陷列表（区分解决方案架构）
export const listAvailableIssues = ({
  PageNo,
  PageSize,
  ...params
}: {
  SolutionVersion?: string;
  Arch?: string;
  PageNo: number;
  PageSize: number;
  CreatedAt?: string;
  TAPD?: string;
}) => {
  const { CreatedAt, TAPD, ...otherParams } = params || {};

  const filters =
    toListParamsCApi(otherParams, {
      useEqFields: ['SolutionVersion', 'Arch', 'IssueID'],
      retainEmptyString: false,
    })._Filter || [];

  if (CreatedAt?.length) {
    const beginTime = moment(CreatedAt?.[0])?.format('YYYY-MM-DD 00:00:00');
    const endTime = moment(CreatedAt?.[1])?.format('YYYY-MM-DD 23:59:59');
    filters.push({
      Key: 'CreatedAt',
      Op: '>=',
      Value: beginTime,
    });
    filters.push({
      Key: 'CreatedAt',
      Op: '<=',
      Value: endTime,
    });
  }

  return requestAuroraApi<{
    Issues: IAvailableIssuesResponse[];
    Total: number;
  }>('ListAvailableIssues', {
    PageNo,
    PageSize,
    TAPD,
    Filters: filters,
  });
};

// 检查缺陷单出包问题;
export const checkIssuePackageProblems = (params: { IssueSolutionArchUUIDs: string[] }) =>
  requestAuroraApi<{
    IssueProblems: IIssueProblems[];
  }>('CheckIssuePackageProblems', params);
