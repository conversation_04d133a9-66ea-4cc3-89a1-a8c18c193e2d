import { TApiAuroraServicesForTable } from '@tencent/tcsc-base';
import type { TAapiDecoratorMoreOptions } from '@tencent/tcsc-base';

export class siteListApi {
  // 查询局点列表
  @TApiAuroraServicesForTable({
    returnModelName: 'SiteList',
  })
  static ListOperationSiteList(
    _params?: {
      PageNo: Number;
      PageSize: Number;
      ClientName?: string;
      SiteName?: string;
      AndonID?: Number;
      LTCID?: Number;
      SiteStatus?: string[];
      AndonSiteStatus?: string[];
    },
    _options?: TAapiDecoratorMoreOptions,
    _res?: any,
  ): Promise<any> {
    return _res;
  }
}
