import { TApiAuroraServices, TApiDataEngine, TApiDataEngineForTable, tapi } from '@tencent/tcsc-base';
import { AxiosRequestConfig } from 'axios';
import type { TApiParamsWithPage, TAapiDecoratorMoreOptions } from '@tencent/tcsc-base';

let baseURL;
if (window.location.hostname.startsWith('dev.') || window.location.hostname.startsWith('pre.')) {
  // 开发环境和预发布环境
  baseURL = `${window.location.protocol}//patch.pri.woa.com:81/dep/apis/v1/`;
} else {
  // 线上环境
  baseURL = `${window.location.protocol}//patch.pri.woa.com/dep/apis/v1/`;
}

// 定义通用请求
const requestPostApi = async (endpoint: string, data: any) => {
  const config: AxiosRequestConfig = {
    baseURL,
    url: endpoint,
    method: 'POST',
    data,
    withCredentials: true, // 允许跨域请求携带 Cookie
  };
  try {
    const response = await tapi.requestNormalApi(config);
    return response?.data;
  } catch (error) {
    throw error;
  }
};

export interface ICreateBugParams {
  // 发现架构
  Arch: string;
  // 交付同学
  DeliverOwners: string;
  DeliverType: '仅后续新增出包' | '指定客户出包' | '存量及新增客户出包';
  // 内容描述
  Description: string;
  // 研发负责人
  DevOwners: string;
  // 是否免测
  IsTestFree: false;
  // 处理人 分号分割
  Owner: string;
  // 优先级
  Priority: string;
  // 缺陷单模板，定义缺陷单类型
  ProcedureUUID: string;
  // 严重程度
  SeverityLevel: string;
  // 发现版本
  SolutionVersion: string;
  // 问题来源 内部发现或者客户名称
  Source: string;
  // 测试同学
  TestOwners: string;
  // 标题
  Title: string;
}

export namespace ICreateIssueRelatedAppsApi {
  export interface ISiteInfo {
    SiteName: string;
    ClientName: string;
    SiteUUID: string;
    SiteType: string;
    OperationTool: string;
  }

  export interface ISite {
    Arch: string;
    SolutionVersionUUID: String;
    SiteInfos: ISiteInfo[];
  }

  export interface IIssueApps {
    Arch: string;
    Solution: string;
    SolutionVersion: string;
    SolutionVersionUUID: string;
    IssueType: string;
    IssueID: string;
    DeliverType: string;
    IssueAppRel: Array<{
      // 应用版本的UUID
      ApplicationBranchUUID: string;
      // 对应的应用名
      ApplicationName: string;
      // // 应用类型，tad 表示tad应用 ted表示老版本的组件
      // ApplicationType: rel.ApplicationType,
      // 架构，x86/arm/power等
      Arch: string;
      // 关联的缺陷单，对应story表中的一个需求单或者bug表中的一个缺陷单
      IssueID: string;
      // 产品版本
      ProductVersion: string;
      // 产品版本UUID
      ProductVersionUUID: string;
    }>;
  }

  export interface IRequest {
    IssueApps: IIssueApps[];
    Sites?: ISite[];
  }
  export interface IResponse {
    Item: IIssueApps[];
    Error: {
      Message: string;
    };
  }
}

export interface GetTAPDOperationSheetParams {
  // TapdID: string;
  // TapdType: string;
  IssueSolutionRelUUID: string;
  OperationTool: string;
}

export interface GetTAPDOperationSheet {
  SheetID: string;
  Name: string;
  Description: string;
  Status: string;
  Creator: string;
  Modifier: string;
  CreatedAt: string;
  UpdatedAt: string;
}

export interface GetTAPDOperationSheetResponse {
  Dawn: GetTAPDOperationSheet;
  Tops: GetTAPDOperationSheet;
  OriginTops: GetTAPDOperationSheet;
  Kaleido: GetTAPDOperationSheet;
}

export namespace IListProjectClientSiteApi {
  export interface ISiteBaseInfo {
    // SiteUUID 局点UUID
    SiteUUID: string;
    // SiteName 局点名称
    SiteName: string;
    // SiteType 局点类型，next, 2.0
    SiteType: string;

    BugfixType?: string;
  }

  export interface IClientSites {
    // ClientUUID 客户 UUID
    ClientUUID: string;
    // ClientName 客户名称
    ClientName: string;
    ProjectSite: ISiteBaseInfo[];
  }

  export interface IResponse {
    ClientInfos: IClientSites[];
  }

  export interface IRequest {
    SolutionVersionID: string;
    Arch: string;
    Applications?: string[];
    // IsFullSites 全量可推送局点  == true 表示全量可交付局点    ==false  精准过滤，只支持出过指定应用的，架构组合match 的，可交付局点
    IsFullSites: boolean;
    PatchID?: string;
    // 对于Bugfix，也可以选择到Patch局点
    ShowPatchSites?: boolean;
  }
}

export namespace IListDeliverySitesForIssueSolutionVersionApi {
  export interface IRequest {
    IssueSolutionRelUUIDs: string[];
  }

  export interface IResponse {
    IssueSolutionRelSites: Array<{
      IssueSolutionRelUUID: string;
      Sites: ICreateIssueRelatedAppsApi.ISiteInfo[];
    }>;
  }
}
export namespace IGetDependencyEvaluationsApi {
  export interface GetDependencyEvaluationsParams {
    IssueID: string;
    TAPDUrl: string;
    SolutionVersionUUID: string;
    ApplicationUUIDs: string[];
  }

  export interface DependentApplication {
    Application: string;
    ApplicationUUID: string;
  }

  export interface DependencyRelation {
    Application: string;
    ApplicationUUID: string;
    Creator: string;
    DependentApplications: DependentApplication[];
    AdaptedApplications: DependentApplication[];
  }

  export interface GetDependencyEvaluationsResponse {
    DependencyRelations: DependencyRelation[];
  }
}

export namespace IGetAppDependenciesApi {
  export interface GetAppDependenciesParams {
    ApplicationUUID: string;
    SolutionVersionUUID: string;
    Target: string;
  }

  export interface GetAppDependenciesResponse {
    ID: number;
    SolutionName: string;
    SolutionVersion: string;
    SolutionVersionUUID: string;
    ProductName: string;
    ProductVersion: string;
    Application: string;
    ApplicationUUID: string;
    DependentProductName: string;
    DependentProductVersion: string;
    ApplicationBranch: string;
    DependentApplication: string;
    DependentApplicationUUID: string;
    DependentApplicationBranch: string;
    CreatedAt: string;
    DeletedAt: number;
  }
}

export namespace IListUnEvaluatedAppsApi {
  export interface ListUnEvaluatedAppsParams {
    ApplicationUUIDs: string[];
    SolutionVersionUUID: string;
    TAPDUrl: string;
    IssueID: string;
  }
  export interface ListUnEvaluatedAppsResponse {
    HasAllApplicationEvaluated: boolean;
    UnEvaluatedApplications: { Application: string; ApplicationUUID: string }[];
  }
}

export interface ConvertToTopsSheetParams {
  SheetId: string;
  IssueSolutionRelUUID: string;
}

export interface ConvertToTopsSheetResponse {
  DownloadURL: string;
  RequestId: string;
  TopsSheetId: string;
  TopsSheetYaml: string;
}

export namespace IEvaluateDependenciesApi {
  export interface DependencyRelation {
    Application: string;
    ApplicationUUID: string;
    DependentApplications: { Application: string; ApplicationUUID: string }[];
    AdaptedApplications: { Application: string; ApplicationUUID: string }[];
  }
  export interface EvaluateDependenciesParams {
    IssueID: string;
    TAPDUrl: string;
    SolutionVersionUUID: string;
    DependencyRelations: DependencyRelation[];
    IssueSolutionRelUUID: string;
  }
}
export type GetTAPDDeliverySitesParams =
  | {
      TapdID: string;
      TapdType: string;
      WorkspaceID: string;
    }
  | {
      IssueSolutionRelUUID: string;
    };

export interface Site {
  BugfixType: string;
  CheckMessage: string;
  ClientName: string;
  DawnPlan: string;
  ForceDawnPlan: string;
  MultipleArch: string;
  OperationTool: string;
  PackageApplicationArch: string;
  PackageStatus: string;
  PushPackageType: string;
  SiteArch: string;
  SiteAzConfig: any;
  SiteName: string;
  SiteType: string;
  SiteUUID: string;
  SolutionVersionArchs: any;
  ZoneName: string;
}

export interface GetTAPDDeliverySitesResponse {
  OperationTools: string[];
  RequestId: string;
  Sites: Site[];
}

export interface GetTAPDDeliverySites {
  SiteName: string;
  ClientName: string;
  SiteUUID: string;
  SiteType: string;
  ZoneName: string;
  OperationTool: string;
  PatchageApplicationArch: string;
  PackageStatus: string;
}

export interface GetComponentListForTAPDParams {
  TAPD: string;
}

export interface Component {
  ApplicationName: string;
  ComponentName: string;
  Version: string;
  BuildTime: string;
  CommitID: string;
  PackageVersion: string;
  IsPlan: boolean;
  IsQci: boolean;
}

export interface GetComponentListForTAPDResponse {
  ComponentList: Component[];
}
export interface ListSiteVariableDictionariesParams {
  SiteUUID?: string[];
  Key?: string;
}

export interface ListSiteVariableDictionariesResponse {
  Key: string;
  SiteUUID: string;
  Value: string;
}
export interface ListIssueSolutionTestResultsParams {
  IssueSolutionRelUUID: string;
  pageSize?: number;
  current?: number;
}

export interface ListIssueSolutionTestResultsResponse {
  Conclusion: string;
  CreatedAt: string;
  Creator: string;
  DeletedAt: number;
  IssueSolutionRelUUID: string;
  ReasonClass: string;
  Remark: string;
  SheetResults: {
    Conclusion: string;
    OperationTool: string;
    Rollback: {
      Conclusion: string;
      Remark: string;
    };
    Update: {
      Conclusion: string;
      Remark: string;
    };
  }[];
  UUID: string;
  UpdatedAt: string;
  ValidatedPackages: {
    ApplicationVersion: string;
    BuildTime: string;
    CommitID: string;
    PackageUUID: string;
  }[];
}
// 定义请求参数类型
export interface GetIssueSolutionTestResultParams {
  IssueSolutionRelUUID?: string;
  UUID?: string;
  pageSize?: number;
  current?: number;
}

// 定义返回数据类型
export interface GetIssueSolutionTestResultResponse {
  Conclusion: string;
  CreatedAt: string;
  Creator: string;
  DeletedAt: number;
  IssueSolutionRelUUID: string;
  ReasonClass: string;
  Remark: string;
  SheetResults: {
    Conclusion: string;
    OperationTool: string;
    Rollback: {
      Conclusion: string;
      Remark: string;
    };
    Update: {
      Conclusion: string;
      Remark: string;
    };
  }[];
  UUID: string;
  UpdatedAt: string;
  ValidatedPackages: {
    ApplicationVersion: string;
    BuildTime: string;
    CommitID: string;
    PackageUUID: string;
  }[];
}
// 定义请求参数类型
export interface CreateIssueSolutionTestResultResultParams {
  IssueSolutionRelUUID: string;
  ValidatedPackages: {
    PackageUUID: string;
  }[];
  SheetResults: {
    OperationTool: string;
    Update: {
      Conclusion: string;
      Remark: string;
    };
    Rollback: {
      Conclusion: string;
      Remark: string;
    };
    Conclusion: string;
  }[];
  Conclusion: string;
  ReasonClass: string;
  Remark: string;
}

// 定义返回数据类型
export interface CreateIssueSolutionTestResultResultResponse {
  UUID: string;
  RequestId: string;
}

export interface DiffRegisteredApplicationPackagesParams {
  IssueSolutionRelUUID: string;
}

export interface ApplicationInfo {
  ApplicationName: string;
  PackageUUID: string;
  ComponentName: string;
  Version: string;
  BuildTime: string;
  CommitID: string;
  NextPackageVersion: string;
  OldPackageVersion: string;
  IsPlan: boolean;
  IsQci: boolean;
  IsDifferent: boolean;
}

// 基础对象类型
interface Application {
  ApplicationName: string;
  ID: number;
  UUID: string;
}

interface ApplicationBranch {
  Application: Application;
  ApplicationUUID: string;
  BranchName: string;
  BranchType: string;
  ID: number;
  UUID: string;
}

interface ApplicationPackage {
  ApplicationVersion: string;
  Arch: string;
  BuildTime: string;
  CommitID: string;
  ID: number;
  UUID: string;
}

interface ProductInfo {
  Code: string;
  ID: number;
  Name: string;
  NameEN: string;
  UUID: string;
}

interface ProductVersionInfo {
  ID: number;
  ProductID: string;
  ProductInfo: ProductInfo;
  UUID: string;
}

// 关联关系类型
export interface IssueApplicationRelation {
  ApplicationBranch: ApplicationBranch;
  ApplicationBranchUUID: string;
  ApplicationName: string;
  ApplicationType: string;
  Arch: string;
  BasePackageVersion: string;
  DevPackageUUID?: string; // 可选字段
  IssueSolutionRelUUID: string;
  LatestRegisteredApplicationPackage: ApplicationPackage;
  LatestRegisteredPackageUUID: string;
  ProductVersion: string;
  ProductVersionInfo: ProductVersionInfo;
  ProductVersionUUID: string;
  ReleaseApplicationPackage: ApplicationPackage;
  ReleasePackageUUID: string;
  Source: string;
  Status: string;
  UUID: string;
  DevApplicationPackage?: ApplicationPackage; // 可选字段
}

// 主解决方案类型
export interface IssueSolutionRelsWithDetail {
  Arch: string;
  CreatedAt: string;
  Creator: string;
  DeliverType: string;
  IsTestFree: boolean;
  IssueAppRel: IssueApplicationRelation[];
  IssueID: string;
  IssueType: string;
  OperationSheet: {
    ID: number;
    SheetID: string;
    Status: string;
  };
  OperationSheetUUID: string;
  SolutionVersion: string;
  SolutionVersionUUID: string;
  Status: string;
  TapdUrl: string;
  TestFreeApplier: string;
  TestFreeReason: string;
  UUID: string;
}

// 产品能力类型扩展（根据 ProductCapability 结构补充）

export interface DiffRegisteredApplicationPackagesResponse {
  ApplicationList: ApplicationInfo[];
}
export interface SyncTAPDApplicationPackagesParams {
  Method: 'NextToOld' | 'OldToNext';
  IssueSolutionRelUUID: string;
}

export class IterationManage {
  @TApiAuroraServices({
    paramModelName: 'Bug',
  })
  static async CreateBug(_params: ICreateBugParams, apiResult?: any): Promise<unknown> {
    return apiResult;
  }

  @TApiDataEngineForTable({
    paramModelName: 'IssueSolutionRel',
    paramConversion: {
      useEqualFields: ['IssueID', 'IssueType', 'TapdUrl'],
    },
    returnModelName: 'ListIssueSolutionRels',
  })
  static ListIssueSolutionRelsWithDetail(
    _params: TApiParamsWithPage<any>,
    apiResult?: any,
  ): Promise<{ data: IssueSolutionRelsWithDetail[] }> {
    return apiResult;
  }

  // 创建缺陷评估组件
  @TApiAuroraServices()
  static CreateIssueRelatedApps(
    _params: ICreateIssueRelatedAppsApi.IRequest,
    apiResult?: any,
  ): Promise<ICreateIssueRelatedAppsApi.IResponse> {
    return apiResult;
  }

  @TApiAuroraServices()
  static ListProjectClientSite(
    _parmas: IListProjectClientSiteApi.IRequest,
    apiResult?: any,
  ): Promise<IListProjectClientSiteApi.IResponse> {
    return apiResult;
  }

  // 修改修改缺陷单出包类型
  @TApiAuroraServices()
  static UpdateIssueSolutionArchDeliveryType(
    _params: { UUID: string; DeliverType: string; SiteInfos: ICreateIssueRelatedAppsApi.ISiteInfo[] },
    apiResult?: any,
  ): Promise<unknown> {
    return apiResult;
  }

  // 获取缺陷单对应的解决方案版本架构组合下的对应交付单的局点列表请求
  @TApiAuroraServices()
  static ListDeliverySitesForIssueSolutionVersion(
    _params: { IssueSolutionRelUUIDs: string[] },
    apiResult?: any,
  ): Promise<IListDeliverySitesForIssueSolutionVersionApi.IResponse> {
    return apiResult;
  }

  @TApiDataEngine({
    paramModelName: 'TapdWorkspaceConfig',
    paramConversion: {
      useEqualFields: ['WorkspaceID'],
      selectFields: ['tenant'],
    },
  })
  static GetTapdWorkspaceConfig(
    _params: { WorkspaceID: string },
    _res?: any,
  ): Promise<{ GetTapdWorkspaceConfig: { Tenant: string } }> {
    return _res;
  }

  // 获取已评估的解决方案版本列表
  @TApiAuroraServices()
  static ListEvaluatedSolutionVersions(
    _params: { IssueID: string; TAPDUrl: string },
    apiResult?: any,
  ): Promise<unknown> {
    return apiResult;
  }
  // 确认回合版本评估
  @TApiAuroraServices()
  static EvaluateStorySolutionVersions(
    _params: { TAPDUrl: string; SourceSolutionVersion: string; SolutionVersions: string[] },
    apiResult?: any,
  ): Promise<unknown> {
    return apiResult;
  }

  // 删除解决方案
  @TApiAuroraServices()
  static DeleteIssueSolutionRel(_params: { UUID: string }, apiResult?: any): Promise<unknown> {
    return apiResult;
  }

  @TApiAuroraServices({})
  static GetTAPDOperationSheet(
    _params: GetTAPDOperationSheetParams,
    _res?: Promise<GetTAPDOperationSheetResponse>,
  ): Promise<GetTAPDOperationSheetResponse> {
    return _res!;
  }

  @TApiAuroraServices({
    customErrorTips: true,
  })
  static ConvertToTopsSheet(
    _params: ConvertToTopsSheetParams,
    _res?: Promise<ConvertToTopsSheetResponse>,
  ): Promise<ConvertToTopsSheetResponse> {
    return _res!;
  }

  @TApiAuroraServices({})
  static GetTAPDDeliverySites(
    _params: GetTAPDDeliverySitesParams,
    _res?: Promise<GetTAPDDeliverySitesResponse>,
  ): Promise<GetTAPDDeliverySitesResponse> {
    return _res!;
  }

  @TApiAuroraServices({})
  static GetComponentListForTAPD(
    _params: GetComponentListForTAPDParams,
    _res?: Promise<GetComponentListForTAPDResponse>,
  ): Promise<GetComponentListForTAPDResponse> {
    return _res!;
  }
  @TApiAuroraServices({})
  static UpdateAppVersionInTopsSheet(_params: { IssueSolutionRelUUID?: string; PatchID?: string }): Promise<any> {}

  @TApiAuroraServices({})
  static UpdateIssueAppRel(
    _params: { UUID: string; ApplicationBranchUUID: string; ProductVersionUUID: string },
    _res?: Promise<any>,
  ): Promise<any> {
    return _res!;
  }

  @TApiDataEngine({
    paramModelName: 'SiteVariableDictionary',
    paramConversion: {
      useEqualFields: ['Key'],
      selectFields: ['`site_uuid`', '`key`', '`value`'],
      page: false,
    },
  })
  static ListSiteVariableDictionaries(
    _params: ListSiteVariableDictionariesParams,
    _res?: Promise<{ ListSiteVariableDictionaries: ListSiteVariableDictionariesResponse[] }>,
  ): Promise<{ ListSiteVariableDictionaries: ListSiteVariableDictionariesResponse[] }> {
    return _res!;
  }

  @TApiDataEngineForTable({
    paramModelName: 'IssueSolutionTestResult',
    paramConversion: {
      useEqualFields: ['IssueSolutionRelUUID'], // 指定需要转换为 = 操作符的字段
      page: true, // 启用分页
      order: [{ Key: 'CreatedAt', Sort: 'DESC' }], // 指定排序规则
    },
    returnModelName: 'ListIssueSolutionTestResults', // 指定返回的模型名称
  })
  static ListIssueSolutionTestResults(
    _params: ListIssueSolutionTestResultsParams,
    _options?: TAapiDecoratorMoreOptions,
    _res?: Promise<TApiParamsWithPage<{ data: ListIssueSolutionTestResultsResponse[] }>>,
  ): Promise<TApiParamsWithPage<{ data: ListIssueSolutionTestResultsResponse[] }>> {
    return _res!;
  }

  @TApiDataEngine({
    paramModelName: 'IssueSolutionTestResult', // 指定请求参数模型名称
    paramConversion: {
      useEqualFields: ['UUID', 'IssueSolutionRelUUID'], // 指定需要转换为 = 操作符的字段
      page: true,
      order: [{ Key: 'CreatedAt', Sort: 'DESC' }], // 指定排序规则
    },
  })
  static GetIssueSolutionTestResult(
    _params: GetIssueSolutionTestResultParams,
    _res?: Promise<{ GetIssueSolutionTestResult: GetIssueSolutionTestResultResponse }>,
  ): Promise<{ GetIssueSolutionTestResult: GetIssueSolutionTestResultResponse }> {
    return _res!;
  }

  @TApiAuroraServices({})
  static CreateIssueSolutionTestResult(
    _params: CreateIssueSolutionTestResultResultParams,
    _res?: Promise<CreateIssueSolutionTestResultResultResponse>,
  ): Promise<CreateIssueSolutionTestResultResultResponse> {
    return _res!;
  }

  @TApiAuroraServices({})
  static DiffRegisteredApplicationPackages(
    _params: DiffRegisteredApplicationPackagesParams,
    _res?: Promise<DiffRegisteredApplicationPackagesResponse>,
  ): Promise<DiffRegisteredApplicationPackagesResponse> {
    return _res!;
  }

  @TApiAuroraServices({})
  static SyncTAPDApplicationPackages(_params: SyncTAPDApplicationPackagesParams, _res?: Promise<any>): Promise<any> {
    return _res!;
  }

  // TAPD 关联极光 2.0 应用
  @TApiAuroraServices({})
  static AddTAPDRelatedComponents(
    _params: { TapdURL: string; ComponentNames: string[] },
    _res?: Promise<any>,
  ): Promise<any> {
    return _res!;
  }

  // TAPD 关联极光 2.0 应用
  @TApiAuroraServices({})
  static DeleteTAPDRelatedComponents(
    _params: { TapdURL: string; ComponentNames: string[] },
    _res?: Promise<any>,
  ): Promise<any> {
    return _res!;
  }

  // 获取tapd单数据
  @TApiAuroraServices({})
  static GetTAPDContent(_params: { TapdURL: string }, _res?: Promise<any>): Promise<any> {
    return _res!;
  }
}

// 依赖数据
export const getDependencyEvaluations = (params: IGetDependencyEvaluationsApi.GetDependencyEvaluationsParams) =>
  requestPostApi('getDependencyEvaluations', params);

// 确认依赖评估
export const evaluateDependencies = (params: IEvaluateDependenciesApi.EvaluateDependenciesParams) =>
  requestPostApi('evaluateDependencies', params);

// 谁依赖我，我依赖谁
export const getAppDependencies = (params: IGetAppDependenciesApi.GetAppDependenciesParams) =>
  requestPostApi('getAppDependencies', params);

// 评估校验
export const listUnEvaluatedApps = (params: IListUnEvaluatedAppsApi.ListUnEvaluatedAppsParams) =>
  requestPostApi('listUnEvaluatedApps', params);
