/*
 * @Author: lucyfang
 * @Date: 2024-12-13 11:05:41
 * @LastEditors: lucyfang
 * @LastEditTime: 2024-12-24 17:17:41
 * @Description: 请输入注释信息
 */
import { TApiAuroraServices, TApiAuroraServicesForTable } from '@tencent/tcsc-base';
import type { TAapiDecoratorMoreOptions } from '@tencent/tcsc-base';
export interface ListPatchParams {
  MarketStatus?: string;
  ProductVersionUUID?: string;
  PatchID?: string;
  PageNo?: number;
  PageSize?: number;
  Arch?: string;
  ArtifactBranchUUID?: string;
  FuzzyProductCode?: string;
  IsOwner?: boolean;
  PatchName?: string;
  PatchQueueUUID?: string;
  Status?: string[];
}
export interface PatchMarket {
  CreatedAt: string;
  Creator: string;
  Description: string;
  ID: number;
  PatchID: string;
  SourceTenantUUID: string;
  Status: string;
  TenantUUIDList: string;
  UUID: string;
  UpdatedAt: string;
  Updater: string;
}
export interface PatchItem {
  Applications: any[];
  Arch: string;
  BaselineTagNum: number;
  BranchCode: string;
  BranchName: string;
  DeliveryStatus: any;
  Issues: any[];
  Name: string;
  OperationSheetID: string;
  Owner: string;
  PatchID: string;
  PatchQueue: {
    Arch: string;
    ArtifactBranchUUID: string;
    Description: string;
    ID: number;
    Name: string;
    NamingRule: string;
    SolutionVersionUUID: string;
    UUID: string;
  };
  PatchQueueUUID: string;
  PatchTagNum: number;
  PatchType: string;
  Products: any[];
  SolutionCode: string;
  SolutionName: string;
  SolutionVersionCode: string;
  SolutionVersionUUID: string;
  Status: string;
  Stories: any[];
  Warning: string;
  PatchMarket: PatchMarket[];
}
export interface GeneratePatchMarketDeliveryParams {
  PatchID: string;
}
export interface DeliveryDiffPlanInfoParams {
  DeliveryUUID: string;
  SiteUUID: string;
}

export interface PlanSolutionInfo {
  arch: string | null;
  solutionBaselineName: string;
  solutionBaselineUUID: string;
  solutionCode: string;
  solutionName: string;
  solutionNameEn: string;
  solutionUUID: string;
  versionBranchCode: string;
  versionBranchName: string;
  versionName: string;
  versionSnapshot: string;
  versionTag: string;
  versionTagCreationTime: string;
  versionUUID: string;
}

export interface NewProductVersion {
  ProductCode: string;
  ProductDataBranch: string;
  ProductDataTagNum: number;
  ProductVersionID: number;
  ProductVersionName: string;
  ProductVersionTag: string;
  ProductVersionUUID: string;
}

export interface PlanProductVersion {
  BpsVisible: boolean;
  Branch: string;
  Category: string;
  Code: string;
  FromSpace: string;
  ID: number;
  Name: string;
  NameEN: string;
  Owner: string;
  ProductUUID: string;
  ProductVersionDataBranchCode: string;
  ProductVersionDataBranchName: string;
  ProductVersionDataBranchType: string;
  ProductVersionDataBranchUUID: string;
  ProductVersionID: number;
  ProductVersionName: string;
  ProductVersionTag: string;
  ProductVersionTagNum: number;
  ProductVersionUUID: string;
  SolutionVersionID: string;
  SolutionVersionRelRequire: string;
  Source: string;
  Status: string;
  Tag: string;
  VersionInfo: {
    ID: number;
    Tag: string;
    TagNum: number;
  };
}

export interface DeliveryDiffPlanInfoResponse {
  PlanSolutionInfo: PlanSolutionInfo;
  MessageInfo?: {
    Level: string;
    Message: string;
  };
  ProductList: {
    NewProductVersion: NewProductVersion;
    PlanProductVersion: PlanProductVersion | null;
  }[];
  RequestId: string;
}
export interface GenerateWorkflowInstanceParams {
  WorkflowUUID: string;
  WorkflowTagNum: number;
  GlobalValues: {
    SiteUUID: string;
    DeliveryUUID: string;
  };
}

export interface GenerateWorkflowInstanceResponse {
  WorkflowInstanceID: number;
  WorkflowID: number;
  RequestId: string;
}

export class PatchPublishMarketApi {
  @TApiAuroraServicesForTable({
    returnModelName: 'List',
  })
  static ListMarketPatch(_params: ListPatchParams, _options?: TAapiDecoratorMoreOptions, _res?: any): Promise<any> {
    return _res!;
  }

  @TApiAuroraServices({})
  static GeneratePatchMarketDelivery(_params: GeneratePatchMarketDeliveryParams, _res?: Promise<any>): Promise<any> {
    return _res!;
  }

  @TApiAuroraServices({})
  static DeliveryDiffPlanInfo(
    _params: DeliveryDiffPlanInfoParams,
    _res?: Promise<DeliveryDiffPlanInfoResponse>,
  ): Promise<DeliveryDiffPlanInfoResponse> {
    return _res!;
  }

  @TApiAuroraServices({})
  static GenerateWorkflowInstance(
    _params: GenerateWorkflowInstanceParams,
    _res?: Promise<GenerateWorkflowInstanceResponse>,
  ): Promise<GenerateWorkflowInstanceResponse> {
    return _res!;
  }
}
