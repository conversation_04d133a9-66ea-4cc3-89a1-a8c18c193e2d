import { requestCloudApi, toListParamsCApi } from './api';

export interface ISolution {
  Code: string;
  CreatedAt: string;
  Creator: string;
  DeletedAt: number;
  Description: string;
  ID: number;
  Modifier: string;
  Name: string;
  NameEN: string;
  Owner: string;
  TenantID: string;
  UUID: string;
  UpdatedAt: string;
}

export interface ISolutionVersion {
  ApplicationConfig: string;
  AuditDescription: string;
  AuditStatus: string;
  BaselineID: string;
  Branch: string;
  Code: string;
  CreatedAt: string;
  Creator: string;
  DeletedAt: number;
  Description: string;
  Etag: string;
  ID: number;
  InheritID: string;
  Modifier: string;
  ServiceBaselineTagID: string;
  SolutionID: string;
  Status: string;
  Tag: string;
  TagNum: number;
  TagStatus: string;
  UUID: string;
  UpdatedAt: string;
}
export interface IPatchQueue {
  Arch: string;
  ArtifactBranchUUID: string;
  CreatedAt: string;
  Name: string;
  SolutionName: string;
  SolutionUUID: string;
  SolutionVersionUUID: string;
  SolutionVersionName: string;
  UUID: string;
}

// 查询解决方案列表
export const listSolution = () =>
  requestCloudApi<{ ListSolutions: ISolution[] }>('ListSolutions', {
    Solution: toListParamsCApi(
      {
        TenantID: (window as any).jiguang_currentNs,
      },
      {
        useEqFields: ['TenantID'],
        page: false,
      },
    ),
  });

// 根据解决方案UUID查询解决方案版本清单
export const listSolutionVersion = (params: { SolutionUUID?: string }) =>
  requestCloudApi<{ ListSolutionVersions: ISolutionVersion[] }>('ListSolutionVersionWithMarketFlag', {
    SolutionVersion: toListParamsCApi(
      {
        SolutionID: params.SolutionUUID,
        Status: ['01', '02', '50', '60'],
        Tag: '',
        Branch: 'master',
      },
      {
        useEqFields: ['SolutionID', 'Tag', 'Branch'],
        page: false,
        order: [{ Key: 'CreatedAt', Sort: 'DESC' }],
      },
    ),
  });
