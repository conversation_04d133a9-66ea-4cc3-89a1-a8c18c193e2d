import {
  TApiAuroraServices,
  TApiAuroraServicesForTable,
  TApiDataEngine,
  TApiDataEngineForTable,
} from '@tencent/tcsc-base';
import { Filter, requestAuroraApi, requestCloudApi } from './api';
import type { TAapiDecoratorMoreOptions, TApiParamsWithPage } from '@tencent/tcsc-base';

export interface IListPatchRequestParams {
  SolutionUUID?: string;
  SolutionVersionUUID?: string;
  Arch?: string;
  Status?: string[];
  /**
   * 产品Code模糊查询
   */
  FuzzyProductCode?: string;
  PatchIDList?: string;
  PageNo: number;
  PageSize: number;
}

export interface IList {
  PatchID: string;
  SolutionCode: string;
  SolutionVersionCode: string;
  Arch: string;
  Status: string;
  BaselineTag: string;
  PatchTag: string;
  Name: string;
  Issues: {
    IssueID: string;
    Title: string;
    SeverityLevel: string;
  }[];
  Products: {
    Name: string;
    Code: string;
    NameEn: string;
    VersionCode: string;
  }[];
  Applications: {
    Name: string;
    ArtifactTag: string;
  }[];
  DeliveryStatus: {
    Status: string;
    Sites: {
      Name: string;
    }[];
  }[];
  PatchQueue?: {
    Arch: string;
    ArtifactBranchUUID: string;
    Description: string;
    ID: number;
    Name: string;
    SolutionVersionUUID: string;
    UUID: string;
    PatchQueueUUID: string;
  };
}

export interface IDetailRequestParams {
  Patch: {
    _Filter?: Filter[];
  };
}

export interface ISelectPatchRequestParams {
  Operation: string;
  PatchID: string;
  IssueSolutionUUIDs: string[];
}

export interface IPatchDetails {
  Arch: string;
  BaselineTagName: string;
  BaselineTagUUID: string;
  CreatedAt: string;
  Creator: string;
  Description: string;
  Name: string;
  PatchID: string;
  PatchTagName: string;
  PatchTagUUID: string;
  PatchedApplications: {
    ApplicationName: string;
    CreatedAt: string;
    Details: {
      Latest: string;
      Selected: {
        [k: string]: unknown;
      }[];
      Baseline: string;
    };
    ProductUUID: string;
    ProductVersionUUID: string;
    UpdatedAt: string;
    Product: {
      Code: string;
      Name: string;
    };
    ProductVersion: {
      Name: string;
    };
  }[];
  SelectedIssues: {
    CreatedAt: string;
    Issue: {
      Description: string;
      IssueID: string;
      ProductVersion: string;
      SeverityLevel: string;
    };
    IssueID: string;
    IssueSolution: {
      Arch: string;
      Creator: string;
      IssueAppRel: {
        ApplicationName: string;
      }[];
      Status: string;
      TapdUrl: string;
      UUID: string;
    };
    IssueSolutionRelUUID: string;
    Warning: string;
  }[];
  Solution: {
    Code: string;
    Name: string;
  };
  SolutionUUID: string;
  SolutionVersion: {
    Code: string;
  };
  SolutionVersionUUID: string;
  Status: string;
  TenantUUID: string;
  Verifications: {
    CreatedAt: string;
    Description: string;
    Status: string;
    VerificationTag: {
      [k: string]: unknown;
    };
    VerificationTagUUID: string;
    WorkflowID: number & string;
  }[];
}

export interface IPatchIssues {
  PatchID?: string;
  ProductCode?: string;
  ApplicationName?: string;
  IssueID?: string;
  TapdID?: string;
  PageNo?: number;
  PageSize?: number;
  Severity?: string[];
  OrderBy?: { Key: string; Sort: string }[];
  Filters?: any;
}

export interface IBaselineTagsRequestParams {
  SolutionVersionUUID: string;
  Arch: string;
}

export interface IBaselineTags {
  Arch: string;
  ArtifactBranchUUID: string;
  Creator: string;
  DataTag: string;
  Label: string;
  PublishStatus: string;
  UUID: string;
  SolutionVersionArtifactBranch: {
    BranchCode: string;
    BranchName: string;
    Description: string;
  };
  TagNum: number;
  Description: string;
  TagDescription: string;
}

export interface ICreatePatchRequestParams {
  SolutionUUID: string;
  SolutionVersionUUID: string;
  Arch: string;
  BaselineTagID: string;
  SolutionVersionID: number;
  ArtifactBranchUUID: string;
  PatchQueueUUID: string;
  PatchName: string;
  PatchType: string;
  MainProductCode?: string;
  UpdateItem?: string;
  IncrementalNum?: string;
  Owner: string;
}

export interface IPublishPatchRequestParams {
  PatchID: string;
  Name: string;
  NeedSheet?: boolean;
  SiteUUIDList?: string[];
  NeedCreateTag?: boolean;
}

export interface IModifyPatchRequestParams {
  PatchID: string;
  Applications: {
    ApplicationName: string;
    ApplicationPackageID?: string;
    PackageVersionName?: string;
    ApplicationBranchUUID?: string;
  }[];
  Operation: string;
}

export interface IDataSolutionVersionsParams {
  SolutionVersion: {
    _Filter?: Filter[];
    _Order: { Key: string; Sort: string }[];
    _Select?: string[];
  };
}

export interface IUpdateVerification {
  PatchVerificationRel: {
    /**
     * 描述信息内容
     */
    Variable?: string;
    ID?: Number;
    Description?: string;
    _Filter?: Filter[];
  };
}

export interface IssueDependencies {
  PatchID: string;
  IssueSolutionRelUUIDs: string[];
}

export interface IArtifactBranchesParams {
  SolutionVersionArtifactBranch: {
    _Filter?: Filter[];
    _Order: { Key: string; Sort: string }[];
  };
}

export interface IListBranches {
  BranchCode: string;
  BranchName: string;
  BranchType: string;
  CreatedAt: string;
  Creator: string;
  DeletedAt: number;
  Description: string;
  EnablePermission: number;
  ID: number;
  Label: string;
  Modifier: string;
  Operator: string;
  Owner: string;
  SolutionVersionID: string;
  Status: string;
  UUID: string;
  UpdatedAt: string;
}

// 获取Patch列表页
export const listPatch = (params: IListPatchRequestParams) => requestAuroraApi<{ List: IList[] }>('ListPatch', params);

// 获取Patch详情
export const getPatchDetails = (params: IDetailRequestParams) =>
  requestCloudApi<{ GetPatch: IPatchDetails }>('GetPatchDetails', params);

// 勾选缺陷单
export const selectPatchIssues = (params: ISelectPatchRequestParams) => requestAuroraApi('SelectPatchIssues', params);

// 获取缺陷单列表
export const listPatchIssues = (params: IPatchIssues) => requestAuroraApi<{ Issues: any[] }>('ListPatchIssues', params);

// 修改Patch中的应用制品版本，可以是添加、更新。
export const modifyPatchApplications = (params: IModifyPatchRequestParams) =>
  requestAuroraApi('ModifyPatchApplications', params);

// 创建patch时展示出的patch基线列表
export const listBaselineTags = (params: IBaselineTagsRequestParams) =>
  requestAuroraApi<{ Tags: IBaselineTags[] }>('ListPatchBaselineTags', params);

// 新建Patch
export const createPatch = (params: ICreatePatchRequestParams) =>
  requestAuroraApi<{ PatchID: string }>('CreatePatch', params);

// 发布Patch测试流程
export const publishPatch = (params: IPublishPatchRequestParams) => requestAuroraApi('PublishPatch', params);

// 发起Patch测试出包流程
export const createPatchVerification = (params: { PatchID: string; PackageUUIDList?: string[] }) =>
  requestAuroraApi('CreatePatchVerification', params);

// Patch详情页中，调整应用制品版本时，展示出来某个应用可用的制品版本列表
export const listPatchApplicationPackages = (params: {
  PatchID?: string;
  ApplicationName?: string;
  ApplicationBranchUUID?: string;
  ApplicationUUID?: string;
}) => requestAuroraApi<{ ApplicationPackageList: any[] }>('ListPatchApplicationPackages', params);

// 获取应用列表
export const listApplications = (params: { pageSize: number; pageNo: number }) =>
  requestAuroraApi<{ Applications: { ApplicationName: string }[] }>('ListApplications', params);
export const ListApplicationMeta = (params: { PageSize: number; ApplicationNameKeyWord?: string }) =>
  requestAuroraApi<{ ApplicationMetaList: { ApplicationName: string }[] }>('ListApplicationMeta', params);

// 获取解决方案数据快照版本列表
export const listDataSolutionVersions = (params: IDataSolutionVersionsParams) =>
  requestCloudApi<{ ListSolutionVersions: any[] }>('ListSolutionVersions', params);

// 更新Patch单中的基本信息
export const updatePatchInfos = (params: {
  PatchID: string;
  SolutionVersionID: number;
  Description: string;
  PatchName?: string;
}) => requestAuroraApi('UpdatePatchInfos', params);

// 更新Patch验证的描述信息
export const updatePatchApplicationRel = (params: IUpdateVerification) =>
  requestCloudApi('UpdatePatchVerificationRel', params);

export const getPatchIssueDependencies = (params: IssueDependencies) =>
  requestAuroraApi<{ Dependencies: any }>('GetPatchIssueDependencies', params);

export const listPatchApplicationInfos = (params: { PatchID: string }) =>
  requestAuroraApi<{ List: any[] }>('ListPatchApplicationInfos', params);

export const getSolutionVersion = (params) =>
  requestCloudApi<{ GetSolutionVersion: any }>('GetSolutionVersion', params);

// 制品分支下拉列表接口
export const listSolutionVersionArtifactBranches = (params: IArtifactBranchesParams) =>
  requestCloudApi<{ ListSolutionVersionArtifactBranches: any[] }>('ListSolutionVersionArtifactBranches', params);

// 初始制品TAG下拉列表
export const listSolutionVersionArtifactTagsSimple = (params: {
  SolutionVersionArtifactTag: {
    _Filter?: Filter[];
    _Order: { Key: string; Sort: string }[];
  };
}) => requestCloudApi<{ ListSolutionVersionArtifactTags: any }>('ListSolutionVersionArtifactTagsSimple', params);

export const getSolutionVersionArtifactTag = (params: {
  SolutionVersionArtifactTag: {
    _Filter?: Filter[];
  };
}) => requestCloudApi<{ GetSolutionVersionArtifactTag: any }>('GetSolutionVersionArtifactTag', params);

// 同步至周期出包
export const syncPatchToJG = (params: { PatchID: string }) => requestAuroraApi('SyncPatchToJG', params);

export interface AddApplicationToPatchParams {
  PatchID: string;
  ApplicationList: {
    ApplicationName: string;
    PackageVersion: string;
    Arch?: string;
  }[];
  IgnoreErrorApplication?: boolean;
}

export interface AddApplicationToPatchResponse {
  Applications: {
    ApplicationName: string;
    ApplicationBranchUUID?: string;
    ApplicationPackageID: number;
    ApplicationPackageUUID: string;
    PackageVersionName: string;
    Arch: string;
    CanImport: boolean;
    Reason?: string;
  }[];
}
export interface PatchArtifactTagApplicationParams {
  PatchID: string;
  SourceTagUUID: string;
  TargetTagUUID: string;
}

export interface PackageInfo {
  ArtifactTagID: number;
  ProductCode: string;
  ProductVersionID: string;
  ProductVersionName: string;
  TagNum: number;
  ArtifactBranchUUID: string;
  ArtifactBranchName: string;
  SubsystemID: string;
  ApplicationBranchUUID: string;
  ApplicationName: string;
  ApplicationPackageID: number;
  PackageVersionName: string;
}

export interface PatchArtifactTagApplicationResponse {
  ProductCode: string;
  ProductVersionName: string;
  ApplicationName: string;
  ApplicationBranchUUID: string;
  SubSystemID: string;
  SourcePackageInfo?: PackageInfo | null;
  TargetPackageInfo?: PackageInfo | null;
  PatchPackageInfo?: PackageInfo | null;
}

export interface GetPatchMarketPublishPreDataParams {
  PatchID: string;
}

export interface GetPatchMarketPublishPreDataResponse {
  PatchID: string;
  PatchName: string;
  ProductList: {
    ProductCode: string;
    ProductDataBranch: string;
    ProductDataTagNum: number;
    ProductVersionID: number;
    ProductVersionName: string;
    ProductVersionUUID: string;
  }[];
  TenantList: {
    Name: string;
    UUID: string;
  }[];
}
export interface PublishPatchToMarketParams {
  PatchID: string;
  TenantUUIDList: string[];
}

export interface RecallMarketPatchParams {
  SubAccountUin: string;
  PatchID: string;
  Reason: string;
}

export interface ListMultiArchPatchesParams {
  Name?: string;
  SolutionUUID?: string;
  SolutionVersionUUID?: string;
  PatchID?: string;
  PageNo?: number;
  PageSize?: number;
  Order?: [{ Key: 'CreatedAt'; Sort: 'DESC' }];
}
export interface MultiArchPatchList {
  Name: string;
  PatchMultiArchUUID: string;
  SolutionUUID: string;
  SolutionName: string;
  SolutionVersionUUID: string;
  SolutionVersionCode: string;
  Creator: string;
  CreatedAt: string;
  UpdatedAt: string;
}
export interface CreateMultiArchPatchParams {
  SolutionUUID: string;
  SolutionVersionUUID: string;
  Name: string;
  X86PatchID?: string;
  ARMPatchID?: string;
}
export interface GetAssociatedPatchesParams {
  SolutionUUID: string;
  SolutionVersionUUID: string;
  Arches: string[];
}

export interface AssociatedPatch {
  PatchName: string;
  PatchID: string;
  Arch: string;
}

export interface GetAssociatedPatchesResponse {
  AssociatedPatches: AssociatedPatch[];
}

export interface CreateMultiArchPatchResponse {
  RequestId: string;
  Success: boolean;
}
export interface AssociatedPatch {
  PatchID: string;
  SolutionName: string;
  SolutionCode: string;
  SolutionVersionCode: string;
  SolutionVersionUUID: string;
  Arch: string;
  Status: string;
  BranchCode: string;
  BranchName: string;
  BaselineTagNum: number;
  PatchTagNum: number;
  Name: string;
  OperationSheetID: string;
  Issues: any[];
  Stories: any[];
  Products: any[];
  Applications: any[];
  DeliveryStatus: string | null;
  Warning: string;
  Owner: string;
  PatchQueueUUID: string;
  PatchQueue: {
    Description: string;
    ID: number;
    Name: string;
    NamingRule: string;
    UUID: string;
  };
  PatchType: string;
  IsPublishMarket: number;
}

export class PatchManageApi {
  @TApiAuroraServicesForTable({
    returnModelName: 'Issues',
    specifiedFieldConvertion: [
      {
        field: 'Filters',
        convertType: 'onlyFilter',
        paramConversion: {
          dateFields: ['CreatedAt'],
        },
      },
    ],
  })
  static ListPatchIssues(
    _params: {
      PatchID: string;
      Filters: {
        CreatedAt?: string[];
      };
    },
    _res?: any,
  ): Promise<{ Issues: any[] }> {
    return _res;
  }

  @TApiAuroraServices({})
  static BulkAddPatchApplications(
    _params: AddApplicationToPatchParams,
    _res?: Promise<AddApplicationToPatchResponse>,
  ): Promise<AddApplicationToPatchResponse> {
    return _res!;
  }

  @TApiAuroraServices({})
  static GetPatchArtifactTagDiffInfo(
    _params: PatchArtifactTagApplicationParams,
    _res?: Promise<{ PatchArtifactTagApplication: PatchArtifactTagApplicationResponse[] }>,
  ): Promise<{ PatchArtifactTagApplication: PatchArtifactTagApplicationResponse[] }> {
    return _res!;
  }

  @TApiDataEngine({
    paramModelName: 'SolutionVersionDataBranch',
    paramConversion: {
      page: false, // 指定不进行分页处理
      useEqualFields: ['SolutionVersionID', 'Status'],
      order: [
        {
          Sort: 'ASC',
          Key: 'BranchType',
        },
        {
          Sort: 'DESC',
          Key: 'UpdatedAt',
        },
      ],
    },
  })
  static ListSolutionVersionDataBranches(
    _params: {
      SolutionVersionID: string;
      Status: '10';
    },
    _res?: any,
    // 返回值为Promise
  ): Promise<{ ListSolutionVersionDataBranches: IListBranches[] }> {
    return _res;
  }
  // 创建附件
  @TApiDataEngine({
    paramModelName: 'PatchAttachment',
    paramConversion: {
      page: false, // 指定不进行分页处理
      useEqualFields: ['PatchID', 'FileName', 'DownloadPath', 'Source'],
      unConversionFields: ['PatchID', 'FileName', 'DownloadPath', 'Source'],
    },
  })
  static CreatePatchAttachment(
    _params: {
      PatchID: string;
      FileName: string;
      DownloadPath: string;
      Source: string;
    },
    _res?: any,
    // 返回值为Promise
  ): Promise<{ CreatePatchAttachment: any[] }> {
    return _res;
  }
  // 删除附件
  @TApiDataEngine({
    paramModelName: 'PatchAttachment',
    paramConversion: {
      page: false, // 指定不进行分页处理
      useEqualFields: ['ID'],
    },
  })
  static DeletePatchAttachment(
    _params: {
      ID: string;
    },
    _res?: any,
    // 返回值为Promise
  ): Promise<{}> {
    return _res;
  }

  // 查询附件
  @TApiDataEngineForTable({
    paramModelName: 'PatchAttachment',
    returnModelName: 'ListPatchAttachments',
    paramConversion: {
      useEqualFields: ['PatchID'],
    },
  })
  static ListPatchAttachments(
    _params: {
      PatchID: string;
    },
    _options?: TAapiDecoratorMoreOptions,
    _res?: Promise<TApiParamsWithPage<{ data: any[] }>>,
    // 返回值为Promise
  ): Promise<TApiParamsWithPage<{ data: any[] }>> {
    return _res!;
  }

  // 获取 Patch 公共市场发布前置数据
  @TApiAuroraServices({})
  static GetPatchMarketPublishPreData(
    _params: GetPatchMarketPublishPreDataParams,
    _res?: Promise<GetPatchMarketPublishPreDataResponse>,
  ): Promise<GetPatchMarketPublishPreDataResponse> {
    return _res!;
  }

  // 发布 Patch 单至公共市场
  @TApiAuroraServices({})
  static PublishPatchToMarket(_params: PublishPatchToMarketParams, _res?: Promise<any>): Promise<{}> {
    return _res!;
  }

  // 撤回发布 Patch 至公共市场
  @TApiAuroraServices({})
  static RecallMarketPatch(_params: RecallMarketPatchParams, _res?: Promise<any>): Promise<{}> {
    return _res!;
  }

  @TApiAuroraServicesForTable({
    returnModelName: 'DeliveryDetailInfo',
  })
  static ListVersionApprovalStepMessage(
    _params: {
      PageNo?: number;
      PageSize?: number;
      StepUUID: string;
    },
    _options?: {},
    _res?: any,
  ): Promise<any> {
    return _res;
  }

  @TApiAuroraServices({})
  static ValidateMarketSupportSolution(_params: { SolutionVersionUUID: string }, _res?: Promise<any>): Promise<{}> {
    return _res!;
  }

  // 多架构patch列表
  @TApiAuroraServicesForTable({
    returnModelName: 'MultiArchPatchList',
  })
  static ListMultiArchPatches(
    _params: ListMultiArchPatchesParams,
    _options?: TAapiDecoratorMoreOptions,
    _res?: Promise<TApiParamsWithPage<{ data: MultiArchPatchList[] }>>,
  ): Promise<TApiParamsWithPage<{ data: MultiArchPatchList[] }>> {
    return _res!;
  }
  // 创建多架构patch
  @TApiAuroraServices({})
  static CreateMultiArchPatch(
    _params: CreateMultiArchPatchParams,
    _res?: Promise<CreateMultiArchPatchResponse>,
  ): Promise<CreateMultiArchPatchResponse> {
    return _res!;
  }
  // 获取关联多架构patch
  @TApiAuroraServices({})
  static ListNeedAssociatedPatch(
    _params: GetAssociatedPatchesParams,
    _res?: Promise<GetAssociatedPatchesResponse>,
  ): Promise<GetAssociatedPatchesResponse> {
    return _res!;
  }
  // 获取多架构关联的patch
  @TApiAuroraServicesForTable({ returnModelName: 'AssociatedPatches' })
  static ListAssociatedPatchesByPatchMultiArchUUID(
    _params: { PatchMultiArchUUID: string },
    _res?: Promise<{ data: AssociatedPatch[] }>,
  ): Promise<{ data: AssociatedPatch[] }> {
    return _res!;
  }

  // 删除关联Patch
  @TApiAuroraServices({})
  static DeleteAssociatedPatch(
    _params: { PatchMultiArchUUID: string; PatchID: string },
    _res?: Promise<CreateMultiArchPatchResponse>,
  ): Promise<CreateMultiArchPatchResponse> {
    return _res!;
  }

  // 删除多架构Patch
  @TApiAuroraServices({})
  static DeleteMultiArchPatch(
    _params: { PatchMultiArchUUID: string },
    _res?: Promise<CreateMultiArchPatchResponse>,
  ): Promise<CreateMultiArchPatchResponse> {
    return _res!;
  }

  // 关联关联patch
  @TApiAuroraServices({})
  static AddAssociatedPatch(
    _params: { PatchMultiArchUUID: string; X86PatchID?: string; ARMPatchID?: string },
    _res?: Promise<CreateMultiArchPatchResponse>,
  ): Promise<CreateMultiArchPatchResponse> {
    return _res!;
  }
}
