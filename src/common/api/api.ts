import { TApiDataEngine, TcscApiError, tapi } from '@tencent/tcsc-base';

// const AppCode = 'defect_manage';
export interface IResponse {
  RequestId?: string;
  VerificationID?: Number;
  WorkflowID?: Number;
  WorkflowInstanceID?: Number;
  Error?: {
    Message: string;
  };
  // 分页才会有这个参数
  Total?: number;
}

/* 列表params格式 */
export interface GetListParams {
  _Offset?: number;
  _Limit?: number;
  _Filter?: Filter[];
  _Order?: Order[];
}

/* Filter格式 */
export interface Filter {
  Key: string;
  Op: string;
  Value?: string;
  Values?: string[];
}

export interface Order {
  Key: string;
  Sort: 'DESC' | 'ASC';
}

export type IResponseResult<T extends object> = IResponse & T;

export async function requestCloudApi<T extends object>(Action: string, params = {}) {
  return new Promise<IResponseResult<T>>((resolve, reject) => {
    tapi
      .requestDataEngineApi<T>(Action, {
        data: params,
        autoConversionParam: false,
        customErrorTips: true,
      })
      .then((res) => {
        resolve(res);
      })
      .catch((error: TcscApiError) => {
        if (error.type === 'dataError') {
          resolve({
            Error: error.errorDetail,
          } as any);
        } else {
          reject(error.message);
        }
      });
  });
}

export async function requestAuroraApi<T extends object>(Action: string, params = {}): Promise<IResponseResult<T>> {
  return new Promise<IResponseResult<T>>((resolve, reject) => {
    tapi
      .requestAuroraApi<T>(Action, {
        data: params,
        customErrorTips: true,
      })
      .then((res) => {
        resolve(res as IResponseResult<T>);
      })
      .catch((error: TcscApiError) => {
        if (error.type === 'dataError') {
          resolve({
            Error: error.errorDetail,
          } as any);
        } else {
          reject(error.message);
        }
      });
  });
}

/**
 * 云api接口参数格式，将 ProTable 中的request属性的参数 params 转成接口所需格式
 * @param params 查询表格条件
 * @param restParam 其他附加的查询条件，例如外层的id之类的
 * @param order 返回数据的排序
 */
export const toListParamsCApi = (
  params: { [key in string]: any },
  options?: {
    // 使用 = 的字段
    useEqFields?: string[];
    // 日期字段
    dateFields?: string[];
    // 更多额外的参数
    restParam?: { [key in string]: any };
    // 排序字段
    order?: Order[];
    // 是否分页
    page?: boolean;
    // 过滤条件中是否保留空字符串, 默认为true
    retainEmptyString?: boolean;
  },
): GetListParams => {
  const { restParam, order, useEqFields = [], page = true, retainEmptyString = true } = options || {};
  const { pageSize = 10, current = 1, ...rest } = params;

  const Filters = Object.keys(rest)
    .filter((key) => {
      // 如果不保留空格且值为空字符串
      if (retainEmptyString === false && rest[key] === '') {
        return false;
      }
      return rest[key] !== undefined && !(Array.isArray(rest[key]) && rest[key].length === 0);
    })
    .map((key) => {
      let op = rest[key]?.Op;
      if (!op) {
        if (Array.isArray(rest[key])) {
          op = 'in';
        } else {
          op = useEqFields.includes(key) ? '=' : 'like';
        }
      }
      const newObj: any = {
        Key: key,
        Op: op,
      };
      // 主要是为了适配 正则模式下 Op: "regexp" 传入自定义的 Value
      // 多选字段为 Values，Values为数组；字符串时为Value

      if (Array.isArray(rest[key]) || Array.isArray(rest[key]?.Values)) {
        newObj.Values = rest[key]?.Values || rest[key];
      } else if (rest[key]?.Value === null) {
        newObj.Value = null;
      } else {
        const value = rest[key]?.Value || rest[key];
        newObj.Value = value;
      }
      if (newObj.Op === 'like') {
        newObj.Value = `%${newObj.Value}%`;
      }
      return newObj;
    });

  const result = {
    ...restParam,
    _Filter: Filters,
    _Order: order,
  };
  if (page) {
    Object.assign(result, {
      _Offset: (current - 1) * pageSize,
      _Limit: pageSize,
    });
  }

  return result;
};

export interface INormalRequestOptions {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  data?: any;
  appCode?: string;
  headers?: Record<string, any>;
  domain?: string;
  prefixUrl?: string;
}

export const requestNormalApi = async (options: INormalRequestOptions) => {
  // const domain = options.domain || (window as any).jiguang_domain;
  const { url, method = 'GET', data, appCode, headers } = options;
  const currentAppCode = appCode || window.location.pathname.split('/')[2];
  const requestHeader = {
    'content-type': 'application/json',
    Version: '1.0.0',
    authorization: localStorage.getItem(currentAppCode),
    ...headers,
  };
  const requestOptions: Record<string, any> = {
    headers: requestHeader,
    method,
  };
  let requestUrl = url;
  if (method.toUpperCase() === 'GET') {
    if (data) {
      const queryParams = Object.keys(data)
        .map((k) => `${encodeURIComponent(k)}=${encodeURIComponent(data[k])}`)
        .join('&');
      requestUrl = `${requestUrl.includes('?') ? '&' : '?'}${queryParams}`;
    }
  } else if (data) {
    if (data instanceof FormData || data instanceof Blob || data instanceof File || data instanceof Buffer) {
      requestOptions.body = data;
    } else {
      requestOptions.body = JSON.stringify(data);
    }
  }

  let fetchUrl = `//${(window as any).jiguang_domain}${requestUrl}`;
  if (options?.prefixUrl) {
    if (options.prefixUrl.startsWith('http')) {
      fetchUrl = `${options?.prefixUrl}${requestUrl}`;
    } else {
      fetchUrl = `//${options?.prefixUrl}${requestUrl}`;
    }
  }

  // 发送请求并返回 promise 对象 注意 fetch不会拦截其他异常请求️
  return fetch(fetchUrl, requestOptions).then(
    // 可在这里封装 响应拦截函数
    (response) => response.json(),
  );
};

// 获取公共数据字典
export const listGlobalLookups = ({ lookupCodes }: { lookupCodes: Array<string> }) =>
  requestCloudApi<{ ListProductDictionaries: any[] }>('ListProductDictionaryDetails', {
    ProductDictionary: toListParamsCApi(
      {
        Type: lookupCodes || [],
        TenantID: '0',
      },
      {
        order: [{ Key: 'Sort', Sort: 'ASC' }],
        page: false,
        useEqFields: ['TenantID'],
      },
    ),
  });

// 获取公共数据字典
export const listCurrentTenantLookups = ({ lookupCodes }: { lookupCodes: Array<string> }) =>
  requestCloudApi<{ ListProductDictionaries: any[] }>('ListProductDictionaryDetails', {
    ProductDictionary: toListParamsCApi(
      {
        Type: lookupCodes || [],
        TenantID: (window as any).jiguang_currentNs,
      },
      {
        order: [{ Key: 'Sort', Sort: 'ASC' }],
        page: false,
        useEqFields: ['TenantID'],
      },
    ),
  });

/**
 * 极光next接口API调用定义:方法,产品,版本,地域
 */
export const jgNextCapiRequest = tapi.capiFactory('OperateAuroraData', 'pcss', '2023-04-18', 33); // 33地域是南京一区

/**
 * 极光2.0接口调用
 */
export const jg2CapiRequest = tapi.capiFactory('OperateAurora2Data', 'pcss', '2023-04-18', 33);

export class ProductDictionaryApi {
  @TApiDataEngine({
    paramConversion: {
      useEqualFields: ['Type', 'TenantID'],
    },
    defaultParams: {
      Type: 'ProductCategory',
      TenantID: '0',
    },
  })
  static ListProductDictionaryDetails(
    // 装饰器会自动给接口注入请求的返回值,并放到接口的最后一个参数后面
    _result?: any,
  ) {
    return _result.ListProductDictionaries.filter((t) => t.Type === 'ProductCategory').reduce((ret, t) => {
      // eslint-disable-next-line no-param-reassign
      ret[t.Code] = t;
      return ret;
    }, {});
  }
}
