import { Bubble } from '@tencent/tea-component';
import React from 'react';

const domain = window.jiguang_domain;
const currentNs = window.jiguang_currentNs;
const preDomain = 'pre.jiguang.woa.com';
const devDomain = 'dev.jiguang.woa.com';
const proDomain = 'jiguang.woa.com';
const tiyanNs = 'f79e20b77cc3464bbb5ddd16bc816643';

/**
 * 获取文本溢出显示的bubble组件
 * @param text
 * @returns
 */
export const getEllipsisBubbleText = (text: string) => (
  <Bubble content={text}>
    <span className={'one-line-text'}>{text}</span>
  </Bubble>
);

/**
 * 判断当前是否为开发环境、预发环境、生产环境且为体验空间
 * @returns
 */
export const checkCurrentEnvState = () => {
  const allowDomain = [preDomain, devDomain];
  const proDoaminAndTiyanNS = domain === proDomain && currentNs === tiyanNs;
  return allowDomain?.includes(domain) || proDoaminAndTiyanNS;
};

// 数据状态
export const ArtifactBranchType = {
  RELEASE: 'release',
  TEST: 'test',
};

/**
 * 获取制品分支的tag颜色
 */
export function getArtifactBranchTypeTeaColor(branchType: string) {
  switch (branchType) {
    case ArtifactBranchType.RELEASE:
      return 'error';
    case ArtifactBranchType.TEST:
      return 'success';
    default:
      return 'warning';
  }
}
