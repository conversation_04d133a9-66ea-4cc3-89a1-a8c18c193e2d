/*
 * @Author: superfeng
 * @Date: 2023-03-21 19:35:16
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-03-06 15:18:33
 * @Description: 请输入注释信息
 */
import { message } from '@tencent/tea-component';
import qs from 'querystring';

// 获取url链接参数
export const getUrlParams = () => {
  if (window.location.search.substr(1)) {
    return qs.parse(window.location.search.substr(1)) as Record<string, string>;
  }
  return {};
};

export const getAppVersion = (appInfo: {
  ApplicationVersion: string;
  BuildTime: string;
  CommitID: string;
  Arch: string;
}) =>
  `${appInfo?.ApplicationVersion || ''}-${appInfo?.BuildTime || ''}-${appInfo?.CommitID?.substr(0, 7) || ''}.${
    appInfo?.Arch || ''
  }`;

export function copyText(text: string, msg = '内容已成功复制') {
  (window as any).getSelection().removeAllRanges();
  // 创建input对象
  const selectEl = document.createElement('pre');
  selectEl.style.position = 'fixed';
  selectEl.style.left = '10000px';
  // 将文本框插入到NewsToolBox这个之后
  const toolBoxWrap = document.body;
  // 添加元素
  toolBoxWrap.appendChild(selectEl);
  selectEl.textContent = text;
  const range = document.createRange();

  range.selectNodeContents(selectEl);
  const selection = window.getSelection();
  selection?.removeAllRanges();
  selection?.addRange(range);
  let flag: boolean;
  try {
    // 执行复制
    flag = document.execCommand('Copy', false, undefined);
    message.success({
      content: msg,
    });
  } catch (eo) {
    flag = false;
    message.warning({
      content: '内容复制错误，请重试',
    });
  }
  // 删除元素
  toolBoxWrap.removeChild(selectEl);
  return flag;
}

// 是否极光管理员
export const isAdmin = () => window.jiguang_currentRole === 'jiguang2_manager';
