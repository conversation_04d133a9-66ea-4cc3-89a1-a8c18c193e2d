import { TcsColumnType, TcsColumns, TcsDictItem } from '@tencent/tcs-component';
import ExcelJS from 'exceljs';
import { get } from 'lodash-es';
import moment from 'moment';

interface ProcessCellParams {
  valueType: TcsColumnType['valueType'];
  data: any;
  valueEnum?: any;
  rowData: any;
  rowIndex: number;
  dictType?: string;
  dataDictionary: TcsDictItem[];
}

interface GenerateExcelParams {
  columns: TcscExcelExportColumns[];
  tableData: any[];
  fileName?: string;
  dataDictionary: TcsDictItem[];
}

export interface TcscExcelExportColumns extends TcsColumns<any> {
  // 某些表格的列的render会返回一个jsx,这个是excel无法识别的，所以提供了renderExcelText来处理
  renderExcelText?: (text: any, record: any, index: number) => string;
}

// 辅助函数，用于处理不同类型的URL
function getFullUrl(url: string | undefined): string | undefined {
  if (!url) {
    return undefined;
  }
  if (/^(f|ht)tps?:\/\//i.test(url)) {
    // 如果URL以协议开头，直接返回
    return url;
  }
  if (/^\/\//.test(url)) {
    // 如果URL以//开头，追加当前页面的协议
    return window.location.protocol + url;
  }
  if (/^\//.test(url)) {
    // 如果URL以/page开头，追加当前页面的origin
    return window.location.origin + url;
  }
  // 如果URL不符合上述任何一种格式，返回undefined
  return undefined;
}

function processCell(params: ProcessCellParams, column: TcscExcelExportColumnType): any {
  if (column.renderExcelText) {
    return column.renderExcelText(params.data, params.rowData, params.rowIndex);
  }
  if (column.render) {
    return column.render(params.data, params.rowData, params.rowIndex, null as any, null as any);
  }

  const { valueType, data, valueEnum, dictType, dataDictionary } = params;
  let parsedData: any;
  switch (valueType) {
    case 'select': {
      if (typeof valueEnum === 'object' && valueEnum[data]) {
        parsedData = valueEnum[data].text || valueEnum[data];
      } else {
        parsedData = data;
      }
      break;
    }
    case 'dateTime': {
      parsedData = moment(data).format('YYYY-MM-DD HH:mm:ss');
      break;
    }
    case 'date': {
      parsedData = moment(data).format('YYYY-MM-DD');
      break;
    }
    // case 'progress': {
    //   parsedData = data / 100; // Convert to a decimal for percentage format in Excel
    //   break;
    // }
    case 'digit': {
      parsedData = Number(data);
      break;
    }
    case 'dictSelect': {
      if (!dictType) return data;
      const dictItem = dataDictionary.find((item) => item.Type === dictType && item.Code === data);
      parsedData = dictItem ? dictItem.Name : data;
      break;
    }
    case 'text':
    default: {
      parsedData = data;
      break;
    }
  }
  return parsedData;
}

export async function generateExcel(params: GenerateExcelParams): Promise<void> {
  const { columns: originColumns, tableData, fileName = 'export.xlsx', dataDictionary } = params;
  const columns = originColumns.filter((item) => item.valueType !== 'option');
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Sheet1');
  // 设置工作表视图以冻结表头行（第一行）
  worksheet.views = [{ state: 'frozen', ySplit: 1 }];
  // 计算需要冻结的列数
  const fixedLeftColumnsCount = params.columns.filter((column) => column.fixed === 'left').length;
  // 设置工作表视图以冻结固定在左侧的列
  if (fixedLeftColumnsCount > 0) {
    worksheet.views = [{ state: 'frozen', xSplit: fixedLeftColumnsCount, ySplit: 1 }];
  }
  // Set header style and height
  worksheet.getRow(1).height = 22;
  columns.forEach((column, index) => {
    const headerCell = worksheet.getCell(1, index + 1);
    headerCell.value = column.title;
    headerCell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFD8F8FF' },
    };
    headerCell.font = {
      bold: true,
      italic: false,
      size: 12,
      name: '微软雅黑',
    };
    headerCell.alignment = {
      vertical: 'middle',
      horizontal: 'left',
      wrapText: false,
    };
    headerCell.border = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' },
    };
  });

  // Add data and set content cell height
  tableData.forEach((row, rowIndex) => {
    const currentRow = worksheet.getRow(rowIndex + 2);
    currentRow.height = 20; // Slightly higher height for content rows
    columns.forEach((column, colIndex) => {
      const cell = currentRow.getCell(colIndex + 1);
      const cellData = Array.isArray(column.dataIndex) ? get(row, column.dataIndex) : row[column.dataIndex];
      const cellValue = processCell(
        {
          valueType: column.valueType,
          data: cellData,
          valueEnum: column.valueEnum,
          rowData: row,
          rowIndex,

          dictType: column.fieldProps?.dictType,
          dataDictionary,
        },
        column,
      );
      // if (column.valueType === 'progress') {
      //   cell.numFmt = '0%';
      // }
      // else
      if (column.linkable && column.linkProps) {
        const { linkUrl, linkText } = column.linkProps;
        const text = linkText ? linkText(cellData, row, rowIndex) : cellData;
        const url = linkUrl ? linkUrl(cellData, row, rowIndex) : cellData;
        const fullUrl = getFullUrl(url);
        if (fullUrl) {
          cell.value = { text, hyperlink: fullUrl };
          cell.style = {
            font: {
              color: { argb: 'FF0000FF' }, // 蓝色
              underline: true,
            },
          };
        } else {
          cell.value = text;
        }
      } else {
        cell.value = cellValue;
      }

      if (
        column.valueType === 'select' &&
        typeof column.valueEnum === 'object' &&
        column.valueEnum[cellData] &&
        column.valueEnum[cellData].color
      ) {
        const { color } = column.valueEnum[cellData];
        cell.font = {
          color: { argb: `FF${color}` },
        };
      }
      // 如果是dictSelect类型，设置文字颜色
      if (column.valueType === 'dictSelect' && column.fieldProps?.dictType) {
        const dictItem = dataDictionary.find(
          (item) => item.Type === column.fieldProps.dictType && item.Code === cellData,
        );
        if (dictItem?.Extra?.Color) {
          cell.font = {
            color: { argb: dictItem.Extra.Color.replace('#', 'FF') },
          };
        }
      }
    });
  });
  const defaultColumnWidthInChars = 30; // 标准列宽
  const maxTotalWidthInChars = columns.length * defaultColumnWidthInChars;

  // 假设每个字符宽度大约等于7个像素
  const pixelsPerCharWidth = 7;
  const totalColumnsWidth = columns.reduce((totalWidth, column) => {
    let width: number;
    if (typeof column.width === 'string' && column.width.endsWith('%')) {
      // 如果宽度是百分比，计算相对于最大总宽度的宽度
      width = (parseFloat(column.width) / 100) * maxTotalWidthInChars;
    } else if (typeof column.width === 'number') {
      // 如果宽度是数字（像素），转换为字符宽度
      width = column.width / pixelsPerCharWidth;
    } else {
      // 如果没有指定宽度或宽度无效，使用默认最小宽度
      width = defaultColumnWidthInChars;
    }
    return totalWidth + width;
  }, 0);
  const fullWidthInChars = totalColumnsWidth;

  // Set column widths
  columns.forEach((column, index) => {
    let { width } = column;
    if (typeof width === 'string' && width.endsWith('%')) {
      // const fullWidthInChars = 100; // Full width is 100 characters
      const percent = parseFloat(width) / 100;
      width = fullWidthInChars * percent;
    } else if (typeof width === 'number') {
      width = width;
    } else {
      width = defaultColumnWidthInChars; // Default width
    }
    worksheet.getColumn(index + 1).width = width;
  });

  // Export Excel file
  try {
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    a.click();
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error exporting Excel file:', error);
  }
}
