import React, { createContext, useState } from 'react';

export interface SheetStageIPops {
  UUID: string;
  Name: string;
  StageOrder: number;
  Status: string;
  ApprovalLevel?: string;
}
export interface IProps {
  updateData: boolean;
  handleUpdateSuccess: () => void;
  stageInfo: any;
  getStageInfo: (value) => void;
  stageOwner: boolean;
  getApprovalStages: (value) => void;
  approvalSheetStage: SheetStageIPops[];
  baseInfo: Record<string, any>;
  isManage: boolean;
  stageDetail: Record<string, any>;
  productLevelStepData: any;
}

const ReleaseVersionContext = createContext<IProps>({
  updateData: true,
  handleUpdateSuccess: () => {},
  stageInfo: null,
  getStageInfo: (value) => value,
  stageOwner: false,
  getApprovalStages: (value) => value,
  approvalSheetStage: [],
  baseInfo: {},
  isManage: false,
  stageDetail: {},
  productLevelStepData: null,
});
export const ReleaseVersionProvider: React.FC = ({ children }) => {
  const [updateData, setUpdateData] = useState<boolean>(false);
  const [stageInfo, setStageInfo] = useState<any>();
  const [stageOwner, setStageOwner] = useState<boolean>(false);
  const [approvalSheetStage, setApprovalSheetStage] = useState<any>([]);
  const [baseInfo, setBaseInfo] = useState<any>();
  const [isManage, setIsManage] = useState<boolean>(false);
  const [stageDetail, setStageDetail] = useState<any>();
  const [productLevelStepData, setProductLevelStepData] = useState<any>();

  const handleUpdateSuccess = () => {
    setUpdateData(!updateData);
  };
  const getStageInfo = (value) => {
    const stepData = value?.ApprovalTasks?.[0]?.ApprovalSteps?.[0];
    const productLevelStepData = value?.ApprovalTasks;
    const isOwners = stepData?.Owners?.includes(window?.jiguang_username);
    setStageOwner(isOwners);
    setStageInfo(stepData);
    setProductLevelStepData(productLevelStepData);
    setStageDetail(value);
  };
  const getApprovalStages = (value) => {
    setApprovalSheetStage(value?.ApprovalStages);
    setBaseInfo(value);
    const creator = value?.Creator?.includes((window as any).jiguang_username);
    const owner = value?.Owners?.Owners?.includes((window as any).jiguang_username);
    setIsManage(creator || owner);
  };

  return (
    <ReleaseVersionContext.Provider
      value={{
        updateData,
        handleUpdateSuccess,
        stageInfo,
        getStageInfo,
        stageOwner,
        getApprovalStages,
        approvalSheetStage,
        baseInfo,
        isManage,
        stageDetail,
        productLevelStepData,
      }}
    >
      {children}
    </ReleaseVersionContext.Provider>
  );
};
export default ReleaseVersionContext;
