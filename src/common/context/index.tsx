/*
 * @Author: superfeng
 * @Date: 2022-08-03 16:41:52
 * @LastEditors: superfeng
 * @LastEditTime: 2023-05-15 16:25:31
 * @Description: 请输入注释信息
 */
import { TcsContext } from '@tencent/tcs-component';
import React, { createContext, useContext, useEffect, useState } from 'react';

export interface ILookup {
  Name: string;
  Code: string;
  Type: string;
  Extra?: {
    Color?: string;
  };
}

export interface IState {
  lookupMaps: Record<string, ILookup[]>;
}

export enum ActionType {
  INIT_STATE = 'init_state',
}

export interface IInitData {
  type: ActionType.INIT_STATE;
  data: {
    id: string;
    item: IState;
  };
}

export type IReducerData = {
  type: ActionType;
  data: any;
};

const initState: IState = {
  lookupMaps: {},
};

const StateContext = createContext(initState);

export const useStore = (): IState => useContext(StateContext);

export const DefectManageProvider: React.PropsWithChildren<any> = ({ children }) => {
  const { dictList } = useContext(TcsContext);
  const [lookupMaps, setLookupMaps] = useState<{ [key: string]: ILookup[] }>({});

  useEffect(() => {
    const maps: Record<string, ILookup[]> = {};
    dictList?.forEach((item) => {
      if (!maps[item.Type]) {
        maps[item.Type] = [];
      }
      maps[item.Type].push(item);
    });
    setLookupMaps(maps);
  }, [dictList, dictList?.length]);

  return (
    <StateContext.Provider
      value={{
        lookupMaps,
      }}
    >
      {children}
    </StateContext.Provider>
  );
};
