/*
 * @Author: superfeng
 * @Date: 2023-03-27 19:40:55
 * @LastEditors: superfeng
 * @LastEditTime: 2023-04-07 14:19:12
 * @Description: 请输入注释信息
 */
import { useCallback, useMemo } from 'react';
import { useStore, ILookup } from '@/common/context/index';
import { isNil } from 'lodash';

export default (codes: string[] = []) => {
  const { lookupMaps = {} } = useStore();

  const getLookupByCode = useCallback(
    (type: String, code: String) => {
      if (isNil(code)) {
        return undefined;
      }
      const list = lookupMaps?.[type];
      if (list?.length) {
        return list.find((item) => item.Code === code);
      }
      return undefined;
    },
    [lookupMaps],
  );

  const getLookupByType = useCallback((type: String) => lookupMaps?.[type] || [], [lookupMaps]);

  const lookups = useMemo(() => {
    const maps: Record<string, ILookup[]> = {};
    codes.forEach((code) => {
      maps[code] = lookupMaps[code];
    });
    return maps;
  }, [codes, lookupMaps]);

  return {
    allLookupMaps: lookupMaps,
    lookups,
    getLookupByCode,
    getLookupByType,
  };
};
