import { TcsModal } from '@tencent/tcs-component';
import { Button, Form, Select, Table, message } from '@tencent/tea-component';
import React, { forwardRef, useState } from 'react';

interface AppSelectionModalProps {
  visible: boolean;
  onOk: () => void;
  onCancel: () => void;
}

const AppSelectionModal: React.FC<AppSelectionModalProps> = ({ visible, onOk, onCancel }) => {
  const [selectedAppNames, setSelectedAppNames] = useState<string[]>([]);
  const [applicationNames, setApplicationNames] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  // 模拟获取应用列表
  const fetchApplicationNames = async () => {
    setLoading(true);
    try {
      // 这里替换为实际的 API 调用
      const mockData = ['App1', 'App2', 'App3'];
      setApplicationNames(mockData);
    } catch (error) {
      message.error('获取应用列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化时获取应用列表
  React.useEffect(() => {
    fetchApplicationNames();
  }, []);

  // 表格数据
  const tableData = [
    { key: '1', appName: 'App1', status: 'Active' },
    { key: '2', appName: 'App2', status: 'Inactive' },
    { key: '3', appName: 'App3', status: 'Active' },
  ];

  // 表格列配置
  const columns = [
    { header: '应用名称', key: 'appName', width: 200 },
    { header: '状态', key: 'status', width: 100 },
  ];

  return (
    <TcsModal
      title="选择关联应用"
      visible={visible}
      onOk={onOk}
      onCancel={onCancel}
      width={800}
      okText="确认"
    >
      <Form>
        <Form.Item label="选择应用" required>
          <Select
            multiple
            placeholder="请选择应用"
            options={applicationNames.map((app) => ({ value: app, text: app }))}
            value={selectedAppNames}
            onChange={(value) => setSelectedAppNames(value)}
            loading={loading}
          />
        </Form.Item>
      </Form>
      <Table
        recordKey="key"
        columns={columns}
        records={tableData}
        addons={[
          Table.addons.scrollable({ maxHeight: 400 }),
          Table.addons.autotip({ isLoading: false }),
        ]}
      />
    </TcsModal>
  );
};

export default AppSelectionModal;