@import '@ant-design/pro-table/dist/table.css';
@import '@ant-design/pro-form/dist/form.css';
@import '@ant-design/pro-field/dist/field.css';
@import '@ant-design/pro-card/dist/card.css';

.tcs-layout__table_wrap {
  height: 100%;
  display: flex;
  flex-direction: column;

  .tcs-layout__table_search {
    width: 100%;
  }

  .tcs-layout__table_card {
    width: 100%;
    flex: 1;
    height: 0;

    &.tcs-layout__table_card-nobtn {
      .tea-table {
        height: calc(100% - 46px);
        overflow: auto;
      }
    }

    .tea-table {
      height: calc(100% - 96px);
      overflow: hidden;
    }

    > .tea-card__body {
      height: 100%;

      > .tea-card__content {
        height: 100%;

        .tea-table__body {
          height: calc(100% - 42px);
        }
      }
    }
  }
}

.one-line-text {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.warning-table-row {
  background: rgba(255, 237, 223, 0.6);
}
.common-tag-select-lis-height > div {
  height: 240px !important;
}
