{"page": {"id": "c11320be-0b57-42ee-9546-607dfef02a8a", "name": "页面", "code": "__page__", "properties": {"pageWidth": "420", "pageAlign": "center", "showPageHeader": false, "inputParams": {"value": "return {\n  readonly:false,\n  formData: {\n    Status:\"new\",\n    CategoryID: \"1010130691002010395\"\n  }\n}", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return {\n      readonly: false,\n      formData: {\n        Status: \"new\",\n        CategoryID: \"1010130691002010395\"\n      }\n    };\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": []}}, "events": {"eventList": [{"eventName": "onOpen", "eventType": "custom", "eventCode": "// 工具类\nfunction requestCloudApi(options) {\n  const {\n    action,\n    paramVar,\n    resultCode,\n    outerParamName,\n    isPaging = false\n  } = options\n  return $utils.api.requestCloudApi(action, {\n    [outerParamName]: paramVar\n  }).then(res => {\n    if (res.Error) {\n      throw new Error(res.Error.Message)\n    } else {\n      // 如果有分页，则返回数据和分页\n      if (isPaging) {\n        return {\n          data: res[resultCode],\n          total: res.Total\n        }\n      }\n      return res[resultCode]\n    }\n  }).catch(error => {\n    $utils.ui.message.error(error.message);\n    throw error;\n  })\n}\n\nasync function requestLookups(types, useTenantID) {\n  return await requestCloudApi({\n    action: \"ListProductDictionaryDetails\",\n    paramVar: $utils.api.transformCloudApiParams({\n      Type: {\n        Op: \"in\",\n        Value: types || []\n      },\n      TenantID: useTenantID ? window.jiguang_currentNs: 0\n    }, {\n      useEqFields: [\"TenantID\"]\n    }),\n    resultCode: \"ListProductDictionaries\",\n    outerParamName: \"ProductDictionary\"\n  })\n}\n\nfunction arrayToObject(array, key) {\n  const object = {}\n  array.forEach(item => {\n    if (!object[item[key]]) {\n      object[item[key]] = []\n    }\n    object[item[key]].push(item)\n  })\n  return object;\n}\n\n// 加载数据字典值\ntry {\n  const [lookupResult_PackageArch_Severity_Priority_StorySource_IssueStatus, IssueStatus]= await Promise.all([requestLookups([\"PackageArch\",\n    \"Severity\",\n    \"Priority\",\n    \"StorySource\",\n    \"IssueStatus\",\n    \"StoryType\"],\n    false),requestLookups([\"IssueStatus\"],true)]);\n    \n     const lookupMaps = arrayToObject(lookupResult_PackageArch_Severity_Priority_StorySource_IssueStatus || [], \"Type\")\n    \n    if(IssueStatus?.length) {\n      const globalIssueStatus = lookupMaps[\"IssueStatus\"] || [];\n      const globalIssueStatusCodes = globalIssueStatus.map(item => item.Code);\n      IssueStatus.forEach(issue => {\n        if(!globalIssueStatusCodes.includes(issue.Code)){\n          globalIssueStatus.push(issue)\n        }\n      })\n    }\n    \n  $utils.state.updateState(state => {\n    state.lookupMaps = {\n      ...state.lookupMaps,\n      ...lookupMaps\n    }\n  })\n\n} catch (error) {}", "codeGenerateInfo": "[{\"id\":\"requestLookup\",\"uuid\":\"c12cfa62-9e8a-49a9-934b-6a523530a6ad\",\"title\":\"加载数据字典值\",\"properties\":{\"type\":\"OSArch,Severity,Priority,StorySource,IssueStatus\",\"useTenantID\":false,\"mountGlobalState\":true,\"globalStateName\":\"lookupMaps\"},\"catchList\":[],\"finallyList\":[],\"chosen\":false,\"selected\":false,\"name\":\"\"}]", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = async function () {\n  try {\n    // 工具类\n    function requestCloudApi(options) {\n      const {\n        action,\n        paramVar,\n        resultCode,\n        outerParamName,\n        isPaging = false\n      } = options;\n      return $utils.api.requestCloudApi(action, {\n        [outerParamName]: paramVar\n      }).then(res => {\n        if (res.Error) {\n          throw new Error(res.Error.Message);\n        } else {\n          // 如果有分页，则返回数据和分页\n          if (isPaging) {\n            return {\n              data: res[resultCode],\n              total: res.Total\n            };\n          }\n          return res[resultCode];\n        }\n      }).catch(error => {\n        $utils.ui.message.error(error.message);\n        throw error;\n      });\n    }\n    async function requestLookups(types, useTenantID) {\n      return await requestCloudApi({\n        action: \"ListProductDictionaryDetails\",\n        paramVar: $utils.api.transformCloudApiParams({\n          Type: {\n            Op: \"in\",\n            Value: types || []\n          },\n          TenantID: useTenantID ? window.jiguang_currentNs : 0\n        }, {\n          useEqFields: [\"TenantID\"]\n        }),\n        resultCode: \"ListProductDictionaries\",\n        outerParamName: \"ProductDictionary\"\n      });\n    }\n    function arrayToObject(array, key) {\n      const object = {};\n      array.forEach(item => {\n        if (!object[item[key]]) {\n          object[item[key]] = [];\n        }\n        object[item[key]].push(item);\n      });\n      return object;\n    }\n\n    // 加载数据字典值\n    try {\n      const [lookupResult_PackageArch_Severity_Priority_StorySource_IssueStatus, IssueStatus] = await Promise.all([requestLookups([\"PackageArch\", \"Severity\", \"Priority\", \"StorySource\", \"IssueStatus\", \"StoryType\"], false), requestLookups([\"IssueStatus\"], true)]);\n      const lookupMaps = arrayToObject(lookupResult_PackageArch_Severity_Priority_StorySource_IssueStatus || [], \"Type\");\n      if (IssueStatus !== null && IssueStatus !== void 0 && IssueStatus.length) {\n        const globalIssueStatus = lookupMaps[\"IssueStatus\"] || [];\n        const globalIssueStatusCodes = globalIssueStatus.map(item => item.Code);\n        IssueStatus.forEach(issue => {\n          if (!globalIssueStatusCodes.includes(issue.Code)) {\n            globalIssueStatus.push(issue);\n          }\n        });\n      }\n      $utils.state.updateState(state => {\n        state.lookupMaps = {\n          ...state.lookupMaps,\n          ...lookupMaps\n        };\n      });\n    } catch (error) {}\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()"}]}, "list": [{"properties": {"initialValues": {"value": "const formData = $context.inputParams?.formData || {}\n\nreturn {\n  ...formData,\n  TapdUrl: formData.TapdUrl ? \"点击查看\" : \"-\",\n   IterationName: $context.globalState.currentIterationDetail?.Name\n}", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    var _$context$inputParams, _$context$globalState;\n    const formData = ((_$context$inputParams = $context.inputParams) === null || _$context$inputParams === void 0 ? void 0 : _$context$inputParams.formData) || {};\n    return {\n      ...formData,\n      TapdUrl: formData.TapdUrl ? \"点击查看\" : \"-\",\n      IterationName: (_$context$globalState = $context.globalState.currentIterationDetail) === null || _$context$globalState === void 0 ? void 0 : _$context$globalState.Name\n    };\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.inputParams.formData", "$context.globalState.currentIterationDetail.Name"]}, "colNum": 1, "layout": "horizontal", "disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "labelWrap": true, "labelWidth": 110, "dataMonitor": true, "readonly": {"value": "return $context.inputParams.readonly", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return $context.inputParams.readonly;\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.inputParams.readonly"]}, "submitterButtons": {"showCount": 3, "buttons": [], "preRender": {"useExpr": true, "wrapFunction": false, "dependencyVariableList": []}, "postRender": {"useExpr": true, "wrapFunction": false, "dependencyVariableList": []}}}, "id": "fe387b01-158f-42ce-8113-d056ddce9e9c", "type": "form", "category": "container", "list": [{"properties": {"formItemProps": {"label": {"value": "状态", "useExpr": false, "wrapFunction": false}, "name": "Status", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": true, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"useExpr": false, "wrapFunction": false}, "tag": false, "showSearch": true, "searchCaseSensitive": false, "defaultValue": {"useExpr": false, "wrapFunction": false}, "placeholder": {"useExpr": false, "wrapFunction": false}, "labelInValue": false, "propLoading": {"value": false, "useExpr": false, "wrapFunction": false}, "dataSource": {"sourceType": "context", "contextInfo": {"source": "globalState", "key": "lookupMaps.IssueStatus"}}, "autoTransformData": true, "changeDataSelectValue": "nothing", "transformDataType": "object", "labelField": "Name", "valueField": "Code", "colorField": "Extra.Color"}}, "id": "7982216a-8fca-4d48-bb01-373116948281", "type": "select", "category": "component", "events": {"eventList": []}, "name": "下拉框", "code": "select_1687242518027", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "下拉框", "code": "select_1687242518027", "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}, "isRender": {"value": "return $context.inputParams.readonly", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return $context.inputParams.readonly;\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.inputParams.readonly"]}}}, {"properties": {"formItemProps": {"label": {"value": "需求类型", "useExpr": false, "wrapFunction": false}, "name": "Type", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": true, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}, "requiredMessage": {"value": "该项为必填项", "useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"useExpr": false, "wrapFunction": false}, "tag": false, "showSearch": true, "searchCaseSensitive": false, "defaultValue": {"useExpr": false, "wrapFunction": false}, "placeholder": {"useExpr": false, "wrapFunction": false}, "labelInValue": false, "propLoading": {"value": false, "useExpr": false, "wrapFunction": false}, "dataSource": {"sourceType": "context", "contextInfo": {"source": "globalState", "key": "lookupMaps.StoryType"}}, "autoTransformData": true, "changeDataSelectValue": "nothing", "transformDataType": "object", "labelField": "Name", "valueField": "Code"}}, "id": "30ca3366-9b3c-4a51-80ec-f269ebec0540", "type": "select", "category": "component", "events": {"eventList": []}, "name": "下拉框", "code": "select_1693895090797", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "下拉框", "code": "select_1693895090797", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "需求分类", "useExpr": false, "wrapFunction": false}, "name": "CategoryID", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": true, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}, "requiredMessage": {"value": "该项为必填项", "useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"useExpr": false, "wrapFunction": false}, "showSearch": true, "placeholder": {"useExpr": false, "wrapFunction": false}, "propLoading": {"value": false, "useExpr": false, "wrapFunction": false}, "changeOnSelect": true, "defaultValue": {"value": "return []", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return [];\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": []}, "isFlat": true, "id": "UUID", "parentId": "ParentID", "dataSource": {"sourceType": "request", "requestDependParams": {"value": "return {}", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return {};\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": []}, "requestCode": {"value": "// 工具类\nfunction requestCloudApi(options) {\n  const {\n    action,\n    paramVar,\n    resultCode,\n    outerParamName,\n    isPaging = false\n  } = options\n  return $utils.api.requestCloudApi(action, {\n    [outerParamName]: paramVar\n  }).then(res => {\n    if (res.Error) {\n      throw new Error(res.Error.Message)\n    } else {\n      // 如果有分页，则返回数据和分页\n      if (isPaging) {\n        return {\n          data: res[resultCode],\n          total: res.Total\n        }\n      }\n      return res[resultCode]\n    }\n  }).catch(error => {\n    $utils.ui.message.error(error.message);\n    throw error;\n  })\n}\n\nasync function requestLookups(types, useTenantID) {\n  return await requestCloudApi({\n    action: \"ListProductDictionaryDetails\",\n    paramVar: $utils.api.transformCloudApiParams({\n      Type: {\n        Op: \"in\",\n        Value: types || []\n      },\n      TenantID: useTenantID ? window.jiguang_currentNs: 0\n    }, {\n      useEqFields: [\"TenantID\"]\n    }),\n    resultCode: \"ListProductDictionaries\",\n    outerParamName: \"ProductDictionary\"\n  })\n}\n\nfunction arrayToObject(array, key) {\n  const object = {}\n  array.forEach(item => {\n    if (!object[item[key]]) {\n      object[item[key]] = []\n    }\n    object[item[key]].push(item)\n  })\n  return object;\n}\n\n// 加载数据字典值\ntry {\n  const result = await requestLookups([\"StoryCategory\"],\n    true);\n\n  const category = $context.inputParams.formData.CategoryID\n  if (category) {\n    const data = []\n    const buildCategoryFormData = function(uuid) {\n      result.forEach(item => {\n        if (item.UUID === uuid ) {\n          data.unshift(item.Code)\n          if(item.ParentID) {\n             buildCategoryFormData(item.ParentID)\n          }\n        }\n      })\n    }\n    const current = result.find(item => item.Code === category)\n    if (current) {\n      buildCategoryFormData(current.UUID)\n      $context.parentRef.setFieldValue(\"CategoryID\", data)\n    }\n\n  }\n\n  return result.map(item => {\n    return {\n      label: item.Name,\n      value: item.Code,\n      ...item\n    }\n\n  })\n\n} catch (error) {}", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = async function () {\n  try {\n    // 工具类\n    function requestCloudApi(options) {\n      const {\n        action,\n        paramVar,\n        resultCode,\n        outerParamName,\n        isPaging = false\n      } = options;\n      return $utils.api.requestCloudApi(action, {\n        [outerParamName]: paramVar\n      }).then(res => {\n        if (res.Error) {\n          throw new Error(res.Error.Message);\n        } else {\n          // 如果有分页，则返回数据和分页\n          if (isPaging) {\n            return {\n              data: res[resultCode],\n              total: res.Total\n            };\n          }\n          return res[resultCode];\n        }\n      }).catch(error => {\n        $utils.ui.message.error(error.message);\n        throw error;\n      });\n    }\n    async function requestLookups(types, useTenantID) {\n      return await requestCloudApi({\n        action: \"ListProductDictionaryDetails\",\n        paramVar: $utils.api.transformCloudApiParams({\n          Type: {\n            Op: \"in\",\n            Value: types || []\n          },\n          TenantID: useTenantID ? window.jiguang_currentNs : 0\n        }, {\n          useEqFields: [\"TenantID\"]\n        }),\n        resultCode: \"ListProductDictionaries\",\n        outerParamName: \"ProductDictionary\"\n      });\n    }\n    function arrayToObject(array, key) {\n      const object = {};\n      array.forEach(item => {\n        if (!object[item[key]]) {\n          object[item[key]] = [];\n        }\n        object[item[key]].push(item);\n      });\n      return object;\n    }\n\n    // 加载数据字典值\n    try {\n      const result = await requestLookups([\"StoryCategory\"], true);\n      const category = $context.inputParams.formData.CategoryID;\n      if (category) {\n        const data = [];\n        const buildCategoryFormData = function (uuid) {\n          result.forEach(item => {\n            if (item.UUID === uuid) {\n              data.unshift(item.Code);\n              if (item.ParentID) {\n                buildCategoryFormData(item.ParentID);\n              }\n            }\n          });\n        };\n        const current = result.find(item => item.Code === category);\n        if (current) {\n          buildCategoryFormData(current.UUID);\n          $context.parentRef.setFieldValue(\"CategoryID\", data);\n        }\n      }\n      return result.map(item => {\n        return {\n          label: item.Name,\n          value: item.Code,\n          ...item\n        };\n      });\n    } catch (error) {}\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.inputParams.formData.CategoryID", "$context.parentRef.setFieldValue"]}}}}, "id": "abb39d14-2c89-4e6c-8a95-526e8b52a411", "type": "cascader", "category": "component", "events": {"eventList": []}, "name": "级联选择", "code": "cascader_1693817078374", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "级联选择", "code": "cascader_1693817078374", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "解决方案", "useExpr": false, "wrapFunction": false}, "name": "SolutionUUID", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": true, "useExpr": false, "wrapFunction": false}, "tooltip": {"value": "", "useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}, "requiredMessage": {"value": "该项为必填项", "useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"useExpr": false, "wrapFunction": false}, "tag": false, "showSearch": true, "searchCaseSensitive": false, "defaultValue": {"useExpr": false, "wrapFunction": false}, "placeholder": {"value": "请选择解决方案", "useExpr": false, "wrapFunction": false}, "labelInValue": false, "propLoading": {"value": false, "useExpr": false, "wrapFunction": false}, "dataSource": {"sourceType": "request", "requestDependParams": {"value": "return {}", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return {};\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": []}, "requestCode": {"value": "// 工具类\nfunction requestCloudApi(options) {\n  const {\n    action,\n    paramVar,\n    resultCode,\n    outerParamName,\n    isPaging = false\n  } = options\n  return $utils.api.requestCloudApi(action, {\n    [outerParamName]: paramVar\n  }).then(res => {\n    if (res.Error) {\n      throw new Error(res.Error.Message)\n    } else {\n      // 如果有分页，则返回数据和分页\n      if (isPaging) {\n        return {\n          data: res[resultCode],\n          total: res.Total\n        }\n      }\n      return res[resultCode]\n    }\n  }).catch(error => {\n    $utils.ui.message.error(error.message);\n    throw error;\n  })\n}\n\n// 请求数据引擎数据\ntry {\n  const selectData = await requestCloudApi({\n    action: \"ListSolutions\",\n    paramVar: {\n      _Filter: [{\n        \"Key\": \"TenantID\",\n        \"Op\": \"=\",\n        \"Value\": window.jiguang_currentNs\n      }]\n    },\n    resultCode: \"ListSolutions\",\n    outerParamName: \"Solution\",\n    isPaging: false\n  });\n  \n  return selectData.map(item => {\n    return {\n      ...item,\n      Name: `${item.Name}(${item.NameEN})`\n    }\n    \n  })\n\n} catch (error) {}", "useExpr": true, "wrapFunction": false, "codeGenerateInfo": "[{\"id\":\"requestCloud\",\"uuid\":\"2c927616-713a-4122-b3f1-d4722e66b5c7\",\"title\":\"请求数据引擎数据\",\"properties\":{\"action\":\"ListSolutions\",\"resultCode\":\"ListSolutions\",\"outerParamName\":\"Solution\",\"isPaging\":false,\"autoReturn\":true},\"catchList\":[],\"finallyList\":[],\"chosen\":false,\"selected\":false,\"name\":\"\"}]", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = async function () {\n  try {\n    // 工具类\n    function requestCloudApi(options) {\n      const {\n        action,\n        paramVar,\n        resultCode,\n        outerParamName,\n        isPaging = false\n      } = options;\n      return $utils.api.requestCloudApi(action, {\n        [outerParamName]: paramVar\n      }).then(res => {\n        if (res.Error) {\n          throw new Error(res.Error.Message);\n        } else {\n          // 如果有分页，则返回数据和分页\n          if (isPaging) {\n            return {\n              data: res[resultCode],\n              total: res.Total\n            };\n          }\n          return res[resultCode];\n        }\n      }).catch(error => {\n        $utils.ui.message.error(error.message);\n        throw error;\n      });\n    }\n\n    // 请求数据引擎数据\n    try {\n      const selectData = await requestCloudApi({\n        action: \"ListSolutions\",\n        paramVar: {\n          _Filter: [{\n            \"Key\": \"TenantID\",\n            \"Op\": \"=\",\n            \"Value\": window.jiguang_currentNs\n          }]\n        },\n        resultCode: \"ListSolutions\",\n        outerParamName: \"Solution\",\n        isPaging: false\n      });\n      return selectData.map(item => {\n        return {\n          ...item,\n          Name: \"\".concat(item.Name, \"(\").concat(item.NameEN, \")\")\n        };\n      });\n    } catch (error) {}\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": []}}, "autoTransformData": true, "changeDataSelectValue": "nothing", "transformDataType": "object", "labelField": "Name", "valueField": "UUID"}}, "id": "9d73b6d5-a92a-45de-922f-9a21d31d110c", "type": "select", "category": "component", "events": {"eventList": [{"eventName": "onChange", "eventType": "custom", "eventCode": "$context.parentRef.setFieldsValue({\n  SolutionVersion:\"\"\n})", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    $context.parentRef.setFieldsValue({\n      SolutionVersion: \"\"\n    });\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()"}]}, "name": "下拉框", "code": "select_1679302866543", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "下拉框", "code": "select_1679302866543", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "解决方案版本", "useExpr": false, "wrapFunction": false}, "name": "SolutionVersion", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": true, "useExpr": false, "wrapFunction": false}, "tooltip": {"value": "", "useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}, "requiredMessage": {"value": "该项为必填项", "useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"useExpr": false, "wrapFunction": false}, "tag": false, "showSearch": true, "searchCaseSensitive": false, "defaultValue": {"useExpr": false, "wrapFunction": false}, "placeholder": {"value": "请选择解决方案版本", "useExpr": false, "wrapFunction": false}, "labelInValue": false, "propLoading": {"value": false, "useExpr": false, "wrapFunction": false}, "dataSource": {"sourceType": "request", "requestDependParams": {"value": "return {\n  SolutionID: $context.formData.SolutionUUID,\n  Tag: \"\",\n  Branch:\"master\"\n}", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return {\n      SolutionID: $context.formData.SolutionUUID,\n      Tag: \"\",\n      Branch: \"master\"\n    };\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.formData.SolutionUUID"]}, "requestCode": {"value": "// 工具类\nfunction requestCloudApi(options) {\n  const {\n    action,\n    paramVar,\n    resultCode,\n    outerParamName,\n    isPaging = false\n  } = options\n  return $utils.api.requestCloudApi(action, {\n    [outerParamName]: paramVar\n  }).then(res => {\n    if (res.Error) {\n      throw new Error(res.Error.Message)\n    } else {\n      // 如果有分页，则返回数据和分页\n      if (isPaging) {\n        return {\n          data: res[resultCode],\n          total: res.Total\n        }\n      }\n      return res[resultCode]\n    }\n  }).catch(error => {\n    $utils.ui.message.error(error.message);\n    throw error;\n  })\n}\n\n// 将常规参数转换为数据引擎参数(将依赖参数转换为数据引擎可识别参数)\nconst requestDependParams = $utils.api.transformCloudApiParams(\n  $context.requestDependParams || {},\n  {\n    //TODO: 搜索条件默认会使用like模糊搜索，可以在这里将精准搜索的字段进行指定\n    useEqFields: [\"SolutionID\",\"Tag\",\"Branch\"],\n    //排序字段，格式为 {Key:\"Name\",Sort:\"DESC\"}\n    order: [],\n  }\n)\n// 编码模块\nif (!$context.requestDependParams?.SolutionID) {\n  return []\n}\n// 请求数据引擎数据(请求下拉框数据,请完善配置信息)\ntry {\n  const selectData = await requestCloudApi({\n    action: \"ListSolutionVersionWithMarketFlag\",\n    paramVar: requestDependParams,\n    resultCode: \"ListSolutionVersions\",\n    outerParamName: \"SolutionVersion\",\n    isPaging: false\n  });\n  \n  \n    $utils.state.updateState(state => {\n  // 更新的参数可在$context.globalState中读取\n  state.solutionVersionList = selectData\n})\n    \n  \n  return selectData;\n\n} catch (error) {}", "useExpr": true, "wrapFunction": false, "codeGenerateInfo": "[{\"id\":\"transformCloudApiParams\",\"uuid\":\"352f7ae5-a193-49e8-92ed-91c03dca9ab6\",\"title\":\"将常规参数转换为数据引擎参数\",\"properties\":{\"paramVar\":\"$context.requestDependParams || {}\",\"returnVarName\":\"requestDependParams\"},\"catchList\":[],\"finallyList\":[],\"name\":\"将依赖参数转换为数据引擎可识别参数\",\"chosen\":false,\"selected\":false},{\"id\":\"customCode\",\"uuid\":\"dff3a833-0707-4f72-9566-5a71a6cdee8c\",\"title\":\"编码模块\",\"properties\":{\"code\":\"if(!$context.requestDependParams?.SolutionID){\\n  return []\\n}\",\"closure\":false},\"catchList\":[],\"finallyList\":[],\"chosen\":false,\"selected\":false,\"name\":\"\"},{\"id\":\"requestCloud\",\"uuid\":\"f3688ce0-f244-42e0-a0c4-a6445abda4fa\",\"title\":\"请求数据引擎数据\",\"properties\":{\"action\":\"ListSolutionVersionWithMarketFlag\",\"resultCode\":\"ListSolutionVersions\",\"outerParamName\":\"SolutionVersion\",\"paramVar\":\"requestDependParams\",\"isPaging\":false,\"returnVarName\":\"selectData\",\"autoReturn\":true},\"catchList\":[],\"finallyList\":[],\"chosen\":false,\"name\":\"请求下拉框数据,请完善配置信息\",\"selected\":false}]", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = async function () {\n  try {\n    var _$context$requestDepe;\n    // 工具类\n    function requestCloudApi(options) {\n      const {\n        action,\n        paramVar,\n        resultCode,\n        outerParamName,\n        isPaging = false\n      } = options;\n      return $utils.api.requestCloudApi(action, {\n        [outerParamName]: paramVar\n      }).then(res => {\n        if (res.Error) {\n          throw new Error(res.Error.Message);\n        } else {\n          // 如果有分页，则返回数据和分页\n          if (isPaging) {\n            return {\n              data: res[resultCode],\n              total: res.Total\n            };\n          }\n          return res[resultCode];\n        }\n      }).catch(error => {\n        $utils.ui.message.error(error.message);\n        throw error;\n      });\n    }\n\n    // 将常规参数转换为数据引擎参数(将依赖参数转换为数据引擎可识别参数)\n    const requestDependParams = $utils.api.transformCloudApiParams($context.requestDependParams || {}, {\n      //TODO: 搜索条件默认会使用like模糊搜索，可以在这里将精准搜索的字段进行指定\n      useEqFields: [\"SolutionID\", \"Tag\", \"Branch\"],\n      //排序字段，格式为 {Key:\"Name\",Sort:\"DESC\"}\n      order: []\n    });\n    // 编码模块\n    if (!((_$context$requestDepe = $context.requestDependParams) !== null && _$context$requestDepe !== void 0 && _$context$requestDepe.SolutionID)) {\n      return [];\n    }\n    // 请求数据引擎数据(请求下拉框数据,请完善配置信息)\n    try {\n      const selectData = await requestCloudApi({\n        action: \"ListSolutionVersionWithMarketFlag\",\n        paramVar: requestDependParams,\n        resultCode: \"ListSolutionVersions\",\n        outerParamName: \"SolutionVersion\",\n        isPaging: false\n      });\n      $utils.state.updateState(state => {\n        // 更新的参数可在$context.globalState中读取\n        state.solutionVersionList = selectData;\n      });\n      return selectData;\n    } catch (error) {}\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.requestDependParams", "$context.requestDependParams.SolutionID", "$context.globalState"]}}, "autoTransformData": true, "changeDataSelectValue": "nothing", "transformDataType": "object", "labelField": "Code", "valueField": "Code"}}, "id": "deda44fd-8ed9-4a1e-a61a-317e9ab87042", "type": "select", "category": "component", "events": {"eventList": []}, "name": "下拉框", "code": "select_1679296528351", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "下拉框", "code": "select_1679296528351", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "关联迭代UUID", "useExpr": false, "wrapFunction": false}, "name": "IterationUUID", "hidden": {"value": true, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"showType": "text", "disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "trim": true, "placeholder": {"useExpr": false, "wrapFunction": false}}}, "id": "1557e4ac-00f3-44f4-9b36-5bb7918dee8d", "type": "input", "category": "component", "events": {"eventList": []}, "name": "输入框", "code": "input_1692067543688", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "输入框", "code": "input_1692067543688", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "关联迭代", "useExpr": false, "wrapFunction": false}, "name": "IterationName", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"value": "// 暂时屏蔽\n// if( $context.inputParams?.readonly){\n//   return \"\"\n// }\n\n// return <$components.Button size=\"small\" type=\"link\" onClick={() => {\n//   $utils.dialog.open({ dialogCode: \"modal_1692067628869\", inputParams: {\n//     IterationUUID:$context.pageMapRef[\"form_1679296507834\"].current.getFieldValue(\"IterationUUID\")\n//   }, closeCallback:(result) => {\n//     if(result.operationType === \"confirm\"){\n//       console.log($context)\n//       $context.pageMapRef[\"form_1679296507834\"].current.setFieldsValue({\"IterationUUID\":result.iteration?.UUID || \"\",\"IterationName\":result.iteration?.Name || \"\"})\n//     }\n//   }})\n// }}>选择迭代</$components.Button>", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n        \"use strict\";\n\nconst __result__ = function () {\n  try {\n    // 暂时屏蔽\n    // if( $context.inputParams?.readonly){\n    //   return \"\"\n    // }\n\n    // return <$components.Button size=\"small\" type=\"link\" onClick={() => {\n    //   $utils.dialog.open({ dialogCode: \"modal_1692067628869\", inputParams: {\n    //     IterationUUID:$context.pageMapRef[\"form_1679296507834\"].current.getFieldValue(\"IterationUUID\")\n    //   }, closeCallback:(result) => {\n    //     if(result.operationType === \"confirm\"){\n    //       console.log($context)\n    //       $context.pageMapRef[\"form_1679296507834\"].current.setFieldsValue({\"IterationUUID\":result.iteration?.UUID || \"\",\"IterationName\":result.iteration?.Name || \"\"})\n    //     }\n    //   }})\n    // }}>选择迭代</$components.Button>\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n          return __result__;\n      })()", "dependencyVariableList": ["$context.inputParams.readonly", "$context.pageMapRef[\"form_1679296507834\"].current.getFieldValue", "$context.pageMapRef[\"form_1679296507834\"].current.setFieldsValue"]}}, "fieldProps": {"type": "link", "copyable": false, "editable": false}}, "id": "4e3c3b0b-6315-4fc0-97bb-3e1277b279e7", "type": "editText", "category": "component", "events": {"eventList": [{"eventName": "onClick", "eventType": "custom", "eventCode": "const uuid = $context.parentRef.getFieldValue(\"IterationUUID\")\nif(uuid) {\n  window.open(`//${window.location.host}/page/defect_manage/__develop_center_next/requirement_iteration_manage/detail?iteration_id=${uuid}`)\n}", "compileExprCode": "return (function(){\n        \"use strict\";\n\nconst __result__ = function () {\n  try {\n    const uuid = $context.parentRef.getFieldValue(\"IterationUUID\");\n    if (uuid) {\n      window.open(\"//\".concat(window.location.host, \"/page/defect_manage/__develop_center_next/requirement_iteration_manage/detail?iteration_id=\").concat(uuid));\n    }\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n          return __result__;\n      })()"}]}, "name": "可编辑文本", "code": "editText_1692067574936", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "可编辑文本", "code": "editText_1692067574936", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}}}, {"properties": {"formItemProps": {"label": {"value": "产品", "useExpr": false, "wrapFunction": false}, "name": "ProductUUID", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"useExpr": false, "wrapFunction": false}, "tag": false, "showSearch": true, "searchCaseSensitive": false, "defaultValue": {"useExpr": false, "wrapFunction": false}, "placeholder": {"useExpr": false, "wrapFunction": false}, "labelInValue": false, "propLoading": {"value": false, "useExpr": false, "wrapFunction": false}, "dataSource": {"sourceType": "request", "requestDependParams": {"value": "return {\n  TenantID:{\n    Value:window.jiguang_currentNs,\n    Op:\"=\"\n  }\n}\n", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return {\n      TenantID: {\n        Value: window.jiguang_currentNs,\n        Op: \"=\"\n      }\n    };\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": []}, "requestCode": {"value": "// 工具类\nfunction requestCloudApi(options) {\n  const {\n    action,\n    paramVar,\n    resultCode,\n    outerParamName,\n    isPaging = false\n  } = options\n  return $utils.api.requestCloudApi(action, {\n    [outerParamName]: paramVar\n  }).then(res => {\n    if (res.Error) {\n      throw new Error(res.Error.Message)\n    } else {\n      // 如果有分页，则返回数据和分页\n      if (isPaging) {\n        return {\n          data: res[resultCode],\n          total: res.Total\n        }\n      }\n      return res[resultCode]\n    }\n  }).catch(error => {\n    $utils.ui.message.error(error.message);\n    throw error;\n  })\n}\n\n// 将常规参数转换为数据引擎参数(将依赖参数转换为数据引擎可识别参数)\nconst requestDependParams = $utils.api.transformCloudApiParams(\n  $context.requestDependParams || {},\n  {\n    //TODO: 搜索条件默认会使用like模糊搜索，可以在这里将精准搜索的字段进行指定\n    useEqFields: [],\n    //排序字段，格式为 {Key:\"Name\",Sort:\"DESC\"}\n    order: [],\n  }\n)\n// 请求数据引擎数据(请求下拉框数据,请完善配置信息)\ntry {\n  const selectData = await requestCloudApi({\n    action: \"ListProductInfoDetails\",\n    paramVar: requestDependParams,\n    resultCode: \"ListProductInfos\",\n    outerParamName: \"ProductInfo\",\n    isPaging: false\n  });\n  return selectData;\n\n} catch (error) {}", "useExpr": true, "wrapFunction": false, "codeGenerateInfo": "[{\"id\":\"transformCloudApiParams\",\"uuid\":\"dda7a7e4-76c5-4659-aad7-61e9d1a80440\",\"title\":\"将常规参数转换为数据引擎参数\",\"properties\":{\"paramVar\":\"$context.requestDependParams || {}\",\"returnVarName\":\"requestDependParams\"},\"catchList\":[],\"finallyList\":[],\"name\":\"将依赖参数转换为数据引擎可识别参数\",\"chosen\":false,\"selected\":false},{\"id\":\"requestCloud\",\"uuid\":\"5cb12ce6-a3b4-467c-8aa8-9f1b69e0c588\",\"title\":\"请求数据引擎数据\",\"properties\":{\"action\":\"ListProductInfoDetails\",\"resultCode\":\"ListProductInfos\",\"outerParamName\":\"ProductInfo\",\"paramVar\":\"requestDependParams\",\"isPaging\":false,\"returnVarName\":\"selectData\",\"autoReturn\":true},\"catchList\":[],\"finallyList\":[],\"chosen\":false,\"name\":\"请求下拉框数据,请完善配置信息\",\"selected\":false}]", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = async function () {\n  try {\n    // 工具类\n    function requestCloudApi(options) {\n      const {\n        action,\n        paramVar,\n        resultCode,\n        outerParamName,\n        isPaging = false\n      } = options;\n      return $utils.api.requestCloudApi(action, {\n        [outerParamName]: paramVar\n      }).then(res => {\n        if (res.Error) {\n          throw new Error(res.Error.Message);\n        } else {\n          // 如果有分页，则返回数据和分页\n          if (isPaging) {\n            return {\n              data: res[resultCode],\n              total: res.Total\n            };\n          }\n          return res[resultCode];\n        }\n      }).catch(error => {\n        $utils.ui.message.error(error.message);\n        throw error;\n      });\n    }\n\n    // 将常规参数转换为数据引擎参数(将依赖参数转换为数据引擎可识别参数)\n    const requestDependParams = $utils.api.transformCloudApiParams($context.requestDependParams || {}, {\n      //TODO: 搜索条件默认会使用like模糊搜索，可以在这里将精准搜索的字段进行指定\n      useEqFields: [],\n      //排序字段，格式为 {Key:\"Name\",Sort:\"DESC\"}\n      order: []\n    });\n    // 请求数据引擎数据(请求下拉框数据,请完善配置信息)\n    try {\n      const selectData = await requestCloudApi({\n        action: \"ListProductInfoDetails\",\n        paramVar: requestDependParams,\n        resultCode: \"ListProductInfos\",\n        outerParamName: \"ProductInfo\",\n        isPaging: false\n      });\n      return selectData;\n    } catch (error) {}\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.requestDependParams"]}}, "autoTransformData": true, "changeDataSelectValue": "nothing", "transformDataType": "object", "labelField": "Name", "valueField": "UUID"}}, "id": "fec398dc-e2a4-4a86-b1d9-f0225b3e9f49", "type": "select", "category": "component", "events": {"eventList": []}, "name": "下拉框", "code": "select_1679296602333", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "下拉框", "code": "select_1679296602333", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "模块", "useExpr": false, "wrapFunction": false}, "name": "<PERSON><PERSON><PERSON>", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"showType": "text", "disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": true, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "trim": true, "placeholder": {"useExpr": false, "wrapFunction": false}}}, "id": "0654cfdf-2521-48d0-b3a5-1adb6cd837ee", "type": "input", "category": "component", "events": {"eventList": []}, "name": "输入框", "code": "input_1693212057975", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "输入框", "code": "input_1693212057975", "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}, "isRender": {"value": "return $context.inputParams.readonly && $context.inputParams?.formData?.TapdUrl", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    var _$context$inputParams, _$context$inputParams2;\n    return $context.inputParams.readonly && ((_$context$inputParams = $context.inputParams) === null || _$context$inputParams === void 0 ? void 0 : (_$context$inputParams2 = _$context$inputParams.formData) === null || _$context$inputParams2 === void 0 ? void 0 : _$context$inputParams2.TapdUrl);\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.inputParams.readonly", "$context.inputParams.formData.TapdUrl"]}}}, {"properties": {"formItemProps": {"label": {"value": "需求来源", "useExpr": false, "wrapFunction": false}, "name": "Source", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": true, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "requiredMessage": {"value": "该项为必填项", "useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"value": false, "useExpr": false, "wrapFunction": false}, "tag": false, "showSearch": true, "searchCaseSensitive": false, "defaultValue": {"useExpr": false, "wrapFunction": false}, "placeholder": {"useExpr": false, "wrapFunction": false}, "labelInValue": false, "propLoading": {"value": false, "useExpr": false, "wrapFunction": false}, "dataSource": {"sourceType": "context", "contextInfo": {"source": "globalState", "key": "lookupMaps.StorySource"}}, "autoTransformData": true, "changeDataSelectValue": "nothing", "transformDataType": "object", "labelField": "Name", "valueField": "Code"}}, "id": "13c3deaf-4e4d-4ef0-a0fb-c6128d1d0ed7", "type": "select", "category": "component", "events": {"eventList": []}, "name": "下拉框", "code": "select_1684753481788", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "下拉框", "code": "select_1684753481788", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}}}, {"properties": {"formItemProps": {"label": {"value": "优先级", "useExpr": false, "wrapFunction": false}, "name": "Priority", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": true, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "requiredMessage": {"value": "该项为必填项", "useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"useExpr": false, "wrapFunction": false}, "tag": false, "showSearch": true, "searchCaseSensitive": false, "defaultValue": {"useExpr": false, "wrapFunction": false}, "placeholder": {"value": "请选择优先级", "useExpr": false, "wrapFunction": false}, "labelInValue": false, "propLoading": {"value": false, "useExpr": false, "wrapFunction": false}, "dataSource": {"sourceType": "context", "contextInfo": {"source": "globalState", "key": "lookupMaps.Priority"}}, "autoTransformData": true, "changeDataSelectValue": "nothing", "transformDataType": "object", "labelField": "Name", "valueField": "Code"}}, "id": "3a77e3e1-c0b8-453a-ac51-611642258724", "type": "select", "category": "component", "events": {"eventList": []}, "name": "下拉框", "code": "select_1679296679697", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "下拉框", "code": "select_1679296679697", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}}}, {"properties": {"formItemProps": {"label": {"value": "处理人", "useExpr": false, "wrapFunction": false}, "name": "Owner", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": true, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}, "requiredMessage": {"value": "该项为必填项", "useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"value": true, "useExpr": false, "wrapFunction": false}, "tag": true}}, "id": "e09f1769-8b21-47ae-a718-e585b629f7c4", "type": "userSelect", "category": "component", "events": {"eventList": []}, "name": "人员选择框", "code": "userSelect_1679311054781", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "人员选择框", "code": "userSelect_1679311054781", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "研发负责人", "useExpr": false, "wrapFunction": false}, "name": "DevOwners", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": true, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}, "requiredMessage": {"value": "该项为必填项", "useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"value": true, "useExpr": false, "wrapFunction": false}, "tag": true}}, "id": "637bf748-4054-4b1f-8b01-c9e81bbed0a6", "type": "userSelect", "category": "component", "events": {"eventList": []}, "name": "人员选择框", "code": "userSelect_1679311134565", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "人员选择框", "code": "userSelect_1679311134565", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "测试负责人", "useExpr": false, "wrapFunction": false}, "name": "TestOwners", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"value": true, "useExpr": false, "wrapFunction": false}, "tag": true}}, "id": "6b3f306f-8354-4d30-8bac-889ef623b6c3", "type": "userSelect", "category": "component", "events": {"eventList": []}, "name": "人员选择框", "code": "userSelect_1679311163482", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "人员选择框", "code": "userSelect_1679311163482", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}, {"properties": {"formItemProps": {"label": {"value": "是否免测需求", "useExpr": false, "wrapFunction": false}, "name": "是否免测需求", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": true, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "multiple": {"useExpr": false, "wrapFunction": false}, "tag": false, "showSearch": true, "searchCaseSensitive": false, "defaultValue": {"useExpr": false, "wrapFunction": false}, "placeholder": {"useExpr": false, "wrapFunction": false}, "labelInValue": false, "propLoading": {"value": false, "useExpr": false, "wrapFunction": false}, "dataSource": {"sourceType": "data", "data": {"value": "1:是\n0:否", "useExpr": false, "wrapFunction": false}}, "autoTransformData": false, "changeDataSelectValue": "nothing"}}, "id": "25b05f22-a185-4313-a6bb-f5d7805572a1", "type": "select", "category": "component", "events": {"eventList": []}, "name": "下拉框", "code": "select_1687242928810", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "下拉框", "code": "select_1687242928810", "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}, "isRender": {"value": "return $context.inputParams.readonly", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return $context.inputParams.readonly;\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.inputParams.readonly"]}}}, {"properties": {"formItemProps": {"label": {"value": "关联tapd单", "useExpr": false, "wrapFunction": false}, "name": "TapdUrl", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"type": "link", "copyable": false, "editable": false}}, "id": "6e89d4bf-bf50-411d-a7e2-b78927ab866c", "type": "editText", "category": "component", "events": {"eventList": [{"eventName": "onClick", "eventType": "custom", "eventCode": "const tapdUrl = $context.inputParams?.formData?.TapdUrl\nif(tapdUrl) {\n  window.open(tapdUrl,\"_blank\")\n}", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    var _$context$inputParams, _$context$inputParams2;\n    const tapdUrl = (_$context$inputParams = $context.inputParams) === null || _$context$inputParams === void 0 ? void 0 : (_$context$inputParams2 = _$context$inputParams.formData) === null || _$context$inputParams2 === void 0 ? void 0 : _$context$inputParams2.TapdUrl;\n    if (tapdUrl) {\n      window.open(tapdUrl, \"_blank\");\n    }\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()"}]}, "name": "可编辑文本", "code": "editText_1687940032897", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "可编辑文本", "code": "editText_1687940032897", "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}, "isRender": {"value": "return $context.inputParams.readonly", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return $context.inputParams.readonly;\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.inputParams.readonly"]}}}, {"properties": {"formItemProps": {"label": {"value": "创建人", "useExpr": false, "wrapFunction": false}, "name": "Creator", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"showType": "text", "disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": true, "useExpr": false, "wrapFunction": false}, "allowClear": {"useExpr": false, "wrapFunction": false}, "trim": true, "placeholder": {"useExpr": false, "wrapFunction": false}}}, "id": "c467ba1d-0db7-494b-918c-859609b445a5", "type": "input", "category": "component", "events": {"eventList": []}, "name": "输入框", "code": "input_1687242618465", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "输入框", "code": "input_1687242618465", "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}, "isRender": {"value": "return $context.inputParams.readonly", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return $context.inputParams.readonly;\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.inputParams.readonly"]}}}, {"properties": {"formItemProps": {"label": {"value": "创建时间", "useExpr": false, "wrapFunction": false}, "name": "CreatedAt", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": true, "useExpr": false, "wrapFunction": false}, "type": "datetime", "isRange": false, "placeholderLeft": {"value": "请选择日期", "useExpr": false, "wrapFunction": false}}}, "id": "67e0f342-2a14-453b-ae12-96d8627faa1e", "type": "datePicker", "category": "component", "events": {"eventList": []}, "name": "日期选择器", "code": "datePicker_1687242768631", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "日期选择器", "code": "datePicker_1687242768631", "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}, "isRender": {"value": "return $context.inputParams.readonly", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return $context.inputParams.readonly;\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.inputParams.readonly"]}}}, {"properties": {"formItemProps": {"label": {"value": "最后更新时间", "useExpr": false, "wrapFunction": false}, "name": "UpdateAt", "hidden": {"value": false, "useExpr": false, "wrapFunction": false}, "required": {"value": false, "useExpr": false, "wrapFunction": false}, "tooltip": {"useExpr": false, "wrapFunction": false}, "extra": {"useExpr": false, "wrapFunction": false}, "addonBefore": {"useExpr": false, "wrapFunction": false}, "addonAfter": {"useExpr": false, "wrapFunction": false}}, "fieldProps": {"disabled": {"value": false, "useExpr": false, "wrapFunction": false}, "readonly": {"value": false, "useExpr": false, "wrapFunction": false}, "type": "datetime", "isRange": false, "placeholderLeft": {"value": "请选择日期", "useExpr": false, "wrapFunction": false}}}, "id": "64b3ad98-6c2e-451a-bfca-d50f9047bbf0", "type": "datePicker", "category": "component", "events": {"eventList": []}, "name": "日期选择器", "code": "datePicker_1687242817750", "extra": {"targetGroupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "日期选择器", "code": "datePicker_1687242817750", "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}, "isRender": {"value": "return $context.inputParams.readonly", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return $context.inputParams.readonly;\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.inputParams.readonly"]}}}], "events": {"eventList": []}, "name": "常规表单", "code": "form_1679296507834", "extra": {"targetGroupName": "container", "groupName": "formItem"}, "advancedProperties": {}, "commonProperties": {"name": "常规表单", "code": "form_1679296507834", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}], "type": "page", "category": "container", "advancedProperties": {"triggerList": [{"type": "manualTrigger", "condition": {"useExpr": true, "wrapFunction": false, "dependencyVariableList": []}, "triggerCode": {"useExpr": true, "wrapFunction": false, "value": "// 工具类\nasync function getFormData(formCode) {\n  const formRef = $context.pageMapRef[formCode]\n  if (formRef) {\n    return await formRef.current.validateFields()\n  } else {\n    $utils.ui.message.warn(\"表单不存在，请检查组件code是否正确\")\n    throw new Error(\"表单不存在，请检查表单code是否正确\")\n  }\n}\n\n// 读取表单数据\ntry {\n  const formData = await getFormData(\"form_1679296507834\");\n  formData.CategoryID = Array.isArray(formData.CategoryID) ? formData.CategoryID.pop() : formData.CategoryID;\n  const solutionVersionList = $context.globalState?.solutionVersionList || [];\n  const SolutionVersionUUID = solutionVersionList.find( item=>item.Code === formData.SolutionVersion)?.UUID\n  \n  return {\n    ...formData,\n    SolutionVersionUUID\n  };\n\n} catch (error) {\n  throw new Error(\"请检查表单是否已通过校验\")\n}", "codeGenerateInfo": "[{\"id\":\"formValidateField\",\"uuid\":\"8b0a36c8-e17e-472e-974c-3d1802e12410\",\"title\":\"读取表单数据\",\"properties\":{\"isBatch\":false,\"formCode\":\"form_1679296507834\",\"returnVarName\":\"formData\",\"autoReturn\":true},\"catchList\":[],\"finallyList\":[]}]", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = async function () {\n  try {\n    // 工具类\n    async function getFormData(formCode) {\n      const formRef = $context.pageMapRef[formCode];\n      if (formRef) {\n        return await formRef.current.validateFields();\n      } else {\n        $utils.ui.message.warn(\"表单不存在，请检查组件code是否正确\");\n        throw new Error(\"表单不存在，请检查表单code是否正确\");\n      }\n    }\n\n    // 读取表单数据\n    try {\n      var _$context$globalState, _solutionVersionList$;\n      const formData = await getFormData(\"form_1679296507834\");\n      formData.CategoryID = Array.isArray(formData.CategoryID) ? formData.CategoryID.pop() : formData.CategoryID;\n      const solutionVersionList = ((_$context$globalState = $context.globalState) === null || _$context$globalState === void 0 ? void 0 : _$context$globalState.solutionVersionList) || [];\n      const SolutionVersionUUID = (_solutionVersionList$ = solutionVersionList.find(item => item.Code === formData.SolutionVersion)) === null || _solutionVersionList$ === void 0 ? void 0 : _solutionVersionList$.UUID;\n      return {\n        ...formData,\n        SolutionVersionUUID\n      };\n    } catch (error) {\n      throw new Error(\"请检查表单是否已通过校验\");\n    }\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.pageMapRef[formCode]", "$context.globalState.solutionVersionList"]}, "title": "获取当前表单数据", "code": "getFormData"}, {"type": "conditionTrigger", "condition": {"useExpr": true, "wrapFunction": false, "value": "return {\n  UUID: $context.allFormData[\"form_1679296507834\"]?.IterationUUID\n}", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    var _$context$allFormData;\n    return {\n      UUID: (_$context$allFormData = $context.allFormData[\"form_1679296507834\"]) === null || _$context$allFormData === void 0 ? void 0 : _$context$allFormData.IterationUUID\n    };\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.allFormData[\"form_1679296507834\"].IterationUUID"]}, "triggerCode": {"useExpr": true, "wrapFunction": false, "value": "// 工具类\nfunction requestCloudApi(options) {\n  const {\n    action,\n    paramVar,\n    resultCode,\n    outerParamName,\n    isPaging = false\n  } = options\n  return $utils.api.requestCloudApi(action, {\n    [outerParamName]: paramVar\n  }).then(res => {\n    if (res.Error) {\n      throw new Error(res.Error.Message)\n    } else {\n      // 如果有分页，则返回数据和分页\n      if (isPaging) {\n        return {\n          data: res[resultCode],\n          total: res.Total\n        }\n      }\n      return res[resultCode]\n    }\n  }).catch(error => {\n    $utils.ui.message.error(error.message);\n    throw error;\n  })\n}\n\nconst {\n  UUID\n} = $context.triggerParams || {}\n\nif (!UUID) {\n  return\n}\n\n// 请求数据引擎数据\ntry {\n  const result = await requestCloudApi({\n    action: \"GetIterationDetail\",\n    paramVar: $utils.api.transformCloudApiParams($context.triggerParams, {\n      useEqFields: [\"UUID\"]\n    }),\n    resultCode: \"GetIteration\",\n    outerParamName: \"Iteration\",\n    isPaging: false\n  });\n\n\n  $utils.state.updateState(state => {\n    state.currentIterationDetail = result\n  })\n\n\n} catch (error) {}", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = async function () {\n  try {\n    // 工具类\n    function requestCloudApi(options) {\n      const {\n        action,\n        paramVar,\n        resultCode,\n        outerParamName,\n        isPaging = false\n      } = options;\n      return $utils.api.requestCloudApi(action, {\n        [outerParamName]: paramVar\n      }).then(res => {\n        if (res.Error) {\n          throw new Error(res.Error.Message);\n        } else {\n          // 如果有分页，则返回数据和分页\n          if (isPaging) {\n            return {\n              data: res[resultCode],\n              total: res.Total\n            };\n          }\n          return res[resultCode];\n        }\n      }).catch(error => {\n        $utils.ui.message.error(error.message);\n        throw error;\n      });\n    }\n    const {\n      UUID\n    } = $context.triggerParams || {};\n    if (!UUID) {\n      return;\n    }\n\n    // 请求数据引擎数据\n    try {\n      const result = await requestCloudApi({\n        action: \"GetIterationDetail\",\n        paramVar: $utils.api.transformCloudApiParams($context.triggerParams, {\n          useEqFields: [\"UUID\"]\n        }),\n        resultCode: \"GetIteration\",\n        outerParamName: \"Iteration\",\n        isPaging: false\n      });\n      $utils.state.updateState(state => {\n        state.currentIterationDetail = result;\n      });\n    } catch (error) {}\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.triggerParams", "$context.triggerParams"]}, "title": "获取关联迭代的详情", "code": "getIterationDetail"}]}}, "modalPage": [{"properties": {"inputParams": "{}", "title": {"value": "关联迭代"}, "type": "modal", "width": "1000", "height": "auto", "isMove": false, "customButton": false, "okText": "确认", "cancelText": "取消"}, "id": "b6347d75-8fc9-416b-8622-0a5dabc625c2", "name": "弹框", "type": "modal", "category": "container", "list": [{"properties": {"cardProps": {"title": {"useExpr": false, "wrapFunction": false}}, "cardBordered": false, "height": {"fixed": false}, "columns": [{"title": "迭代名称", "dataIndex": "Name", "width": 100, "valueType": "default", "align": "left", "fixed": "none", "tooltip": "", "isSearch": true, "isSorter": false, "ellipsis": false, "copyable": false, "customRender": false}, {"title": "解决方案", "dataIndex": "SolutionVersion.Solution.Name", "width": 100, "valueType": "default", "align": "left", "fixed": "none", "tooltip": "", "isSearch": false, "isSorter": false, "ellipsis": false, "copyable": false, "customRender": false}, {"title": "解决方案版本", "dataIndex": "SolutionVersion.Code", "valueType": "default", "width": 100, "align": "left", "fixed": "none", "tooltip": "", "isSearch": false, "isSorter": false, "ellipsis": false, "copyable": false, "customRender": false}, {"width": 100, "align": "left", "fixed": "none", "tooltip": "", "isSearch": true, "isSorter": false, "ellipsis": false, "copyable": false, "customRender": false, "valueType": "select", "title": "迭代状态", "dataIndex": "Status", "selectOptions": {"useExpr": true, "wrapFunction": false, "value": "return ($context.globalState?.lookupMaps?.IterationStatus || []).map(item => {\n  return {\n    label: item.Name,\n    value: item.Code,\n    color: item.Extra?.Color\n  }\n  \n})", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    var _$context$globalState, _$context$globalState2;\n    return (((_$context$globalState = $context.globalState) === null || _$context$globalState === void 0 ? void 0 : (_$context$globalState2 = _$context$globalState.lookupMaps) === null || _$context$globalState2 === void 0 ? void 0 : _$context$globalState2.IterationStatus) || []).map(item => {\n      var _item$Extra;\n      return {\n        label: item.Name,\n        value: item.Code,\n        color: (_item$Extra = item.Extra) === null || _item$Extra === void 0 ? void 0 : _item$Extra.Color\n      };\n    });\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.globalState.lookupMaps.IterationStatus"]}, "selectShowType": "badge"}, {"width": 100, "align": "left", "fixed": "none", "tooltip": "", "isSearch": true, "isSorter": false, "ellipsis": false, "copyable": false, "customRender": false, "valueType": "select", "title": "迭代类型", "dataIndex": "Type", "selectOptions": {"useExpr": true, "wrapFunction": false, "value": "return ($context.globalState?.lookupMaps?.IterationType || []).map(item => {\n  return {\n    label: item.Name,\n    value: item.Code,\n    color: item.Extra?.Color\n  }\n  \n})", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    var _$context$globalState, _$context$globalState2;\n    return (((_$context$globalState = $context.globalState) === null || _$context$globalState === void 0 ? void 0 : (_$context$globalState2 = _$context$globalState.lookupMaps) === null || _$context$globalState2 === void 0 ? void 0 : _$context$globalState2.IterationType) || []).map(item => {\n      var _item$Extra;\n      return {\n        label: item.Name,\n        value: item.Code,\n        color: (_item$Extra = item.Extra) === null || _item$Extra === void 0 ? void 0 : _item$Extra.Color\n      };\n    });\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.globalState.lookupMaps.IterationType"]}, "selectShowType": "badge"}], "handleColumn": {"title": "操作", "width": 120, "align": "center", "fixed": "right"}, "handleColumnButtons": {"showCount": 3, "preRender": {"useExpr": true, "wrapFunction": false, "dependencyVariableList": []}, "postRender": {"useExpr": true, "wrapFunction": false, "dependencyVariableList": []}}, "topButtons": {"showCount": 3, "preRender": {"useExpr": true, "wrapFunction": false, "dependencyVariableList": []}, "postRender": {"useExpr": true, "wrapFunction": false, "dependencyVariableList": []}}, "options": {"show": true}, "topRightButtons": {"showCount": 3, "preRender": {"useExpr": true, "wrapFunction": false, "dependencyVariableList": []}, "postRender": {"useExpr": true, "wrapFunction": false, "dependencyVariableList": []}}, "pagination": {"enabled": true, "pageSize": 10}, "dataSource": {"sourceType": "request", "requestDependParams": {"value": "return {\n  TenantUUID: window.jiguang_currentNs,\n  DeletedAt: 0\n}", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return {\n      TenantUUID: window.jiguang_currentNs,\n      DeletedAt: 0\n    };\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": []}, "requestCode": {"value": "// 工具类\nfunction requestCloudApi(options) {\n  const {\n    action,\n    paramVar,\n    resultCode,\n    outerParamName,\n    isPaging = false\n  } = options\n  return $utils.api.requestCloudApi(action, {\n    [outerParamName]: paramVar\n  }).then(res => {\n    if (res.Error) {\n      throw new Error(res.Error.Message)\n    } else {\n      // 如果有分页，则返回数据和分页\n      if (isPaging) {\n        return {\n          data: res[resultCode],\n          total: res.Total\n        }\n      }\n      return res[resultCode]\n    }\n  }).catch(error => {\n    $utils.ui.message.error(error.message);\n    throw error;\n  })\n}\n\n// 将常规参数转换为数据引擎参数(读取表格依赖参数并转换数据格式)\nconst tableFilterParams = $utils.api.transformCloudApiParams(\n  $context.tableFilterParams || {},\n  {\n    //TODO: 搜索条件默认会使用like模糊搜索，可以在这里将精准搜索的字段进行指定\n    useEqFields: [\"TenantUUID\",\"DeletedAt\"],\n    //排序字段，格式为 {Key:\"Name\",Sort:\"DESC\"}\n    order: [],\n  }\n)\n// 请求数据引擎数据(请求表格数据，需完善参数)\ntry {\n  const tableData = await requestCloudApi({\n    action: \"ListIterationsWithDetail\",\n    paramVar: tableFilterParams,\n    resultCode: \"ListIterations\",\n    outerParamName: \"Iteration\",\n    isPaging: true\n  });\n  return tableData;\n  // 编码模块(将请求返回的数据处理为表格可识别的数据格式)\n  //如果返回总条数，说明分页了，否则就是没有分页\n  const {\n    data,\n    total\n  } = typeof tableData.total === \"number\" ? tableData: {\n    data: tableData\n  }\n  return {\n    data: data || [],\n    success: true,\n    total: total\n  }\n} catch (error) {\n  // 编码模块(接口请求失败返回空数组给表格)\n  return {\n    success: false,\n    data: []\n  }\n}", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = async function () {\n  try {\n    // 工具类\n    function requestCloudApi(options) {\n      const {\n        action,\n        paramVar,\n        resultCode,\n        outerParamName,\n        isPaging = false\n      } = options;\n      return $utils.api.requestCloudApi(action, {\n        [outerParamName]: paramVar\n      }).then(res => {\n        if (res.Error) {\n          throw new Error(res.Error.Message);\n        } else {\n          // 如果有分页，则返回数据和分页\n          if (isPaging) {\n            return {\n              data: res[resultCode],\n              total: res.Total\n            };\n          }\n          return res[resultCode];\n        }\n      }).catch(error => {\n        $utils.ui.message.error(error.message);\n        throw error;\n      });\n    }\n\n    // 将常规参数转换为数据引擎参数(读取表格依赖参数并转换数据格式)\n    const tableFilterParams = $utils.api.transformCloudApiParams($context.tableFilterParams || {}, {\n      //TODO: 搜索条件默认会使用like模糊搜索，可以在这里将精准搜索的字段进行指定\n      useEqFields: [\"TenantUUID\", \"DeletedAt\"],\n      //排序字段，格式为 {Key:\"Name\",Sort:\"DESC\"}\n      order: []\n    });\n    // 请求数据引擎数据(请求表格数据，需完善参数)\n    try {\n      const tableData = await requestCloudApi({\n        action: \"ListIterationsWithDetail\",\n        paramVar: tableFilterParams,\n        resultCode: \"ListIterations\",\n        outerParamName: \"Iteration\",\n        isPaging: true\n      });\n      return tableData;\n      // 编码模块(将请求返回的数据处理为表格可识别的数据格式)\n      //如果返回总条数，说明分页了，否则就是没有分页\n      const {\n        data,\n        total\n      } = typeof tableData.total === \"number\" ? tableData : {\n        data: tableData\n      };\n      return {\n        data: data || [],\n        success: true,\n        total: total\n      };\n    } catch (error) {\n      // 编码模块(接口请求失败返回空数组给表格)\n      return {\n        success: false,\n        data: []\n      };\n    }\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.tableFilterParams"]}}, "rowSelection": {"show": true, "rowKey": "UUID", "preserveSelectedRowKeys": {"value": true, "useExpr": false, "wrapFunction": false}, "type": "radio", "fixed": false, "tableAlertRender": true, "defaultSelectedRowKeys": {"value": "return $context.inputParams.IterationUUID ? [ $context.inputParams.IterationUUID] : []", "useExpr": true, "wrapFunction": false, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    return $context.inputParams.IterationUUID ? [$context.inputParams.IterationUUID] : [];\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": ["$context.inputParams.IterationUUID", "$context.inputParams.IterationUUID]"]}, "getCheckboxProps": {"value": "\n    /**\n     * 可返回的属性\n     * disabled: boolean  是否禁用复选框\n     * tooltip: string | React.ReactNode  在复选框上面添加冒泡信息\n     */\n    return {}\n                    ", "useExpr": true, "wrapFunction": true, "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    /**\n     * 可返回的属性\n     * disabled: boolean  是否禁用复选框\n     * tooltip: string | React.ReactNode  在复选框上面添加冒泡信息\n     */\n    return {};\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()", "dependencyVariableList": []}}, "search": {"labelWidth": 100, "defaultCollapsed": true, "filterType": "query", "syncToUrl": false}}, "id": "2ca99708-bb0b-4c08-9672-b3266fc03c63", "type": "table", "category": "component", "events": {"eventList": []}, "name": "基础表格", "code": "table_1692067670669", "extra": {"targetGroupName": "container"}, "advancedProperties": {}, "commonProperties": {"name": "基础表格", "code": "table_1692067670669", "isRender": {"value": true, "useExpr": false, "wrapFunction": false}, "style": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0}}}], "events": {"eventList": [{"eventName": "onOk", "eventType": "custom", "eventCode": "// 工具类\nfunction getComponentRef(code) {\n  const ref = $context.pageMapRef[code]\n  if (ref?.current) {\n    return ref\n  } else {\n    console.error(\"组件不存在\")\n  }\n}\n\n// 读取表格勾选的数据key\nconst result = getComponentRef(\"table_1692067670669\")?.current?.getSelectedRows?.();\nif(result?.length) {\n   $utils.dialog.close({\n    operationType:\"confirm\",\n    iteration: result[0]\n  });\n}else{\n  $utils.dialog.close({\n    operationType:\"confirm\"\n  });\n}", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    var _getComponentRef, _getComponentRef$curr, _getComponentRef$curr2;\n    // 工具类\n    function getComponentRef(code) {\n      const ref = $context.pageMapRef[code];\n      if (ref !== null && ref !== void 0 && ref.current) {\n        return ref;\n      } else {\n        console.error(\"组件不存在\");\n      }\n    }\n\n    // 读取表格勾选的数据key\n    const result = (_getComponentRef = getComponentRef(\"table_1692067670669\")) === null || _getComponentRef === void 0 ? void 0 : (_getComponentRef$curr = _getComponentRef.current) === null || _getComponentRef$curr === void 0 ? void 0 : (_getComponentRef$curr2 = _getComponentRef$curr.getSelectedRows) === null || _getComponentRef$curr2 === void 0 ? void 0 : _getComponentRef$curr2.call(_getComponentRef$curr);\n    if (result !== null && result !== void 0 && result.length) {\n      $utils.dialog.close({\n        operationType: \"confirm\",\n        iteration: result[0]\n      });\n    } else {\n      $utils.dialog.close({\n        operationType: \"confirm\"\n      });\n    }\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()"}, {"eventName": "onCancel", "eventType": "custom", "eventCode": "\n$utils.dialog.close({\n  operationType:\"cancel\"\n});", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = function () {\n  try {\n    $utils.dialog.close({\n      operationType: \"cancel\"\n    });\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()"}, {"eventName": "onOpen", "eventType": "custom", "eventCode": "// 工具类\nfunction requestCloudApi(options) {\n  const {\n    action,\n    paramVar,\n    resultCode,\n    outerParamName,\n    isPaging = false\n  } = options\n  return $utils.api.requestCloudApi(action, {\n    [outerParamName]: paramVar\n  }).then(res => {\n    if (res.Error) {\n      throw new Error(res.Error.Message)\n    } else {\n      // 如果有分页，则返回数据和分页\n      if (isPaging) {\n        return {\n          data: res[resultCode],\n          total: res.Total\n        }\n      }\n      return res[resultCode]\n    }\n  }).catch(error => {\n    $utils.ui.message.error(error.message);\n    throw error;\n  })\n}\n\nasync function requestLookups(types, useTenantID) {\n  return await requestCloudApi({\n    action: \"ListProductDictionaryDetails\",\n    paramVar: $utils.api.transformCloudApiParams({\n      Type: {\n        Op: \"in\",\n        Value: types || []\n      },\n      TenantID: useTenantID ? window.jiguang_currentNs: 0\n    }, {\n      useEqFields: [\"TenantID\"]\n    }),\n    resultCode: \"ListProductDictionaries\",\n    outerParamName: \"ProductDictionary\"\n  })\n}\n\nfunction arrayToObject(array, key) {\n  const object = {}\n  array.forEach(item => {\n    if (!object[item[key]]) {\n      object[item[key]] = []\n    }\n    object[item[key]].push(item)\n  })\n  return object;\n}\n\n// 加载数据字典值\ntry {\n  const lookupResult_IterationType_IterationStatus = await requestLookups([\"IterationType\",\n    \"IterationStatus\"],\n    false);\n  $utils.state.updateState(state => {\n    state.lookupMaps = {\n      ...state.lookupMaps,\n      ...arrayToObject(lookupResult_IterationType_IterationStatus || [], \"Type\")\n    }\n  })\n\n} catch (error) {}", "compileExprCode": "return (function(){\n    \"use strict\";\n\nconst __result__ = async function () {\n  try {\n    // 工具类\n    function requestCloudApi(options) {\n      const {\n        action,\n        paramVar,\n        resultCode,\n        outerParamName,\n        isPaging = false\n      } = options;\n      return $utils.api.requestCloudApi(action, {\n        [outerParamName]: paramVar\n      }).then(res => {\n        if (res.Error) {\n          throw new Error(res.Error.Message);\n        } else {\n          // 如果有分页，则返回数据和分页\n          if (isPaging) {\n            return {\n              data: res[resultCode],\n              total: res.Total\n            };\n          }\n          return res[resultCode];\n        }\n      }).catch(error => {\n        $utils.ui.message.error(error.message);\n        throw error;\n      });\n    }\n    async function requestLookups(types, useTenantID) {\n      return await requestCloudApi({\n        action: \"ListProductDictionaryDetails\",\n        paramVar: $utils.api.transformCloudApiParams({\n          Type: {\n            Op: \"in\",\n            Value: types || []\n          },\n          TenantID: useTenantID ? window.jiguang_currentNs : 0\n        }, {\n          useEqFields: [\"TenantID\"]\n        }),\n        resultCode: \"ListProductDictionaries\",\n        outerParamName: \"ProductDictionary\"\n      });\n    }\n    function arrayToObject(array, key) {\n      const object = {};\n      array.forEach(item => {\n        if (!object[item[key]]) {\n          object[item[key]] = [];\n        }\n        object[item[key]].push(item);\n      });\n      return object;\n    }\n\n    // 加载数据字典值\n    try {\n      const lookupResult_IterationType_IterationStatus = await requestLookups([\"IterationType\", \"IterationStatus\"], false);\n      $utils.state.updateState(state => {\n        state.lookupMaps = {\n          ...state.lookupMaps,\n          ...arrayToObject(lookupResult_IterationType_IterationStatus || [], \"Type\")\n        };\n      });\n    } catch (error) {}\n  } catch (error) {\n    console.error(\"表达式解析错误，错误原因: \", error, error.message);\n    if ($utils) {\n      $utils.ui.message.error(\"表达式执行错误，详情请查看浏览器控制台日志\");\n    }\n  }\n}();\n      return __result__;\n  })()"}]}, "extra": {}, "code": "modal_1692067628869", "advancedProperties": {}}]}