<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="shortcut icon" href="%PUBLIC_URL%/static/img/favicon.ico" />
    <title>CSIG极光</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="jiguang-platform" />
    <link  rel="stylesheet" type="text/css" href="%PUBLIC_URL%/layout.css"/>
  </head>
  <body class="jiguang-platform">
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root-layout">
      <div
        style="
          text-align: center;
          background: #f0f2f5;
          width: 100vw;
          height: 100vh;
        "
      >
        欢迎使用TCE极光平台,请稍安勿躁! 系统正在努力加载中......
      </div>
    </div>

<script type="text/javascript">
  /*1.配置基本信息*/
      window["jiguang_domain"]="pre.jiguang2.tce.oa.com"
      window["jiguang_api_domain"]="pre.jiguang2.tce.oa.com/gw"
      window['jiguang_username']="gavingliang"
      window['jiguang_dataRight']={basic_data_center: []}
      window['jiguang_currentRole']="jiguang2_manager";
      window['jiguang_headerRoleInfo']=[
                                        { code: 'jiguang2_manager', name: '新极光管理员' },
                                        { code: 'developer', name: '研发工程师' },
                                        { code: 'architect', name: '架构师' },
                                        { code: 'business', name: '商务' },
                                        { code: 'region_engineer', name: '区域技术工程师' },
                                      ];

      var _JIGUANG_APP_TOKEN={basic_data_center:'eyJhbGciOiJIUzI1Ni'}
      Object.keys(_JIGUANG_APP_TOKEN).forEach(key=>{
          window.localStorage.setItem(key,_JIGUANG_APP_TOKEN[key])
      })

     /*2.导航应用配置*/
      window['jiguang_headerNavApp'] = [
        {
        appCode: 'basic_data_center',
        appName: '基础数据中心',
        defaultUrl: '/page/basic_data_center',
        js: [
          'http://localhost:3008/static/js/jiguang-dev-basic.js',
        ],
        css: [
          '/admin/upload/basic_data_center/css/basic_data_center.css?id=1639473052155',
        ],
        typeCode: 'test',
        typeName: '规划中心',
        parentApp: '',
      },
      {
        appCode: 'admin',
        appName: '极光管理平台',
        defaultUrl: '/page/admin',
        js: ['/admin/upload/admin/js/admin.js?id=1629186848889'],
        css: ['/admin/upload/admin/css/admin.css?id=1629117471892'],
        typeCode: 'admin',
        typeName: '极光管理平台',
        parentApp: '',
      },
 ];

    /*3.菜单配置*/
      window['jiguang_menus'] = {
          basic_data_center: [
          {
            id: 236,
            menu_code: 'basic_data_center',
            menu_name: '基础数据管理中心',
            parent_id: 0,
            menu_level: 0,
            uri: '',
            icon: '',
            level_order: 1,
            enabled: 'Y',
            children: [
              {
                id: 237,
                menu_code: 'basic_data_center_solution_management',
                menu_name: '解决方案管理',
                parent_id: 236,
                menu_level: 1,
                uri: '',
                icon: null,
                level_order: 1,
                enabled: 'Y',
                children: [
                  {
                    id: 238,
                    menu_code: 'basic_data_center_solution_kanban',
                    menu_name: '解决方案看板',
                    parent_id: 237,
                    menu_level: 2,
                    uri: '/solution_manage/kanban',
                    icon: null,
                    level_order: 1,
                    enabled: 'Y',
                    children: [],
                  },
                  {
                    id: 239,
                    menu_code: 'basic_data_center_solution_definition',
                    menu_name: '解决方案定义',
                    parent_id: 237,
                    menu_level: 2,
                    uri: '/solution_manage/definition',
                    icon: null,
                    level_order: 2,
                    enabled: 'Y',
                    children: [],
                  },
                ],
              },
              {
                id: 241,
                menu_code: 'basic_data_center_sub-product_management1',
                menu_name: '子产品管理',
                parent_id: 236,
                menu_level: 1,
                uri: '',
                icon: null,
                level_order: 2,
                enabled: 'Y',
                children: [
                  {
                    id: 247,
                    menu_code: 'basic_data_center_sub_product_kanban',
                    menu_name: '子产品看板',
                    parent_id: 241,
                    menu_level: 2,
                    uri: '/subproduct/kanban',
                    icon: null,
                    level_order: 1,
                    enabled: 'Y',
                    children: [],
                  }
                ],
              },
            ],
          },
        ]
  };
    </script>
    <script type="text/javascript" src="%PUBLIC_URL%/layout.js"></script>
    <script type="text/javascript" src="//top.oa.com/js/users.js"></script>
  </body>
</html>
