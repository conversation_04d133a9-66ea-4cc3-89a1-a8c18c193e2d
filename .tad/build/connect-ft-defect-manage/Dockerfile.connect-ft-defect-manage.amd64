FROM mirrors.tencent.com/connectfe/tad:0.0.3 AS builder

ARG WORKSPACE=/data/source
ARG APPLICATION_NAME
ARG QCI_REPO_BRANCH
ARG QCI_REPO_COMMIT
ARG TAD_PACKAGE_BUILD_NO

WORKDIR ${WORKSPACE}
COPY . ${WORKSPACE}

RUN npm install @tencent/tnpm@9.0.0 -g --registry=http://r.tnpm.oa.com
RUN tnpm install
RUN tnpm run build

RUN ls -la /data/source/build/static

ENV APPLICATION_NAME=${APPLICATION_NAME}
ENV QCI_REPO_BRANCH=${QCI_REPO_BRANCH}
ENV QCI_REPO_COMMIT=${QCI_REPO_COMMIT}
ENV TAD_PACKAGE_BUILD_NO=${TAD_PACKAGE_BUILD_NO}

RUN echo APPLICATION_NAME=$APPLICATION_NAME && echo QCI_REPO_BRANCH=$QCI_REPO_BRANCH && echo QCI_REPO_COMMIT=$QCI_REPO_COMMIT

WORKDIR /data/deploy_shells

RUN <NAME_EMAIL>:jiguang-web/deploy_shells.git .
RUN ls

# 运行 generator_version_info.bash 脚本
RUN ./generator_version_info.bash

# 新增以下两行命令
RUN mkdir -p /data/bin
RUN cp -R frontend/* /data/bin

FROM mirrors.tencent.com/connectfe/tlinux4-minimal-s3cmd:latest

RUN mkdir -p /data/software && mkdir -p /data/bin && mkdir -p /data/config

# copy打包后资源到目标镜像
COPY --from=builder /data/source/build/static/css /data/temp_software/css
COPY --from=builder /data/source/build/static/js /data/temp_software/js
COPY --from=builder /data/source/build/static/media /data/temp_software/media
# 将生成的 version.json 文件复制到目标镜像
COPY --from=builder /data/deploy_shells/versionInfo/ /data/temp_software/versionInfo/

# copy部署命令到目标镜像
COPY --from=builder /data/bin /data/bin
